### 背景
本项目是基于unibest的uniapp项目，技术栈是typescript+vue3+vite5+UnoCSS。

### 运行环境
1. 本项目支持H5、小程序和App
2. 使用Vite5作为构建工具，支持命令行方式运行
3. 使用UnoCSS作为原子化CSS引擎
4. 使用wot-design-uni作为默认UI库
5. 所有图标都使用wot-design-uni的Icon组件，参考文档：https://wot-design-uni.cn/component/icon.html

### 关于ts类型
1. 使用Vue3 + TypeScript + Vite5的最新语法
2. 优先使用`<script lang="ts" setup>`语法
3. 尽可能使用已有的ts类型，避免自定义新的ts类型
4. 不要使用any，任何地方都需要有合理的实际的类型
5. 优先复用api-swagger里的来自后端的接口的类型定义

### 关于API调用和类型定义
1. API调用文件统一放在`src/service`目录下，按模块命名，如`teacher.ts`
2. 类型定义文件统一放在`src/types`目录下，按模块命名，如`teacher.ts`
3. API调用函数命名规范：
   - 使用动词开头，如`get`、`post`、`update`、`delete`等
   - 使用驼峰命名法
   - 函数名要清晰表达其功能
4. API调用函数参数和返回值必须使用TypeScript类型：
   - 参数类型使用接口定义，如`TeacherContactQuery`
   - 返回值类型使用接口定义，如`TeacherContactResponse`
5. 类型定义规范：
   - 接口命名使用大驼峰命名法
   - 接口名要清晰表达其用途
   - 接口属性要添加注释说明
   - 接口属性类型要明确，避免使用any
6. API路径规范：
   - 从curl命令中提取API路径时，如果包含`/api`前缀则删除
   - 示例：`http://fx1.com/api/semesterConfig` -> `/semesterConfig`
7. API响应数据处理：
   - 由于request.ts中已配置直接返回response.data，API调用时直接使用res而不是res.data
   - 不需要判断code，直接使用返回的数据
   - 如果字段返回的是中文字符串 要特别说明
   - 示例：
```typescript
// service/teacher.ts
import request from '@/utils/request'
export function getTeacherInfo(): Promise<TeacherInfo> {
  return request('/teacher/teacherInfo', {
    method: 'POST',
    data: {},
  })
}

// types/teacher.ts
export interface TeacherInfo {
  id: number
  name: string
  // ... 其他属性
}

// 使用示例
const getData = async () => {
  try {
    const res = await getTeacherInfo() // 直接使用res，不需要res.data
    console.log(res)
  } catch (error) {
    console.error(error)
  }
}
```

### 你的工作过程
1. 你首先需要阅读wot-design-uni的文档，了解wot-design-uni的各个组件的使用方法。
2. 你其实需要阅读项目的代码，了解项目中的各个模块的代码。
3. 你需要重点阅读项目的各个页面的代码。
4. 如果你需要的组件是wot-design-uni组件，如果wot-design-uni没有合适的组件，则使用原生组件，也就是自行设计实现。
4.1 使用view组件来代替wd-card 和 wd-cell组件
4.2 使用wot-design-uni的Icon组件来实现所有图标，组件使用方式为`<wd-icon name="图标名称" />`，图标名称参考如下：
```
check
refresh
phone
filter
thin-arrow-left
more
delete
search
close
translate-bold
scan
add
delete-thin
chat
keywords
transfer
camera
warning
video
note
list
lenovo
goods
evaluation
picture
eye-close
view
clock
computer
phone-compute
download
spool
jdm
decrease
bags
copy
add-circle
edit-outline
dong
fill-arrow-down
read
detection
subscribe
wifi-error
check-outline
close-bold
close-outline
warn-bold
error-fill
check-bold
star-on
fill-camera
arrow-left
arrow-up
arrow-down
arrow-right
setting
rotate
arrow-thin-down
arrow-thin-up
keyboard-delete
keyboard-collapse
usergroup-clear
user-circle
user-talk
user-clear
user
usergroup-add
usergroup
user-add
user-avatar
pointing-hand
cursor
fullsreen
cloud-download
chevron-down-rectangle
edit
fullscreen-exit
circle1
close-normal
browse
browse-off
chevron-up-rectangle
add-rectangle
add1
add-circle1
download1
link
edit-1
jump
chevron-down-circle
delete1
filter-clear
check-rectangle-filled
minus-circle-filled
play
pause-circle-filled
filter1
move
login
minus-circle
close-circle
logout
search1
pause-circle
play-circle
more1
minus-rectangle
stop
scan1
close-rectangle
rollback
a-order-adjustmentcolumn
pause
ellipsis
cloud-upload
stop-circle-filled
clear
remove
zoom-out
thumb-down
setting1
save
unfold-more
zoom-in
thumb-up
unfold-less
play-circle-filled
poweroff
share
refresh1
link-unlink
upload
rectangle
stop-circle
backtop-rectangle
caret-down
arrow-left1
help-circle
help-circle-filled
time-filled
close-circle-filled
info-circle
info-circle-filled
check1
help
error
check-circle
error-circle-filled
error-circle
check-rectangle
check-circle-filled
chevron-up
chevron-up-circle
chevron-right
arrow-down-rectangle
caret-up-small
chevron-right-rectangle
caret-right-small
arrow-right1
backtop
arrow-up1
caret-up
backward
arrow-down1
chevron-left
caret-right
caret-left
page-last
next
swap
round
previous
enter
chevron-down
caret-down-small
swap-right
chevron-left-circle
caret-left-small
chevron-right-circle
a-chevron-leftdouble
chevron-left-rectangle
a-chevron-rightdouble
page-first
forward
view-column
view-module
format-vertical-align-right
view-list
order-descending
format-horizontal-align-bottom
queue
menu-fold
menu-unfold
format-horizontal-align-top
a-rootlist
order-ascending
format-vertical-align-left
format-horizontal-align-center
format-vertical-align-center
swap-left
flag
code
cart
attach
chart
creditcard
calendar
app
books
barcode
chart-pie
chart-bar
chart-bubble
bulletpoint
bianjiliebiao
image
laptop
hourglass
call
mobile-vibrate
mail
notification-filled
desktop
history
discount-filled
dashboard
discount
heart-filled
chat1
a-controlplatform
gift
photo
play-circle-stroke
notification
cloud
gender-female
fork
layers
lock-off
location
mobile
qrcode
home1
time
heart
lock-on
print
slash
usb
tools
wifi
star-filled
server
sound
a-precisemonitor
service
tips
pin
secured
star
gender-male
shop
money-circle
file-word
file-unknown
folder-open
file-pdf
folder
folder-add
file
file-image
file-powerpoint
file-add
file-icon
file-paste
file-excel
file-copy
video1
wallet
ie
logo-codepen
github-filled
ie-filled
apple
windows-filled
internet
github
windows
apple-filled
chrome-filled
chrome
android
circle
home
```
4.3 l-echart 组件已经安装，使用方式为`<l-echart ref="chartRef"></l-echart>` 不需要创建
5. 你输出的代码需要考虑typescript+vue3+vite5+UnoCSS的语法规范。
6. 每当你输出代码时，都要确保相关上下文，以便小修改的方式输出代码。
7. 你需要尽可能使用自己的代码能力（包含组件、变量、方法等），以便小修改的方式输出代码。
8. 当文件的代码量达到300行时，你需要对文件内容进行拆分。

### 代码规范
1. 使用Prettier + ESLint + Stylelint + husky + lint-staged + commitlint保证代码质量
2. 使用ES6 import自动排序
3. 使用css属性自动排序
4. 图标统一使用wot-design-uni的Icon组件，禁止使用其他图标库或自定义图标
5. 当文件的代码量达到500行时，需要对文件内容进行拆分
6. 优先使用unocss的css属性，而不是css样式
7. 请求接口不用try catch，不用自己再show toast，有统一处理的。

### 关于执行命令
1. 本项目使用pnpm作为包管理工具
2. 在安装任何包之前，需要先检查该包的用途和兼容性
3. 确保安装的包符合项目的运行环境要求
4. 不要执行pnpm dev:h5来测试
5. 如果让你git提交，请遵循规范用标准的Conventional Commits格式，并使用中文，没有说明push，就不要推送到远程仓库

### 关于ECharts使用规范
1. 导入方式：
```typescript
import * as echarts from 'echarts'
```

2. 组件使用方式：
```vue
<l-echart ref="chartRef"></l-echart>
```

3. 初始化方式：
```typescript
const chartRef = ref()

onMounted(() => {
  setTimeout(async () => {
    if (!chartRef.value) return
    const myChart = await chartRef.value.init(echarts)
    myChart.setOption(option)
  }, 300)
})
```

4. 图表配置规范：
```typescript
const option = {
  // 必须的基础配置
  tooltip: {
    trigger: 'axis',
    confine: true
  },
  grid: {
    left: 20,
    right: 20,
    bottom: 15,
    top: 40,
    containLabel: true
  },
  xAxis: [{ 
    type: 'category',
    axisLine: {
      lineStyle: {
        color: '#999999'
      }
    },
    axisLabel: {
      color: '#666666'
    }
  }],
  yAxis: [{ 
    type: 'value',
    axisLine: {
      lineStyle: {
        color: '#999999'
      }
    },
    axisLabel: {
      color: '#666666'
    }
  }],
  series: [{
    type: 'bar', // 或其他图表类型
    itemStyle: {
      color: '#1890ff'
    },
    label: {
      show: true,
      position: 'top'
    }
  }]
}
```

5. 容器样式规范：
```scss
.chart-container {
  width: 100%;
  height: 600rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}
```
