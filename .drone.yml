kind: pipeline
type: docker
name: default

volumes:
  - name: fe-path
    host:
      path: /home/<USER>/apps/openresty/openresty/www/sites/jw-dev-h5.fjpit.com
clone:
  disable: true

trigger:
  branch:
    - dev
  event:
    - custom

steps:
  - name: pull
    image: alpine/git
    volumes:
      - name: fe-path
        path: /fe
    commands:
      - git config --global --add safe.directory /fe
      - git -C /fe/code pull
  - name: install
    image: 1panel/node:20.14.0
    volumes:
      - name: fe-path
        path: /fe
    commands:
      - cd /fe/code
      - pnpm run build:prod
      - rm -rf /fe/index/* || { echo "清空目标目录失败"; exit 1; }
      - cp -r /fe/code/dist/build/h5/* /fe/index/ || { echo "复制文件失败"; exit 1; }
  - name: feishu-notify
    image: keepchen/drone-feishu:latest
    settings:
      token: 57ed6d24-a5c9-4ba7-8fca-3505649b3af0
      secret: Jiy0SrkM2VsaSC8hxZSj5e
      card_title: "移动端部署成功"
    when:
      status:
        - success
