import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: 'unibest',
    navigationBarBackgroundColor: '#ffffff',
    navigationBarTextStyle: 'black',
    backgroundColor: '#f7f8fa',
  },
  easycom: {
    autoscan: true,
    custom: {
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue',
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
    },
  },
  tabBar: {
    color: '#999999',
    selectedColor: '#3a8eff',
    backgroundColor: '#ffffff',
    borderStyle: 'white',
    height: '100rpx',
    fontSize: '20rpx',
    iconWidth: '40rpx',
    spacing: '8rpx',
    list: [
      {
        iconPath: 'static/tabbar/home.png',
        selectedIconPath: 'static/tabbar/home-active.png',
        pagePath: 'pages/index/index',
        text: '首页',
      },
      {
        iconPath: 'static/tabbar/workflow.png',
        selectedIconPath: 'static/tabbar/workflow-active.png',
        pagePath: 'pages/application/index',
        text: '应用',
      },
      {
        iconPath: 'static/tabbar/apps.png',
        selectedIconPath: 'static/tabbar/apps-active.png',
        pagePath: 'pages/workflow/index',
        text: '流程',
      },
      {
        iconPath: 'static/tabbar/message.png',
        selectedIconPath: 'static/tabbar/message-active.png',
        pagePath: 'pages/Mail/mailList',
        text: '消息',
      },
      {
        iconPath: 'static/tabbar/profile.png',
        selectedIconPath: 'static/tabbar/profile-active.png',
        pagePath: 'pages/about/index',
        text: '我的',
      },
    ],
  },
})
