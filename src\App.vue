<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'
import { useUserStore } from '@/store/user'
import { useMenuStore } from '@/store/menu'
import { getUserMenus } from '@/service/auth'

// 白名单路径，这些页面不需要登录就能访问
const whiteList = ['/pages/login/login', '/pages/login/auto-login']
const userStore = useUserStore()
const menuStore = useMenuStore()

/**
 * 初始化菜单数据
 * 获取最新的菜单数据并存入store
 */
const initMenuData = async () => {
  try {
    // 只有在用户已登录的情况下才获取菜单
    if (userStore.tokenInfo.token) {
      const menus = await getUserMenus()
      menuStore.setMenus(menus)
    }
  } catch (error) {
    console.error('获取菜单数据失败:', error)
  }
}

/**
 * 解析URL参数
 */
const parseUrlParams = (url: string) => {
  const params: Record<string, string> = {}
  const queryString = url.split('?')[1]
  if (!queryString) return params

  const paramPairs = queryString.split('&')
  paramPairs.forEach((pair) => {
    const [key, value] = pair.split('=')
    params[key] = decodeURIComponent(value || '')
  })
  return params
}

/**
 * 从URL获取需要跳转的路径
 * @param url 完整URL
 * @returns 路径信息，如 /pages/xxx/xxx
 */
const getRoutePathFromUrl = (url: string) => {
  // 在H5环境下，从URL中提取路径
  if (url.includes('#/')) {
    // 例如 http://192.168.199.105:9000/#/pages/teacher/teaching-affairs/mediation-course?token=xxx&uid=2
    const hashPath = url.split('#')[1] // 获取 /pages/teacher/teaching-affairs/mediation-course?token=xxx&uid=2
    return hashPath.split('?')[0] // 获取 /pages/teacher/teaching-affairs/mediation-course
  }
  return ''
}

/**
 * 检查URL中是否包含自动登录参数
 */
const checkAutoLoginParams = () => {
  // 获取当前URL
  const currentUrl =
    uni.getSystemInfoSync().platform === 'mp-weixin'
      ? '' // 微信小程序不支持获取URL
      : window.location.href

  // 解析URL参数
  const query =
    uni.getSystemInfoSync().platform === 'mp-weixin'
      ? {} // 微信小程序不支持获取URL参数
      : parseUrlParams(currentUrl)

  return {
    hasLoginParams: !!(query.token && query.uid),
    token: query.token as string,
    uid: parseInt(query.uid as string),
    currentUrl,
    query,
  }
}

/**
 * 重定向到自动登录页面
 * @param targetPath 目标路径
 * @param query 查询参数
 * @param token 登录token（可选）
 * @param uid 用户ID（可选）
 */
const redirectToAutoLogin = (
  targetPath: string,
  query: Record<string, string>,
  token?: string,
  uid?: string,
) => {
  // 保存重定向信息到store（不强制覆盖已有值）
  userStore.setRedirectInfo(
    {
      path: targetPath,
      query,
    },
    false,
  )

  // 构建自动登录URL
  let url = '/pages/login/auto-login'

  // 如果有token和uid，直接添加到URL以便自动登录
  if (token && uid) {
    url += `?token=${encodeURIComponent(token)}&uid=${encodeURIComponent(uid)}`
  }

  console.log('重定向到自动登录页面, 目标路径:', targetPath)

  // 跳转到自动登录页面
  uni.reLaunch({
    url,
  })

  return false
}

// 全局路由守卫
uni.addInterceptor('navigateTo', {
  invoke(e) {
    const token = userStore.tokenInfo.token
    const url = e.url

    // 如果已登录且正在访问登录页，则直接跳转到首页
    if (
      token &&
      url.startsWith('/pages/login/login') &&
      !url.startsWith('/pages/login/auto-login')
    ) {
      uni.switchTab({
        url: '/pages/index/index',
      })
      return false
    }

    // 如果没有token且不在白名单中，则需要登录
    if (!token && !whiteList.some((path) => url.startsWith(path))) {
      // 检查URL中是否包含自动登录参数
      const { hasLoginParams, token: autoLoginToken, uid, query } = checkAutoLoginParams()

      // 获取目标路径（去掉/pages前缀）
      const targetPath = url

      // 如果有自动登录参数，跳转到自动登录页面
      if (hasLoginParams) {
        return redirectToAutoLogin(targetPath, query, autoLoginToken, String(uid))
      } else {
        // 没有自动登录参数，也跳转到自动登录页面，由自动登录页面处理获取登录凭证
        return redirectToAutoLogin(targetPath, query)
      }
    }

    return true
  },
})

// 同样需要拦截 switchTab
uni.addInterceptor('switchTab', {
  invoke(e) {
    const token = userStore.tokenInfo.token
    const url = e.url

    // 如果已登录且正在访问登录页，则直接跳转到首页
    if (token && url.startsWith('/pages/login/login')) {
      uni.switchTab({
        url: '/pages/index/index',
      })
      return false
    }

    // 如果没有token且不在白名单中，则需要登录
    if (!token && !whiteList.some((path) => url.startsWith(path))) {
      // 检查URL中是否包含自动登录参数
      const { hasLoginParams, token: autoLoginToken, uid, query } = checkAutoLoginParams()

      // 获取目标路径
      const targetPath = url

      // 如果有自动登录参数，跳转到自动登录页面
      if (hasLoginParams) {
        return redirectToAutoLogin(targetPath, query, autoLoginToken, String(uid))
      } else {
        // 没有自动登录参数，也跳转到自动登录页面，由自动登录页面处理获取登录凭证
        return redirectToAutoLogin(targetPath, query)
      }
    }
    return true
  },
})

// 拦截 reLaunch
uni.addInterceptor('reLaunch', {
  invoke(e) {
    const token = userStore.tokenInfo.token
    const url = e.url

    // 如果是自动登录页面，始终允许
    if (url.startsWith('/pages/login/auto-login')) {
      return true
    }

    // 如果已登录且正在访问登录页，则直接跳转到首页
    if (
      token &&
      url.startsWith('/pages/login/login') &&
      !url.startsWith('/pages/login/auto-login')
    ) {
      uni.switchTab({
        url: '/pages/index/index',
      })
      return false
    }

    // 如果没有token且不在白名单中，则需要登录
    if (!token && !whiteList.some((path) => url.startsWith(path))) {
      // 检查URL中是否包含自动登录参数
      const { hasLoginParams, token: autoLoginToken, uid, query } = checkAutoLoginParams()

      // 获取目标路径
      const targetPath = url

      // 如果有自动登录参数，跳转到自动登录页面
      if (hasLoginParams) {
        return redirectToAutoLogin(targetPath, query, autoLoginToken, String(uid))
      } else {
        // 没有自动登录参数，也跳转到自动登录页面，由自动登录页面处理获取登录凭证
        return redirectToAutoLogin(targetPath, query)
      }
    }

    return true
  },
})

// 页面首次加载时也需要检查登录状态
onLaunch(() => {
  // 检查URL中是否包含自动登录参数
  const { hasLoginParams, token: autoLoginToken, uid, query, currentUrl } = checkAutoLoginParams()

  // 获取当前页面路径
  const currentPath = getRoutePathFromUrl(currentUrl) || '/pages/index/index'

  // 如果当前是自动登录页面，不做处理
  if (currentPath.startsWith('/pages/login/auto-login')) {
    return
  }

  // 检查是否已登录
  const token = userStore.tokenInfo.token

  // 如果当前是登录页且已有token，则跳转到首页
  if (currentPath.startsWith('/pages/login/login') && token) {
    uni.switchTab({
      url: '/pages/index/index',
    })
    return
  }

  // 如果当前页面不在白名单中且没有token
  if (!whiteList.some((path) => currentPath.startsWith(path)) && !token) {
    // 如果URL包含token和uid参数，跳转到自动登录页面
    if (hasLoginParams) {
      redirectToAutoLogin(currentPath, query, autoLoginToken, String(uid))
    } else {
      // 没有登录参数，跳转到自动登录页，尝试获取登录凭证
      redirectToAutoLogin(currentPath, query)
    }
  }

  // 初始化菜单数据
  initMenuData()
})

onShow(() => {
  console.log('App Show')
  // 每次从后台切回前台时也刷新菜单数据
})

onHide(() => {
  console.log('App Hide')
})
</script>

<style lang="scss">
/* stylelint-disable selector-type-no-unknown */
button::after {
  border: none;
}

swiper,
scroll-view {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

// 单行省略，优先使用 unocss: text-ellipsis
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 两行省略
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 三行省略
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
</style>
