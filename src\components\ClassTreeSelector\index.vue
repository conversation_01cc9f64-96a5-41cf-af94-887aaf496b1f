<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import Pagination from '@/components/Pagination/index.vue'
import { getTreeClassList } from '@/service/organization'
import type { SubstanceOrg, OrgClass } from '@/types/organization'

// 定义树形节点接口
interface TreeNode {
  id: string
  label: string
  value: string
  children?: TreeNode[]
}

// 定义组件属性
interface Props {
  // 是否多选
  multiple?: boolean
  // 是否显示搜索框
  searchable?: boolean
  // 占位符文本
  placeholder?: string
  // 选中的值
  modelValue?: string[] | string
}

const props = withDefaults(defineProps<Props>(), {
  multiple: true,
  searchable: true,
  placeholder: '请选择班级',
  modelValue: () => [],
})

// 定义emit
const emit = defineEmits(['update:modelValue', 'change'])

// 选中的值 - 使用code
const selectedValues = ref<string[]>(
  Array.isArray(props.modelValue)
    ? [...props.modelValue]
    : props.modelValue
      ? [props.modelValue]
      : [],
)

// 从value中提取code（去掉前缀）
const getCodeFromValue = (value: string): string => {
  if (value.startsWith('college_')) {
    return value.replace('college_', '')
  }
  if (value.startsWith('class_')) {
    return value.replace('class_', '')
  }
  return value
}

// 显示已选文本
const selectedText = computed(() => {
  if (selectedValues.value.length === 0) {
    return ''
  }

  const texts: string[] = []

  // 遍历选中值，查找对应节点的label
  selectedValues.value.forEach((code) => {
    let found = false

    // 遍历学院
    for (const college of treeData.value) {
      // 检查是否为学院
      if (getCodeFromValue(college.value) === code) {
        texts.push(college.label)
        found = true
        break
      }

      // 检查班级
      if (college.children) {
        for (const cls of college.children) {
          if (getCodeFromValue(cls.value) === code) {
            texts.push(cls.label)
            found = true
            break
          }
        }
        if (found) break
      }
    }
  })

  return texts.join(', ')
})

// 搜索关键词
const keyword = ref('')

// 树形数据
const treeData = ref<TreeNode[]>([])

// 加载树形班级数据
const loadTreeClassData = async () => {
  try {
    const response = await getTreeClassList()
    console.log('获取树形班级数据成功:', response)

    // 将API返回的数据格式化为组件所需的TreeNode[]格式
    const formattedData: TreeNode[] = response.map((college: SubstanceOrg) => {
      const collegeNode: TreeNode = {
        id: String(college.id),
        label: college.name,
        value: `college_${college.code}`,
        children: [],
      }

      // 处理班级子节点
      if (college.children && college.children.length > 0) {
        collegeNode.children = college.children.map((classItem: OrgClass) => ({
          id: `${college.id}_${classItem.id}`,
          label: classItem.className || classItem.name,
          value: `class_${classItem.classCode || classItem.code}`,
        }))
      }

      return collegeNode
    })

    treeData.value = formattedData
  } catch (error) {
    console.error('获取树形班级数据失败:', error)
    // 设置默认数据以防API调用失败
    treeData.value = [
      {
        id: '1',
        label: '计算机学院',
        value: 'college_1',
        children: [
          { id: '1_1', label: '计算机2023级1班', value: 'class_1001' },
          { id: '1_2', label: '计算机2023级2班', value: 'class_1002' },
        ],
      },
      {
        id: '2',
        label: '数学学院',
        value: 'college_2',
        children: [
          { id: '2_1', label: '数学2023级1班', value: 'class_2001' },
          { id: '2_2', label: '数学2023级2班', value: 'class_2002' },
        ],
      },
    ]
  }
}

// 筛选后的树形数据
const filteredTreeData = computed(() => {
  if (!keyword.value) {
    return treeData.value
  }

  return treeData.value.filter((college) => {
    // 学院名称符合关键词
    if (college.label.includes(keyword.value)) {
      return true
    }

    // 过滤符合关键词的班级
    const filteredChildren = college.children?.filter((cls) => cls.label.includes(keyword.value))

    if (filteredChildren && filteredChildren.length > 0) {
      return {
        ...college,
        children: filteredChildren,
      }
    }

    return false
  })
})

// 展开状态
const expandedColleges = ref<Set<string>>(new Set())

// 弹窗状态
const showPopup = ref(false)

// 处理搜索
const handleSearch = () => {
  // 搜索时自动展开所有学院
  expandedColleges.value = new Set(treeData.value.map((college) => college.id))
}

// 切换学院展开状态
const toggleCollege = (collegeId: string) => {
  if (expandedColleges.value.has(collegeId)) {
    expandedColleges.value.delete(collegeId)
  } else {
    expandedColleges.value.add(collegeId)
  }
}

// 判断学院是否全选
const isCollegeFullySelected = (college: TreeNode): boolean => {
  if (!college.children || college.children.length === 0) {
    return false
  }

  return college.children.every((cls) => isSelected(getCodeFromValue(cls.value)))
}

// 判断学院是否半选（部分子级被选中）
const isCollegePartiallySelected = (college: TreeNode): boolean => {
  if (!college.children || college.children.length === 0) {
    return false
  }

  // 有任意子级被选中，但不是全部
  return (
    college.children.some((cls) => isSelected(getCodeFromValue(cls.value))) &&
    !college.children.every((cls) => isSelected(getCodeFromValue(cls.value)))
  )
}

// 获取特定学院的所有子级班级code
const getCollegeClassCodes = (college: TreeNode): string[] => {
  if (!college.children || college.children.length === 0) {
    return []
  }

  return college.children.map((cls) => getCodeFromValue(cls.value))
}

// 选择/取消选择节点
const toggleSelect = (node: TreeNode, collegeNode: TreeNode) => {
  const nodeCode = getCodeFromValue(node.value)
  const collegeCode = getCodeFromValue(collegeNode.value)

  if (!props.multiple) {
    // 单选模式
    selectedValues.value = [nodeCode]
    emit('update:modelValue', nodeCode)
    emit('change', nodeCode)
    closePopup()
    return
  }

  // 多选模式
  const index = selectedValues.value.indexOf(nodeCode)

  if (index > -1) {
    // 已选中，取消选择
    selectedValues.value.splice(index, 1)

    // 如果学院被选中，也需要取消学院选择
    const collegeIndex = selectedValues.value.indexOf(collegeCode)
    if (collegeIndex > -1) {
      selectedValues.value.splice(collegeIndex, 1)
    }
  } else {
    // 未选中，添加选择
    selectedValues.value.push(nodeCode)

    // 如果该班级所在学院的所有班级都被选中，自动选中学院
    if (
      collegeNode.children &&
      collegeNode.children.every((cls) =>
        selectedValues.value.includes(getCodeFromValue(cls.value)),
      ) &&
      !selectedValues.value.includes(collegeCode)
    ) {
      selectedValues.value.push(collegeCode)
    }
  }

  emit('update:modelValue', [...selectedValues.value])
  emit('change', [...selectedValues.value])
}

// 判断节点是否被选中
const isSelected = (code: string) => {
  return selectedValues.value.includes(code)
}

// 选择/取消选择学院
const toggleCollegeSelect = (college: TreeNode) => {
  if (!props.multiple) return

  const collegeCode = getCodeFromValue(college.value)
  const classCodes = getCollegeClassCodes(college)

  if (selectedValues.value.includes(collegeCode)) {
    // 已选中，取消选择学院和所有班级
    const index = selectedValues.value.indexOf(collegeCode)
    selectedValues.value.splice(index, 1)

    // 同时取消选择所有子级班级
    classCodes.forEach((classCode) => {
      const classIndex = selectedValues.value.indexOf(classCode)
      if (classIndex > -1) {
        selectedValues.value.splice(classIndex, 1)
      }
    })
  } else {
    // 未选中，添加选择学院和所有班级
    selectedValues.value.push(collegeCode)

    // 同时选择所有子级班级
    classCodes.forEach((classCode) => {
      if (!selectedValues.value.includes(classCode)) {
        selectedValues.value.push(classCode)
      }
    })
  }

  emit('update:modelValue', [...selectedValues.value])
  emit('change', [...selectedValues.value])
}

// 打开弹窗
const openPopup = () => {
  showPopup.value = true
}

// 关闭弹窗
const closePopup = () => {
  showPopup.value = false
}

// 清空选择
const clearSelection = (e: Event) => {
  e.stopPropagation()
  selectedValues.value = []
  emit('update:modelValue', props.multiple ? [] : '')
  emit('change', props.multiple ? [] : '')
}

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (Array.isArray(newVal)) {
      selectedValues.value = [...newVal]
    } else if (newVal) {
      selectedValues.value = [newVal]
    } else {
      selectedValues.value = []
    }
  },
  { deep: true },
)

// 初始化
onMounted(() => {
  // 确保初始值同步
  if (Array.isArray(props.modelValue)) {
    selectedValues.value = [...props.modelValue]
  } else if (props.modelValue) {
    selectedValues.value = [props.modelValue]
  }

  // 在组件挂载时加载数据
  loadTreeClassData()
})
</script>

<template>
  <view class="class-tree-selector">
    <!-- 选择器输入框 -->
    <view
      class="selector-input flex items-center bg-gray-100 rounded-lg p-3 w-full relative box-border"
      @click="openPopup"
    >
      <text v-if="selectedText" class="flex-1 text-ellipsis overflow-hidden pr-2">
        {{ selectedText }}
      </text>
      <text v-else class="text-gray-400 flex-1">{{ placeholder }}</text>

      <view
        v-if="selectedValues.length > 0"
        class="clear-btn mr-2 flex-shrink-0"
        @click="clearSelection"
      >
        <wd-icon name="close" size="16px" class="text-gray-400"></wd-icon>
      </view>

      <wd-icon
        name="arrow-down"
        size="16px"
        class="text-gray-400 transition-transform flex-shrink-0"
        :class="{ 'rotate-180': showPopup }"
      ></wd-icon>
    </view>

    <!-- 弹窗 -->
    <wd-popup
      v-model="showPopup"
      position="bottom"
      :close-on-click-modal="true"
      closable
      custom-style="height: 80vh; border-radius: 16px 16px 0 0; overflow: hidden; max-width: 100%;"
      @close="closePopup"
    >
      <view class="popup-header px-4 py-3 border-b border-gray-100">
        <view class="text-lg font-semibold">选择班级</view>
      </view>

      <view class="popup-content">
        <!-- 搜索框 -->
        <view v-if="searchable" class="search-box flex items-center mb-2 px-4 pt-3">
          <view class="search-input flex-1 flex items-center bg-gray-100 rounded-md px-3 py-2 mr-2">
            <wd-icon name="search" size="16px" class="mr-2 text-gray-400"></wd-icon>
            <input
              type="text"
              v-model="keyword"
              placeholder="搜索学院或班级"
              class="flex-1"
              @confirm="handleSearch"
            />
          </view>
          <wd-button size="small" type="primary" @click="handleSearch">搜索</wd-button>
        </view>

        <!-- 树形列表 -->
        <scroll-view
          class="tree-list px-4"
          :scroll-y="true"
          :enhanced="true"
          :show-scrollbar="false"
          :fast-deceleration="true"
          :scroll-with-animation="true"
          :style="{ height: 'calc(80vh - 140px)' }"
        >
          <!-- 无数据状态 -->
          <view v-if="filteredTreeData.length === 0" class="empty p-10 text-center text-gray-400">
            暂无相关班级
          </view>

          <!-- 有数据状态 -->
          <view v-else class="space-y-2 pb-4">
            <!-- 学院循环 -->
            <view
              v-for="college in filteredTreeData"
              :key="college.id"
              class="college-item bg-white rounded-lg overflow-hidden mb-3 box-border"
            >
              <!-- 学院标题行 -->
              <view
                class="college-header flex items-center justify-between p-3 border-b border-gray-100 bg-gray-50 box-border"
              >
                <view class="flex items-center flex-1 min-w-0">
                  <!-- 折叠图标 -->
                  <wd-icon
                    :name="expandedColleges.has(college.id) ? 'arrow-down' : 'arrow-right'"
                    size="16px"
                    class="mr-2 text-gray-500 flex-shrink-0"
                    @click.stop="toggleCollege(college.id)"
                  ></wd-icon>

                  <!-- 学院选择框 -->
                  <view
                    v-if="multiple"
                    class="checkbox mr-2 flex-shrink-0"
                    @click.stop="toggleCollegeSelect(college)"
                  >
                    <view
                      v-if="isSelected(getCodeFromValue(college.value))"
                      class="checkbox-checked"
                    >
                      <wd-icon name="check" size="12px" class="text-white"></wd-icon>
                    </view>
                    <view
                      v-else-if="isCollegePartiallySelected(college)"
                      class="checkbox-indeterminate"
                    >
                      <view class="indeterminate-line"></view>
                    </view>
                    <view v-else class="checkbox-unchecked"></view>
                  </view>

                  <!-- 学院名称 -->
                  <text class="font-medium truncate" @click.stop="toggleCollegeSelect(college)">
                    {{ college.label }}
                  </text>
                </view>

                <!-- 班级数量 -->
                <text
                  class="text-sm text-gray-400 ml-2 flex-shrink-0"
                  @click.stop="toggleCollege(college.id)"
                >
                  {{ college.children?.length || 0 }}个班级
                </text>
              </view>

              <!-- 班级列表 -->
              <view
                v-if="expandedColleges.has(college.id) && college.children?.length"
                class="class-list"
              >
                <view
                  v-for="cls in college.children"
                  :key="cls.id"
                  class="class-item flex items-center p-3 border-b border-gray-100 box-border"
                  @click="toggleSelect(cls, college)"
                >
                  <!-- 班级选择框 -->
                  <view class="checkbox mr-3 flex-shrink-0">
                    <view v-if="isSelected(getCodeFromValue(cls.value))" class="checkbox-checked">
                      <wd-icon name="check" size="12px" class="text-white"></wd-icon>
                    </view>
                    <view v-else class="checkbox-unchecked"></view>
                  </view>

                  <!-- 班级名称 -->
                  <text class="truncate flex-1">{{ cls.label }}</text>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>

        <!-- 底部按钮 -->
        <view
          class="popup-footer flex items-center justify-between px-4 py-3 border-t border-gray-100"
        >
          <view v-if="multiple" class="selected-info text-sm text-gray-500">
            已选: {{ selectedValues.length }} 个选项
          </view>
          <view class="flex">
            <wd-button v-if="multiple" size="small" @click="clearSelection" class="mr-2">
              清空
            </wd-button>
            <wd-button size="small" type="primary" @click="closePopup">确定</wd-button>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<style lang="scss" scoped>
.class-tree-selector {
  box-sizing: border-box;
  width: 100%;

  .selector-input {
    min-height: 80rpx;
    background-color: rgba(242, 242, 242, 1); /* 确保与其他表单一致的背景色 */

    .clear-btn {
      padding: 4rpx;
    }
  }

  .checkbox {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36rpx;
    height: 36rpx;

    .checkbox-checked {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36rpx;
      height: 36rpx;
      background-color: var(--primary-color, #2979ff);
      border-radius: 4rpx;
    }

    .checkbox-indeterminate {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36rpx;
      height: 36rpx;
      background-color: var(--primary-color, #2979ff);
      border-radius: 4rpx;

      .indeterminate-line {
        width: 16rpx;
        height: 4rpx;
        background-color: white;
      }
    }

    .checkbox-unchecked {
      width: 36rpx;
      height: 36rpx;
      background-color: white;
      border: 2rpx solid #ccc;
      border-radius: 4rpx;
    }
  }

  .college-item {
    width: 100%;
    margin-bottom: 16rpx;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);

    .college-header {
      &:active {
        background-color: #f2f2f2;
      }
    }

    .class-item {
      &:active {
        background-color: #f7f8fa;
      }

      &:last-child {
        border-bottom: none;
      }
    }
  }

  .popup-content {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 100%;
    height: calc(80vh - 60px);
    overflow: hidden;
  }

  .tree-list {
    box-sizing: border-box;
    flex: 1;
    width: 100%;
  }

  .popup-footer {
    z-index: 1;
    box-sizing: border-box;
    flex-shrink: 0;
    width: 100%;
    background-color: #fff;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  }

  .truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
