# CourseSelector 课程选择器组件

一个用于选择课程的组件，包含搜索、列表展示和分页功能。

## 功能特点

- 支持课程搜索（按代码或名称）
- 显示课程分类、代码、名称和简称
- 集成分页功能
- 响应式布局
- 支持课程选择回调

## 使用方法

### 基础用法

```vue
<template>
  <CourseSelector @select="handleCourseSelect" />
</template>

<script setup lang="ts">
import CourseSelector from '@/components/CourseSelector/index.vue'
import type { Course } from '@/types/course'

const handleCourseSelect = (course: Course) => {
  console.log('选中的课程:', course)
}
</script>
```

## API

### Props

暂无

### Events

| 事件名 | 说明           | 回调参数                   |
| ------ | -------------- | -------------------------- |
| select | 选择课程时触发 | `(course: Course) => void` |

### Course 类型定义

```typescript
interface Course {
  id: string
  code: string
  name: string
  shortName: string
  category: string
}
```

## 注意事项

1. 组件依赖 `wd-icon` 和 `wd-loading` 组件
2. 需要配合后端API实现真实的数据获取逻辑
3. 当前使用的是模拟数据，实际使用时需要替换为真实的API调用
