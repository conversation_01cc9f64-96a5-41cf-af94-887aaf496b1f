<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { getCourseList } from '@/service/course'
import { getTeachOfficeTeachingTaskList } from '@/service/teachOfficeTeachingTask'
import { getTeachingTaskListWithPagination } from '@/service/teachingTask'
import type { Course, CourseSelectParams } from '@/types/course'
import type { TeachOfficeTeachingTaskItem } from '@/types/teachOfficeTeachingTask'
import type { TeachingTaskItem } from '@/types/teachingTask'
import Pagination from '@/components/Pagination/index.vue'

// 定义组件属性
const props = defineProps({
  /** 接口类型，默认为course，可选值：course、teachingTask、teachingTaskPagination */
  apiType: {
    type: String,
    default: 'course',
  },
  /** 学期，格式为"学年|学期"，如"2024-2025|2" */
  semesters: {
    type: String,
    default: '',
  },
})

// 扩展查询参数接口
interface ExtendedCourseSelectParams extends CourseSelectParams {
  searchKeyword?: string
  semesters?: string // 学年学期，格式为"学年|学期"，如"2024-2025|2"
}

// 查询参数
const queryParams = ref<ExtendedCourseSelectParams>({
  page: 1,
  pageSize: 10,
  format: 'select',
  notPage: 0,
  searchKeyword: '',
})

// 列表数据
const courseList = ref<(Course | TeachOfficeTeachingTaskItem | TeachingTaskItem)[]>([])
const total = ref(0)
const loading = ref(false)
// 防抖定时器
let searchTimer: ReturnType<typeof setTimeout> | null = null

// 设置学年学期参数
const setSemestersParam = () => {
  if (props.semesters) {
    queryParams.value.semesters = props.semesters
  } else {
    delete queryParams.value.semesters
  }
}

// 获取数据
const getData = async () => {
  loading.value = true
  try {
    if (props.apiType === 'teachingTask') {
      // 使用教研室教学任务接口
      if (!queryParams.value.semesters) {
        uni.showToast({
          title: '请设置学期参数',
          icon: 'none',
        })
        loading.value = false
        return
      }

      const res = await getTeachOfficeTeachingTaskList({
        page: queryParams.value.page,
        pageSize: queryParams.value.pageSize,
        searchKeyword: queryParams.value.searchKeyword || '',
        semesters: queryParams.value.semesters,
      })
      courseList.value = res.items
      total.value = res.total
    } else if (props.apiType === 'teachingTaskPagination') {
      // 使用教学任务分页接口
      const res = await getTeachingTaskListWithPagination({
        page: queryParams.value.page,
        pageSize: queryParams.value.pageSize,
        searchKeyword: queryParams.value.searchKeyword || '',
      })
      courseList.value = res.list
      total.value = res.total
    } else {
      // 默认使用课程接口
      const res = await getCourseList(queryParams.value)
      courseList.value = res.items
      total.value = res.total
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 防抖搜索处理
const handleInputChange = (value: string) => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  searchTimer = setTimeout(() => {
    queryParams.value.page = 1
    getData()
    searchTimer = null
  }, 500) // 500毫秒的防抖延迟
}

// 搜索处理
const handleSearch = () => {
  queryParams.value.page = 1
  getData()
}

// 页码变化
const handlePageChange = (page: number) => {
  queryParams.value.page = page
  getData()
}

// 选择课程或教学任务
const emit = defineEmits<{
  (e: 'select', item: Course | TeachOfficeTeachingTaskItem | TeachingTaskItem): void
}>()

const handleSelect = (item: Course | TeachOfficeTeachingTaskItem | TeachingTaskItem) => {
  emit('select', item)
}

// 监听属性变化
watch(
  () => [props.apiType, props.semesters],
  () => {
    setSemestersParam()
    queryParams.value.page = 1
    getData()
  },
)

// 监听搜索关键词变化
watch(
  () => queryParams.value.searchKeyword,
  (newValue) => {
    handleInputChange(newValue)
  },
)

// 初始化
onMounted(() => {
  setSemestersParam()
  getData()
})
</script>

<template>
  <view class="course-selector">
    <!-- 搜索框 -->
    <view class="search-box flex items-center mb-2">
      <view class="search-input flex-1 flex items-center bg-gray-100 rounded-md px-3">
        <wd-icon name="search" size="16px" class="mr-2 text-gray-400"></wd-icon>
        <input
          type="text"
          v-model="queryParams.searchKeyword"
          :placeholder="
            apiType === 'teachingTask' || apiType === 'teachingTaskPagination'
              ? '搜索课程代码或名称'
              : '搜索课程代码或名称'
          "
          class="flex-1"
          @confirm="handleSearch"
        />
      </view>
    </view>

    <!-- 列表 -->
    <view class="course-list">
      <wd-loading v-if="loading" type="ring" />

      <template v-else>
        <!-- 课程列表 -->
        <template v-if="apiType === 'course'">
          <view
            v-for="item in courseList"
            :key="(item as Course).id"
            class="course-item flex items-center p-3 border-b border-gray-200"
            @click="handleSelect(item)"
          >
            <view class="course-info flex-1">
              <view class="flex items-center mb-1">
                <text class="course-code text-sm text-gray-500 mr-2">
                  {{ (item as Course).courseCode }}
                </text>
                <text class="course-category text-xs px-2 py-0.5 bg-blue-50 text-blue-500 rounded">
                  {{ (item as Course).dictionaryName || '未分类' }}
                </text>
              </view>
              <view class="course-name text-base font-medium">
                {{ (item as Course).courseName }}
                <text
                  v-if="(item as Course).courseAlias"
                  class="course-alias text-sm text-gray-400 ml-1"
                >
                  ({{ (item as Course).courseAlias }})
                </text>
              </view>
            </view>
            <wd-icon name="arrow-right" size="16px" class="text-gray-300"></wd-icon>
          </view>
        </template>

        <!-- 教研室教学任务列表 -->
        <template v-else-if="apiType === 'teachingTask'">
          <view
            v-for="item in courseList"
            :key="(item as TeachOfficeTeachingTaskItem).id"
            class="course-item flex items-center p-3 border-b border-gray-200"
            @click="handleSelect(item)"
          >
            <view class="course-info flex-1">
              <view class="flex items-center mb-1">
                <text class="course-code text-sm text-gray-500 mr-2">
                  {{ (item as TeachOfficeTeachingTaskItem).courseCode }}
                </text>
                <text class="course-category text-xs px-2 py-0.5 bg-blue-50 text-blue-500 rounded">
                  {{ (item as TeachOfficeTeachingTaskItem).teachOfficeName }}
                </text>
              </view>
              <view class="course-name text-base font-medium">
                {{ (item as TeachOfficeTeachingTaskItem).courseName }}
              </view>
              <view class="course-info text-sm text-gray-500">
                {{ (item as TeachOfficeTeachingTaskItem).className }} |
                {{ (item as TeachOfficeTeachingTaskItem).leaderTeacherName }}
              </view>
            </view>
            <wd-icon name="arrow-right" size="16px" class="text-gray-300"></wd-icon>
          </view>
        </template>

        <!-- 教学任务分页列表 -->
        <template v-else-if="apiType === 'teachingTaskPagination'">
          <view
            v-for="item in courseList"
            :key="(item as TeachingTaskItem).id"
            class="course-item flex items-center p-3 border-b border-gray-200"
            @click="handleSelect(item)"
          >
            <view class="course-info flex-1">
              <view class="flex items-center mb-1">
                <text class="course-code text-sm text-gray-500 mr-2">
                  {{ (item as TeachingTaskItem).courseCode }}
                </text>
                <text
                  class="course-category text-xs px-2 py-0.5 bg-green-50 text-green-500 rounded"
                >
                  {{ (item as TeachingTaskItem).deptName }}
                </text>
              </view>
              <view class="course-name text-base font-medium">
                {{ (item as TeachingTaskItem).courseName }}
              </view>
              <view class="course-info text-sm text-gray-500 mb-1">
                {{ (item as TeachingTaskItem).className }} |
                {{ (item as TeachingTaskItem).leaderTeacherName }}
              </view>
              <view class="course-detail text-xs text-gray-400">
                {{ (item as TeachingTaskItem).studyYear }}-{{
                  (item as TeachingTaskItem).studyTerm
                }}学期 | 周学时: {{ (item as TeachingTaskItem).weekHours }}
              </view>
            </view>
            <wd-icon name="arrow-right" size="16px" class="text-gray-300"></wd-icon>
          </view>
        </template>

        <view v-if="courseList.length === 0" class="empty p-10 text-center text-gray-400">
          暂无相关数据
        </view>
      </template>
    </view>

    <!-- 分页 -->
    <view v-if="total > 0" class="pagination-container mt-4">
      <Pagination
        :total="total"
        :page="queryParams.page"
        :pageSize="queryParams.pageSize"
        @update:page="handlePageChange"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.course-selector {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;

  .search-box {
    padding: 8px 16px;
    border-bottom: 1px solid #ebedf0;

    .search-input {
      height: 32px;
    }
  }

  .course-list {
    flex: 1;
    padding: 0 16px;
    overflow-y: auto;

    .course-item {
      transition: background-color 0.2s;

      &:active {
        background-color: #f7f8fa;
      }

      &:last-child {
        border-bottom: none;
      }
    }

    .empty {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100px;
    }
  }

  .pagination-container {
    padding: 0 16px 16px;
  }
}
</style>
