# FormWithApproval 表单与审批流程组件

FormWithApproval 组件是一个用于表单提交和审批流程查看的组合组件。它将表单内容和审批流程分为两个标签页，左侧用于表单内容输入，右侧用于审批流程查看。

## 组件特点

- 分离表单内容和审批流程，清晰展示
- 表单内容完全自定义，通过插槽传入
- 审批流程统一展示，避免重复代码
- 支持多个审批分支展示
- 提供折叠/展开功能，方便查看历史审批记录

## 组件属性

| 属性 | 类型          | 必填 | 说明         |
| ---- | ------------- | ---- | ------------ |
| id   | number/string | 是   | 审批流程ID   |
| code | string        | 是   | 审批流程代码 |

## 事件

| 事件名 | 说明               |
| ------ | ------------------ |
| return | 返回按钮点击时触发 |

## 插槽

| 插槽名           | 说明                                             |
| ---------------- | ------------------------------------------------ |
| form-content     | 表单内容区域                                     |
| form-buttons     | 表单按钮区域                                     |
| approval-buttons | 审批流程页面的按钮区域（可选，默认只有返回按钮） |

## 使用示例

```vue
<template>
  <FormWithApproval :id="flowId" :code="flowCode" @return="handleReturn">
    <!-- 表单内容 -->
    <template #form-content>
      <view class="bg-white rounded-xl p-4 shadow-sm mb-4">
        <view class="text-lg font-semibold mb-4 text-gray-800">表单标题</view>

        <!-- 表单内容... -->
        <view class="form-item">
          <view class="form-label">字段名称</view>
          <wd-input v-model="formData.field" placeholder="请输入" />
        </view>
      </view>
    </template>

    <!-- 表单按钮 -->
    <template #form-buttons>
      <view class="flex space-x-4 mb-8">
        <button class="btn-cancel flex-1" @click="cancel">取消</button>
        <button class="btn-primary flex-1" @click="submit">提交</button>
      </view>
    </template>
  </FormWithApproval>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import FormWithApproval from '@/components/FormWithApproval/index.vue'

const flowId = ref(123)
const flowCode = ref('approval_code')

const formData = reactive({
  field: '',
})

const handleReturn = () => {
  uni.navigateBack()
}

const cancel = () => {
  uni.navigateBack()
}

const submit = () => {
  // 提交表单逻辑
}
</script>
```

## 注意事项

1. 必须提供 `id` 和 `code` 属性，否则无法获取审批流程数据
2. 表单内容通过 `form-content` 插槽传入，按钮通过 `form-buttons` 插槽传入
3. 当切换到审批流程标签页时，组件会自动调用接口获取审批流程数据
4. 可以根据需要自定义审批流程页面的按钮

## 样式定制

组件内部提供了一些基础样式，但表单内容区域的样式需要在使用组件时自行定义。建议参考示例中的样式。
