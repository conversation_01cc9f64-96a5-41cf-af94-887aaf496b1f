<template>
  <FormWithApproval :id="flowId" :code="flowCode" @return="handleReturn">
    <!-- 表单内容示例 -->
    <template #form-content>
      <view class="bg-white rounded-xl p-4 shadow-sm mb-4">
        <view class="text-lg font-semibold mb-4 text-gray-800">表单示例</view>

        <!-- 表单项示例 -->
        <view class="form-item">
          <view class="form-label">
            <text class="text-red-500 mr-1">*</text>
            字段名称
          </view>
          <wd-input
            v-model="formData.name"
            placeholder="请输入名称"
            class="form-input"
            :disabled="isDisabled"
          />
        </view>

        <view class="form-item">
          <view class="form-label">
            <text class="text-red-500 mr-1">*</text>
            选择项
          </view>
          <view
            class="form-select rounded-lg p-3 flex justify-between items-center"
            @click="handleSelectClick"
          >
            <text v-if="formData.selection" class="flex-1 text-ellipsis overflow-hidden pr-1">
              {{ formData.selection }}
            </text>
            <text v-else class="text-[#bfbfbf] text-[14px]">点击选择</text>
            <wd-icon name="arrow-right" size="16px" class="text-[#bfbfbf] flex-shrink-0" />
          </view>
        </view>

        <view class="form-item">
          <view class="form-label">
            <text class="text-red-500 mr-1">*</text>
            备注说明
          </view>
          <wd-textarea
            v-model="formData.remark"
            placeholder="请输入备注说明"
            class="form-textarea"
            :maxlength="200"
            show-count
            :disabled="isDisabled"
          />
        </view>
      </view>
    </template>

    <!-- 表单按钮 -->
    <template #form-buttons>
      <view class="flex space-x-4 mb-8" v-if="!isDisabled">
        <button class="btn-cancel flex-1" @click="handleCancel">取消</button>
        <button class="btn-primary flex-1" @click="handleSubmit">提交</button>
      </view>
      <view class="flex mb-8" v-else>
        <button class="btn-cancel w-full" @click="handleReturn">返回</button>
      </view>
    </template>
  </FormWithApproval>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import FormWithApproval from '@/components/FormWithApproval/index.vue'

// 表单数据
const formData = reactive({
  name: '',
  selection: '',
  remark: '',
})

// 审批流程ID和代码
const flowId = ref<number | string>(123) // 流程ID，实际应用中应从接口获取或路由参数传入
const flowCode = ref<string>('approval_code') // 流程代码，实际应用中应根据业务类型确定

// 是否禁用编辑
const isDisabled = ref<boolean>(false)

// 处理选择操作
const handleSelectClick = () => {
  // 在实际应用中，可能会打开一个选择器弹窗
  // 这里简化为直接设置一个值
  formData.selection = '已选择的项目'

  uni.showToast({
    title: '已选择项目',
    icon: 'success',
  })
}

// 处理提交
const handleSubmit = () => {
  // 表单验证
  if (!formData.name) {
    uni.showToast({ title: '请输入名称', icon: 'none' })
    return
  }
  if (!formData.selection) {
    uni.showToast({ title: '请选择项目', icon: 'none' })
    return
  }
  if (!formData.remark) {
    uni.showToast({ title: '请输入备注说明', icon: 'none' })
    return
  }

  // 提交表单
  uni.showLoading({ title: '提交中...' })

  // 模拟接口调用
  setTimeout(() => {
    uni.hideLoading()
    uni.showToast({
      title: '提交成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      },
    })
  }, 1000)
}

// 处理取消
const handleCancel = () => {
  uni.navigateBack()
}

// 处理返回
const handleReturn = () => {
  uni.navigateBack()
}
</script>

<style lang="scss">
.form-item {
  margin-bottom: 16px;
}

.form-label {
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.form-input,
.form-textarea,
.form-select {
  box-sizing: border-box;
  width: 100%;
  background-color: #ffffff;
  border: 1px solid #bfbfc3;
  border-radius: 8px;
}

.text-ellipsis {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
