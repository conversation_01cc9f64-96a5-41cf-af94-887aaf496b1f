<template>
  <view class="form-with-approval bg-gray-50 min-h-screen">
    <view class="p-4">
      <!-- 分段选择器 -->
      <wd-tabs v-model="activeTab" class="mb-4" v-if="showWorkflow">
        <wd-tab title="基本信息"></wd-tab>
        <wd-tab title="审批流程"></wd-tab>
      </wd-tabs>

      <!-- 基本信息内容 -->
      <view v-show="activeTab === 0">
        <!-- 传入的表单内容插槽 -->
        <slot name="form-content"></slot>

        <!-- 按钮组插槽 -->
        <slot name="form-buttons"></slot>
      </view>

      <!-- 审批流程内容 -->
      <view v-show="activeTab === 1">
        <view class="bg-white rounded-xl p-4 shadow-sm mb-4">
          <view class="text-lg font-semibold mb-4 text-gray-800">审批流程</view>

          <!-- 加载中提示 -->
          <view v-if="loadingWorkflow" class="flex justify-center items-center py-10">
            <wd-loading color="#3b82f6" />
            <text class="ml-2 text-gray-600">加载审批流程中...</text>
          </view>

          <!-- 无数据提示 -->
          <view
            v-else-if="!workflowTimelineData || flowSteps.length === 0"
            class="flex flex-col justify-center items-center py-10"
          >
            <wd-icon name="info-circle" size="48px" color="#9ca3af" />
            <text class="mt-3 text-gray-500">暂无审批流程数据</text>
          </view>

          <!-- 多个审批分支 -->
          <view v-else v-for="(branch, branchIndex) in flowSteps" :key="branchIndex" class="mb-6">
            <!-- 分支标题和折叠按钮 -->
            <view class="flex justify-between items-center mb-3">
              <!-- 分支标题 -->
              <view
                v-if="flowSteps.length > 1"
                class="text-sm font-medium flex items-center"
                :class="{
                  'text-blue-600': !isBranchRejected(branch),
                  'text-red-500': isBranchRejected(branch),
                }"
              >
                <wd-icon
                  :name="isBranchRejected(branch) ? 'close-circle' : 'flag'"
                  size="14px"
                  :color="isBranchRejected(branch) ? '#ef4444' : '#2563eb'"
                  class="mr-1"
                ></wd-icon>
                {{ branchIndex === 0 ? '审批流程 1' : '历史流程 ' + (branchIndex + 1) }}
                <view
                  v-if="isBranchRejected(branch)"
                  class="ml-2 px-1.5 py-0.5 rounded-sm text-xs text-white bg-red-500"
                >
                  已拒绝
                </view>
              </view>

              <!-- 折叠/展开按钮 -->
              <view
                v-if="flowSteps.length > 1"
                class="flex items-center cursor-pointer text-xs text-gray-500 px-2 py-1 rounded hover:bg-gray-100"
                @tap="toggleBranchExpand(branchIndex)"
              >
                {{ expandedBranches[branchIndex] ? '收起' : '展开' }}
                <wd-icon
                  :name="expandedBranches[branchIndex] ? 'chevron-up' : 'chevron-down'"
                  size="14px"
                  color="#666"
                  class="ml-1"
                ></wd-icon>
              </view>
            </view>

            <!-- 流程节点内容 - 根据折叠状态显示或隐藏 -->
            <view class="relative py-2" v-if="expandedBranches[branchIndex]">
              <view
                class="flex relative mb-4"
                v-for="(step, index) in branch"
                :key="step.id"
                :class="{ 'mb-0': index === branch.length - 1 }"
              >
                <view
                  class="w-4 h-4 rounded-full mr-4 flex-shrink-0 z-2"
                  :class="{
                    'bg-green-500': step.status === 'success',
                    'bg-blue-500': step.status === 'active',
                    'bg-red-500': step.status === 'rejected',
                    'bg-gray-400': step.status === 'waiting',
                  }"
                ></view>
                <view
                  v-if="index !== branch.length - 1"
                  class="absolute left-2 top-4 w-0.5 h-full bg-gray-200 z-1"
                ></view>
                <view class="flex-1">
                  <view class="flex items-center justify-between">
                    <view class="flex-1">
                      <view class="text-sm font-medium text-gray-800 mb-1">{{ step.name }}</view>
                      <view class="text-xs text-gray-500 mb-1">{{ step.handler }}</view>
                      <view class="flex items-center">
                        <view class="text-xs text-gray-400 mb-1">{{ step.time }}</view>
                        <!-- 显示拒绝标记 -->
                        <view
                          v-if="step.status === 'rejected'"
                          class="ml-2 px-1.5 py-0.5 rounded text-xs text-white bg-red-500"
                        >
                          已拒绝
                        </view>
                      </view>
                    </view>
                    <view
                      v-if="step.status === 'success'"
                      class="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center"
                    >
                      <wd-icon name="check" size="14px" color="#22c55e"></wd-icon>
                    </view>
                    <view
                      v-else-if="step.status === 'rejected'"
                      class="w-6 h-6 rounded-full bg-red-100 flex items-center justify-center"
                    >
                      <wd-icon name="close" size="14px" color="#ef4444"></wd-icon>
                    </view>
                    <view
                      v-else-if="step.status === 'active'"
                      class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center"
                    >
                      <wd-icon name="time" size="14px" color="#3b82f6"></wd-icon>
                    </view>
                  </view>

                  <!-- 审批意见显示 - 只在有意见时显示 -->
                  <view
                    v-if="step.comment"
                    class="mt-2 py-2 px-3 rounded-lg border-l-2 text-sm text-gray-700"
                    :class="{
                      'bg-gray-50 border-blue-400': !step.isRejected,
                      'bg-red-50 border-red-400': step.isRejected,
                    }"
                  >
                    <view class="text-sm leading-relaxed break-all">{{ step.comment }}</view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 折叠状态展示 -->
            <view
              v-if="!expandedBranches[branchIndex]"
              class="flex items-center py-2 px-3 rounded-md text-sm"
              :class="branchStatusSummaries[branchIndex].bgColor"
            >
              <wd-icon
                :name="branchStatusSummaries[branchIndex].icon"
                size="14px"
                :color="branchStatusSummaries[branchIndex].color"
                class="mr-2"
              ></wd-icon>
              <view class="flex-1" :class="branchStatusSummaries[branchIndex].textColor">
                {{ branchStatusSummaries[branchIndex].message }}
              </view>
              <view class="ml-2 text-xs text-gray-400">点击"展开"查看详情</view>
            </view>

            <!-- 分支分隔符 -->
            <view
              v-if="branchIndex < flowSteps.length - 1"
              class="border-t border-dashed border-gray-300 my-4"
            ></view>
          </view>
        </view>

        <!-- 审批流程页面的按钮 -->
        <slot name="approval-buttons">
          <view class="flex mb-8">
            <button class="btn-cancel w-full" @click="handleReturn">返回</button>
          </view>
        </slot>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted } from 'vue'
import { getWorkflowTimeline } from '@/service/workflow'
import type { WorkflowTimelineResponse, WorkflowProcessItem } from '@/types/workflow'

// 组件接收的属性
const props = defineProps<{
  id?: number | string // 审批流程ID
  code?: string // 审批流程代码
  showWorkflow?: boolean // 是否显示审批流程
}>()

// 组件对外暴露的事件
const emit = defineEmits<{
  (e: 'return'): void // 返回事件
  (e: 'timeline-loaded', data: WorkflowTimelineResponse): void // timeline数据加载完成事件
}>()

// 分段选择器当前激活的tab
const activeTab = ref(0)

// 工作流程数据相关状态
const loadingWorkflow = ref(false)
const workflowTimelineData = ref<WorkflowTimelineResponse | null>(null)

// 审批步骤数据结构
interface FlowStepItem {
  id: number | string
  name: string
  handler: string
  time: string
  status: 'success' | 'rejected' | 'active' | 'waiting'
  comment: string
  commentInfo: {
    user: string
    time: string
    content: string
    status: number
  } | null
  isRejected: boolean
}

// 分支状态摘要接口
interface BranchStatusSummary {
  status: 'rejected' | 'approved' | 'active' | 'pending'
  message: string
  icon: string
  color: string
  bgColor: string
  textColor: string
}

// 控制审批流程折叠状态
const expandedBranches = ref<Record<number, boolean>>({})

// 转换后的流程步骤数据，二维数组表示多个审批流程分支
const flowSteps = ref<FlowStepItem[][]>([])

// 将工作流节点数据转换为审批步骤格式
const convertToApprovalSteps = (processItems: WorkflowProcessItem[]): FlowStepItem[] => {
  return processItems.map((item) => {
    // 确定步骤状态
    let status: 'success' | 'rejected' | 'active' | 'waiting' = 'waiting'

    if (String(item.status) === '1') {
      status = 'success'
    } else if (String(item.status) === '2') {
      status = 'rejected'
    } else if (String(item.status) === '0') {
      status = 'active'
    }

    // 格式化时间
    const time =
      typeof item.time === 'string'
        ? item.time
        : item.create_time
          ? new Date(item.create_time * 1000).toLocaleString()
          : status === 'active'
            ? '待处理'
            : '未到达'

    return {
      id: item.id || Math.random().toString(36).substr(2, 9),
      name: item.name,
      handler: item.user_name || '',
      time,
      status,
      comment: item.approval_opinion || '',
      commentInfo: item.approval_opinion
        ? {
            user: item.user_name || '',
            time,
            content: item.approval_opinion,
            status: Number(item.status),
          }
        : null,
      isRejected: status === 'rejected',
    }
  })
}

// 获取工作流程数据
const fetchWorkflowTimeline = async () => {
  // 检查是否有必要的ID和代码
  if (!props.id || !props.code) {
    console.error('缺少审批流程ID或代码，无法获取工作流程')
    return
  }

  try {
    loadingWorkflow.value = true

    // 调用工作流时间线接口获取审批记录
    const res = await getWorkflowTimeline({
      id: Number(props.id),
      code: props.code,
    })

    workflowTimelineData.value = res

    // 向外部发送timeline数据

    emit('timeline-loaded', res)

    // 将API返回的流程数据转换为前端需要的格式
    if (res.process && res.process.length > 0) {
      flowSteps.value = res.process.map((branch) => convertToApprovalSteps(branch))

      // 初始化展开状态
      initBranchExpandState()
    } else {
      flowSteps.value = []
    }
  } catch (error) {
    console.error('获取工作流程数据失败:', error)
    uni.showToast({
      title: '获取审批流程失败',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    loadingWorkflow.value = false
  }
}

// 监听标签页切换
watch(activeTab, (newTab) => {
  // 当切换到审批流程标签页时，获取工作流程数据
  if (newTab === 1) {
    fetchWorkflowTimeline()
  }
})

// 监听ID和代码的变化，重新获取工作流程数据
watch(
  [() => props.id, () => props.code],
  ([newId, newCode]) => {
    if (newId && newCode && activeTab.value === 1) {
      fetchWorkflowTimeline()
    }
  },
  { immediate: false },
)

// 判断分支是否被拒绝
const isBranchRejected = (branch: FlowStepItem[]): boolean => {
  return branch.some((step) => step.status === 'rejected')
}

// 判断分支是否已全部通过
const isBranchApproved = (branch: FlowStepItem[]): boolean => {
  // 检查是否所有节点都是成功状态
  return branch.every((step) => step.status === 'success')
}

// 获取分支审批状态摘要
const getBranchStatusSummary = (branch: FlowStepItem[]): BranchStatusSummary => {
  // 如果分支被拒绝，返回拒绝信息
  if (isBranchRejected(branch)) {
    // 查找被拒绝的节点和审批信息
    const rejectedStep = branch.find((step) => step.status === 'rejected')
    if (rejectedStep && rejectedStep.commentInfo) {
      return {
        status: 'rejected',
        message: `${rejectedStep.commentInfo.user} 拒绝：${rejectedStep.commentInfo.content}`,
        icon: 'close-circle',
        color: '#ef4444',
        bgColor: 'bg-red-50',
        textColor: 'text-red-600',
      }
    }
    return {
      status: 'rejected',
      message: '审批已被拒绝',
      icon: 'close-circle',
      color: '#ef4444',
      bgColor: 'bg-red-50',
      textColor: 'text-red-600',
    }
  }

  // 如果分支全部通过，返回通过信息
  if (isBranchApproved(branch)) {
    // 找到最后一个通过的节点
    const lastApprovedStep = [...branch].reverse().find((step) => step.status === 'success')
    if (lastApprovedStep && lastApprovedStep.commentInfo) {
      return {
        status: 'approved',
        message: `${lastApprovedStep.commentInfo.user} 已通过`,
        icon: 'check-circle',
        color: '#22c55e',
        bgColor: 'bg-green-50',
        textColor: 'text-green-600',
      }
    }
    return {
      status: 'approved',
      message: '审批已通过',
      icon: 'check-circle',
      color: '#22c55e',
      bgColor: 'bg-green-50',
      textColor: 'text-green-600',
    }
  }

  // 如果分支正在审批中
  const activeStep = branch.find((step) => step.status === 'active')
  if (activeStep) {
    return {
      status: 'active',
      message: `等待 ${activeStep.handler} 审批`,
      icon: 'time',
      color: '#3b82f6',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-600',
    }
  }

  // 默认状态
  return {
    status: 'pending',
    message: '等待审批',
    icon: 'info-circle',
    color: '#6b7280',
    bgColor: 'bg-gray-50',
    textColor: 'text-gray-500',
  }
}

// 获取每个分支的状态摘要的计算属性
const branchStatusSummaries = computed(() => {
  const summaries: Record<number, BranchStatusSummary> = {}

  if (flowSteps.value) {
    flowSteps.value.forEach((branch, index) => {
      summaries[index] = getBranchStatusSummary(branch)
    })
  }

  return summaries
})

// 切换分支的展开/折叠状态
const toggleBranchExpand = (branchIndex: number) => {
  expandedBranches.value[branchIndex] = !expandedBranches.value[branchIndex]
}

// 初始化各个审批分支的展开/折叠状态
const initBranchExpandState = () => {
  if (!flowSteps.value || flowSteps.value.length === 0) return

  // 查找是否有已通过的流程
  const approvedIndex = flowSteps.value.findIndex((branch) => isBranchApproved(branch))
  // 查找是否有待处理的流程
  const pendingIndex = flowSteps.value.findIndex((branch) => {
    // 存在活跃节点但没有拒绝节点的分支视为待处理
    return (
      branch.some((step) => step.status === 'active') &&
      !branch.some((step) => step.status === 'rejected')
    )
  })

  // 遍历所有分支
  flowSteps.value.forEach((branch, index) => {
    // 如果只有一个分支，则展开
    if (flowSteps.value.length === 1) {
      expandedBranches.value[index] = true
      return
    }

    // 优先展开已通过的流程
    if (approvedIndex !== -1) {
      expandedBranches.value[index] = index === approvedIndex
    }
    // 如果没有已通过的流程，则展开待处理的流程
    else if (pendingIndex !== -1) {
      expandedBranches.value[index] = index === pendingIndex
    }
    // 如果既没有已通过的也没有待处理的，则展开最后一个流程（即使是被拒绝的）
    else {
      expandedBranches.value[index] = index === flowSteps.value.length - 1
    }
  })
}

// 处理返回按钮点击
const handleReturn = () => {
  emit('return')
}

// 暴露给外部的方法和数据
defineExpose({
  workflowTimelineData: computed(() => workflowTimelineData.value),
  flowSteps: computed(() => flowSteps.value),
  fetchWorkflowTimeline,
  loadingWorkflow: computed(() => loadingWorkflow.value),
})

// 页面加载时初始化审批流程分支的展开/折叠状态
onMounted(() => {
  initBranchExpandState()
})
</script>

<style lang="scss">
.form-with-approval {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

// 按钮样式
button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  padding: 0 16px;
  font-size: 15px;
  border-radius: 10px;
  transition: opacity 0.3s;

  &:active {
    opacity: 0.85;
  }
}

// 主要按钮样式 - 蓝色
.btn-primary {
  color: #fff;
  background-color: #007aff;
  border: 1px solid #007aff;
}

// 取消按钮样式 - 灰色
.btn-cancel {
  color: #374151;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
}

// 危险按钮样式 - 红色
.btn-danger {
  color: #fff;
  background-color: #ff3b30;
  border: 1px solid #ff3b30;
}

// 折叠/展开按钮样式
.cursor-pointer {
  cursor: pointer;
}

// 添加折叠区域的过渡效果
.relative.py-2 {
  transition: all 0.3s ease;
}

// 审批时间线样式
.approval-timeline {
  position: relative;

  .relative.py-2 {
    transition: all 0.3s ease;
  }

  .w-4.h-4.rounded-full {
    position: relative;
    z-index: 2;
  }

  .absolute.left-2.top-4.w-0\.5.h-full {
    position: absolute;
    z-index: 1;
  }
}

.z-1 {
  z-index: 1;
}

.z-2 {
  z-index: 2;
}
</style>
