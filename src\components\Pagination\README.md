# Pagination 分页组件

一个简洁美观的分页组件，支持页码跳转、上下页切换等功能。

## 功能特点

- 显示总条数和当前页码
- 支持上一页/下一页快速切换
- 支持输入页码直接跳转
- 自动计算总页数
- 优雅的禁用态和hover效果
- 响应式布局设计

## 使用方法

### 基础用法

```vue
<template>
  <Pagination
    v-model:page="page"
    :total="total"
    :page-size="pageSize"
    @update:page="handlePageChange"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import Pagination from '@/components/Pagination/index.vue'

const page = ref(1)
const total = ref(100)
const pageSize = ref(10)

const handlePageChange = (newPage: number) => {
  // 处理页码变化
  console.log('当前页码:', newPage)
}
</script>
```

## API

### Props

| 参数     | 说明     | 类型     | 默认值 |
| -------- | -------- | -------- | ------ |
| page     | 当前页码 | `number` | `1`    |
| total    | 总条目数 | `number` | `0`    |
| pageSize | 每页条数 | `number` | `10`   |

### Events

| 事件名      | 说明           | 回调参数                 |
| ----------- | -------------- | ------------------------ |
| update:page | 页码改变时触发 | `(page: number) => void` |

## 功能说明

1. **总条数显示**

   - 在左侧显示数据总条数
   - 格式：`共 xx 条`

2. **页码切换**

   - 上一页按钮：当前页大于1时可用
   - 下一页按钮：当前页小于总页数时可用
   - 按钮hover效果：蓝色边框和背景

3. **页码显示**

   - 显示格式：`当前页/总页数`
   - 当前页加粗显示
   - 总页数自动计算：`Math.ceil(total / pageSize)`

4. **页码跳转**
   - 支持手动输入页码跳转
   - 输入框失焦或回车时触发跳转
   - 自动校验页码有效性
   - 无效页码会显示提示信息

## 样式定制

组件使用SCSS编写样式，可以通过以下类名进行样式覆盖：

```scss
.pagination {
  // 容器样式
  &-info {
    /* 总条数区域 */
  }
  &-operations {
    /* 操作区域 */
  }
  &-current {
    /* 当前页码区域 */
  }
  &-btn {
    /* 按钮样式 */
  }
  &-jump {
    /* 跳转区域 */
  }
}
```

## 注意事项

1. 组件依赖 `wd-icon` 组件显示箭头图标，请确保项目中已引入
2. 页码输入限制为数字类型
3. 组件会自动处理边界情况（第一页和最后一页）
4. 页码改变时会触发 `update:page` 事件，需要在父组件中监听并处理

## 最佳实践

1. **合理设置每页条数**

   ```vue
   <Pagination v-model:page="page" :total="total" :page-size="10" />
   ```

2. **处理页码变化**

   ```vue
   <Pagination v-model:page="page" :total="total" :page-size="pageSize" @update:page="loadData" />
   ```

3. **配合加载状态使用**
   ```vue
   <template>
     <view>
       <view class="list">
         <!-- 数据列表 -->
       </view>
       <Pagination
         v-model:page="queryParams.page"
         :total="total"
         :page-size="queryParams.pageSize"
         @update:page="getList"
       />
     </view>
   </template>
   ```

## 示例展示

1. **基础分页**

   ```vue
   <Pagination v-model:page="page" :total="100" :page-size="10" />
   ```

2. **带加载状态的分页**

   ```vue
   <template>
     <view>
       <view v-if="loading">加载中...</view>
       <Pagination
         v-model:page="page"
         :total="total"
         :page-size="pageSize"
         @update:page="handlePageChange"
       />
     </view>
   </template>
   ```

3. **自定义每页条数**
   ```vue
   <Pagination v-model:page="page" :total="total" :page-size="20" />
   ```
