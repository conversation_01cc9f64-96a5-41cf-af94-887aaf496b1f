<script setup lang="ts">
interface Props {
  total: number
  page: number
  pageSize: number
}

interface Emits {
  (e: 'update:page', page: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 跳转页码
const jumpPage = ref('')

// 计算总页数
const totalPages = computed(() => Math.ceil(props.total / props.pageSize))

// 跳转到指定页
const handleJumpPage = () => {
  if (!jumpPage.value) {
    return
  }

  const page = parseInt(jumpPage.value)

  if (isNaN(page) || page < 1 || page > totalPages.value) {
    uni.showToast({
      title: '请输入有效页码',
      icon: 'none',
    })
    jumpPage.value = ''
    return
  }

  if (page === props.page) {
    jumpPage.value = ''
    return
  }

  emit('update:page', page)
  jumpPage.value = ''
}

// 上一页
const handlePrevPage = () => {
  if (props.page <= 1) return
  emit('update:page', props.page - 1)
}

// 下一页
const handleNextPage = () => {
  if (props.page >= totalPages.value) return
  emit('update:page', props.page + 1)
}
</script>

<template>
  <view class="pagination">
    <view class="pagination-info">
      <text>共 {{ total }} 条</text>
    </view>
    <view class="pagination-operations">
      <view
        class="pagination-btn"
        :class="{ disabled: page <= 1 }"
        hover-class="pagination-btn--hover"
        @click="handlePrevPage"
      >
        <wd-icon name="arrow-left" size="22px"></wd-icon>
      </view>
      <view class="pagination-current">
        <text class="current">{{ page }}</text>
        <text class="separator">/</text>
        <text class="total">{{ totalPages }}</text>
      </view>
      <view
        class="pagination-btn"
        :class="{ disabled: page >= totalPages }"
        hover-class="pagination-btn--hover"
        @click="handleNextPage"
      >
        <wd-icon name="arrow-right" size="22px"></wd-icon>
      </view>
    </view>
    <view class="pagination-jump">
      <input
        class="pagination-jump-input"
        type="number"
        v-model="jumpPage"
        @blur="handleJumpPage"
        @keyup.enter="handleJumpPage"
        :placeholder="String(page)"
      />
      <text>页</text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.pagination {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 20rpx 24rpx;
  margin-top: 24rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(31, 35, 41, 0.05);

  &-info {
    font-size: 28rpx;
    color: #86909c;
  }

  &-operations {
    display: flex;
    flex: 1;
    gap: 12rpx;
    align-items: center;
    justify-content: center;
    margin: 0 24rpx;
  }

  &-current {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 140rpx;
    height: 60rpx;
    padding: 0 16rpx;
    font-size: 26rpx;
    text-align: center;

    .current {
      font-weight: 600;
      color: #1f2329;
    }

    .separator {
      margin: 0 0;
      color: #c9cdd4;
    }

    .total {
      color: #86909c;
    }
  }

  &-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60rpx;
    height: 60rpx;
    color: #1f2329;
    background: #ffffff;
    border: 2rpx solid #e5e6eb;
    border-radius: 12rpx;
    transition: all 0.2s ease;

    &.disabled {
      color: #c9cdd4;
      cursor: not-allowed;
      background: #f7f8fc;
      border-color: #e5e6eb;
      opacity: 0.6;
    }

    &--hover:not(.disabled) {
      color: #3a8eff;
      background: rgba(58, 142, 255, 0.04);
      border-color: #3a8eff;
    }
  }

  &-jump {
    display: flex;
    gap: 8rpx;
    align-items: center;
    font-size: 28rpx;
    color: #86909c;

    &-input {
      width: 80rpx;
      height: 60rpx;
      padding: 0 16rpx;
      font-size: 26rpx;
      color: #1f2329;
      text-align: center;
      background: #ffffff;
      border: 2rpx solid #e5e6eb;
      border-radius: 12rpx;

      &:focus {
        background: #ffffff;
        border-color: #3a8eff;
        outline: none;
      }
    }
  }
}
</style>
