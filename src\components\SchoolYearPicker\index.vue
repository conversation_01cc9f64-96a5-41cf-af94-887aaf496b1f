<script setup lang="ts">
import { ref, onMounted, watch, defineProps, defineEmits, defineExpose } from 'vue'
import { useToast } from 'wot-design-uni'
import { getSemesterSelect } from '@/service/system'
import type { SchoolYearOption } from '@/types/semester'

// 定义组件的属性
const props = defineProps({
  // 学年部分
  // 学年标签文本
  yearLabel: {
    type: String,
    default: '学年',
  },
  // 初始选中的学年值
  yearValue: {
    type: String,
    default: '',
  },
  // 是否显示学年标签
  showYearLabel: {
    type: Boolean,
    default: true,
  },
  // 是否显示全部学年选项
  showAllYear: {
    type: Boolean,
    default: false,
  },
  // 全部学年选项的标签
  allYearLabel: {
    type: String,
    default: '全部学年',
  },
  // 组件大小
  size: {
    type: String,
    default: 'normal', // normal, large
    validator: (value: string) => ['normal', 'large'].includes(value),
  },
  // 是否在选择后自动显示提示
  showToast: {
    type: Boolean,
    default: false,
  },
  // 默认返回的学年类型
  defaultType: {
    type: String,
    default: 'default', // default, next, xkxn
    validator: (value: string) => ['default', 'next', 'xkxn'].includes(value),
  },
})

// 定义组件的事件
const emit = defineEmits(['update:yearValue', 'yearChange', 'change'])

// toast提示
const toast = useToast()

// 学年选择
const currentYear = ref<string>('')
const currentYearValue = ref<string>(props.yearValue)
const showYearPicker = ref<boolean>(false)
const yearOptions = ref<SchoolYearOption[]>([])
const yearLoading = ref<boolean>(false)

// 追踪组件是否已初始化完成
const isInitialized = ref<boolean>(false)

// 初始化学年选项
const initYearOptions = async () => {
  yearLoading.value = true
  try {
    const res = await getSemesterSelect()

    // 从学期中提取学年信息
    const schoolYears: Map<string, SchoolYearOption> = new Map()

    // 根据defaultType获取默认学年值
    let defaultYearValue: string
    switch (props.defaultType) {
      case 'next':
        defaultYearValue = res.next
        break
      case 'xkxn':
        defaultYearValue = res.xkxn
        break
      case 'default':
      default:
        defaultYearValue = res.default
        break
    }

    // 遍历学期列表，提取学年部分
    res.data.forEach((semester) => {
      // 直接使用完整的value
      const yearValue = semester.value
      // 学期标签格式为"yyyy-yyyy学年 第t学期"，提取"yyyy-yyyy学年"部分
      const yearLabel = semester.label

      if (!schoolYears.has(yearValue)) {
        schoolYears.set(yearValue, {
          label: yearLabel,
          value: yearValue,
          isCurrent: yearValue === defaultYearValue,
        })
      }
    })

    // 转换为数组并按学年降序排序
    const schoolYearList = Array.from(schoolYears.values())
    schoolYearList.sort((a, b) => b.value.localeCompare(a.value))

    // 如果需要显示全部学年选项，添加到列表最前面
    if (props.showAllYear) {
      schoolYearList.unshift({
        label: props.allYearLabel,
        value: '',
        isCurrent: false,
      })
    }

    yearOptions.value = schoolYearList

    // 默认选择当前学年
    if (props.yearValue === '') {
      // 如果配置了显示全部学年且没有传值，则选择全部学年选项
      if (props.showAllYear) {
        currentYear.value = props.allYearLabel
        currentYearValue.value = ''
        emit('update:yearValue', '')
        emit('yearChange', {
          label: props.allYearLabel,
          value: '',
        })
        emit('change', {
          label: props.allYearLabel,
          value: '',
        })
      } else {
        // 根据defaultType找到对应的学年选项
        const currentYearOption = schoolYearList.find((item) => item.isCurrent)
        if (currentYearOption) {
          currentYear.value = currentYearOption.label
          currentYearValue.value = currentYearOption.value
          emit('update:yearValue', currentYearOption.value)
          emit('yearChange', {
            label: currentYearOption.label,
            value: currentYearOption.value,
          })
          emit('change', {
            label: currentYearOption.label,
            value: currentYearOption.value,
          })
        } else if (schoolYearList.length > 0) {
          currentYear.value = schoolYearList[0].label
          currentYearValue.value = schoolYearList[0].value
          emit('update:yearValue', schoolYearList[0].value)
          emit('yearChange', {
            label: schoolYearList[0].label,
            value: schoolYearList[0].value,
          })
          emit('change', {
            label: schoolYearList[0].label,
            value: schoolYearList[0].value,
          })
        }
      }
    } else {
      // 如果有传入值，选中对应学年
      const selectedYear = schoolYearList.find((item) => item.value === props.yearValue)
      if (selectedYear) {
        currentYear.value = selectedYear.label
        currentYearValue.value = selectedYear.value
      }
    }
  } catch (error) {
    console.error('获取学年列表失败:', error)
    // 错误处理，可以设置一些默认值
    yearOptions.value = []
    if (props.showAllYear) {
      yearOptions.value.push({
        label: props.allYearLabel,
        value: '',
        isCurrent: false,
      })
      currentYear.value = props.allYearLabel
      currentYearValue.value = ''
    }
  } finally {
    yearLoading.value = false
  }
}

// 选择学年
const selectYear = (year: SchoolYearOption): void => {
  currentYear.value = year.label
  currentYearValue.value = year.value
  showYearPicker.value = false

  // 更新v-model
  emit('update:yearValue', year.value)

  // 触发change事件
  emit('yearChange', {
    label: year.label,
    value: year.value,
  })

  // 触发综合变化事件
  emit('change', {
    label: year.label,
    value: year.value,
  })

  // 如果设置了显示toast，则显示
  if (props.showToast) {
    toast.show(`已切换到${year.label}`)
  }
}

// 监听yearValue变化
watch(
  () => props.yearValue,
  (newVal) => {
    if (newVal !== currentYearValue.value) {
      const selectedOption = yearOptions.value.find((item) => item.value === newVal)
      if (selectedOption) {
        currentYear.value = selectedOption.label
        currentYearValue.value = selectedOption.value
      }
    }
  },
)

// 获取组件初始化状态的方法
const getInitializationStatus = (): boolean => {
  return isInitialized.value
}

// 初始化
onMounted(async () => {
  try {
    await initYearOptions()

    // 标记组件已完成初始化
    isInitialized.value = true
  } catch (error) {
    console.error('组件初始化失败:', error)
  }
})

// 对外暴露方法和状态
defineExpose({
  getInitializationStatus,
  isInitialized,
})
</script>

<template>
  <view class="school-year-picker-component">
    <view class="picker-container">
      <!-- 学年选择按钮 -->
      <view
        class="year-select"
        :class="[size === 'large' ? 'year-select-large' : '']"
        @click="showYearPicker = true"
      >
        <text
          v-if="showYearLabel"
          class="year-label"
          :class="[size === 'large' ? 'year-label-large' : '']"
        >
          {{ yearLabel }}:
        </text>
        <text class="year-text" :class="[size === 'large' ? 'year-text-large' : '']">
          {{ currentYear }}
        </text>
        <wd-icon
          name="arrow-down"
          :size="size === 'large' ? '26rpx' : '20rpx'"
          color="#666666"
        ></wd-icon>
      </view>
    </view>

    <!-- 学年选择器弹窗 -->
    <wd-popup v-model="showYearPicker" position="bottom" round>
      <view class="picker-modal">
        <view class="picker-header">
          <text class="picker-title">选择{{ yearLabel }}</text>
          <wd-icon name="close" size="32rpx" @click="showYearPicker = false"></wd-icon>
        </view>

        <!-- 加载中状态 -->
        <view v-if="yearLoading" class="loading-container">
          <wd-loading size="36px" color="#3a8eff" />
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 选项列表 -->
        <scroll-view v-else class="picker-content" scroll-y>
          <view
            v-for="item in yearOptions"
            :key="item.value"
            :class="['picker-item', { active: currentYear === item.label }]"
            @click="selectYear(item)"
          >
            <text class="item-label">{{ item.label }}</text>
            <wd-icon
              v-if="currentYear === item.label"
              name="check"
              size="32rpx"
              color="#3a8eff"
            ></wd-icon>
          </view>
        </scroll-view>
      </view>
    </wd-popup>

    <!-- Toast提示 -->
    <wd-toast />
  </view>
</template>

<style lang="scss" scoped>
.school-year-picker-component {
  width: 100%;
}

.picker-container {
  display: flex;
  gap: 12rpx;
  align-items: center;
}

// 学年选择
.year-select {
  display: flex;
  align-items: center;
  max-width: 65%;
  padding: 6rpx 12rpx;
  margin-right: 4rpx;
  background: #f7f8fa;
  border-radius: 10rpx;
  transition: all 0.3s;

  &:active {
    background: #f0f2f5;
  }

  .year-label {
    margin-right: 4rpx;
    font-size: 22rpx;
    color: #666;
  }

  .year-text {
    font-size: 24rpx;
    font-weight: 500;
    color: #333;
  }

  .wd-icon {
    margin-left: 4rpx;
  }
}

// 大尺寸的学年选择器样式
.year-select-large {
  padding: 10rpx 16rpx;
  border-radius: 12rpx;

  .year-label-large {
    margin-right: 6rpx;
    font-size: 28rpx;
  }

  .year-text-large {
    font-size: 30rpx;
  }
}

// 选择器弹窗样式
.picker-modal {
  max-height: 60vh;
  padding: 32rpx;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;

  .picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32rpx;
  }

  .picker-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  .picker-content {
    max-height: calc(60vh - 100rpx);
  }
}

// 选择器通用样式
.picker-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background: #f7f8fa;
  border-radius: 12rpx;
  transition: all 0.3s;

  &:last-child {
    margin-bottom: 0;
  }

  &.active {
    color: #3a8eff;
    background: rgba(58, 142, 255, 0.1);
  }

  .item-label {
    font-size: 28rpx;
  }

  &.active .item-label {
    color: #3a8eff;
  }
}

// 加载中样式
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;

  .loading-text {
    margin-top: 20rpx;
    font-size: 26rpx;
    color: #86909c;
  }
}
</style>
