# SearchForm 动态搜索表单组件

一个灵活的动态搜索表单组件，支持多种输入类型和自定义配置。

## 功能特点

- 支持多种输入类型（选择器、日期范围、输入框等）
- 支持自定义表单字段配置
- 响应式布局，适配不同屏幕尺寸
- 支持弹出式高级搜索
- 支持自定义保留字段配置
- 内置重置和搜索功能

## 使用方法

### 基础用法

```vue
<template>
  <SearchForm
    v-model="queryParams"
    :fields="searchFields"
    :loading="loading"
    @search="handleSearch"
    @reset="handleReset"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SearchForm from '@/components/SearchForm/index.vue'

// 查询参数
const queryParams = ref({
  keyword: '',
  status: '',
  date: [],
})

// 搜索字段配置
const searchFields = ref([
  {
    label: '关键词',
    type: 'input',
    key: 'keyword',
    placeholder: '请输入关键词',
  },
  {
    label: '状态',
    type: 'picker',
    key: 'status',
    placeholder: '请选择状态',
    options: [
      { label: '启用', value: 1 },
      { label: '禁用', value: 0 },
    ],
  },
  {
    label: '日期范围',
    type: 'daterange',
    key: 'date',
    placeholder: '请选择日期范围',
  },
])
</script>
```

### 配置项说明

#### Props

| 参数          | 说明                 | 类型                  | 默认值  |
| ------------- | -------------------- | --------------------- | ------- |
| modelValue    | 表单数据对象         | `Record<string, any>` | -       |
| fields        | 表单字段配置数组     | `FormField[]`         | -       |
| loading       | 搜索按钮加载状态     | `boolean`             | `false` |
| preservedKeys | 重置时保留的字段配置 | `Record<string, any>` | `{}`    |

#### FormField 字段配置

| 参数        | 说明                                      | 类型                                                    | 必填 |
| ----------- | ----------------------------------------- | ------------------------------------------------------- | ---- |
| label       | 字段标签                                  | `string`                                                | 是   |
| type        | 字段类型                                  | `'select-picker' \| 'daterange' \| 'picker' \| 'input'` | 是   |
| key         | 字段键名                                  | `string`                                                | 是   |
| placeholder | 占位提示                                  | `string`                                                | 否   |
| options     | 选项数据（用于select-picker和picker类型） | `array`                                                 | 否   |
| multiple    | 是否多选（用于select-picker类型）         | `boolean`                                               | 否   |

#### Events

| 事件名            | 说明               | 回调参数                               |
| ----------------- | ------------------ | -------------------------------------- |
| update:modelValue | 表单数据更新时触发 | `(value: Record<string, any>) => void` |
| search            | 点击搜索按钮时触发 | -                                      |
| reset             | 点击重置按钮时触发 | -                                      |

### 高级用法

#### 自定义保留字段

```vue
<template>
  <SearchForm
    v-model="queryParams"
    :fields="searchFields"
    :preserved-keys="{
      page: 1,
      pageSize: 10,
      sortBy: 'id',
      sortOrder: 'desc',
    }"
  />
</template>
```

#### 多选示例

```vue
<script setup lang="ts">
const searchFields = ref([
  {
    label: '标签',
    type: 'select-picker',
    key: 'tags',
    placeholder: '请选择标签',
    options: [
      { label: '标签1', value: '1' },
      { label: '标签2', value: '2' },
    ],
    multiple: true,
  },
])
</script>
```

#### 日期范围示例

```vue
<script setup lang="ts">
const searchFields = ref([
  {
    label: '创建时间',
    type: 'daterange',
    key: 'createTime',
    placeholder: '请选择创建时间范围',
  },
])
</script>
```

## 注意事项

1. 字段类型说明：

   - `select-picker`: 下拉选择器，支持多选
   - `picker`: 单选选择器
   - `daterange`: 日期范围选择器
   - `input`: 输入框

2. 日期范围选择器返回格式为 `['YYYY-MM-DD', 'YYYY-MM-DD']`

3. 重置功能说明：

   - 通过 `preservedKeys` 配置的字段会保留其默认值
   - 其他字段会根据类型重置为空值（数组类型重置为 `[]`，其他类型重置为 `''`）

4. 组件会自动处理弹出层的显示和隐藏

## 样式定制

组件使用 SCSS 编写样式，可以通过以下类名进行样式覆盖：

- `.search-form`: 整个搜索表单容器
- `.search-bar`: 搜索栏
- `.form-item`: 表单项
- `.btn-reset`: 重置按钮
- `.btn-search`: 搜索按钮
- `.popup-content`: 弹出层内容

## 最佳实践

1. 设置合理的字段顺序，最常用的字段放在第一位（会显示在搜索栏中）

2. 为所有字段提供合适的 placeholder

3. 使用 `preservedKeys` 保留分页等关键字段的默认值

4. 处理好加载状态的显示

```vue
<template>
  <SearchForm
    v-model="queryParams"
    :fields="searchFields"
    :loading="loading"
    :preserved-keys="preservedKeys"
    @search="handleSearch"
    @reset="handleReset"
  />
</template>

<script setup lang="ts">
const loading = ref(false)
const preservedKeys = {
  page: 1,
  pageSize: 10,
  sortBy: 'id',
  sortOrder: 'desc',
}

const handleSearch = async () => {
  loading.value = true
  try {
    await fetchData()
  } finally {
    loading.value = false
  }
}
</script>
```
