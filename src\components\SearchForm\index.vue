<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'

// 定义表单字段类型
type FormFieldType = 'select-picker' | 'daterange' | 'select' | 'input' | 'date' | 'picker'

interface FormField {
  label: string
  type: FormFieldType
  key: string
  placeholder: string
  options?: any[]
  multiple?: boolean
}

interface Props {
  modelValue: Record<string, any>
  fields: FormField[]
  loading?: boolean
  preservedKeys?: { [key: string]: any } // 需要保留的字段及其默认值
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  preservedKeys: () => ({}),
})

const emit = defineEmits(['update:modelValue', 'search', 'reset'])

// 本地表单状态
const formData = ref({ ...props.modelValue })

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    formData.value = { ...newValue }
  },
  { deep: true },
)

// 监听fields变化
watch(
  () => props.fields,
  () => {
    // 强制组件重新渲染
    nextTick(() => {
      if (showPopup.value) {
        showPopup.value = false
        nextTick(() => {
          showPopup.value = true
        })
      }
    })
  },
  { deep: true },
)

// 更新父组件的值
const updateModelValue = (newValue: Record<string, any>) => {
  formData.value = newValue
  emit('update:modelValue', newValue)
}

// 弹出层状态
const showPopup = ref(false)

// 格式化日期
const formatDate = (timestamp: number) => {
  const date = new Date(timestamp)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 处理日期选择确认
const handleDateConfirm = (key: string, { value }: { value: number[] }) => {
  if (Array.isArray(value) && value.length === 2) {
    updateModelValue({
      ...formData.value,
      [key]: [formatDate(value[0]), formatDate(value[1])],
    })
  }
}

// 处理值更新
const handleValueChange = (key: string, value: any) => {
  // 处理picker组件的特殊返回值
  if (value && typeof value === 'object' && 'value' in value) {
    value = value.value
  }

  updateModelValue({
    ...formData.value,
    [key]: value,
  })
}

// 重置搜索条件
const handleReset = () => {
  const resetValue = Object.keys(formData.value).reduce(
    (acc, key) => {
      // 如果是需要保留的字段，使用保留的值
      if (key in props.preservedKeys) {
        acc[key] = props.preservedKeys[key]
        return acc
      }

      // 其他字段根据类型重置
      if (Array.isArray(formData.value[key])) {
        acc[key] = []
      } else if (typeof formData.value[key] === 'string') {
        acc[key] = ''
      } else if (typeof formData.value[key] === 'number') {
        acc[key] = ''
      } else {
        acc[key] = ''
      }
      return acc
    },
    {} as Record<string, any>,
  )

  updateModelValue(resetValue)
  emit('reset')
}

// 搜索并关闭弹出层
const handleSearch = () => {
  showPopup.value = false
  emit('search')
}

// 展开/收起切换
const toggleExpand = () => {
  showPopup.value = !showPopup.value
}

// 关闭弹出层
const handleClose = () => {
  showPopup.value = false
}
</script>

<template>
  <view class="search-form">
    <!-- 搜索栏 -->
    <view class="search-bar">
      <view class="search-form">
        <view class="form-item">
          <text class="label">{{ fields[0]?.label }}</text>
          <view class="value">
            <template v-if="fields[0]?.type === 'select-picker'">
              <wd-select-picker
                v-model="formData[fields[0]?.key]"
                :columns="fields[0]?.options"
                :placeholder="fields[0]?.placeholder"
                :multiple="fields[0]?.multiple"
                @change="(value) => handleValueChange(fields[0]?.key, value)"
              />
            </template>
            <template v-else-if="fields[0]?.type === 'picker'">
              <wd-picker
                v-model="formData[fields[0]?.key]"
                :columns="fields[0]?.options"
                :placeholder="fields[0]?.placeholder"
                @confirm="(value) => handleValueChange(fields[0]?.key, value.value)"
              />
            </template>
            <template v-else-if="fields[0]?.type === 'input'">
              <wd-input
                v-model="formData[fields[0]?.key]"
                :placeholder="fields[0]?.placeholder"
                @input="(value) => handleValueChange(fields[0]?.key, value)"
              />
            </template>
            <template v-else-if="fields[0]?.type === 'daterange'">
              <wd-calendar
                v-model="formData[fields[0]?.key]"
                type="daterange"
                :placeholder="fields[0]?.placeholder"
                @confirm="(value) => handleDateConfirm(fields[0]?.key, value)"
              />
            </template>
          </view>
        </view>
      </view>
      <view class="search-actions">
        <view class="action-buttons">
          <wd-button class="btn-reset" size="small" plain @click="handleReset" type="info">
            重置
          </wd-button>
          <wd-button
            class="btn-search"
            size="small"
            type="primary"
            @click="handleSearch"
            icon="search"
            :loading="loading"
          >
            搜索
          </wd-button>
        </view>
        <view class="expand-trigger" @click="toggleExpand">
          <text>{{ showPopup ? '收起' : '更多' }}</text>
          <wd-icon :name="showPopup ? 'arrow-up' : 'arrow-down'" size="22px" />
        </view>
      </view>
    </view>

    <!-- 弹出层搜索选项 -->
    <wd-popup
      v-model="showPopup"
      position="bottom"
      custom-style="padding: 32rpx; border-radius: 24rpx 24rpx 0 0;"
      safe-area-inset-bottom
      @close="handleClose"
    >
      <view class="popup-content">
        <view class="popup-header">
          <text class="popup-title">筛选条件</text>
          <wd-icon name="close" size="36rpx" color="#86909C" @click="handleClose" />
        </view>
        <view class="popup-form">
          <view v-for="field in fields" :key="field.key" class="form-item">
            <text class="label">{{ field.label }}</text>
            <view class="value">
              <template v-if="field.type === 'select-picker'">
                <wd-select-picker
                  v-model="formData[field.key]"
                  :columns="field.options"
                  :placeholder="field.placeholder"
                  :multiple="field.multiple"
                  @change="(value) => handleValueChange(field.key, value)"
                />
              </template>
              <template v-else-if="field.type === 'picker'">
                <wd-picker
                  v-model="formData[field.key]"
                  :columns="field.options"
                  :placeholder="field.placeholder"
                  @confirm="(value) => handleValueChange(field.key, value.value)"
                />
              </template>
              <template v-else-if="field.type === 'input'">
                <wd-input
                  v-model="formData[field.key]"
                  :placeholder="field.placeholder"
                  @input="(value) => handleValueChange(field.key, value)"
                />
              </template>
              <template v-else-if="field.type === 'daterange'">
                <wd-calendar
                  v-model="formData[field.key]"
                  type="daterange"
                  :placeholder="field.placeholder"
                  @confirm="(value) => handleDateConfirm(field.key, value)"
                />
              </template>
            </view>
          </view>
        </view>
        <view class="popup-footer">
          <view class="action-buttons">
            <wd-button class="btn-reset" type="info" plain @click="handleReset">重置</wd-button>
            <wd-button
              class="btn-search"
              type="primary"
              @click="handleSearch"
              icon="search"
              :loading="loading"
            >
              搜索
            </wd-button>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<style lang="scss" scoped>
.search-form {
  margin-bottom: 24rpx;
}

.search-bar {
  box-sizing: border-box;
  width: 100%;
  padding: 32rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(31, 35, 41, 0.05);

  .search-form {
    display: flex;
    flex-direction: column;

    .form-item {
      display: flex;
      gap: 24rpx;
      align-items: flex-start;

      .label {
        flex-shrink: 0;
        width: 160rpx;
        padding-top: 16rpx;
        font-size: 28rpx;
        color: #1f2329;
      }

      .value {
        flex: 1;
        min-width: 0;

        :deep(.wd-picker),
        :deep(.wd-input),
        :deep(.wd-calendar) {
          width: 100%;

          .wd-input__inner {
            height: 72rpx;
            padding: 0 24rpx;
            line-height: 72rpx;
          }
        }
      }
    }
  }

  .search-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 32rpx;
    margin-top: 32rpx;
    border-top: 2rpx solid #f2f3f5;

    .action-buttons {
      display: flex;
      gap: 16rpx;

      .btn-reset,
      .btn-search {
        width: 160rpx;
        height: 64rpx;
        font-size: 26rpx;
        font-weight: 500;
        border-radius: 8rpx;
        transition: all 0.3s ease;

        :deep(.wd-button__text) {
          display: flex;
          gap: 6rpx;
          align-items: center;
          justify-content: center;
        }
      }

      .btn-reset {
        color: #4e5969;
        background: #f7f8fa;
        border-color: transparent;
      }

      .btn-search {
        color: #ffffff;
        background: linear-gradient(135deg, #3a8eff 0%, #6fa6ff 100%);
        border: none;
        box-shadow: 0 6rpx 12rpx rgba(58, 142, 255, 0.2);
      }
    }

    .expand-trigger {
      display: flex;
      gap: 8rpx;
      align-items: center;
      padding: 16rpx;
      margin: -16rpx;
      font-size: 24rpx;
      color: #86909c;
      cursor: pointer;
      transition: all 0.3s ease;

      &:active {
        opacity: 0.8;
      }
    }
  }
}

.popup-content {
  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32rpx;

    .popup-title {
      font-size: 32rpx;
      font-weight: 600;
      color: #1f2329;
    }
  }

  .popup-form {
    display: flex;
    flex-direction: column;
    gap: 32rpx;
    max-height: 60vh;
    margin-bottom: 48rpx;
    overflow-y: auto;

    .form-item {
      display: flex;
      gap: 24rpx;
      align-items: flex-start;

      .label {
        flex-shrink: 0;
        width: 160rpx;
        padding-top: 16rpx;
        font-size: 28rpx;
        color: #1f2329;
      }

      .value {
        flex: 1;
        min-width: 0;

        :deep(.wd-picker),
        :deep(.wd-input),
        :deep(.wd-calendar) {
          width: 100%;

          .wd-input__inner {
            height: 72rpx;
            padding: 0 24rpx;
            line-height: 72rpx;
          }
        }
      }
    }
  }

  .popup-footer {
    position: sticky;
    right: 0;
    bottom: 0;
    left: 0;
    padding-top: 24rpx;
    background: #ffffff;

    .action-buttons {
      display: flex;
      gap: 24rpx;

      > .btn-reset {
        flex: 1;
        height: 72rpx;
        font-size: 28rpx;
        font-weight: 500;
        color: #4e5969;
        background: #f7f8fa;
        border-color: transparent;
        border-radius: 8rpx;

        &:active {
          color: #1f2329;
          background: #e5e6eb;
        }
      }

      > .btn-search {
        flex: 1;
        height: 72rpx;
        font-size: 28rpx;
        font-weight: 500;
        color: #ffffff;
        background: linear-gradient(135deg, #3a8eff 0%, #6fa6ff 100%);
        border: none;
        border-radius: 8rpx;
        box-shadow: 0 6rpx 12rpx rgba(58, 142, 255, 0.2);

        &:active {
          box-shadow: 0 3rpx 6rpx rgba(58, 142, 255, 0.2);
          transform: translateY(2rpx);
        }

        :deep(.wd-button__text) {
          display: flex;
          gap: 6rpx;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}
</style>
