<script lang="ts" setup>
import { ref, computed, watch } from 'vue'

// 定义组件接收的属性
const props = defineProps({
  // 搜索框的占位文本
  placeholder: {
    type: String,
    default: '请输入搜索内容',
  },
  // 是否显示搜索说明文本
  showHint: {
    type: Boolean,
    default: true,
  },
  // 防抖延迟时间（毫秒）
  debounceDelay: {
    type: Number,
    default: 500,
  },
})

// 定义组件事件
const emit = defineEmits(['update:modelValue', 'search', 'clear'])

// 组件内部状态
const keyword = ref('')
let searchTimer: number | null = null

// 搜索说明文本是否显示
const isSearchActive = computed(() => !!keyword.value)

// v-model双向绑定
const modelValue = defineModel<string>()

// 监听modelValue变化同步到内部状态
watch(modelValue, (newVal) => {
  keyword.value = newVal || ''
})

// 监听内部状态变化同步到modelValue
watch(keyword, (newVal) => {
  modelValue.value = newVal
})

// 处理搜索输入（带防抖）
const handleSearchInput = () => {
  // 清除之前的定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  // 设置新的定时器，延迟执行搜索
  searchTimer = setTimeout(() => {
    // 发出搜索事件
    emit('search', keyword.value)
    // 清除定时器引用
    searchTimer = null
  }, props.debounceDelay)
}

// 清除搜索内容
const clearSearch = () => {
  // 清除可能存在的防抖定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
    searchTimer = null
  }

  keyword.value = ''
  modelValue.value = ''
  // 发出清除事件
  emit('clear')
}

// 处理确认搜索（按下回车键）
const handleConfirmSearch = () => {
  // 清除可能存在的防抖定时器
  if (searchTimer) {
    clearTimeout(searchTimer)
    searchTimer = null
  }

  // 直接发出搜索事件
  emit('search', keyword.value)
}
</script>

<template>
  <view class="search-input-component">
    <!-- 搜索输入框 -->
    <view class="relative w-full box-border">
      <input
        type="text"
        class="box-border h-10 block w-full pl-10 pr-12 py-2 border border-gray-400 rounded-lg focus:outline-none focus:border-primary shadow-sm search-input"
        :placeholder="placeholder"
        v-model="keyword"
        @confirm="handleConfirmSearch"
        @input="handleSearchInput"
      />
      <view class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <wd-icon name="search" color="#9ca3af" />
      </view>
      <!-- 清除按钮 -->
      <view
        v-if="keyword"
        class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer"
        @click="clearSearch"
      >
        <wd-icon name="close-circle" color="#9ca3af" />
      </view>
    </view>

    <!-- 搜索说明文本 -->
    <view v-if="showHint && isSearchActive" class="mt-2 text-xs text-gray-500 flex items-center">
      <wd-icon name="info-circle" size="24rpx" color="#9ca3af" class="mr-1" />
      当前搜索: {{ keyword }}
      <view class="ml-2 text-primary" @click="clearSearch">清除</view>
    </view>
  </view>
</template>

<style lang="scss">
.search-input-component {
  box-sizing: border-box;
  width: 100%;

  .search-input {
    box-sizing: border-box;
    width: 100%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  :deep(.text-primary) {
    color: #2563eb;
  }
}
</style>
