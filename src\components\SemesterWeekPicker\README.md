# SemesterWeekPicker 组件文档

## 组件概述

`SemesterWeekPicker` 是一个用于选择学期和周次的Vue3组件，适用于教育类应用场景。组件支持学期列表展示、当前学期标识、周次选择以及全部周次选项等功能。

## 功能特点

- 学期和周次双选择器
- 自动获取并显示当前学期和当前周次
- 支持学期列表倒序排序
- 支持"全部周次"选项
- 当前学期和当前周次标记
- 可单独使用周次选择器
- 支持自定义标签文本
- 支持选择后的Toast提示
- 完整的TypeScript类型支持

## 安装与导入

### 导入组件

```typescript
import SemesterWeekPicker from '@/components/SemesterWeekPicker/index.vue'
```

### 组件依赖

组件依赖以下模块，确保项目中已正确配置：

- `wot-design-uni` UI组件库
- `@/service/student` 和 `@/service/semester` API服务
- `@/types/student` 和 `@/types/semester` 类型定义

## 基本用法

### 基础示例

```vue
<template>
  <view class="container">
    <semester-week-picker
      v-model:semesterValue="semesterValue"
      v-model:weekValue="weekValue"
      @change="handleChange"
    />
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SemesterWeekPicker from '@/components/SemesterWeekPicker/index.vue'

const semesterValue = ref('') // 学期值，如: '2023-2024-1'
const weekValue = ref(null) // 周次值，如: 3，表示第3周，null表示全部周次

const handleChange = (data: {
  semester: { label: string; value: string }
  week: { label: string; value: number | null }
}) => {
  console.log('选择的学期:', data.semester)
  console.log('选择的周次:', data.week)
}
</script>
```

### 仅使用周次选择器

```vue
<template>
  <semester-week-picker
    v-model:weekValue="weekValue"
    :showSemester="false"
    @weekChange="handleWeekChange"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SemesterWeekPicker from '@/components/SemesterWeekPicker/index.vue'

const weekValue = ref(null)

const handleWeekChange = (data: { label: string; value: number | null }) => {
  console.log('选择的周次:', data)
}
</script>
```

## 组件属性（Props）

### 周次属性

| 属性名             | 类型    | 默认值     | 说明                 |
| ------------------ | ------- | ---------- | -------------------- |
| weekLabel          | String  | '周数'     | 周数标签文本         |
| weekValue          | Number  | null       | 初始选中的周数值     |
| showAllWeek        | Boolean | true       | 是否显示全部周数选项 |
| allWeekLabel       | String  | '全部周数' | 全部周数选项的标签   |
| defaultCurrentWeek | Boolean | true       | 是否默认选中当前周   |
| showWeekLabel      | Boolean | true       | 是否显示周数标签     |

### 学期属性

| 属性名            | 类型    | 默认值 | 说明               |
| ----------------- | ------- | ------ | ------------------ |
| semesterLabel     | String  | '学期' | 学期标签文本       |
| semesterValue     | String  | ''     | 初始选中的学期值   |
| showSemesterLabel | Boolean | true   | 是否显示学期标签   |
| showSemester      | Boolean | true   | 是否显示学期选择器 |

### 公共属性

| 属性名    | 类型    | 默认值 | 说明                     |
| --------- | ------- | ------ | ------------------------ |
| showToast | Boolean | false  | 是否在选择后自动显示提示 |

## 组件事件（Events）

| 事件名               | 参数                                                                                           | 说明         |
| -------------------- | ---------------------------------------------------------------------------------------------- | ------------ |
| update:weekValue     | (value: number \| null)                                                                        | 更新周数值   |
| update:semesterValue | (value: string)                                                                                | 更新学期值   |
| weekChange           | { label: string, value: number \| null }                                                       | 周数变更事件 |
| semesterChange       | { label: string, value: string }                                                               | 学期变更事件 |
| change               | { semester: { label: string, value: string }, week: { label: string, value: number \| null } } | 综合变化事件 |

## 高级用法

### 自定义标签和提示

```vue
<template>
  <semester-week-picker
    semesterLabel="教学学期"
    weekLabel="教学周"
    allWeekLabel="全部教学周"
    :showToast="true"
    v-model:semesterValue="semesterValue"
    v-model:weekValue="weekValue"
  />
</template>
```

### 默认选中指定学期和周次

```vue
<template>
  <semester-week-picker semesterValue="2023-2024-2" :weekValue="3" :defaultCurrentWeek="false" />
</template>
```

### 监听变化并执行操作

```vue
<template>
  <semester-week-picker
    v-model:semesterValue="semesterValue"
    v-model:weekValue="weekValue"
    @semesterChange="loadSemesterData"
    @weekChange="loadWeekData"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'

const semesterValue = ref('')
const weekValue = ref(null)

const loadSemesterData = (data: { label: string; value: string }) => {
  // 加载该学期的数据
  console.log(`加载 ${data.label} 的数据...`)
}

const loadWeekData = (data: { label: string; value: number | null }) => {
  if (data.value === null) {
    console.log('加载全部周次数据...')
  } else {
    console.log(`加载第 ${data.value} 周数据...`)
  }
}
</script>
```

## 使用注意事项

1. **API依赖**：组件需要调用`getWeekInfo`和`getSemesterList` API，确保这些接口已正确实现

2. **类型定义**：组件依赖以下TypeScript类型：

   ```typescript
   // @/types/student.ts
   export interface WeekItem {
     zc: number // 周次
     // 其他属性...
   }

   // @/types/semester.ts
   export interface SemesterOption {
     label: string // 学期名称
     value: string // 学期值
     isCurrent: boolean // 是否为当前学期
     seasonal?: string // 可选的季节标记
     // 其他属性...
   }
   ```

3. **当前学期和周次**：当选择非当前学期时，默认会自动切换到"全部周次"

4. **初始化顺序**：组件内部会先初始化学期选项，再初始化周次选项，确保正确识别当前学期状态

5. **样式定制**：组件使用scss编写样式，可以通过CSS变量或样式覆盖进行自定义

## 内部实现说明

组件内部维护了以下状态：

- 当前选中的学期和周次
- 学期列表和周次列表
- 是否为当前学期的标记
- 弹出选择器的显示状态
- 加载状态

组件会在初始化时自动获取学期列表和周次信息，并根据配置选择默认值。

## 贡献与反馈

如果发现任何问题或有改进建议，请联系开发团队。

---

_最后更新: 2025-04-21_
