<script setup lang="ts">
import { ref, onMounted, watch, defineProps, defineEmits, defineExpose } from 'vue'
import { useToast } from 'wot-design-uni'
import { getWeekInfo } from '@/service/student'
import { getSemesterList } from '@/service/semester'
import type { WeekItem } from '@/types/student'
import type { SemesterOption } from '@/types/semester'

// 定义组件的属性
const props = defineProps({
  // 周数部分
  // 标签文本
  weekLabel: {
    type: String,
    default: '周数',
  },
  // 初始选中的周数值
  weekValue: {
    type: Number,
    default: null,
  },
  // 是否显示全部周数选项
  showAllWeek: {
    type: Boolean,
    default: true,
  },
  // 全部周数选项的标签
  allWeekLabel: {
    type: String,
    default: '全部周数',
  },
  // 是否默认选中当前周
  defaultCurrentWeek: {
    type: Boolean,
    default: true,
  },

  // 学期部分
  // 学期标签文本
  semesterLabel: {
    type: String,
    default: '学期',
  },
  // 初始选中的学期值
  semesterValue: {
    type: String,
    default: '',
  },
  // 是否显示学期标签
  showSemesterLabel: {
    type: Boolean,
    default: true,
  },
  // 是否显示全部学期选项
  showAllSemester: {
    type: Boolean,
    default: false,
  },
  // 全部学期选项的标签
  allSemesterLabel: {
    type: String,
    default: '全部学年学期',
  },
  // 是否自动格式化学期格式（将yyyy-yyyy-t转换为yyyy-yyyy|t）
  formatSemester: {
    type: Boolean,
    default: false,
  },

  // 公共配置
  // 是否显示周数标签
  showWeekLabel: {
    type: Boolean,
    default: true,
  },
  // 是否在选择后自动显示提示
  showToast: {
    type: Boolean,
    default: false,
  },
  // 是否显示学期选择器（可以只显示周数选择器）
  showSemester: {
    type: Boolean,
    default: true,
  },
  // 是否显示周数选择器
  showWeek: {
    type: Boolean,
    default: true,
  },
  // 组件大小
  size: {
    type: String,
    default: 'normal', // normal, large
    validator: (value: string) => ['normal', 'large'].includes(value),
  },
})

// 定义组件的事件
const emit = defineEmits([
  'update:weekValue',
  'update:semesterValue',
  'weekChange',
  'semesterChange',
  'change', // 综合变化事件
])

// toast提示
const toast = useToast()

// 周数选择
const currentWeek = ref<string>(props.allWeekLabel)
const currentWeekValue = ref<number | null>(props.weekValue)
const showWeekPicker = ref<boolean>(false)
const weekOptions = ref<Array<{ label: string; value: number | null }>>([])
const weekLoading = ref<boolean>(false)
const currentWeekNumber = ref<number>(0) // 保存当前周次

// 学期选择
const currentSemester = ref<string>('')
const currentSemesterValue = ref<string>(props.semesterValue)
const showSemesterPicker = ref<boolean>(false)
const semesterOptions = ref<SemesterOption[]>([])
const semesterLoading = ref<boolean>(false)
// 添加状态跟踪当前选中的学期是否是当前学期
const isCurrentSemester = ref<boolean>(false)

// 追踪组件是否已初始化完成
const isInitialized = ref<boolean>(false)

/**
 * 格式化学期值
 * 根据formatSemester属性决定是否转换学期格式
 * @param value 原始学期值
 * @returns 格式化后的学期值
 */
const formatSemesterValue = (value: string): string => {
  if (!props.formatSemester || !value) return value

  // 如果已经是yyyy-yyyy|n格式，则不需要转换
  if (value.includes('|')) return value

  // 将yyyy-yyyy-n格式转换为yyyy-yyyy|n
  return value.replace(/^(\d{4}-\d{4})-(\d)$/, '$1|$2')
}

// 初始化周数选项
const initWeekOptions = async () => {
  weekLoading.value = true
  try {
    const res = await getWeekInfo()

    // 设置当前周次
    currentWeekNumber.value = res.dqz

    // 构建周数选项
    const options: Array<{ label: string; value: number | null }> = []

    // 添加"全部"选项
    if (props.showAllWeek) {
      options.push({ label: props.allWeekLabel, value: null })
    }

    // 添加周数选项，只有当前学期才显示"(当前)"标记
    res.weekList.forEach((item: WeekItem) => {
      options.push({
        label: `第${item.zc}周${item.zc === res.dqz && isCurrentSemester.value ? '(当前)' : ''}`,
        value: item.zc,
      })
    })

    weekOptions.value = options

    // 当指定默认选中当前周时，且为当前学期时
    if (props.defaultCurrentWeek && props.weekValue === null && isCurrentSemester.value) {
      const currentWeekOption = options.find((item) => item.value === res.dqz)
      if (currentWeekOption) {
        currentWeek.value = currentWeekOption.label
        currentWeekValue.value = currentWeekOption.value
        emit('update:weekValue', currentWeekOption.value)
        emit('weekChange', {
          label: currentWeekOption.label,
          value: currentWeekOption.value,
        })
        emit('change', {
          semester: {
            label: currentSemester.value,
            value: formatSemesterValue(currentSemesterValue.value),
          },
          week: {
            label: currentWeekOption.label,
            value: currentWeekOption.value,
          },
        })
      }
    } else if (props.weekValue !== null) {
      // 如果有传入的weekValue，则选中对应的选项
      const selectedOption = options.find((item) => item.value === props.weekValue)
      if (selectedOption) {
        currentWeek.value = selectedOption.label
        currentWeekValue.value = selectedOption.value
      }
    }
  } catch (error) {
    console.error('获取周次数据失败:', error)
    // 加载失败时使用默认数据
    const defaultOptions: Array<{ label: string; value: number | null }> = []
    if (props.showAllWeek) {
      defaultOptions.push({ label: props.allWeekLabel, value: null })
    }
    // 添加默认的20周
    for (let i = 1; i <= 20; i++) {
      defaultOptions.push({ label: `第${i}周`, value: i })
    }
    weekOptions.value = defaultOptions
  } finally {
    weekLoading.value = false
  }
}

// 初始化学期选项
const initSemesterOptions = async () => {
  semesterLoading.value = true
  try {
    const res = await getSemesterList()

    // 对学期列表进行倒序排序
    let sortedSemesters = res.semesters.sort((a, b) => {
      // 按照学期值（如：2024-2025-1）进行倒序排序
      return b.value.localeCompare(a.value)
    })

    // 如果需要显示全部学期选项，添加到列表最前面
    if (props.showAllSemester) {
      sortedSemesters = [
        {
          label: props.allSemesterLabel,
          value: '',
          isCurrent: false,
          seasonal: '', // 添加seasonal属性以符合SemesterOption接口
        },
        ...sortedSemesters,
      ]
    }

    semesterOptions.value = sortedSemesters

    // 默认选择当前学期
    if (props.semesterValue === '') {
      // 如果配置了显示全部学期且没有传值，则选择全部学期选项
      if (props.showAllSemester) {
        currentSemester.value = props.allSemesterLabel
        currentSemesterValue.value = ''
        isCurrentSemester.value = false
        emit('update:semesterValue', '')
        emit('semesterChange', {
          label: props.allSemesterLabel,
          value: '',
        })
        emit('change', {
          semester: {
            label: props.allSemesterLabel,
            value: '',
          },
          week: {
            label: currentWeek.value,
            value: currentWeekValue.value,
          },
        })
      } else {
        const currentSemesterOption = res.semesters.find((item: SemesterOption) => item.isCurrent)
        if (currentSemesterOption) {
          currentSemester.value = currentSemesterOption.label
          currentSemesterValue.value = currentSemesterOption.value
          isCurrentSemester.value = true // 设置为当前学期
          emit('update:semesterValue', formatSemesterValue(currentSemesterOption.value))
          emit('semesterChange', {
            label: currentSemesterOption.label,
            value: formatSemesterValue(currentSemesterOption.value),
          })
          emit('change', {
            semester: {
              label: currentSemesterOption.label,
              value: formatSemesterValue(currentSemesterOption.value),
            },
            week: {
              label: currentWeek.value,
              value: currentWeekValue.value,
            },
          })
        } else if (res.semesters.length > 0) {
          currentSemester.value = res.semesters[0].label
          currentSemesterValue.value = res.semesters[0].value
          isCurrentSemester.value = res.semesters[0].isCurrent || false // 检查是否为当前学期
          emit('update:semesterValue', formatSemesterValue(res.semesters[0].value))
          emit('semesterChange', {
            label: res.semesters[0].label,
            value: formatSemesterValue(res.semesters[0].value),
          })
          emit('change', {
            semester: {
              label: res.semesters[0].label,
              value: formatSemesterValue(res.semesters[0].value),
            },
            week: {
              label: currentWeek.value,
              value: currentWeekValue.value,
            },
          })
        }
      }
    } else {
      // 如果有传入值，选中对应学期
      const selectedSemester = res.semesters.find((item) => item.value === props.semesterValue)
      if (selectedSemester) {
        currentSemester.value = selectedSemester.label
        console.log(selectedSemester)

        currentSemesterValue.value = selectedSemester.value
      }
    }
  } catch (error) {
    console.error('获取学期列表失败:', error)
    // 错误处理，可以设置一些默认值
  } finally {
    semesterLoading.value = false
  }
}

// 选择周数
const selectWeek = (week: { label: string; value: number | null }): void => {
  currentWeek.value = week.label
  currentWeekValue.value = week.value
  showWeekPicker.value = false

  // 更新v-model
  emit('update:weekValue', week.value)

  // 触发change事件
  emit('weekChange', {
    label: week.label,
    value: week.value,
  })

  // 触发综合变化事件
  emit('change', {
    semester: {
      label: currentSemester.value,
      value: formatSemesterValue(currentSemesterValue.value),
    },
    week: {
      label: week.label,
      value: week.value,
    },
  })

  // 如果设置了显示toast，则显示
  if (props.showToast) {
    toast.show(`已切换到${week.label}`)
  }
}

// 选择学期
const selectSemester = (semester: SemesterOption): void => {
  currentSemester.value = semester.label
  currentSemesterValue.value = semester.value
  showSemesterPicker.value = false

  // 设置是否为当前学期
  isCurrentSemester.value = semester.isCurrent || false

  // 如果切换到非当前学期，将周数选择设为"全部周数"
  if (!isCurrentSemester.value && props.showAllWeek) {
    const allWeekOption = weekOptions.value.find((item) => item.value === null)
    if (allWeekOption) {
      currentWeek.value = allWeekOption.label
      currentWeekValue.value = allWeekOption.value
      emit('update:weekValue', null)
    }
  }

  // 重新获取周数信息，以更新标签（是否显示"当前"）
  // 注意这里不能直接调用异步函数，需要使用then/catch模式
  getWeekInfo()
    .then((res) => {
      // 设置当前周次
      currentWeekNumber.value = res.dqz

      // 构建周数选项
      const options: Array<{ label: string; value: number | null }> = []

      // 添加"全部"选项
      if (props.showAllWeek) {
        options.push({ label: props.allWeekLabel, value: null })
      }

      // 添加周数选项，只有当前学期才显示"(当前)"标记
      res.weekList.forEach((item: WeekItem) => {
        options.push({
          label: `第${item.zc}周${item.zc === res.dqz && isCurrentSemester.value ? '(当前)' : ''}`,
          value: item.zc,
        })
      })

      weekOptions.value = options

      // 如果是非当前学期，并且已经设置为全部周数，确保UI上的显示匹配
      if (!isCurrentSemester.value && currentWeekValue.value === null) {
        const allOption = options.find((item) => item.value === null)
        if (allOption) {
          currentWeek.value = allOption.label
        }
      }
    })
    .catch((error) => {
      console.error('更新周次数据失败:', error)
    })

  // 更新v-model
  emit('update:semesterValue', formatSemesterValue(semester.value))

  // 触发change事件
  emit('semesterChange', {
    label: semester.label,
    value: formatSemesterValue(semester.value),
  })

  // 触发综合变化事件
  emit('change', {
    semester: {
      label: semester.label,
      value: formatSemesterValue(semester.value),
    },
    week: {
      label: currentWeek.value,
      value: currentWeekValue.value,
    },
  })

  // 如果设置了显示toast，则显示
  if (props.showToast) {
    toast.show(`已切换到${semester.label}`)
  }
}

// 监听weekValue变化
watch(
  () => props.weekValue,
  (newVal) => {
    if (newVal !== currentWeekValue.value) {
      const selectedOption = weekOptions.value.find((item) => item.value === newVal)
      if (selectedOption) {
        currentWeek.value = selectedOption.label
        currentWeekValue.value = selectedOption.value
      } else {
        // 如果找不到对应的选项，则默认选择"全部"
        if (props.showAllWeek) {
          const allOption = weekOptions.value.find((item) => item.value === null)
          if (allOption) {
            currentWeek.value = allOption.label
            currentWeekValue.value = allOption.value
          }
        }
      }
    }
  },
)

// 监听semesterValue变化
watch(
  () => props.semesterValue,
  (newVal) => {
    if (newVal !== currentSemesterValue.value) {
      const selectedOption = semesterOptions.value.find((item) => item.value === newVal)
      if (selectedOption) {
        currentSemester.value = selectedOption.label
        console.log(selectedOption)

        currentSemesterValue.value = selectedOption.value
      }
    }
  },
)

// 获取组件初始化状态的方法
const getInitializationStatus = (): boolean => {
  return isInitialized.value
}

// 初始化
onMounted(async () => {
  try {
    // 先初始化学期选项（如果需要显示学期）
    if (props.showSemester) {
      await initSemesterOptions()
    }

    // 学期初始化完成后，再初始化周次选项
    // 这样可以确保在初始化周次时，已经正确识别了当前学期状态
    await initWeekOptions()

    // 标记组件已完成初始化
    isInitialized.value = true
  } catch (error) {
    console.error('组件初始化失败:', error)
  }
})

// 对外暴露方法和状态
defineExpose({
  getInitializationStatus,
  isInitialized,
})
</script>

<template>
  <view class="semester-week-picker-component">
    <view class="picker-container">
      <!-- 学期选择按钮 -->
      <view
        v-if="showSemester"
        class="semester-select"
        :class="[size === 'large' ? 'semester-select-large' : '']"
        @click="showSemesterPicker = true"
      >
        <text
          v-if="showSemesterLabel"
          class="semester-label"
          :class="[size === 'large' ? 'semester-label-large' : '']"
        >
          {{ semesterLabel }}:
        </text>
        <text class="semester-text" :class="[size === 'large' ? 'semester-text-large' : '']">
          {{ currentSemester }}
        </text>
        <wd-icon
          name="arrow-down"
          :size="size === 'large' ? '26rpx' : '20rpx'"
          color="#666666"
        ></wd-icon>
      </view>

      <!-- 周数选择按钮 -->
      <view
        v-if="showWeek"
        class="week-select"
        :class="[size === 'large' ? 'week-select-large' : '']"
        @click="showWeekPicker = true"
      >
        <text
          v-if="showWeekLabel"
          class="week-label"
          :class="[size === 'large' ? 'week-label-large' : '']"
        >
          {{ weekLabel }}:
        </text>
        <text class="week-text" :class="[size === 'large' ? 'week-text-large' : '']">
          {{ currentWeek }}
        </text>
        <wd-icon
          name="arrow-down"
          :size="size === 'large' ? '26rpx' : '20rpx'"
          color="#666666"
        ></wd-icon>
      </view>
    </view>

    <!-- 学期选择器弹窗 -->
    <wd-popup v-model="showSemesterPicker" position="bottom" round>
      <view class="picker-modal">
        <view class="picker-header">
          <text class="picker-title">选择{{ semesterLabel }}</text>
          <wd-icon name="close" size="32rpx" @click="showSemesterPicker = false"></wd-icon>
        </view>

        <!-- 加载中状态 -->
        <view v-if="semesterLoading" class="loading-container">
          <wd-loading size="36px" color="#3a8eff" />
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 选项列表 -->
        <scroll-view v-else class="picker-content" scroll-y>
          <view
            v-for="item in semesterOptions"
            :key="item.value"
            :class="['picker-item', { active: currentSemester === item.label }]"
            @click="selectSemester(item)"
          >
            <text class="item-label">{{ item.label }}</text>
            <text v-if="item.seasonal" class="item-seasonal">{{ item.seasonal }}</text>
            <wd-icon
              v-if="currentSemester === item.label"
              name="check"
              size="32rpx"
              color="#3a8eff"
            ></wd-icon>
          </view>
        </scroll-view>
      </view>
    </wd-popup>

    <!-- 周数选择器弹窗 -->
    <wd-popup v-model="showWeekPicker" position="bottom" round>
      <view class="picker-modal">
        <view class="picker-header">
          <text class="picker-title">选择{{ weekLabel }}</text>
          <wd-icon name="close" size="32rpx" @click="showWeekPicker = false"></wd-icon>
        </view>

        <!-- 加载中状态 -->
        <view v-if="weekLoading" class="loading-container">
          <wd-loading size="36px" color="#3a8eff" />
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 选项列表 -->
        <scroll-view v-else class="picker-content" scroll-y>
          <view
            v-for="item in weekOptions"
            :key="item.value"
            :class="['picker-item', { active: currentWeek === item.label }]"
            @click="selectWeek(item)"
          >
            <text class="item-label">{{ item.label }}</text>
            <wd-icon
              v-if="currentWeek === item.label"
              name="check"
              size="32rpx"
              color="#3a8eff"
            ></wd-icon>
          </view>
        </scroll-view>
      </view>
    </wd-popup>

    <!-- Toast提示 -->
    <wd-toast />
  </view>
</template>

<style lang="scss" scoped>
.semester-week-picker-component {
  width: 100%;
}

.picker-container {
  display: flex;
  gap: 12rpx;
  align-items: center;
}

// 学期选择
.semester-select {
  display: flex;
  align-items: center;
  max-width: 65%;
  padding: 6rpx 12rpx;
  margin-right: 4rpx;
  background: #f7f8fa;
  border-radius: 10rpx;
  transition: all 0.3s;

  &:active {
    background: #f0f2f5;
  }

  .semester-label {
    margin-right: 4rpx;
    font-size: 22rpx;
    color: #666;
  }

  .semester-text {
    font-size: 24rpx;
    font-weight: 500;
    color: #333;
  }

  .wd-icon {
    margin-left: 4rpx;
  }
}

// 大尺寸的学期选择器样式
.semester-select-large {
  padding: 10rpx 16rpx;
  border-radius: 12rpx;

  .semester-label-large {
    margin-right: 6rpx;
    font-size: 28rpx;
  }

  .semester-text-large {
    font-size: 30rpx;
  }
}

// 周数选择
.week-select {
  display: flex;
  align-items: center;
  max-width: 100%;
  padding: 6rpx 12rpx;
  background: #f7f8fa;
  border-radius: 10rpx;
  transition: all 0.3s;

  &:active {
    background: #f0f2f5;
  }

  .week-label {
    margin-right: 4rpx;
    font-size: 22rpx;
    color: #666;
  }

  .week-text {
    font-size: 24rpx;
    font-weight: 500;
    color: #333;
  }

  .wd-icon {
    margin-left: 4rpx;
  }
}

// 大尺寸的周数选择器样式
.week-select-large {
  padding: 10rpx 16rpx;
  border-radius: 12rpx;

  .week-label-large {
    margin-right: 6rpx;
    font-size: 28rpx;
  }

  .week-text-large {
    font-size: 30rpx;
  }
}

// 选择器弹窗样式
.picker-modal {
  max-height: 60vh;
  padding: 32rpx;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;

  .picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32rpx;
  }

  .picker-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  .picker-content {
    max-height: calc(60vh - 100rpx);
  }
}

// 选择器通用样式
.picker-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background: #f7f8fa;
  border-radius: 12rpx;
  transition: all 0.3s;

  &:last-child {
    margin-bottom: 0;
  }

  &.active {
    color: #3a8eff;
    background: rgba(58, 142, 255, 0.1);
  }

  .item-label {
    font-size: 28rpx;
  }

  .item-seasonal {
    margin-left: 16rpx;
    font-size: 24rpx;
    color: #666;
  }

  &.active .item-label {
    color: #3a8eff;
  }

  &.active .item-seasonal {
    color: #3a8eff;
  }
}

// 加载中样式
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;

  .loading-text {
    margin-top: 20rpx;
    font-size: 26rpx;
    color: #86909c;
  }
}
</style>
