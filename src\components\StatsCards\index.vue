<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

// 定义统计项类型
export interface StatItem {
  value: number | string
  title: string
  type?: 'default' | 'success' | 'warning' | 'error'
  clickable?: boolean
  active?: boolean
  onClick?: () => void
}

const props = defineProps<{
  stats: StatItem[]
}>()

const emits = defineEmits<{
  (e: 'click', index: number, item: StatItem): void
}>()

// 处理卡片点击事件
const handleCardClick = (index: number, item: StatItem) => {
  if (item.clickable) {
    if (item.onClick) {
      item.onClick()
    }
    emits('click', index, item)
  }
}
</script>

<template>
  <view class="stats-cards">
    <view
      v-for="(item, index) in stats"
      :key="index"
      class="stat-card"
      :class="{ 'stat-card-active': item.active, 'stat-card-clickable': item.clickable }"
      @click="handleCardClick(index, item)"
    >
      <view :class="['stat-value', item.type]">{{ item.value }}</view>
      <view class="stat-title">{{ item.title }}</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.stats-cards {
  display: flex;
  gap: 12rpx;
  justify-content: space-between;
  width: 100%;
  margin: 24rpx 0;

  .stat-card {
    display: flex;
    flex: 1;
    flex-direction: column;
    padding: 16rpx;
    background-color: white;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &.stat-card-clickable {
      cursor: pointer;

      &:active {
        opacity: 0.8;
      }
    }

    // 选中状态样式
    &.stat-card-active {
      background-color: rgba(255, 149, 0, 0.1);
      border: 1px solid rgba(255, 149, 0, 0.3);
      box-shadow: 0 4rpx 16rpx rgba(255, 149, 0, 0.15);
      transform: translateY(-2rpx);
    }
  }

  .stat-title {
    margin-bottom: 8rpx;
    font-size: 22rpx;
    color: #666;
  }

  .stat-value {
    font-size: 36rpx;
    font-weight: 600;
    color: #3a8eff;

    &.warning {
      color: #faad14;
    }
    &.error {
      color: #ff3860;
    }
    &.success {
      color: #52c41a;
    }
  }
}
</style>
