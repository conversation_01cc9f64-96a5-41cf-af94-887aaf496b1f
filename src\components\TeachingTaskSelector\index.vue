<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { getTeachingTaskList } from '@/service/teachingTask'
import type {
  TeachingTaskItem,
  TeachingTaskQuery,
  TeachingTaskResponse,
} from '@/types/teachingTask'
import Pagination from '@/components/Pagination/index.vue'

// 定义组件属性
const props = defineProps({
  // 学年学期参数
  semester: {
    type: String,
    default: '',
  },
})

// 扩展查询参数接口
interface ExtendedTeachingTaskQuery extends TeachingTaskQuery {
  searchKeyword?: string
}

// 查询参数
const queryParams = ref<ExtendedTeachingTaskQuery>({
  page: 1,
  pageSize: 30,
  searchKeyword: '',
  textbookUse: '0,1',
})

// 列表数据
const taskList = ref<TeachingTaskItem[]>([])
const total = ref(0)
const loading = ref(false)

// 获取教学任务列表
const getTaskData = async () => {
  loading.value = true
  try {
    // 如果有学年学期参数，添加到查询条件
    if (props.semester) {
      // 根据接口要求设置学年学期参数
      // 注意：这里假设接口使用 semester 字段接收学年学期参数
      // 如果实际接口字段名不同，请修改为正确的字段名
      queryParams.value.semesters = props.semester
    }

    const res = await getTeachingTaskList(queryParams.value)
    taskList.value = res.list || []
    total.value = res.total || 0
  } catch (error) {
    console.error('获取教学任务列表失败:', error)
    uni.showToast({
      title: '获取教学任务列表失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 监听学年学期参数变化
watch(
  () => props.semester,
  (newVal) => {
    if (newVal) {
      queryParams.value.semesters = newVal
      // 重置页码并重新获取数据
      queryParams.value.page = 1
      getTaskData()
    }
  },
)

// 搜索处理
const handleSearch = () => {
  queryParams.value.page = 1
  getTaskData()
}

// 页码变化
const handlePageChange = (page: number) => {
  queryParams.value.page = page
  getTaskData()
}

// 选择教学任务
const emit = defineEmits<{
  (e: 'select', task: TeachingTaskItem): void
}>()

const handleSelect = (task: TeachingTaskItem) => {
  emit('select', task)
}

// 初始化
onMounted(() => {
  // 如果有学年学期参数，添加到查询条件
  if (props.semester) {
    queryParams.value.semesters = props.semester
  }
  getTaskData()
})
</script>

<template>
  <view class="teaching-task-selector">
    <!-- 搜索框 -->
    <view class="search-box flex items-center mb-4">
      <view class="search-input flex-1 flex items-center bg-gray-100 rounded-md px-3 py-2 mr-2">
        <wd-icon name="search" size="16px" class="mr-2 text-gray-400"></wd-icon>
        <input
          type="text"
          v-model="queryParams.searchKeyword"
          placeholder="搜索教学任务"
          class="flex-1"
          @confirm="handleSearch"
        />
      </view>
      <wd-button size="small" type="primary" @click="handleSearch">搜索</wd-button>
    </view>

    <!-- 教学任务列表 -->
    <view class="task-list">
      <wd-loading v-if="loading" type="ring" />

      <template v-else>
        <view
          v-for="task in taskList"
          :key="task.id"
          class="task-item flex items-center p-3 border-b border-gray-200"
          @click="handleSelect(task)"
        >
          <view class="task-info flex-1">
            <view class="flex items-center mb-1">
              <text class="course-code text-sm text-gray-500 mr-2">
                {{ task.courseCode || '无课程代码' }}
              </text>
              <text class="semester text-xs px-2 py-0.5 bg-blue-50 text-blue-500 rounded">
                {{ `${task.studyYear}学年第${task.studyTerm}学期` }}
              </text>
            </view>
            <view class="task-name text-base font-medium">
              {{ task.courseName || '未命名课程' }}
            </view>
            <view class="task-detail text-sm text-gray-500">
              {{ task.leaderTeacherName || '未分配教师' }} | {{ task.className || '未分配班级' }}
            </view>
          </view>
          <wd-icon name="arrow-right" size="16px" class="text-gray-300"></wd-icon>
        </view>

        <view v-if="taskList.length === 0" class="empty p-10 text-center text-gray-400">
          暂无相关教学任务
        </view>
      </template>
    </view>

    <!-- 分页 -->
    <view v-if="total > 0" class="pagination-container mt-4">
      <Pagination
        :total="total"
        :page="queryParams.page"
        :pageSize="queryParams.pageSize"
        @update:page="handlePageChange"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.teaching-task-selector {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;

  .search-box {
    padding: 12px 16px;
    border-bottom: 1px solid #ebedf0;

    .search-input {
      height: 36px;
    }
  }

  .task-list {
    flex: 1;
    padding: 0 16px;
    overflow-y: auto;

    .task-item {
      transition: background-color 0.2s;

      &:active {
        background-color: #f7f8fa;
      }

      &:last-child {
        border-bottom: none;
      }
    }

    .empty {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100px;
    }
  }

  .pagination-container {
    padding: 0 16px 16px;
  }
}
</style>
