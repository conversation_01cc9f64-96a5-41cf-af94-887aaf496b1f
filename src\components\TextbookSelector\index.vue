<template>
  <view class="textbook-selector">
    <!-- 搜索框和新增按钮 -->
    <view class="flex items-center justify-between mb-4">
      <view class="search-box flex items-center bg-gray-100 rounded-lg p-2 flex-1 mr-2">
        <wd-icon name="search" size="16px" class="text-gray-400 ml-2 mr-2"></wd-icon>
        <input
          v-model="searchKeyword"
          class="flex-1 bg-transparent p-1"
          placeholder="搜索教材名称/ISBN/作者"
          @input="handleSearch"
          confirm-type="search"
        />
        <wd-icon
          v-if="searchKeyword"
          name="close-fill"
          size="16px"
          class="text-gray-400 mr-2"
          @click="clearSearch"
        ></wd-icon>
      </view>
      <view
        class="add-btn flex items-center justify-center bg-primary text-white rounded-lg py-2 px-3"
        @click="goToAddTextbook"
      >
        <wd-icon name="add" size="16px" class="mr-1"></wd-icon>
        <text class="text-sm">新增教材</text>
      </view>
    </view>

    <!-- 教材列表 -->
    <view class="textbook-list-container" style="height: calc(80vh - 180px)">
      <view v-if="loading" class="flex justify-center items-center py-4">
        <wd-loading color="#2979ff" size="24px"></wd-loading>
      </view>
      <view
        v-else-if="textbookList.length === 0"
        class="empty-state flex flex-col items-center justify-center py-8"
      >
        <wd-icon name="info" size="32px" class="text-gray-300 mb-2"></wd-icon>
        <text class="text-gray-400">{{ searchKeyword ? '未找到相关教材' : '暂无教材数据' }}</text>
      </view>
      <view v-else class="textbook-list">
        <view
          v-for="item in textbookList"
          :key="item.id"
          class="textbook-item p-3 mb-3 bg-white rounded-lg border border-gray-100"
          @click="selectTextbook(item)"
        >
          <view class="flex justify-between items-start mb-2">
            <text class="textbook-name font-medium text-base">{{ item.name }}</text>
            <text
              class="textbook-type text-xs px-2 py-1 bg-primary-light text-primary rounded-full"
            >
              {{ item.typeName }}
            </text>
          </view>
          <view class="textbook-author text-sm text-gray-600 mb-1">
            <text>作者：{{ item.mainEditor }}</text>
          </view>
          <view class="textbook-publisher text-sm text-gray-600 mb-1">
            <text>出版社：{{ item.publishingHouse }}</text>
          </view>
          <view class="flex justify-between items-center text-xs text-gray-500">
            <text>ISBN：{{ item.isbn }}</text>
            <text>编码：{{ item.booksCode }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 分页 -->
    <Pagination
      v-if="textbookList.length > 0"
      :total="total"
      :page="currentPage"
      :pageSize="pageSize"
      @update:page="handlePageChange"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getTeachingMaterialList } from '@/service/teachingMaterial'
import type { TeachingMaterial } from '@/types/teachingMaterial'
import Pagination from '@/components/Pagination/index.vue'

// 定义组件事件
const emit = defineEmits<{
  (e: 'select', textbook: TeachingMaterial): void
}>()

// 状态变量
const searchKeyword = ref('')
const textbookList = ref<TeachingMaterial[]>([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 处理搜索输入
let searchTimer: number | null = null

const handleSearch = () => {
  // 使用防抖处理搜索
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  searchTimer = setTimeout(() => {
    currentPage.value = 1 // 搜索时重置为第1页
    loadTextbooks()
  }, 500) as unknown as number
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
  currentPage.value = 1
  loadTextbooks()
}

// 跳转到新增教材页面
const goToAddTextbook = () => {
  uni.navigateTo({
    url: '/pages/teachingMaterial/add/index',
  })
}

// 加载教材数据
const loadTextbooks = async () => {
  if (loading.value) return

  loading.value = true

  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      searchKeyword: searchKeyword.value,
    }

    const res = await getTeachingMaterialList(params)

    textbookList.value = res.items
    total.value = res.total
  } catch (error) {
    console.error('获取教材列表失败:', error)
    uni.showToast({
      title: '获取教材列表失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 处理页码变化
const handlePageChange = (page: number) => {
  if (currentPage.value === page) return

  currentPage.value = page
  loadTextbooks()
}

// 选择教材
const selectTextbook = (textbook: TeachingMaterial) => {
  emit('select', textbook)
}

// 在组件挂载时加载数据
onMounted(() => {
  loadTextbooks()
})
</script>

<style scoped lang="scss">
.textbook-selector {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16rpx;
}

.search-box {
  background-color: #f5f5f5;
  border-radius: 36rpx;
}

.add-btn {
  background-color: var(--primary-color, #2979ff);
  border-radius: 36rpx;
  transition: all 0.3s;

  &:active {
    opacity: 0.8;
    transform: scale(0.98);
  }
}

.textbook-list-container {
  flex: 1;
  overflow-y: auto;
}

.textbook-item {
  transition: all 0.3s;

  &:active {
    background-color: #f9f9f9;
    transform: scale(0.98);
  }
}

// 强调颜色
.text-primary {
  color: var(--primary-color, #2979ff);
}

.bg-primary-light {
  background-color: rgba(41, 121, 255, 0.1);
}
</style>
