# VenueSelector 授课场地选择器组件

## 概述

VenueSelector 是一个用于选择授课场地的组件，模仿 TextbookSelector 的设计风格，提供了完整的场地搜索、筛选和选择功能。

## 功能特性

- 🔍 **搜索功能**：支持按场地名称、建筑楼、校区进行搜索
- 🏷️ **多维筛选**：支持按校区、建筑楼、场地类别、状态进行筛选
- 📄 **分页显示**：支持分页加载，提升大数据量下的性能
- 📱 **响应式设计**：适配移动端界面，提供良好的用户体验
- ⚡ **防抖搜索**：搜索输入防抖处理，减少不必要的API请求
- 🎨 **统一样式**：与项目整体设计风格保持一致

## 显示字段

每个场地项显示以下信息：

- **场地名称**：主要标识
- **类别**：场地类型（教室、实验室、会议室等）
- **校区**：所属校区名称
- **建筑楼**：所在建筑楼名称
- **容量**：可容纳人数
- **状态**：当前使用状态（空闲/占用/维护）

## 使用方法

### 基本用法

```vue
<template>
  <view>
    <VenueSelector @select="handleVenueSelect" />
  </view>
</template>

<script setup lang="ts">
import VenueSelector from '@/components/VenueSelector/index.vue'
import type { Venue } from '@/types/venue'

const handleVenueSelect = (venue: Venue) => {
  console.log('选择的场地:', venue)
  // 处理场地选择逻辑
}
</script>
```

### 在表单中使用

```vue
<template>
  <view class="form">
    <!-- 其他表单项 -->

    <!-- 场地选择 -->
    <view class="form-item">
      <text class="form-label">授课场地</text>
      <view v-if="selectedVenue" class="selected-venue">
        {{ selectedVenue.name }} - {{ selectedVenue.campusName }}
        <wd-button size="small" @click="showVenueSelector">重新选择</wd-button>
      </view>
      <wd-button v-else type="primary" @click="showVenueSelector">选择场地</wd-button>
    </view>

    <!-- 场地选择器弹窗 -->
    <wd-popup v-model="venuePopupVisible" position="bottom" :height="'80%'">
      <VenueSelector @select="handleVenueSelect" />
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import VenueSelector from '@/components/VenueSelector/index.vue'
import type { Venue } from '@/types/venue'

const selectedVenue = ref<Venue | null>(null)
const venuePopupVisible = ref(false)

const showVenueSelector = () => {
  venuePopupVisible.value = true
}

const handleVenueSelect = (venue: Venue) => {
  selectedVenue.value = venue
  venuePopupVisible.value = false

  uni.showToast({
    title: `已选择：${venue.name}`,
    icon: 'success',
  })
}
</script>
```

## 事件

### @select

当用户选择场地时触发

**参数：**

- `venue: Venue` - 选择的场地对象

## 依赖的类型定义

组件依赖以下类型定义（位于 `src/types/venue.ts`）：

- `Venue` - 场地信息接口
- `VenueQuery` - 场地查询参数接口
- `VenueResponse` - 场地列表响应接口
- `Building` - 建筑楼信息接口

## 依赖的服务接口

组件依赖以下服务接口（位于 `src/service/venue.ts`）：

- `getVenueList()` - 获取场地列表
- `getBuildingList()` - 获取建筑楼列表
- `getVenueTypeOptions()` - 获取场地类型选项
- `getCampusList()` - 获取校区列表（来自 `src/service/campus.ts`）

## 样式定制

组件使用了项目的统一样式变量，主要包括：

- `--primary-color` - 主题色
- 响应式布局适配
- 与 TextbookSelector 一致的视觉风格

## 注意事项

1. **API接口**：确保后端提供相应的场地管理接口
2. **权限控制**：根据用户角色控制"新增场地"按钮的显示
3. **数据缓存**：考虑对校区、建筑楼等基础数据进行缓存
4. **错误处理**：组件内置了基本的错误处理，但建议根据实际需求进行扩展

## 扩展功能

可以根据需要扩展以下功能：

- 场地预约状态实时更新
- 场地详情查看
- 场地收藏功能
- 历史选择记录
- 批量选择功能
