<template>
  <view class="venue-selector">
    <!-- 搜索框和新增按钮 -->
    <view class="flex items-center justify-between mb-4">
      <view class="search-box flex items-center bg-gray-100 rounded-lg p-2 flex-1 mr-2">
        <wd-icon name="search" size="16px" class="text-gray-400 ml-2 mr-2"></wd-icon>
        <input
          v-model="searchKeyword"
          class="flex-1 bg-transparent p-1"
          placeholder="搜索场地名称/建筑楼/校区"
          @input="handleSearch"
          confirm-type="search"
        />
        <wd-icon
          v-if="searchKeyword"
          name="close-fill"
          size="16px"
          class="text-gray-400 mr-2"
          @click="clearSearch"
        ></wd-icon>
      </view>
      <!--  <view
        class="add-btn flex items-center justify-center bg-primary text-white rounded-lg py-2 px-3"
        @click="goToAddVenue"
      >
        <wd-icon name="add" size="16px" class="mr-1"></wd-icon>
        <text class="text-sm">新增场地</text>
      </view> -->
    </view>

    <!-- 多选模式下的选择统计 -->
    <view v-if="multiple && selectedCount > 0" class="selection-summary mb-4">
      <text class="selection-text">已选择 {{ selectedCount }} 个场地</text>
      <view class="clear-selection" @click="clearAllSelection">
        <text class="clear-text">清空</text>
      </view>
    </view>

    <!-- 场地列表 -->
    <view class="venue-list-container" style="height: calc(80vh - 180px)">
      <view v-if="loading" class="flex justify-center items-center py-4">
        <wd-loading color="#2979ff" size="24px"></wd-loading>
      </view>
      <view
        v-else-if="venueList.length === 0"
        class="empty-state flex flex-col items-center justify-center py-8"
      >
        <wd-icon name="info" size="32px" class="text-gray-300 mb-2"></wd-icon>
        <text class="text-gray-400">{{ searchKeyword ? '未找到相关场地' : '暂无场地数据' }}</text>
      </view>
      <view v-else class="venue-list">
        <view
          v-for="item in venueList"
          :key="item.id"
          class="venue-item p-3 mb-3 bg-white rounded-lg border border-gray-100"
          :class="{ 'venue-item-selected': isVenueSelected(item) }"
          @click="selectVenue(item)"
        >
          <view class="flex justify-between items-start mb-2">
            <view class="flex items-center flex-1">
              <!-- 多选模式下的复选框 -->
              <view v-if="multiple" class="checkbox-container mr-3">
                <view class="checkbox" :class="{ 'checkbox-checked': isVenueSelected(item) }">
                  <wd-icon
                    v-if="isVenueSelected(item)"
                    name="check"
                    size="12px"
                    class="text-white"
                  />
                </view>
              </view>
              <text class="venue-name font-medium text-base">{{ item.siteName }}</text>
            </view>
            <text class="venue-type text-xs px-2 py-1 bg-primary-light text-primary rounded-full">
              {{ item.categoryName }}
            </text>
          </view>
          <view class="venue-campus text-sm text-gray-600 mb-1">
            <text>校区：{{ item.campusName }}</text>
          </view>
          <view class="venue-building text-sm text-gray-600 mb-1">
            <text>建筑楼：{{ item.buildingName }}</text>
          </view>
          <view class="flex justify-between items-center text-xs text-gray-500">
            <text>容量：{{ item.studentCapacity }}人</text>
            <!-- <text
              :class="{
                'text-green-500': item.status === 'free',
                'text-red-500': item.status === 'occupied',
                'text-yellow-500': item.status === 'maintenance',
              }"
            >
              {{ getStatusText(item.status) }}
            </text> -->
          </view>
        </view>
      </view>
    </view>

    <!-- 分页 -->
    <Pagination
      v-if="venueList.length > 0"
      :total="total"
      :page="currentPage"
      :pageSize="pageSize"
      @update:page="handlePageChange"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { getVenueList } from '@/service/venue'
import type { Venue } from '@/types/venue'
import Pagination from '@/components/Pagination/index.vue'

// 定义组件属性
interface Props {
  /** 是否支持多选 */
  multiple?: boolean
  /** 已选择的场地列表（多选模式下使用） */
  selectedVenues?: Venue[]
}

const props = withDefaults(defineProps<Props>(), {
  multiple: false,
  selectedVenues: () => [],
})

// 定义组件事件
const emit = defineEmits<{
  (e: 'select', venue: Venue): void
  (e: 'multiSelect', venues: Venue[]): void
}>()

// 状态变量
const searchKeyword = ref('')
const venueList = ref<Venue[]>([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 多选模式下的选中状态
const selectedVenueIds = ref<Set<number>>(new Set())
// 存储所有选中的场地完整信息（跨页面保持）
const allSelectedVenues = ref<Map<number, Venue>>(new Map())

// 计算属性：已选择的场地数量
const selectedCount = computed(() => selectedVenueIds.value.size)

// 处理搜索输入
let searchTimer: number | null = null

const handleSearch = () => {
  // 使用防抖处理搜索
  if (searchTimer) {
    clearTimeout(searchTimer)
  }

  searchTimer = setTimeout(() => {
    currentPage.value = 1 // 搜索时重置为第1页
    loadVenues()
  }, 500) as unknown as number
}

// 清除搜索
const clearSearch = () => {
  searchKeyword.value = ''
  currentPage.value = 1
  loadVenues()
}

// 跳转到新增场地页面
const goToAddVenue = () => {
  uni.navigateTo({
    url: '/pages/venue/add/index',
  })
}

// 加载场地数据
const loadVenues = async () => {
  if (loading.value) return

  loading.value = true

  try {
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      searchKeyword: searchKeyword.value,
    }

    const res = await getVenueList(params)

    venueList.value = res.items
    total.value = res.total

    // 加载完成后，将当前页面中已选中的场地添加到全局存储中
    if (props.multiple) {
      venueList.value.forEach((venue: Venue) => {
        if (selectedVenueIds.value.has(venue.id) && !allSelectedVenues.value.has(venue.id)) {
          allSelectedVenues.value.set(venue.id, venue)
        }
      })
    }
  } catch (error) {
    console.error('获取场地列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理页码变化
const handlePageChange = (page: number) => {
  if (currentPage.value === page) return

  currentPage.value = page
  loadVenues()
}

// 选择场地
const selectVenue = (venue: Venue) => {
  if (props.multiple) {
    // 多选模式
    toggleVenueSelection(venue)
  } else {
    // 单选模式
    emit('select', venue)
  }
}

// 切换场地选中状态（多选模式）
const toggleVenueSelection = (venue: Venue) => {
  // 检查是否已经选中（根据ID或siteCode）
  const isCurrentlySelected = selectedVenueIds.value.has(venue.id)
  const existingSameCodeVenue = Array.from(allSelectedVenues.value.values()).find(
    (selectedVenue) => selectedVenue.siteCode === venue.siteCode && selectedVenue.id !== venue.id,
  )

  if (isCurrentlySelected) {
    // 取消选中
    selectedVenueIds.value.delete(venue.id)
    allSelectedVenues.value.delete(venue.id)
  } else if (existingSameCodeVenue) {
    // 如果存在相同siteCode的场地（比如默认场地），先移除旧的，再添加新的
    selectedVenueIds.value.delete(existingSameCodeVenue.id)
    allSelectedVenues.value.delete(existingSameCodeVenue.id)
    selectedVenueIds.value.add(venue.id)
    allSelectedVenues.value.set(venue.id, venue)
  } else {
    // 选中新场地
    selectedVenueIds.value.add(venue.id)
    allSelectedVenues.value.set(venue.id, venue)
  }

  // 获取所有选中的场地列表（从全局存储中获取）
  const selectedVenues = Array.from(allSelectedVenues.value.values())
  emit('multiSelect', selectedVenues)
}

// 检查场地是否已选中
const isVenueSelected = (venue: Venue) => {
  if (!props.multiple) return false

  // 优先根据ID匹配
  if (selectedVenueIds.value.has(venue.id)) {
    return true
  }

  // 如果ID匹配不上，尝试根据siteCode匹配（用于处理默认场地的情况）
  return Array.from(allSelectedVenues.value.values()).some(
    (selectedVenue) => selectedVenue.siteCode === venue.siteCode,
  )
}

// 初始化已选择的场地
const initSelectedVenues = () => {
  if (props.multiple && props.selectedVenues.length > 0) {
    selectedVenueIds.value = new Set(props.selectedVenues.map((v: Venue) => v.id))
    // 同时初始化全局存储
    allSelectedVenues.value.clear()
    props.selectedVenues.forEach((venue: Venue) => {
      allSelectedVenues.value.set(venue.id, venue)
    })
  }
}

// 清空所有选择
const clearAllSelection = () => {
  selectedVenueIds.value.clear()
  allSelectedVenues.value.clear()
  emit('multiSelect', [])
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    free: '空闲',
    occupied: '占用',
    maintenance: '维护',
  }
  return statusMap[status as keyof typeof statusMap] || '未知'
}

// 监听外部传入的selectedVenues变化，同步内部状态
watch(
  () => props.selectedVenues,
  (newSelectedVenues) => {
    if (props.multiple) {
      // 更新选中ID集合
      selectedVenueIds.value = new Set(newSelectedVenues.map((v: Venue) => v.id))
      // 更新全局存储
      allSelectedVenues.value.clear()
      newSelectedVenues.forEach((venue: Venue) => {
        allSelectedVenues.value.set(venue.id, venue)
      })
    }
  },
  { deep: true, immediate: false },
)

// 在组件挂载时加载数据
onMounted(() => {
  initSelectedVenues()
  loadVenues()
})
</script>

<style scoped lang="scss">
.venue-selector {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 16rpx;
}

.search-box {
  background-color: #f5f5f5;
  border-radius: 36rpx;
}

.add-btn {
  background-color: var(--primary-color, #2979ff);
  border-radius: 36rpx;
  transition: all 0.3s;

  &:active {
    opacity: 0.8;
    transform: scale(0.98);
  }
}

.venue-list-container {
  flex: 1;
  overflow-y: auto;
}

.venue-item {
  transition: all 0.3s;

  &:active {
    background-color: #f9f9f9;
    transform: scale(0.98);
  }

  &.venue-item-selected {
    background-color: rgba(41, 121, 255, 0.05);
    border-color: var(--primary-color, #2979ff);
  }
}

// 强调颜色
.text-primary {
  color: var(--primary-color, #2979ff);
}

.bg-primary-light {
  background-color: rgba(41, 121, 255, 0.1);
}

// 多选相关样式
.selection-summary {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background-color: rgba(41, 121, 255, 0.1);
  border: 1rpx solid rgba(41, 121, 255, 0.2);
  border-radius: 36rpx;

  .selection-text {
    font-size: 28rpx;
    font-weight: 500;
    color: var(--primary-color, #2979ff);
  }

  .clear-selection {
    padding: 8rpx 16rpx;
    background-color: rgba(245, 34, 45, 0.1);
    border: 1rpx solid rgba(245, 34, 45, 0.2);
    border-radius: 36rpx;

    .clear-text {
      font-size: 24rpx;
      color: #f5222d;
    }
  }
}

.checkbox-container {
  .checkbox {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36rpx;
    height: 36rpx;
    border: 2rpx solid #d9d9d9;
    border-radius: 8rpx;
    transition: all 0.3s;

    &.checkbox-checked {
      background-color: var(--primary-color, #2979ff);
      border-color: var(--primary-color, #2979ff);
    }
  }
}
</style>
