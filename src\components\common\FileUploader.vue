<template>
  <view>
    <!-- 附件上传 -->
    <view class="mb-4">
      <view class="text-sm font-medium mb-1" v-if="showTitle">{{ title }}</view>
      <view class="upload-area">
        <view class="upload-buttons">
          <button
            class="upload-button upload-image-button"
            @click="handleUploadImage"
            :disabled="uploadImageLoading"
          >
            <wd-icon name="camera" size="16px" class="mr-1"></wd-icon>
            {{ uploadImageLoading ? '上传中...' : '上传图片' }}
          </button>
          <button class="upload-button" @click="handleUpload" :disabled="uploadLoading">
            <wd-icon name="add" size="16px" class="mr-1"></wd-icon>
            {{ uploadLoading ? '上传中...' : '上传附件' }}
          </button>
        </view>
        <text class="upload-tip">{{ tipText }}</text>
      </view>
    </view>

    <!-- 附件列表 -->
    <view class="attachment-list" v-if="modelValue.length > 0">
      <view class="attachment-item" v-for="(file, index) in modelValue" :key="index">
        <view class="attachment-info" @click="previewAttachment(file)">
          <wd-icon :name="getFileIcon(file.name)" size="18px" class="mr-2"></wd-icon>
          <text class="attachment-name">{{ file.name }}</text>
        </view>
        <view class="attachment-actions">
          <view class="attachment-action" @click="previewAttachment(file)">
            <wd-icon name="view" size="18px" color="#1890ff"></wd-icon>
          </view>
          <view class="attachment-action" @click="deleteAttachment(index)">
            <wd-icon name="delete" size="18px" color="#ff4d4f"></wd-icon>
          </view>
        </view>
      </view>
    </view>
    <view class="empty-tip" v-else>
      <text>{{ emptyText }}</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'
import { useToast } from 'wot-design-uni'
import useUpload, { useUploadAnyFile } from '@/hooks/useUpload'

// 添加toast提示
const toast = useToast()

// 定义组件属性
const props = defineProps({
  // 使用v-model绑定的附件列表
  modelValue: {
    type: Array as () => Array<{ url: string; name: string }>,
    default: () => [],
  },
  // 上传类型，用于后端区分不同业务的上传
  uploadType: {
    type: String,
    default: 'default',
  },
  // 标题
  title: {
    type: String,
    default: '附件',
  },
  // 是否显示标题
  showTitle: {
    type: Boolean,
    default: true,
  },
  // 提示文本
  tipText: {
    type: String,
    default: '支持jpg、png、pdf、word、excel、ppt等常见文件格式',
  },
  // 空列表提示文本
  emptyText: {
    type: String,
    default: '暂无附件',
  },
  // 允许选择的文件数量
  count: {
    type: Number,
    default: 5,
  },
  // 允许的文件扩展名
  extensions: {
    type: Array as () => string[],
    default: () => ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
  },
})

// 定义事件
const emit = defineEmits(['update:modelValue'])

// 图片上传相关
const {
  loading: uploadImageLoading,
  error: uploadImageError,
  data: uploadImageData,
  run: uploadImage,
} = useUpload<any>({
  type: props.uploadType, // 上传类型
})

// 文件上传相关
const {
  loading: uploadLoading,
  error: uploadError,
  data: uploadData,
  run: uploadFile,
} = useUploadAnyFile<any>({
  count: props.count, // 允许一次选择的文件数量
  extension: props.extensions,
  formData: {
    type: props.uploadType, // 上传类型
  },
})

// 处理图片上传
const handleUploadImage = () => {
  uploadImage()
}

// 处理文件上传
const handleUpload = () => {
  uploadFile()
}

// 监听图片上传结果并处理
watch(uploadImageData, (newVal) => {
  if (!newVal) return

  try {
    const result = typeof newVal === 'string' ? JSON.parse(newVal) : newVal

    // 处理图片上传结果
    if (result.code === 1 && result.data?.file) {
      addAttachment(result.data.file.url, result.data.file.name || '图片.jpg')
      toast.show({ msg: '上传成功', iconName: 'success' })
    } else {
      toast.show({ msg: result.msg || '上传失败', iconName: 'error' })
    }
  } catch (e) {
    console.error('处理上传结果失败:', e)
    toast.show({ msg: '上传失败', iconName: 'error' })
  }
})

// 监听上传结果并处理
watch(uploadData, (newVal) => {
  if (!newVal) return

  try {
    const result = typeof newVal === 'string' ? JSON.parse(newVal) : newVal

    // 处理多文件上传结果
    if (Array.isArray(result.data?.fileList)) {
      result.data.fileList.forEach((file) => {
        addAttachment(file.url, file.name)
      })
      toast.show({ msg: '上传成功', iconName: 'success' })
    }
    // 处理单文件上传结果
    else if (result.code === 1 && result.data?.file) {
      addAttachment(result.data.file.url, result.data.file.name)
      toast.show({ msg: '上传成功', iconName: 'success' })
    } else {
      toast.show({ msg: result.msg || '上传失败', iconName: 'error' })
    }
  } catch (e) {
    console.error('处理上传结果失败:', e)
    toast.show({ msg: '上传失败', iconName: 'error' })
  }
})

// 添加附件到列表
const addAttachment = (url: string, name: string) => {
  const newList = [...props.modelValue, { url, name }]
  emit('update:modelValue', newList)
}

// 删除附件
const deleteAttachment = (index: number) => {
  uni.showModal({
    title: '提示',
    content: '确认删除该附件吗？',
    success: (res) => {
      if (res.confirm) {
        // 用户点击确定
        const newList = [...props.modelValue]
        newList.splice(index, 1)
        emit('update:modelValue', newList)

        // TODO: 后续可以添加跳转到授课计划表配置的逻辑
      }
    },
  })
}

// 预览附件
const previewAttachment = (file: { url: string; name: string }) => {
  const ext = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()

  // 图片文件直接预览
  if (['jpg', 'jpeg', 'png', 'gif'].includes(ext)) {
    uni.previewImage({
      urls: [file.url],
      current: file.url,
    })
    return
  }

  // 其他文件根据平台处理
  // #ifdef H5
  window.open(file.url, '_blank')
  // #endif

  // #ifdef APP-PLUS
  uni.downloadFile({
    url: file.url,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.openDocument({
          filePath: res.tempFilePath,
          success: () => console.log('打开文档成功'),
          fail: (err) => {
            console.error('打开文档失败', err)
            toast.show({ msg: '无法预览该类型文件', iconName: 'error' })
          },
        })
      }
    },
    fail: () => toast.show({ msg: '文件下载失败', iconName: 'error' }),
  })
  // #endif

  // #ifdef MP
  toast.show({ msg: '小程序暂不支持该类型文件预览', iconName: 'info' })
  // #endif
}

// 根据文件名获取对应的图标
const getFileIcon = (fileName: string) => {
  const extension = fileName.split('.').pop()?.toLowerCase() || ''

  switch (extension) {
    case 'pdf':
      return 'file-pdf'
    case 'doc':
    case 'docx':
      return 'file-word'
    case 'xls':
    case 'xlsx':
      return 'file-excel'
    case 'ppt':
    case 'pptx':
      return 'file-powerpoint'
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return 'file-image'
    default:
      return 'file-icon'
  }
}
</script>

<style lang="scss" scoped>
.upload-area {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.upload-buttons {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.upload-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 240rpx;
  height: 80rpx;
  font-size: 28rpx;
  color: #ffffff;
  background-color: #1890ff;
  border: none;
  border-radius: 40rpx;
}

.upload-image-button {
  background-color: #52c41a;
}

.upload-tip {
  margin-top: 12rpx;
  font-size: 24rpx;
  color: #999;
}

.attachment-list {
  width: 100%;
  margin-top: 24rpx;
}

.attachment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  margin-bottom: 16rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  transition: all 0.3s;
}

.attachment-item:hover {
  background-color: #e6f7ff;
}

.attachment-info {
  display: flex;
  align-items: center;
  max-width: 80%;
  cursor: pointer;
}

.attachment-name {
  overflow: hidden;
  font-size: 28rpx;
  color: #333;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.attachment-actions {
  display: flex;
  gap: 16rpx;
}

.attachment-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  cursor: pointer;
  background-color: #fff;
  border-radius: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.attachment-action:hover {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transform: translateY(-2rpx);
}

.empty-tip {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #999;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}
</style>
