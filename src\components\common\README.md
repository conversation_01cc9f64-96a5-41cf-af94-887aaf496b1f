# ActionButton 组件

一个可复用的操作按钮组件，支持多种类型和状态。

## 功能特性

- 🎨 多种预设类型（primary、danger、warning、secondary、default）
- 🚫 禁用状态支持
- 🎯 统一的交互效果（悬停、点击）
- 📱 响应式设计
- 🔧 可自定义内容（通过slot）

## 使用方法

### 基础用法

```vue
<template>
  <ActionButton type="primary" text="确认" @click="handleConfirm" />
</template>

<script setup>
import ActionButton from '@/components/common/ActionButton.vue'

const handleConfirm = () => {
  console.log('确认操作')
}
</script>
```

### 使用slot自定义内容

```vue
<template>
  <ActionButton type="danger" @click="handleDelete">
    <wd-icon name="delete" size="14" class="mr-1" />
    删除
  </ActionButton>
</template>
```

### 禁用状态

```vue
<template>
  <ActionButton type="primary" text="提交" :disabled="isSubmitting" @click="handleSubmit" />
</template>
```

## API

### Props

| 参数     | 类型                                                             | 默认值      | 说明     |
| -------- | ---------------------------------------------------------------- | ----------- | -------- |
| type     | `'primary' \| 'danger' \| 'warning' \| 'secondary' \| 'default'` | `'default'` | 按钮类型 |
| text     | `string`                                                         | `''`        | 按钮文本 |
| disabled | `boolean`                                                        | `false`     | 是否禁用 |

### Events

| 事件名 | 说明     | 回调参数 |
| ------ | -------- | -------- |
| click  | 点击事件 | -        |

### Slots

| 名称    | 说明                         |
| ------- | ---------------------------- |
| default | 按钮内容，优先级高于text属性 |

## 样式类型

### primary

- 蓝色主题，用于主要操作
- 背景：`bg-blue-50`，文字：`text-blue-600`，边框：`border-blue-200`

### danger

- 红色主题，用于危险操作
- 背景：`bg-red-50`，文字：`text-red-600`，边框：`border-red-200`

### warning

- 橙色主题，用于警告操作
- 背景：`bg-orange-50`，文字：`text-orange-600`，边框：`border-orange-200`

### secondary

- 灰色主题，用于次要操作
- 背景：`bg-gray-50`，文字：`text-gray-600`，边框：`border-gray-200`

### default

- 默认灰色主题
- 背景：`bg-gray-50`，文字：`text-gray-500`，边框：`border-gray-200`
