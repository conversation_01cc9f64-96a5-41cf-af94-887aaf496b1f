<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue'
import type { ProvinceItem } from '@/types/dict'
import { getCityDictData } from '@/utils/dict'

/**
 * 定义组件的属性
 */
const props = defineProps({
  // 当前选中的地区编码数组
  modelValue: {
    type: Array as () => string[],
    default: () => [],
  },
  // 当前选中的地区名称
  locationName: {
    type: String,
    default: '',
  },
  // 选择器占位文本
  placeholder: {
    type: String,
    default: '请选择地区',
  },
  // 选择器标题
  title: {
    type: String,
    default: '请选择地区',
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 是否处于编辑模式
  isEditing: {
    type: Boolean,
    default: false,
  },
})

/**
 * 定义组件的事件
 */
const emit = defineEmits(['update:modelValue', 'update:locationName', 'region-selected'])

// 城市数据
const cityData = ref<ProvinceItem[]>([])
// 选中的地区编码
const locationValue = ref<string[]>([])
// 选中的地区名称标签
const locationLabels = ref<string[]>([])
// 选择器列数据
const locationColumns = ref<any[]>([])
// 匹配的地区名称
const locationNames = ref<string[]>([])
// 临时选择路径（用于级联选择器）
const tempSelectionPath = ref<string[]>([])
// 加载状态
const loading = ref(false)

// 监听modelValue变化，更新内部状态
watch(
  () => props.modelValue,
  (newValue) => {
    if (Array.isArray(newValue) && newValue.length > 0) {
      // 确保locationValue是字符串数组
      locationValue.value = newValue.map((item) => String(item))
      // 如果有城市数据，尝试匹配名称
      if (cityData.value && cityData.value.length > 0) {
        matchLocationNames()
        // 编辑模式下确保预加载所有必要的列数据
        if (props.isEditing) {
          preloadColumnData()
        }
      }
      console.log('RegionPicker - modelValue变化:', {
        值: newValue,
        locationName: props.locationName,
        已匹配名称: locationNames.value,
      })
    } else {
      locationValue.value = []
      locationNames.value = []
    }
  },
  { immediate: true, deep: true },
)

// 监听locationName变化
watch(
  () => props.locationName,
  (newValue) => {
    if (!props.isEditing || !newValue) return

    console.log('RegionPicker - locationName变化:', newValue)

    // 如果有分隔符"|"，说明是格式化的地区名称
    if (newValue.includes('|')) {
      // 分割省市区名称
      const names = newValue.split('|')
      console.log('RegionPicker - 解析地区名称:', names)

      // 如果已经有城市数据且有地区编码，不需要通过名称去查找
      if (cityData.value?.length > 0 && locationValue.value.length > 0) {
        console.log('RegionPicker - 已有编码数据，无需通过名称反查')
        return
      }

      // 未来可以在这里实现通过名称去反查编码
      console.log('RegionPicker - 当前版本暂不支持通过名称反查编码')
    }
  },
)

// 根据当前值匹配地区名称
const matchLocationNames = () => {
  locationNames.value = []

  if (locationValue.value.length === 0 || !cityData.value?.length) {
    return
  }

  try {
    const codes = locationValue.value
    console.log('RegionPicker - 开始匹配地区名称:', {
      编码: codes,
      可用城市数据: cityData.value.length > 0 ? '已加载' : '未加载',
    })

    // 尝试匹配省级
    if (codes[0]) {
      const province = cityData.value.find((p) => p.value === codes[0])
      if (province) {
        locationNames.value[0] = province.label
        console.log('RegionPicker - 匹配到省级:', province.label)

        // 尝试匹配市级
        if (codes[1] && province.children?.length) {
          const city = province.children.find((c) => c.value === codes[1])
          if (city) {
            locationNames.value[1] = city.label
            console.log('RegionPicker - 匹配到市级:', city.label)

            // 尝试匹配区县级
            if (codes[2] && city.children?.length) {
              const district = city.children.find((d) => d.value === codes[2])
              if (district) {
                locationNames.value[2] = district.label
                console.log('RegionPicker - 匹配到区县级:', district.label)
              } else {
                console.log('RegionPicker - 未找到匹配的区县:', codes[2])
              }
            }
          } else {
            console.log('RegionPicker - 未找到匹配的市级:', codes[1])
          }
        }
      } else {
        console.log('RegionPicker - 未找到匹配的省级:', codes[0])
      }
    }

    // 过滤掉undefined的元素，确保数组中全是有效值
    locationNames.value = locationNames.value.filter((name) => name !== undefined)
    console.log('RegionPicker - 匹配地区名称结果:', locationNames.value)
  } catch (error) {
    console.error('匹配地区名称出错:', error)
  }
}

// 获取城市数据
const getCityData = async () => {
  loading.value = true
  try {
    // 使用优化后的方法获取城市数据，优先从缓存获取
    cityData.value = await getCityDictData()

    // 如果获取到数据，初始化省级数据
    if (cityData.value && cityData.value.length > 0) {
      console.log('RegionPicker - 城市数据加载成功:', {
        省份数量: cityData.value.length,
        示例省份: cityData.value[0]?.label,
        示例市: cityData.value[0]?.children?.[0]?.label,
        来源: cityData.value.length > 0 ? '缓存' : 'API',
      })

      // 初始化选择器的第一列数据(省级)
      locationColumns.value = [
        cityData.value.map((item) => ({
          value: item.value,
          label: item.label,
        })),
      ]

      // 如果有初始值，尝试获取对应的名称并预加载其他层级的数据
      if (locationValue.value.length > 0) {
        matchLocationNames()
        preloadColumnData()
      }
    } else {
      console.error('城市数据为空')
      uni.showToast({
        title: '城市数据加载失败',
        icon: 'error',
      })
    }
  } catch (error) {
    console.error('获取城市数据失败:', error)
    uni.showToast({
      title: '城市数据加载失败，请稍后重试',
      icon: 'error',
    })
  } finally {
    loading.value = false
  }
}

// 预加载其他层级的数据
const preloadColumnData = () => {
  if (!cityData.value || cityData.value.length === 0 || locationValue.value.length === 0) {
    return
  }

  try {
    console.log('RegionPicker - 开始预加载列数据，当前值:', locationValue.value)

    // 确保locationColumns包含省级数据
    if (locationColumns.value.length === 0 || !locationColumns.value[0]?.length) {
      locationColumns.value[0] = cityData.value.map((item) => ({
        value: item.value,
        label: item.label,
      }))
      console.log('RegionPicker - 初始化省级数据，共', locationColumns.value[0].length, '个省份')
    }

    // 尝试预加载第二列（市级）数据
    if (locationValue.value[0]) {
      const province = cityData.value.find((p) => p.value === locationValue.value[0])
      if (province?.children && province.children.length > 0) {
        // 预加载市级数据
        locationColumns.value[1] = province.children.map((item) => ({
          value: item.value,
          label: item.label,
        }))
        console.log(
          'RegionPicker - 预加载市级数据成功:',
          province.label,
          '市级列数据个数:',
          locationColumns.value[1].length,
        )

        // 尝试预加载第三列（区县级）数据
        if (locationValue.value[1]) {
          const city = province.children.find((c) => c.value === locationValue.value[1])
          if (city?.children && city.children.length > 0) {
            // 预加载区县级数据
            locationColumns.value[2] = city.children.map((item) => ({
              value: item.value,
              label: item.label,
            }))
            console.log(
              'RegionPicker - 预加载区县数据成功:',
              city.label,
              '区县列数据个数:',
              locationColumns.value[2].length,
            )
          } else {
            console.log('RegionPicker - 未找到市级或市级无区县数据:', locationValue.value[1])
            // 即使没有找到区县数据，也创建一个空的第三列，确保UI展示正确
            locationColumns.value[2] = []

            // 尝试在其他省份中查找这个城市
            tryFindCityInOtherProvinces(locationValue.value[1])
          }
        } else {
          console.log('RegionPicker - 无需预加载区县数据，locationValue没有市级编码')
        }
      } else {
        console.log('RegionPicker - 未找到省级或省级无城市数据:', locationValue.value[0])
        // 尝试寻找这个省份编码
        tryFindProvince(locationValue.value[0])
      }
    } else {
      console.log('RegionPicker - 无需预加载市级和区县数据，locationValue没有省级编码')
    }

    console.log('RegionPicker - 预加载完成，列数据:', {
      列数量: locationColumns.value.length,
      省级数据个数: locationColumns.value[0]?.length || 0,
      市级数据个数: locationColumns.value[1]?.length || 0,
      区县数据个数: locationColumns.value[2]?.length || 0,
    })
  } catch (error) {
    console.error('预加载列数据出错:', error)
  }
}

// 尝试在其他省份中查找城市
const tryFindCityInOtherProvinces = (cityCode: string) => {
  console.log('RegionPicker - 尝试在所有省份中查找城市:', cityCode)

  for (const province of cityData.value) {
    if (province.children) {
      const city = province.children.find((c) => c.value === cityCode)
      if (city) {
        console.log('RegionPicker - 找到城市所属省份:', province.label, '城市:', city.label)
        return { province, city }
      }
    }
  }

  console.log('RegionPicker - 在所有省份中未找到城市:', cityCode)
  return null
}

// 尝试查找省份
const tryFindProvince = (provinceCode: string) => {
  console.log('RegionPicker - 尝试查找省份:', provinceCode)

  const province = cityData.value.find((p) => p.value === provinceCode)
  if (province) {
    console.log('RegionPicker - 找到省份:', province.label)
    return province
  }

  console.log('RegionPicker - 未找到省份:', provinceCode)
  return null
}

// 地区选择器列变化处理
const handleLocationColumnChange = (options: any) => {
  const { selectedItem, index, resolve, finish } = options

  try {
    console.log('RegionPicker - 列变化处理，当前索引:', index, '选中项:', selectedItem)

    // 将选择的值添加到临时选择路径
    if (index === 0) {
      // 选择省级，重置路径
      tempSelectionPath.value = [selectedItem.value]

      // 如果选择了省级，加载对应的市级数据
      const province = cityData.value.find((p) => p.value === selectedItem.value)
      if (province && province.children && province.children.length > 0) {
        // 返回市级数据
        resolve(
          province.children.map((item) => ({
            value: item.value,
            label: item.label,
          })),
        )
      } else {
        // 没有市级数据，结束选择
        console.log('RegionPicker - 省级无市级数据，结束选择')
        finish()
      }
    } else if (index === 1) {
      // 选择市级，保留省级，更新市级
      // 注意：这里要确保tempSelectionPath至少有一个元素（省级）
      if (tempSelectionPath.value.length === 0 && locationValue.value.length > 0) {
        // 如果tempSelectionPath为空但locationValue有值，使用locationValue的第一个元素作为省级
        tempSelectionPath.value = [locationValue.value[0]]
        console.log('RegionPicker - 使用locationValue作为省级:', locationValue.value[0])
      } else if (tempSelectionPath.value.length === 0 && selectedItem && index === 0) {
        // 如果有选择省级但tempSelectionPath为空，使用当前选择的省级
        tempSelectionPath.value = [selectedItem.value]
        console.log('RegionPicker - 使用当前选择作为省级:', selectedItem.value)
      }

      // 更新临时路径中的市级
      tempSelectionPath.value[1] = selectedItem.value
      console.log('RegionPicker - 更新临时路径:', tempSelectionPath.value)

      // 查找省份数据
      const province = cityData.value.find((p) => p.value === tempSelectionPath.value[0])
      if (province && province.children) {
        // 查找选中的市
        const city = province.children.find((c) => c.value === selectedItem.value)
        if (city && city.children && city.children.length > 0) {
          // 返回区县级数据
          console.log('RegionPicker - 加载区县数据:', city.label, '区县数量:', city.children.length)
          resolve(
            city.children.map((item) => ({
              value: item.value,
              label: item.label,
            })),
          )
        } else {
          // 特殊处理：即使没有区县数据，也创建一个空列，让用户有机会选择
          console.log('RegionPicker - 市级无区县数据，创建空列, 市:', selectedItem.label)
          // 尝试重新从服务器获取该城市的区县数据（可选实现）
          // 如果确实没有区县数据，返回一个空数组，允许用户继续操作
          resolve([])
        }
      } else {
        // 省份数据获取失败，尝试重新查找
        console.log(
          'RegionPicker - 省级查找失败，尝试从所有省份中重新查找市级:',
          selectedItem.value,
        )

        // 尝试在所有省份中查找该市级
        let foundCity = null
        let foundProvince = null

        for (const prov of cityData.value) {
          if (prov.children) {
            const cityFound = prov.children.find((c) => c.value === selectedItem.value)
            if (cityFound) {
              foundCity = cityFound
              foundProvince = prov
              break
            }
          }
        }

        if (foundCity && foundProvince) {
          // 更新临时路径
          tempSelectionPath.value[0] = foundProvince.value
          console.log('RegionPicker - 找到市级所属省份:', foundProvince.label)

          if (foundCity.children && foundCity.children.length > 0) {
            // 返回区县级数据
            console.log(
              'RegionPicker - 加载区县数据:',
              foundCity.label,
              '区县数量:',
              foundCity.children.length,
            )
            resolve(
              foundCity.children.map((item) => ({
                value: item.value,
                label: item.label,
              })),
            )
          } else {
            console.log('RegionPicker - 市级无区县数据，创建空列')
            resolve([])
          }
        } else {
          console.log('RegionPicker - 无法找到市级所属省份，创建空列')
          resolve([])
        }
      }
    } else {
      // 选择区县级，结束选择
      if (selectedItem && selectedItem.value) {
        tempSelectionPath.value = [
          tempSelectionPath.value[0],
          tempSelectionPath.value[1],
          selectedItem.value,
        ]
      }
      console.log('RegionPicker - 区县选择完成，结束选择:', tempSelectionPath.value)
      finish()
    }
  } catch (error) {
    console.error('地区选择器列变化处理出错:', error)
    // 发生错误时，结束选择
    finish(false)
  }
}

// 地区选择器确认处理
const handleLocationConfirm = (event: { value: string[]; selectedItems: any[] }) => {
  console.log('RegionPicker - 选择确认:', event)

  // 保存选中的value值数组(编码)
  locationValue.value = event.value

  // 记录选中项的标签值（省市区名称）
  locationLabels.value = event.selectedItems.map((item) => item.label || '')

  // 数据清理：如果有空值，则进行清理
  // 当选择了省和市，但没有选择区或区数据为空时，确保只保留有效值
  while (
    locationValue.value.length > 0 &&
    (locationValue.value[locationValue.value.length - 1] === undefined ||
      locationValue.value[locationValue.value.length - 1] === '')
  ) {
    locationValue.value.pop()
    locationLabels.value.pop()
  }

  console.log('RegionPicker - 清理后的选择值:', {
    编码: locationValue.value,
    名称: locationLabels.value,
  })

  // 通过自定义事件将选择结果传递给父组件
  emit('update:modelValue', [...locationValue.value])
  // 使用"|"作为分隔符，以便与服务器格式保持一致
  const locationNameValue = locationLabels.value.join('|')
  emit('update:locationName', locationNameValue)

  // 发送完整的选择结果
  emit('region-selected', {
    value: [...locationValue.value],
    labels: [...locationLabels.value],
    locationName: locationNameValue,
  })
}

// 根据城市编码和名称计算显示文本
const displayText = computed(() => {
  // 如果不在编辑模式下且有名称数据
  if (!props.isEditing) {
    // 如果已匹配到名称，优先使用匹配的名称
    if (locationNames.value.length > 0) {
      const joinedNames = locationNames.value.join(' / ')
      console.log('RegionPicker - 使用匹配的地区名称显示:', joinedNames)
      return joinedNames
    }

    // 其次检查locationName字段
    if (props.locationName) {
      // 如果存在locationName，将"|"分隔符替换为" / "显示
      const formattedName = props.locationName.replace(/\|/g, ' / ')
      console.log('RegionPicker - 格式化地区名称:', {
        原始值: props.locationName,
        格式化后: formattedName,
      })
      return formattedName
    }

    return '--'
  }

  // 编辑模式下返回空，由选择器组件显示占位符
  return ''
})

// 组件挂载时获取城市数据
onMounted(() => {
  getCityData().then(() => {
    // 如果在编辑模式下且有初始值，需要确保正确显示
    if (props.isEditing && Array.isArray(props.modelValue) && props.modelValue.length > 0) {
      console.log('RegionPicker - 挂载后初始化编辑模式数据')
      // 确保locationValue是字符串数组
      locationValue.value = props.modelValue.map((item) => String(item))
      matchLocationNames()
      preloadColumnData()
    }
  })
})
</script>

<template>
  <view class="region-picker">
    <!-- 非编辑模式下显示文本 -->
    <text v-if="!isEditing" class="region-text">{{ displayText }}</text>

    <!-- 编辑模式下显示选择器 -->
    <wd-col-picker
      v-else
      v-model="locationValue"
      :columns="locationColumns"
      :placeholder="placeholder"
      :disabled="disabled"
      :column-change="handleLocationColumnChange"
      @confirm="handleLocationConfirm"
      :title="title"
      auto-complete
    >
      <template #default>
        <view class="picker-text">
          <text v-if="locationNames.length > 0">{{ locationNames.join(' / ') }}</text>
          <text v-else-if="props.locationName && props.locationName.includes('|')">
            {{ props.locationName.replace(/\|/g, ' / ') }}
          </text>
          <text v-else class="placeholder-text">{{ placeholder }}</text>
        </view>
      </template>
    </wd-col-picker>

    <!-- 调试信息 (开发环境使用) -->
    <view v-if="false" class="debug-info">
      <text>编码: {{ locationValue.join(',') }}</text>
      <text>名称: {{ locationNames.join(',') }}</text>
      <text>传入名称: {{ locationName }}</text>
      <text>显示文本: {{ displayText }}</text>
    </view>

    <!-- 加载中提示 -->
    <view v-if="loading && !cityData.length" class="loading-tip">
      <wd-loading size="20px" color="#3a8eff" />
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</template>

<style scoped lang="scss">
.region-picker {
  flex: 1;

  .region-text {
    overflow: hidden;
    font-size: 28rpx;
    color: #1f2329;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .picker-text {
    overflow: hidden;
    font-size: 28rpx;
    color: #1f2329;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .placeholder-text {
    color: #c0c4cc;
  }

  .loading-tip {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 6rpx;

    .loading-text {
      margin-left: 8rpx;
      font-size: 24rpx;
      color: #86909c;
    }
  }

  .debug-info {
    display: flex;
    flex-direction: column;
    padding-top: 10rpx;
    margin-top: 10rpx;
    font-size: 24rpx;
    color: #86909c;
    border-top: 1px dashed #eee;
  }
}
</style>
