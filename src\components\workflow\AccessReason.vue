<script setup lang="ts">
import CardHeader from './CardHeader.vue'

defineProps<{
  content: string
}>()
</script>

<template>
  <view class="detail-card bg-white rounded-lg shadow mb-4 p-4">
    <CardHeader title="访问事由" />
    <view class="text-sm text-gray-800 leading-relaxed">
      {{ content }}
    </view>
  </view>
</template>

<style lang="scss" scoped>
.detail-card {
  margin-bottom: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
</style>
