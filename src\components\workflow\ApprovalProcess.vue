<script setup lang="ts">
import { ref, computed } from 'vue'
import CardHeader from './CardHeader.vue'
import FlowBranch, { type BranchStatusSummary } from './FlowBranch.vue'
import type { FlowStep } from './FlowStepItem.vue'
import type { WorkflowDetailResponse } from '@/types/workflow'

interface Props {
  workflowData: WorkflowDetailResponse
  flowSteps: FlowStep[][]
}

const props = defineProps<Props>()

// 控制审批流程折叠状态
const expandedBranches = ref<Record<number, boolean>>({})

/**
 * 初始化各个审批分支的展开/折叠状态
 */
const initBranchExpandState = () => {
  if (!props.flowSteps || props.flowSteps.length === 0) return

  props.flowSteps.forEach((_, index) => {
    expandedBranches.value[index] = index === 0
  })
  /* // 是否是已撤销的流程
  const isRevoked = props.workflowData.form.status === 4 || props.workflowData.form.status === 3

  // 如果是撤销状态，直接展开第一个分支
  if (isRevoked) {
    props.flowSteps.forEach((_, index) => {
      expandedBranches.value[index] = index === 0
    })
    return
  }

  // 查找是否有已通过的流程
  const approvedIndex = props.flowSteps.findIndex((branch) =>
    branch.every((step) => step.status === 'success'),
  )

  // 查找是否有待处理的流程
  const pendingIndex = props.flowSteps.findIndex(
    (branch) =>
      branch.some((step) => step.status === 'active') &&
      !branch.some((step) => step.status === 'rejected'),
  )

  // 遍历所有分支
  props.flowSteps.forEach((_, index) => {
    // 如果只有一个分支，则展开
    if (props.flowSteps.length === 1) {
      expandedBranches.value[index] = true
      return
    }

    // 优先展开已通过的流程
    if (approvedIndex !== -1) {
      expandedBranches.value[index] = index === approvedIndex
    }
    // 如果没有已通过的流程，则展开待处理的流程
    else if (pendingIndex !== -1) {
      expandedBranches.value[index] = index === pendingIndex
    }
    // 如果既没有已通过的也没有待处理的，则展开最后一个流程（即使是被拒绝的）
    else {
      expandedBranches.value[index] = index === props.flowSteps.length - 1
    }
  }) */
}

/**
 * 切换分支的展开/折叠状态
 */
const toggleBranchExpand = (branchIndex: number) => {
  expandedBranches.value[branchIndex] = !expandedBranches.value[branchIndex]
}

/**
 * 判断分支是否已被拒绝
 */
const isBranchRejected = (branch: FlowStep[]): boolean => {
  // 如果整个流程已撤销，则不显示拒绝状态
  if (props.workflowData.form.status === 4) {
    return false
  }
  return branch.some((step) => step.status === 'rejected')
}

/**
 * 判断分支是否已驳回
 */
const isBranchReturned = (branch: FlowStep[]): boolean => {
  // 如果整个流程已撤销，则不显示驳回状态
  if (props.workflowData.form.status === 4) {
    return false
  }
  return branch.some((step) => step.status === 'returned')
}

/**
 * 判断分支是否已撤销
 */
const isBranchRevoked = (branch: FlowStep[]): boolean => {
  return props.workflowData.form.status === 4 || branch.some((step) => step.status === 'revoked')
}

/**
 * 判断分支是否已全部通过
 */
const isBranchApproved = (branch: FlowStep[]): boolean => {
  // 如果整个流程已撤销，则不显示通过状态
  if (props.workflowData.form.status === 4) {
    return false
  }
  // 检查是否所有节点都是成功状态
  return branch.every((step) => step.status === 'success')
}

/**
 * 获取分支审批状态摘要
 */
const getBranchStatusSummary = (branch: FlowStep[]): BranchStatusSummary => {
  // 检查是否整个流程已撤销
  if (props.workflowData.form.status === 4) {
    return {
      status: 'revoked',
      message: '流程已撤销',
      icon: 'close',
      color: '#9ca3af',
      bgColor: 'bg-gray-50',
      textColor: 'text-gray-500',
    }
  }

  // 如果分支被拒绝，返回拒绝信息
  if (isBranchRejected(branch)) {
    // 查找被拒绝的节点和审批信息
    const rejectedStep = branch.find((step) => step.status === 'rejected')
    if (rejectedStep && rejectedStep.commentInfo) {
      return {
        status: 'rejected',
        message: `${rejectedStep.commentInfo.user} 拒绝：${rejectedStep.commentInfo.content}`,
        icon: 'close-circle',
        color: '#ef4444',
        bgColor: 'bg-red-50',
        textColor: 'text-red-600',
      }
    }
    return {
      status: 'rejected',
      message: '审批已被拒绝',
      icon: 'close-circle',
      color: '#ef4444',
      bgColor: 'bg-red-50',
      textColor: 'text-red-600',
    }
  }

  // 如果分支被驳回，返回驳回信息
  if (isBranchReturned(branch)) {
    // 查找被驳回的节点和审批信息
    const returnedStep = branch.find((step) => step.status === 'returned')
    if (returnedStep && returnedStep.commentInfo) {
      return {
        status: 'returned',
        message: `${returnedStep.commentInfo.user} 驳回：${returnedStep.commentInfo.content}`,
        icon: 'backtop',
        color: '#f97316',
        bgColor: 'bg-orange-50',
        textColor: 'text-orange-600',
      }
    }
    return {
      status: 'returned',
      message: '审批已被驳回',
      icon: 'backtop',
      color: '#f97316',
      bgColor: 'bg-orange-50',
      textColor: 'text-orange-600',
    }
  }

  // 如果分支全部通过，返回通过信息
  if (isBranchApproved(branch)) {
    // 找到最后一个通过的节点
    const lastApprovedStep = [...branch].reverse().find((step) => step.status === 'success')
    if (lastApprovedStep && lastApprovedStep.commentInfo) {
      return {
        status: 'approved',
        message: `${lastApprovedStep.commentInfo.user} 已通过`,
        icon: 'check-circle',
        color: '#22c55e',
        bgColor: 'bg-green-50',
        textColor: 'text-green-600',
      }
    }
    return {
      status: 'approved',
      message: '审批已通过',
      icon: 'check-circle',
      color: '#22c55e',
      bgColor: 'bg-green-50',
      textColor: 'text-green-600',
    }
  }

  // 如果分支正在审批中
  const activeStep = branch.find((step) => step.status === 'active')
  if (activeStep) {
    return {
      status: 'active',
      message: `等待 ${activeStep.handler} 审批`,
      icon: 'time',
      color: '#3b82f6',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-600',
    }
  }

  // 默认状态
  return {
    status: 'pending',
    message: '等待审批',
    icon: 'info-circle',
    color: '#6b7280',
    bgColor: 'bg-gray-50',
    textColor: 'text-gray-500',
  }
}

/**
 * 获取每个分支的状态摘要
 */
const branchStatusSummaries = computed(() => {
  const summaries: Record<number, BranchStatusSummary> = {}

  if (props.flowSteps) {
    props.flowSteps.forEach((branch, index) => {
      summaries[index] = getBranchStatusSummary(branch)
    })
  }

  return summaries
})

// 初始化分支展开状态
initBranchExpandState()
</script>

<template>
  <view class="detail-card bg-white rounded-lg shadow mb-4 p-4">
    <CardHeader title="审批流程" />

    <!-- 多个审批分支 -->
    <template v-if="flowSteps && flowSteps.length">
      <template v-for="(branch, branchIndex) in flowSteps" :key="branchIndex">
        <FlowBranch
          :branch="branch"
          :branch-index="branchIndex"
          :is-expanded="expandedBranches[branchIndex]"
          :status-summary="branchStatusSummaries[branchIndex]"
          :is-workflow-revoked="workflowData.form.status === 4"
          @toggle="toggleBranchExpand"
        />

        <!-- 分支分隔符 -->
        <view
          v-if="branchIndex < flowSteps.length - 1"
          class="border-t border-dashed border-gray-300 my-4"
        ></view>
      </template>
    </template>

    <!-- 无数据状态 -->
    <view v-else class="py-4 text-center text-gray-500">暂无审批流程数据</view>
  </view>
</template>

<style lang="scss" scoped>
.detail-card {
  margin-bottom: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}
</style>
