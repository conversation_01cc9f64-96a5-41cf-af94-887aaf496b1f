<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  url: string
  index?: number
  totalCount?: number
  label?: string
}

const props = defineProps<Props>()

const emit = defineEmits(['click'])

/**
 * 获取实际的URL（去除|后的文件名部分）
 */
const actualUrl = computed((): string => {
  return props.url.includes('|') ? props.url.split('|')[0] : props.url
})

/**
 * 获取文件类型
 */
const fileType = computed((): string => {
  if (!props.url) return 'unknown'

  // 如果URL包含|分隔符，只使用|前面的实际URL部分
  const actualUrl = props.url.includes('|') ? props.url.split('|')[0] : props.url
  const extension = actualUrl.split('.').pop()?.toLowerCase() || ''

  // 图片类型
  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
    return 'image'
  }

  // 文档类型
  if (['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'].includes(extension)) {
    return 'document'
  }

  return 'unknown'
})

/**
 * 获取附件名称
 */
const fileName = computed((): string => {
  if (props.label) return props.label

  if (fileType.value === 'image') {
    return props.totalCount && props.totalCount > 1 ? `附件${props.index! + 1}` : '照片'
  }

  if (!props.url) return ''

  // 检查URL中是否包含 | 分隔符，如果有则使用 | 后面的文件名
  if (props.url.includes('|')) {
    const parts = props.url.split('|')
    if (parts.length > 1 && parts[1].trim()) {
      return parts[1].trim()
    }
  }

  // 从URL中提取文件名
  const parts = props.url.split('/')
  const fullName = parts[parts.length - 1]

  // 处理URL编码的文件名
  try {
    return decodeURIComponent(fullName)
  } catch (e) {
    return fullName
  }
})

/**
 * 获取对应的图标
 */
const fileIcon = computed((): string => {
  switch (fileType.value) {
    case 'image':
      return 'picture'
    case 'document':
      return 'file-icon'
    default:
      return 'file'
  }
})

/**
 * 处理点击事件
 */
const handleClick = () => {
  emit('click', props.url)
}
</script>

<template>
  <view class="attachment-item" @tap="handleClick">
    <template v-if="fileType === 'image'">
      <image :src="actualUrl" mode="aspectFill" class="attachment-image" />
      <view class="attachment-label">{{ fileName }}</view>
    </template>
    <template v-else>
      <view class="document-item">
        <wd-icon :name="fileIcon" size="40px" color="#1989fa" />
        <view class="document-name">{{ fileName }}</view>
        <view class="document-action">
          <wd-icon name="download" size="18px" color="#1989fa" />
          <text class="action-text">下载</text>
        </view>
      </view>
    </template>
  </view>
</template>

<style lang="scss" scoped>
.attachment-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  border-radius: 8rpx;
}

.attachment-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.attachment-label {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 4rpx 8rpx;
  overflow: hidden;
  font-size: 20rpx;
  color: #fff;
  text-align: center;
  text-overflow: ellipsis;
  white-space: nowrap;
  background-color: rgba(0, 0, 0, 0.5);
}

.document-item {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 16rpx;
  background-color: #f5f7fa;
}

.document-name {
  display: -webkit-box;
  margin-top: 8rpx;
  overflow: hidden;
  font-size: 22rpx;
  color: #606266;
  text-align: center;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.document-action {
  display: flex;
  align-items: center;
  padding: 4rpx 12rpx;
  margin-top: 8rpx;
  background-color: #ecf5ff;
  border-radius: 24rpx;
}

.action-text {
  margin-left: 4rpx;
  font-size: 20rpx;
  color: #1989fa;
}
</style>
