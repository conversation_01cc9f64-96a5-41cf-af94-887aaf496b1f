<script setup lang="ts">
import { computed } from 'vue'
import CardHeader from './CardHeader.vue'
import AttachmentItem from './AttachmentItem.vue'

interface Props {
  photoUrl?: string
  attachmentUrls?: string
  fileUrls?: string
}

const props = defineProps<Props>()

/**
 * 获取附件URL数组
 */
const attachmentList = computed((): string[] => {
  if (!props.attachmentUrls) return []

  return props.attachmentUrls.split(',').filter((url) => url.trim() !== '')
  // 保留完整的URL（包含|后的文件名），让AttachmentItem组件处理
})

/**
 * 获取文件URL数组
 */
const fileList = computed((): string[] => {
  if (!props.fileUrls) return []

  return props.fileUrls.split(',').filter((url) => url.trim() !== '')
  // 保留完整的URL（包含|后的文件名），让AttachmentItem组件处理
})

/**
 * 获取所有附件列表（包括文件）
 */
const allAttachments = computed((): string[] => {
  return [...attachmentList.value, ...fileList.value]
})

/**
 * 处理附件点击
 */
const handleAttachmentClick = (url: string) => {
  // 如果URL包含|分隔符，只使用|前面的实际URL部分
  const actualUrl = url.includes('|') ? url.split('|')[0] : url
  const fileType = getFileType(actualUrl)

  if (fileType === 'image') {
    previewAttachment(actualUrl)
  } else if (fileType === 'pdf') {
    openPdfInWebview(actualUrl)
  } else {
    downloadAttachment(actualUrl)
  }
}

/**
 * 检查文件类型
 */
const getFileType = (url: string): string => {
  if (!url) return 'unknown'

  // 如果URL包含|分隔符，只使用|前面的实际URL部分
  const actualUrl = url.includes('|') ? url.split('|')[0] : url
  const extension = actualUrl.split('.').pop()?.toLowerCase() || ''

  if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(extension)) {
    return 'image'
  }

  if (extension === 'pdf') {
    return 'pdf'
  }

  if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'].includes(extension)) {
    return 'document'
  }

  return 'unknown'
}

/**
 * 预览图片
 */
const previewAttachment = (url: string) => {
  uni.previewImage({
    urls: [url],
    current: url,
  })
}

/**
 * 在WebView中打开PDF文件
 */
const openPdfInWebview = (url: string) => {
  // 获取文件名
  const fileName = url.split('/').pop() || 'PDF文件'

  // 跳转到webview页面
  uni.navigateTo({
    url: `/pages/public/webview/webview?pdfreview=true&url=${encodeURIComponent(url)}&title=${encodeURIComponent(fileName)}`,
    fail: (err) => {
      console.error('打开PDF失败', err)
      uni.showToast({
        title: '打开PDF失败',
        icon: 'none',
      })
    },
  })
}

/**
 * 下载附件
 */
const downloadAttachment = (url: string) => {
  uni.showLoading({
    title: '准备下载...',
  })

  uni.downloadFile({
    url,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.hideLoading()
        uni.openDocument({
          filePath: res.tempFilePath,
          showMenu: true,
          success: () => {
            console.log('打开文档成功')
          },
          fail: (err) => {
            console.error('打开文档失败', err)
            uni.showToast({
              title: '无法打开此类型文件',
              icon: 'none',
            })
          },
        })
      }
    },
    fail: (err) => {
      console.error('下载失败', err)
      uni.hideLoading()
      uni.showToast({
        title: '下载失败',
        icon: 'none',
      })
    },
  })
}
</script>

<template>
  <view
    v-if="
      photoUrl || (attachmentUrls && attachmentList.length > 0) || (fileUrls && fileList.length > 0)
    "
    class="detail-card bg-white rounded-lg shadow mb-4 p-4"
  >
    <CardHeader title="附件" />
    <view class="attachment-container">
      <!-- 显示照片 -->
      <AttachmentItem v-if="photoUrl" :url="photoUrl" label="照片" @click="handleAttachmentClick" />

      <!-- 显示附件列表 -->
      <AttachmentItem
        v-for="(fileUrl, index) in allAttachments"
        :key="index"
        :url="fileUrl"
        :index="index"
        :total-count="allAttachments.length"
        @click="handleAttachmentClick"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.detail-card {
  margin-bottom: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.attachment-container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  overflow: hidden;
}
</style>
