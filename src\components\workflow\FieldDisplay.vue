<script setup lang="ts">
import { computed } from 'vue'
import type { WorkflowField } from '@/types/workflow'

interface Props {
  field: WorkflowField
  formData: Record<string, any>
  cols?: Record<
    string,
    Array<{
      title: string
      dataIndex: string
      key: string
      minWidth?: number
      width?: number
    }>
  >
}

const props = defineProps<Props>()

/**
 * 字段值
 */
const fieldValue = computed(() => {
  return props.formData[props.field.name] || '-'
})
</script>

<template>
  <view class="info-item mb-3 text-base">
    <view class="info-label text-gray-500 mb-1">{{ field.label }}：</view>

    <!-- 文本类型展示 -->
    <view v-if="field.type === 'text'" class="info-value text-gray-800 break-all">
      {{ fieldValue }}
    </view>

    <!-- 表格类型展示 -->
    <view
      v-else-if="field.type === 'table' && cols && cols[field.name]"
      class="table-container mt-2"
    >
      <view class="table-responsive">
        <!-- 表头 -->
        <view class="table-header flex border-b border-gray-200">
          <view
            v-for="column in cols[field.name]"
            :key="column.key"
            class="table-cell py-2 px-3 text-sm font-medium text-gray-700 bg-gray-50"
            :style="
              column.width
                ? {
                    width: column.width + 'px',
                    minWidth: (column.minWidth || column.width) + 'px',
                  }
                : {}
            "
          >
            {{ column.title }}
          </view>
        </view>

        <!-- 表格内容 -->
        <view v-if="formData[field.name] && formData[field.name].length > 0" class="table-body">
          <view
            v-for="(row, rowIndex) in formData[field.name]"
            :key="rowIndex"
            class="table-row flex border-b border-gray-200 hover:bg-gray-50"
          >
            <view
              v-for="column in cols[field.name]"
              :key="column.key"
              class="table-cell py-2 px-3 text-sm text-gray-800"
              :style="
                column.width
                  ? {
                      width: column.width + 'px',
                      minWidth: (column.minWidth || column.width) + 'px',
                    }
                  : {}
              "
            >
              {{ row[column.dataIndex] || '-' }}
            </view>
          </view>
        </view>

        <!-- 无数据展示 -->
        <view v-else class="empty-data py-4 text-center text-gray-500 text-sm">暂无数据</view>
      </view>
    </view>

    <!-- 多行文本类型展示 -->
    <view
      v-else-if="field.type === 'row'"
      class="info-value text-gray-800 break-all whitespace-pre-wrap"
    >
      {{ fieldValue }}
    </view>

    <!-- 默认展示方式 -->
    <view v-else class="info-value text-gray-800 break-all">
      {{ fieldValue }}
    </view>
  </view>
</template>

<style lang="scss" scoped>
.info-item {
  width: 100%;
}

.info-label {
  font-weight: 500;
}

.info-value {
  word-break: break-all;
  word-wrap: break-word;
  white-space: normal;
}
/* 表格样式 */
.table-container {
  width: 100%;
  overflow-x: auto;
  border: 1px solid #ebeef5;
  border-radius: 8rpx;
}

.table-responsive {
  width: fit-content;
  min-width: 100%;
}

.table-header {
  background-color: #f8fafc;
}

.table-cell {
  flex: 1;
  align-items: center;
  min-width: 120rpx;
  word-break: break-all;
}

.table-row:nth-child(even) {
  background-color: #fafafa;
}

.table-row:hover {
  background-color: #f5f7fa;
}

.empty-data {
  background-color: #fafafa;
}
</style>
