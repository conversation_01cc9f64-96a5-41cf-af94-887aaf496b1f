<script setup lang="ts">
import { computed } from 'vue'
import FlowStepItem, { type FlowStep } from './FlowStepItem.vue'

export interface BranchStatusSummary {
  status: 'rejected' | 'approved' | 'active' | 'pending' | 'revoked' | 'returned'
  message: string
  icon: string
  color: string
  bgColor: string
  textColor: string
}

interface Props {
  branch: FlowStep[]
  branchIndex: number
  isExpanded: boolean
  statusSummary: BranchStatusSummary
  isWorkflowRevoked: boolean
}

const props = defineProps<Props>()
const emit = defineEmits(['toggle'])

/**
 * 切换分支展开/折叠状态
 */
const toggleExpand = () => {
  emit('toggle', props.branchIndex)
}

/**
 * 判断分支是否被拒绝
 */
const isBranchRejected = computed((): boolean => {
  // 如果整个流程已撤销，则不显示拒绝状态
  if (props.isWorkflowRevoked) {
    return false
  }
  return props.branch.some((step) => step.status === 'rejected')
})

/**
 * 判断分支是否已驳回
 */
const isBranchReturned = computed((): boolean => {
  // 如果整个流程已撤销，则不显示驳回状态
  if (props.isWorkflowRevoked) {
    return false
  }
  return props.branch.some((step) => step.status === 'returned')
})

/**
 * 判断分支是否已撤销
 */
const isBranchRevoked = computed((): boolean => {
  return props.isWorkflowRevoked || props.branch.some((step) => step.status === 'revoked')
})

/**
 * 分支标题颜色类
 */
const branchTitleClass = computed((): string => {
  if (isBranchRevoked.value) return 'text-gray-500'
  if (isBranchRejected.value) return 'text-red-500'
  if (isBranchReturned.value) return 'text-orange-500'
  return 'text-blue-600'
})

/**
 * 分支图标
 */
const branchIcon = computed(() => {
  if (isBranchRevoked.value) {
    return {
      name: 'close',
      color: '#9ca3af',
    }
  }

  if (isBranchRejected.value) {
    return {
      name: 'close-circle',
      color: '#ef4444',
    }
  }

  if (isBranchReturned.value) {
    return {
      name: 'backtop',
      color: '#f97316',
    }
  }

  return {
    name: 'flag',
    color: '#2563eb',
  }
})
</script>

<template>
  <view class="mb-6">
    <!-- 分支标题和折叠按钮 -->
    <view class="flex justify-between items-center mb-3">
      <!-- 分支标题 -->
      <view class="text-sm font-medium flex items-center" :class="branchTitleClass">
        <wd-icon :name="branchIcon.name" size="14px" :color="branchIcon.color" class="mr-1" />
        {{ branchIndex === 0 ? '审批流程 1' : '历史流程 ' + (branchIndex + 1) }}

        <view
          v-if="isBranchRejected"
          class="ml-2 px-1.5 py-0.5 rounded-sm text-xs text-white bg-red-500"
        >
          已拒绝
        </view>

        <view
          v-if="isBranchReturned"
          class="ml-2 px-1.5 py-0.5 rounded-sm text-xs text-white bg-orange-500"
        >
          已驳回
        </view>

        <view
          v-if="isBranchRevoked"
          class="ml-2 px-1.5 py-0.5 rounded-sm text-xs text-white bg-gray-500"
        >
          已撤销
        </view>
      </view>

      <!-- 折叠/展开按钮 -->
      <view
        class="flex items-center cursor-pointer text-xs text-gray-500 px-2 py-1 rounded hover:bg-gray-100"
        @tap="toggleExpand"
      >
        {{ isExpanded ? '收起' : '展开' }}
        <wd-icon
          :name="isExpanded ? 'chevron-up' : 'chevron-down'"
          size="14px"
          color="#666"
          class="ml-1"
        />
      </view>
    </view>

    <!-- 展开状态：显示流程步骤 -->
    <view class="relative py-2" v-if="isExpanded">
      <FlowStepItem
        v-for="(step, index) in branch"
        :key="step.id"
        :step="step"
        :is-last="index === branch.length - 1"
        :is-flow-revoked="isWorkflowRevoked"
      />
    </view>

    <!-- 折叠状态：显示摘要 -->
    <view
      v-else
      class="flex items-center py-2 px-3 rounded-md text-sm"
      :class="statusSummary.bgColor"
    >
      <wd-icon :name="statusSummary.icon" size="14px" :color="statusSummary.color" class="mr-2" />
      <view class="flex-1" :class="statusSummary.textColor">
        {{ statusSummary.message }}
      </view>
      <view class="ml-2 text-xs text-gray-400">点击"展开"查看详情</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.cursor-pointer {
  cursor: pointer;
}
</style>
