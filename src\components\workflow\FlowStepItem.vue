<script setup lang="ts">
import { computed } from 'vue'

export interface StepComment {
  user: string
  time: string
  content: string
  status: number
}

export interface FlowStep {
  id: number | string
  name: string
  handler: string
  time: string
  status: 'success' | 'rejected' | 'active' | 'waiting' | 'revoked' | 'returned'
  comment: string
  commentInfo: StepComment | null
  isRejected: boolean
  isRevoked?: boolean
}

export interface Props {
  step: FlowStep
  isLast: boolean
  isFlowRevoked: boolean
}

const props = defineProps<Props>()

/**
 * 获取状态对应的类名
 */
const statusClass = computed(() => {
  switch (props.step.status) {
    case 'success':
      return 'bg-green-500'
    case 'active':
      return 'bg-blue-500'
    case 'rejected':
      return 'bg-red-500'
    case 'returned':
      return 'bg-orange-500'
    case 'revoked':
      return 'bg-gray-500'
    default:
      return 'bg-gray-400'
  }
})

/**
 * 获取状态图标
 */
const statusIcon = computed(() => {
  switch (props.step.status) {
    case 'success':
      return { name: 'check', color: '#22c55e', bgColor: 'bg-green-100' }
    case 'rejected':
      return { name: 'close', color: '#ef4444', bgColor: 'bg-red-100' }
    case 'returned':
      return { name: 'backtop', color: '#f97316', bgColor: 'bg-orange-100' }
    case 'active':
      return { name: 'time', color: '#3b82f6', bgColor: 'bg-blue-100' }
    case 'revoked':
      return { name: 'close', color: '#9ca3af', bgColor: 'bg-gray-100' }
    default:
      return { name: '', color: '', bgColor: '' }
  }
})

/**
 * 评论样式类
 */
const commentClass = computed(() => {
  if (props.step.isRevoked) {
    return 'bg-gray-50 border-gray-400'
  }
  if (props.step.isRejected) {
    return 'bg-red-50 border-red-400'
  }
  return 'bg-gray-50 border-blue-400'
})

/**
 * 评论图标
 */
const commentIcon = computed(() => {
  if (props.step.isRevoked) {
    return { name: 'close', color: '#9ca3af' }
  }
  if (props.step.isRejected) {
    return { name: 'close-circle', color: '#ef4444' }
  }
  return { name: 'chat', color: '#666' }
})

/**
 * 评论用户名类名
 */
const commentUserClass = computed(() => {
  if (props.step.isRevoked) {
    return 'text-gray-400'
  }
  if (props.step.isRejected) {
    return 'text-red-500'
  }
  return 'text-gray-500'
})

/**
 * 获取步骤时间或状态文本
 */
const timeText = computed(() => {
  if (props.step.isRevoked && props.isFlowRevoked) {
    return '已撤销'
  }
  return props.step.time
})
</script>

<template>
  <view class="flex relative mb-4" :class="{ 'mb-0': isLast }">
    <!-- 状态点 -->
    <view class="w-4 h-4 rounded-full mr-4 flex-shrink-0 z-2" :class="statusClass"></view>

    <!-- 连接线 -->
    <view v-if="!isLast" class="absolute left-2 top-4 w-0.5 h-full bg-gray-200 z-1"></view>

    <!-- 内容区 -->
    <view class="flex-1">
      <view class="flex items-center justify-between">
        <view class="flex-1">
          <view class="text-sm font-medium text-gray-800 mb-1">{{ step.name }}</view>
          <view class="text-xs text-gray-500 mb-1">{{ step.handler }}</view>
          <view class="flex items-center">
            <view class="text-xs text-gray-400 mb-1">
              {{ timeText }}
            </view>
            <!-- 拒绝标记 -->
            <view
              v-if="step.status === 'rejected'"
              class="ml-2 px-1.5 py-0.5 rounded text-xs text-white bg-red-500"
            >
              已拒绝
            </view>
            <!-- 驳回标记 -->
            <view
              v-if="step.status === 'returned'"
              class="ml-2 px-1.5 py-0.5 rounded text-xs text-white bg-orange-500"
            >
              已驳回
            </view>
            <!-- 撤销标记 -->
            <view
              v-if="step.status === 'revoked'"
              class="ml-2 px-1.5 py-0.5 rounded text-xs text-white bg-gray-500"
            >
              已撤销
            </view>
          </view>
        </view>

        <!-- 状态图标 -->
        <view
          v-if="statusIcon.name"
          class="w-6 h-6 rounded-full flex items-center justify-center"
          :class="statusIcon.bgColor"
        >
          <wd-icon :name="statusIcon.name" size="14px" :color="statusIcon.color"></wd-icon>
        </view>
      </view>

      <!-- 审批意见 -->
      <view
        v-if="step.comment"
        class="mt-2 py-2 px-3 rounded-lg border-l-2 text-sm text-gray-700"
        :class="commentClass"
      >
        <view v-if="step.commentInfo" class="flex justify-between items-center mb-1">
          <view class="flex items-center">
            <wd-icon :name="commentIcon.name" size="14px" :color="commentIcon.color"></wd-icon>
            <text class="ml-1 text-xs" :class="commentUserClass">
              {{ step.commentInfo.user }}
            </text>
          </view>
          <text class="text-xs text-gray-400">{{ step.commentInfo.time }}</text>
        </view>
        <view class="text-sm leading-relaxed break-all">{{ step.comment }}</view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.z-1 {
  z-index: 1;
}

.z-2 {
  z-index: 2;
}
</style>
