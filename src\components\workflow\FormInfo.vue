<script setup lang="ts">
import { computed } from 'vue'
import FieldDisplay from './FieldDisplay.vue'
import type { WorkflowDetailResponse } from '@/types/workflow'

interface Props {
  workflowData: WorkflowDetailResponse
}

const props = defineProps<Props>()

/**
 * 需要显示的字段列表
 */
const visibleFields = computed(() => {
  return props.workflowData.fields.filter(
    (f) => f.show === '1' && f.name !== 'fjlb' && f.name !== 'dfsy' && f.name !== 'file',
  )
})

/**
 * 获取工作流状态类型
 */
const statusType = computed((): string => {
  switch (props.workflowData.form.status) {
    case 1:
      return 'success'
    case 2:
      return 'error'
    case 3:
      return 'warning'
    case 4:
      return 'info'
    default:
      return 'warning'
  }
})

/**
 * 获取状态标签文本
 */
const statusText = computed((): string => {
  switch (props.workflowData.form.status) {
    case 1:
      return '已审批'
    case 0:
      return '待审批'
    case 2:
      return '已拒绝'
    case 3:
      return '已驳回'
    case 4:
      return '已撤销'
    default:
      return '已撤销'
  }
})

/**
 * 获取状态标签样式
 */
const statusClass = computed((): string => {
  switch (statusType.value) {
    case 'success':
      return 'bg-green-500'
    case 'warning':
      return 'bg-orange-500'
    case 'error':
      return 'bg-red-500'
    case 'info':
      return 'bg-gray-500'
    default:
      return 'bg-gray-500'
  }
})
</script>

<template>
  <view class="detail-card bg-white rounded-lg shadow mb-4 p-4">
    <view class="flex justify-between items-start mb-2">
      <view class="text-lg font-bold text-gray-800 flex-1">{{ workflowData.form.title }}</view>
      <view :class="['status-tag px-2 py-2 text-sm text-white rounded mt-4 ml-2', statusClass]">
        {{ statusText }}
      </view>
    </view>

    <view class="text-sm text-gray-500 mb-4">申请编号：{{ workflowData.node.id || '-' }}</view>

    <view class="border-t border-gray-100 pt-3">
      <!-- 显示表单字段 -->
      <FieldDisplay
        v-for="field in visibleFields"
        :key="field.name"
        :field="field"
        :form-data="workflowData.formData"
        :cols="workflowData.cols"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.detail-card {
  margin-bottom: 32rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.status-tag {
  line-height: 1.2;
}
</style>
