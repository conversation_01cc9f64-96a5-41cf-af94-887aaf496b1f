/**
 * 路由钩子 - 封装uni路由方法
 */

interface RouterOptions {
  url: string
  animationType?:
    | 'auto'
    | 'none'
    | 'slide-in-right'
    | 'slide-in-left'
    | 'slide-in-top'
    | 'slide-in-bottom'
    | 'fade-in'
    | 'zoom-out'
    | 'zoom-fade-out'
    | 'pop-in'
  animationDuration?: number
  events?: Record<string, any>
  success?: (res: any) => void
  fail?: (err: any) => void
  complete?: () => void
}

/**
 * 统一路由钩子
 * 封装uni-app路由方法
 */
export function useRouter() {
  /**
   * 保留当前页面，跳转到应用内的某个页面
   */
  const push = (options: RouterOptions | string) => {
    if (typeof options === 'string') {
      uni.navigateTo({
        url: options,
      })
      return
    }
    uni.navigateTo(options)
  }

  /**
   * 关闭当前页面，返回上一页面或多级页面
   */
  const back = (delta = 1) => {
    uni.navigateBack({
      delta,
    })
  }

  /**
   * 关闭所有页面，打开到应用内的某个页面
   */
  const replace = (options: RouterOptions | string) => {
    if (typeof options === 'string') {
      uni.reLaunch({
        url: options,
      })
      return
    }
    uni.reLaunch(options)
  }

  /**
   * 跳转到 tabBar 页面，并关闭其他所有非 tabBar 页面
   */
  const switchTab = (options: RouterOptions | string) => {
    if (typeof options === 'string') {
      uni.switchTab({
        url: options,
      })
      return
    }
    uni.switchTab(options)
  }

  /**
   * 关闭当前页面，跳转到应用内的某个页面
   */
  const redirectTo = (options: RouterOptions | string) => {
    if (typeof options === 'string') {
      uni.redirectTo({
        url: options,
      })
      return
    }
    uni.redirectTo(options)
  }

  return {
    push,
    back,
    replace,
    switchTab,
    redirectTo,
  }
}
