// TODO: 别忘加更改环境变量的 VITE_UPLOAD_BASEURL 地址。
import { getEnvBaseUploadUrl } from '@/utils'
import { useUserStore } from '@/store/user'
import type { Ref } from 'vue'
import { ref } from 'vue'

const VITE_UPLOAD_BASEURL = `${getEnvBaseUploadUrl()}`

/**
 * useUpload 是一个定制化的请求钩子，用于处理上传图片。
 * @param formData 额外传递给后台的数据，如{name: '菲鸽'}。
 * @returns 返回一个对象{loading, error, data, run}，包含请求的加载状态、错误信息、响应数据和手动触发请求的函数。
 */
export default function useUpload<T = string>(formData: Record<string, any> = {}) {
  const loading = ref(false)
  const error = ref(false)
  const data = ref<T>()
  const run = () => {
    // #ifdef MP-WEIXIN
    // 微信小程序从基础库 2.21.0 开始， wx.chooseImage 停止维护，请使用 uni.chooseMedia 代替。
    // 微信小程序在2023年10月17日之后，使用本API需要配置隐私协议
    uni.chooseMedia({
      count: 1,
      mediaType: ['image'],
      success: (res) => {
        loading.value = true
        const tempFilePath = res.tempFiles[0].tempFilePath
        uploadFile<T>({ tempFilePath, formData, data, error, loading })
      },
      fail: (err) => {
        console.error('uni.chooseMedia err->', err)
        error.value = true
      },
    })
    // #endif
    // #ifndef MP-WEIXIN
    uni.chooseImage({
      count: 1,
      success: (res) => {
        loading.value = true
        const tempFilePath = res.tempFilePaths[0]
        uploadFile<T>({ tempFilePath, formData, data, error, loading })
      },
      fail: (err) => {
        console.error('uni.chooseImage err->', err)
        error.value = true
      },
    })
    // #endif
  }

  return { loading, error, data, run }
}

/**
 * useUploadAnyFile 是一个定制化的请求钩子，用于处理上传所有类型的文件。
 * @param options 上传选项
 * @param options.extension 文件扩展名数组，如['pdf', 'doc', 'docx']，默认允许所有类型
 * @param options.count 最多可以选择的文件数量，默认为1
 * @param options.formData 额外传递给后台的数据，如{name: '文档'}
 * @returns 返回一个对象{loading, error, data, run}，包含请求的加载状态、错误信息、响应数据和手动触发请求的函数。
 */
export function useUploadAnyFile<T = string>(
  options: {
    extension?: string[]
    count?: number
    formData?: Record<string, any>
  } = {},
) {
  const { extension = [], count = 1, formData = {} } = options
  const loading = ref(false)
  const error = ref(false)
  const data = ref<T>()

  const run = () => {
    // #ifdef H5 || APP-PLUS
    uni.chooseFile({
      count,
      type: 'all',
      extension,
      success: (res) => {
        loading.value = true
        const tempFilePath = res.tempFiles[0].path
        uploadFile<T>({ tempFilePath, formData, data, error, loading })
      },
      fail: (err) => {
        console.error('uni.chooseFile err->', err)
        error.value = true
      },
    })
    // #endif

    // #ifdef MP
    // 在小程序中，需要使用特定的API选择不同类型的文件
    uni.showActionSheet({
      itemList: ['图片', '视频', '文件'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0: // 图片
            // #ifdef MP-WEIXIN
            uni.chooseMedia({
              count,
              mediaType: ['image'],
              success: (res) => {
                loading.value = true
                const tempFilePath = res.tempFiles[0].tempFilePath
                uploadFile<T>({ tempFilePath, formData, data, error, loading })
              },
              fail: (err) => {
                console.error('uni.chooseMedia err->', err)
                error.value = true
              },
            })
            // #endif
            // #ifndef MP-WEIXIN
            uni.chooseImage({
              count,
              success: (res) => {
                loading.value = true
                const tempFilePath = res.tempFilePaths[0]
                uploadFile<T>({ tempFilePath, formData, data, error, loading })
              },
              fail: (err) => {
                console.error('uni.chooseImage err->', err)
                error.value = true
              },
            })
            // #endif
            break
          case 1: // 视频
            uni.chooseVideo({
              count: 1,
              success: (res) => {
                loading.value = true
                const tempFilePath = res.tempFilePath
                uploadFile<T>({ tempFilePath, formData, data, error, loading })
              },
              fail: (err) => {
                console.error('uni.chooseVideo err->', err)
                error.value = true
              },
            })
            break
          case 2: // 文件
            // #ifdef MP-WEIXIN
            wx.chooseMessageFile({
              count,
              type: 'file',
              extension,
              success: (res) => {
                loading.value = true
                const tempFilePath = res.tempFiles[0].path
                uploadFile<T>({ tempFilePath, formData, data, error, loading })
              },
              fail: (err) => {
                console.error('wx.chooseMessageFile err->', err)
                error.value = true
              },
            })
            // #endif
            break
        }
      },
      fail: (err) => {
        console.error('uni.showActionSheet err->', err)
        error.value = true
      },
    })
    // #endif
  }

  return { loading, error, data, run }
}

/**
 * useUploadFile 是一个定制化的请求钩子，用于直接上传指定路径的文件。
 * @param filePath 文件的本地路径
 * @param options 上传选项
 * @param options.formData 额外传递给后台的数据，如{name: '文档'}
 * @param options.fileName 上传文件的字段名，默认为'file'
 * @returns 返回一个对象{loading, error, data, upload}，包含请求的加载状态、错误信息、响应数据和上传函数。
 */
export function useUploadFile<T = string>(
  filePath: string,
  options: {
    formData?: Record<string, any>
    fileName?: string
  } = {},
) {
  const { formData = {}, fileName = 'file' } = options
  const loading = ref(false)
  const error = ref(false)
  const data = ref<T>()

  const upload = () => {
    loading.value = true
    uploadFile<T>({
      tempFilePath: filePath,
      formData,
      data,
      error,
      loading,
      fileName,
    })
  }

  return { loading, error, data, upload }
}

/**
 * 上传指定本地文件到服务器
 * @param options 上传选项
 * @param options.tempFilePath 本地文件路径
 * @param options.formData 额外传递给后台的数据
 * @param options.data 响应数据引用
 * @param options.error 错误状态引用
 * @param options.loading 加载状态引用
 * @param options.fileName 上传文件的字段名，默认为'file'
 */
function uploadFile<T>({
  tempFilePath,
  formData,
  data,
  error,
  loading,
  fileName = 'file',
}: {
  tempFilePath: string
  formData: Record<string, any>
  data: Ref<T | undefined>
  error: Ref<boolean>
  loading: Ref<boolean>
  fileName?: string
}) {
  // 获取用户store以获取token
  const userStore = useUserStore()

  // 构建与request.ts相同的请求头
  const headers: Record<string, string> = {
    server: '1',
  }

  // 如果有token，添加到请求头
  if (userStore.tokenInfo.token) {
    headers['ba-token'] = userStore.tokenInfo.token
  }

  // 使用uni.uploadFile上传文件，但使用与request.ts相同的认证头信息
  uni.uploadFile({
    url: VITE_UPLOAD_BASEURL,
    filePath: tempFilePath,
    name: fileName,
    formData,
    header: headers,
    success: (uploadFileRes) => {
      try {
        // 尝试解析JSON
        const responseData = JSON.parse(uploadFileRes.data)
        data.value = responseData as T
      } catch (e) {
        // 如果不是JSON，直接使用字符串
        data.value = uploadFileRes.data as T
      }
    },
    fail: (err) => {
      console.error('uni.uploadFile err->', err)
      error.value = true
    },
    complete: () => {
      loading.value = false
    },
  })
}
