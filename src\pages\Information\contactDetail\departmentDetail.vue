<route lang="json5">
{
  style: {
    navigationBarTitleText: '部门详情',
  },
}
</route>
<template>
  <view class="container">
    <!-- 页面标题 -->

    <!-- 部门详情 -->
    <view class="card">
      <!-- 基本信息 -->
      <view class="header">
        <view :class="['avatar', 'dept']">
          {{ departmentName ? departmentName.substring(0, 1) : '' }}
        </view>
        <view class="info">
          <view class="name">{{ departmentName }}</view>
          <view class="position" v-if="departmentInfo.description">
            {{ departmentInfo.description }}
          </view>
        </view>
      </view>

      <!-- 联系方式 -->
      <view
        class="section"
        v-if="departmentInfo.phone || departmentInfo.email || departmentInfo.address"
      >
        <view class="section-title">联系方式</view>
        <view class="info-item contact-item" v-if="departmentInfo.phone">
          <view class="info-label">电话</view>
          <view class="info-value">{{ departmentInfo.phone }}</view>
          <view class="contact-action" @click="handleCall(departmentInfo.phone)">
            <wd-icon name="phone" color="#1890ff" size="18" />
          </view>
        </view>
        <view class="info-item contact-item" v-if="departmentInfo.email">
          <view class="info-label">电子邮箱</view>
          <view class="info-value">{{ departmentInfo.email }}</view>
          <view class="contact-action" @click="handleEmail(departmentInfo.email)">
            <wd-icon name="mail" color="#1890ff" size="18" />
          </view>
        </view>
        <view class="info-item contact-item" v-if="departmentInfo.address">
          <view class="info-label">办公地点</view>
          <view class="info-value">{{ departmentInfo.address }}</view>
          <view class="contact-action">
            <wd-icon name="location" color="#1890ff" size="18" />
          </view>
        </view>
      </view>

      <!-- 部门成员 -->
      <view class="section" v-if="departmentMembers.length > 0">
        <view class="section-title">部门成员</view>
        <wd-cell-group border>
          <wd-cell
            v-for="member in departmentMembers"
            :key="member.id"
            :title="member.name"
            :label="member.positionName || ''"
            is-link
            @click="goToMemberDetail(member)"
          >
            <template #icon>
              <view class="cell-avatar teacher">{{ member.name.substring(0, 1) }}</view>
            </template>
            <template #right>
              <view class="contact-actions">
                <view class="contact-action" @click.stop="handleCall(member.mobile)">
                  <wd-icon name="phone" color="#1890ff" size="18" />
                </view>
                <view class="contact-action" @click.stop="handleEmail(member.email)">
                  <wd-icon name="mail" color="#1890ff" size="18" />
                </view>
              </view>
            </template>
          </wd-cell>
        </wd-cell-group>
      </view>

      <!-- 操作按钮 -->
      <view class="actions">
        <wd-button
          type="info"
          plain
          custom-class="action-btn"
          @click="handleSendEmail"
          :disabled="!departmentInfo.email"
          v-if="false"
        >
          发送邮件
        </wd-button>
        <wd-button type="primary" custom-class="action-btn" @click="handleAddFrequent" v-if="false">
          添加常用联系人
        </wd-button>
        <wd-button type="info" custom-class="action-btn" @click="handleBack">关闭</wd-button>
      </view>
    </view>

    <!-- 提示组件 -->
    <wd-toast />
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useToast } from 'wot-design-uni'
import type { TeacherContact } from '@/types/teacher'

// 部门信息类型
interface DepartmentInfo {
  id: string | number
  name: string
  description?: string
  phone?: string
  email?: string
  address?: string
  members?: TeacherContact[]
}

// 页面参数
const departmentParam = ref('')
const departmentName = ref('')

// 部门信息
const departmentInfo = ref<DepartmentInfo>({
  id: '',
  name: '',
})

// 部门成员数据
const departmentMembers = ref<TeacherContact[]>([])

// 处理操作
const handleCall = (phone?: string) => {
  if (!phone) {
    const { show } = useToast()
    show('电话号码不存在')
    return
  }

  uni.makePhoneCall({
    phoneNumber: phone,
    fail: () => {
      const { show } = useToast()
      show('拨打电话失败')
    },
  })
}

const handleEmail = (email?: string) => {
  if (!email) {
    const { show } = useToast()
    show('邮箱地址不存在')
    return
  }

  // 小程序不支持直接发送邮件，这里简单复制邮箱地址
  uni.setClipboardData({
    data: email,
    success: () => {
      const { show } = useToast()
      show('已复制邮箱地址')
    },
  })
}

const handleSendEmail = () => {
  if (!departmentInfo.value?.email) {
    const { show } = useToast()
    show('邮箱地址不存在')
    return
  }

  handleEmail(departmentInfo.value.email)
}

const handleAddFrequent = () => {
  // 添加常用联系人逻辑
  const { show } = useToast()
  show('已添加到常用联系人')
}

const goToMemberDetail = (member: TeacherContact) => {
  uni.navigateTo({
    url: `/pages/Information/contactDetail/teacherDetail?teacher=${encodeURIComponent(JSON.stringify(member))}`,
  })
}

const handleBack = () => {
  uni.navigateBack()
}

onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  // @ts-expect-error 获取页面参数
  const options = currentPage?.options || {}

  // 获取传递的部门信息
  if (options.department) {
    try {
      // 解析传递过来的完整部门对象
      const deptData = JSON.parse(decodeURIComponent(options.department))
      departmentInfo.value = {
        id: deptData.id,
        name: deptData.name,
        description: deptData.description || '',
        phone: deptData.phone || '',
        email: deptData.email || '',
        address: deptData.address || '',
      }
      departmentName.value = deptData.name

      // 处理部门成员数据
      if (deptData.members && Array.isArray(deptData.members)) {
        departmentMembers.value = deptData.members
      }
    } catch (error) {
      console.error('解析部门信息失败', error)
      // 如果解析失败，尝试使用id和name
      departmentInfo.value = {
        id: options.id || '',
        name: decodeURIComponent(options.name || ''),
      }
      departmentName.value = decodeURIComponent(options.name || '')
    }
  } else if (options.id) {
    // 兼容旧版本跳转方式，使用id和name
    departmentInfo.value = {
      id: options.id,
      name: decodeURIComponent(options.name || ''),
    }
    departmentName.value = decodeURIComponent(options.name || '')
  }
})
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;

  padding-top: 10rpx;
  background-color: #f7f8fa;
}

.card {
  padding: 20px 16px;
  margin: 12px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  margin-right: 16px;
  font-size: 24px;
  border-radius: 50%;
}

.avatar.dept {
  color: #16c2c2;
  background-color: rgba(22, 194, 194, 0.1);
}

.info {
  flex: 1;
}

.name {
  margin-bottom: 4px;
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.position {
  font-size: 14px;
  color: #666;
}

.section {
  margin-bottom: 24px;
}

.section-title {
  margin-bottom: 12px;
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
}

.info-label {
  width: 80px;
  font-size: 14px;
  color: #666;
}

.info-value {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.contact-item {
  align-items: center;
}

.contact-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.contact-actions {
  display: flex;
}

.cell-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin-right: 12px;
  font-size: 16px;
  border-radius: 50%;
}

.cell-avatar.teacher {
  color: #52c41a;
  background-color: rgba(82, 196, 26, 0.1);
}

.actions {
  display: flex;
  gap: 16px;
}

.action-btn {
  flex: 1;
}
</style>
