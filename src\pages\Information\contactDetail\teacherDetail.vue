<route lang="json5">
{
  style: {
    navigationBarTitleText: '教师详情',
  },
}
</route>
<template>
  <view class="container">
    <!-- 联系人详情 -->
    <view class="card" v-if="teacherInfo">
      <!-- 基本信息 -->
      <view class="header">
        <view class="avatar teacher">
          {{ teacherInfo.name ? teacherInfo.name.substring(0, 1) : '' }}
        </view>
        <view class="info">
          <view class="name">{{ teacherInfo.name }}</view>
          <view class="position">
            {{ teacherInfo.deptName }} - {{ teacherInfo.positionName || '教师' }}
          </view>
        </view>
      </view>

      <!-- 基本信息 -->
      <view class="section">
        <view class="section-title">基本信息</view>
        <view class="info-item">
          <view class="info-label">职称</view>
          <view class="info-value">{{ teacherInfo.positionName || '未设置' }}</view>
        </view>
        <view class="info-item">
          <view class="info-label">工号</view>
          <view class="info-value">{{ teacherInfo.teacherCode || '未设置' }}</view>
        </view>
        <view class="info-item">
          <view class="info-label">性别</view>
          <view class="info-value">{{ genderText }}</view>
        </view>
        <view class="info-item">
          <view class="info-label">在职状态</view>
          <view class="info-value">{{ teacherInfo.establishmentStatusName || '未设置' }}</view>
        </view>
        <view class="info-item">
          <view class="info-label">行政职务</view>
          <view class="info-value">{{ teacherInfo.positionName || '未设置' }}</view>
        </view>
      </view>

      <!-- 联系方式 -->
      <view class="section">
        <view class="section-title">联系方式</view>
        <view class="info-item contact-item">
          <view class="info-label">手机</view>
          <view class="info-value">{{ teacherInfo.mobile || '未设置' }}</view>
          <view
            class="contact-action"
            @click="handleCall(teacherInfo.mobile)"
            v-if="teacherInfo.mobile"
          >
            <wd-icon name="phone" color="#1890ff" size="18" />
          </view>
        </view>
        <view class="info-item contact-item">
          <view class="info-label">办公电话</view>
          <view class="info-value">{{ teacherInfo.officePhone || '未设置' }}</view>
          <view
            class="contact-action"
            @click="handleCall(teacherInfo.officePhone)"
            v-if="teacherInfo.officePhone"
          >
            <wd-icon name="phone" color="#1890ff" size="18" />
          </view>
        </view>
        <view class="info-item contact-item">
          <view class="info-label">电子邮箱</view>
          <view class="info-value">{{ teacherInfo.email || '未设置' }}</view>
          <view
            class="contact-action"
            @click="handleEmail(teacherInfo.email)"
            v-if="teacherInfo.email"
          >
            <wd-icon name="mail" color="#1890ff" size="18" />
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="actions">
        <wd-button
          type="info"
          plain
          custom-class="action-btn"
          @click="handleSendEmail"
          v-if="false"
        >
          发送邮件
        </wd-button>
        <wd-button type="primary" custom-class="action-btn" @click="handleAddFrequent" v-if="false">
          添加常用联系人
        </wd-button>
        <wd-button type="info" custom-class="action-btn" @click="handleBack">关闭</wd-button>
      </view>
    </view>

    <!-- 加载中 -->
    <view class="loading-container" v-if="loading">
      <wd-loading color="#1890ff" />
    </view>

    <!-- 加载失败 -->
    <wd-status-tip
      type="error"
      v-if="loadError"
      content="加载失败,请重试"
      @retry="loadTeacherInfo"
    />

    <!-- 提示组件 -->
    <wd-toast />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import type { TeacherContact } from '@/types/teacher'
import { useToast } from 'wot-design-uni'

// 获取页面参数
const query = ref<Record<string, string>>({})
const loading = ref(false)
const loadError = ref(false)
const teacherInfo = ref<TeacherContact | null>(null)

// 性别文本
const genderText = computed(() => {
  if (!teacherInfo.value) return ''
  const gender = String(teacherInfo.value.gender)

  // 处理不同类型的性别值
  if (gender === '1') {
    return '男'
  } else if (gender === '2') {
    return '女'
  } else {
    return '未知'
  }
})

// 加载教师信息
const loadTeacherInfo = () => {
  try {
    // 从页面参数中获取教师数据
    const teacherStr = decodeURIComponent(query.value.teacher || '')
    teacherInfo.value = JSON.parse(teacherStr)
  } catch (error) {
    console.error('解析教师数据失败', error)
    loadError.value = true
    const { show } = useToast()
    show('加载教师信息失败')
  }
}

// 处理操作
const handleCall = (phone?: string) => {
  if (!phone) {
    const { show } = useToast()
    show('电话号码不存在')
    return
  }

  uni.makePhoneCall({
    phoneNumber: phone,
    fail: () => {
      const { show } = useToast()
      show('拨打电话失败')
    },
  })
}

const handleEmail = (email?: string) => {
  if (!email) {
    const { show } = useToast()
    show('邮箱地址不存在')
    return
  }

  // 小程序不支持直接发送邮件，这里简单复制邮箱地址
  uni.setClipboardData({
    data: email,
    success: () => {
      const { show } = useToast()
      show('已复制邮箱地址')
    },
  })
}

const handleSendEmail = () => {
  if (!teacherInfo.value?.email) {
    const { show } = useToast()
    show('邮箱地址不存在')
    return
  }

  handleEmail(teacherInfo.value.email)
}

const handleAddFrequent = () => {
  // 添加常用联系人逻辑
  const { show } = useToast()
  show('已添加到常用联系人')
}

const handleBack = () => {
  uni.navigateBack()
}

onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  // @ts-expect-error 获取页面参数
  query.value = currentPage?.options || {}

  loadTeacherInfo()
})
</script>

<style lang="scss" scoped>
.container {
  min-height: 100vh;
  padding-top: 10rpx;
  background-color: #f7f8fa;
}

.card {
  padding: 20px 16px;
  margin: 12px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  margin-right: 16px;
  font-size: 24px;
  border-radius: 50%;
}

.avatar.teacher {
  color: #52c41a;
  background-color: rgba(82, 196, 26, 0.1);
}

.info {
  flex: 1;
}

.name {
  margin-bottom: 4px;
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.position {
  font-size: 14px;
  color: #666;
}

.section {
  margin-bottom: 24px;
}

.section-title {
  margin-bottom: 12px;
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.info-item {
  display: flex;
  margin-bottom: 12px;
}

.info-label {
  width: 80px;
  font-size: 14px;
  color: #666;
}

.info-value {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.contact-item {
  align-items: center;
}

.contact-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.actions {
  display: flex;
  gap: 16px;
}

.action-btn {
  flex: 1;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}
</style>
