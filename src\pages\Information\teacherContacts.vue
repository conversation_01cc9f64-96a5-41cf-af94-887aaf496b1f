<route lang="json5">
{
  style: {
    navigationBarTitleText: '教工通讯录',
  },
}
</route>
<template>
  <view class="container">
    <!-- 搜索栏 -->
    <view class="search-card">
      <view class="search-box">
        <wd-icon name="search" color="#999" size="18" />
        <input
          type="text"
          v-model="searchKeyword"
          :placeholder="activeTab === 'department' ? '搜索部门' : '搜索姓名'"
          class="search-input"
        />
      </view>
    </view>

    <!-- 常用联系人 -->
    <view class="card" v-if="false">
      <view class="card-title">常用联系人</view>
      <view class="frequent-list">
        <view
          v-for="(item, index) in frequentContacts"
          :key="index"
          class="frequent-item"
          @click="handleFrequentClick(item)"
        >
          <view :class="['avatar', item.type]">
            {{ item.name.substring(0, 1) }}
          </view>
          <view class="frequent-name">{{ item.name }}</view>
        </view>
        <view class="frequent-item" @click="handleAddFrequent">
          <view class="avatar add">
            <wd-icon name="plus" color="#999" size="20" />
          </view>
          <view class="frequent-name">添加</view>
        </view>
      </view>
    </view>

    <!-- 标签页切换 -->
    <wd-tabs v-model="activeTab" sticky>
      <wd-tab title="教师通讯录" name="teacher">
        <view class="teacher-list-container">
          <wd-index-bar sticky>
            <view v-for="item in groupedTeachers" :key="item.index">
              <wd-index-anchor :index="item.index" />
              <wd-cell-group border>
                <wd-cell
                  v-for="teacher in item.data"
                  :key="teacher.id"
                  :title="teacher.name"
                  :label="teacher.deptName + ' | ' + teacher.positionName"
                  is-link
                  @click="goToTeacherDetail(teacher)"
                >
                  <template #icon>
                    <view class="cell-avatar teacher">{{ teacher.name.substring(0, 1) }}</view>
                  </template>
                  <template #right>
                    <view class="contact-actions">
                      <view class="contact-action" @click.stop="handleCall(teacher.mobile)">
                        <wd-icon name="phone" color="#1890ff" size="18" />
                      </view>
                      <view class="contact-action" @click.stop="handleEmail(teacher.email)">
                        <wd-icon name="mail" color="#1890ff" size="18" />
                      </view>
                    </view>
                  </template>
                </wd-cell>
              </wd-cell-group>
            </view>
          </wd-index-bar>
        </view>
      </wd-tab>
      <wd-tab title="部门通讯录" name="department">
        <view class="card department-list">
          <wd-cell-group border>
            <wd-cell
              v-for="dept in departments"
              :key="dept.id"
              :title="dept.name"
              :label="dept.description"
              is-link
              :title-width="'auto'"
              @click="goToDepartmentDetail(dept)"
            >
              <template #icon>
                <view class="cell-avatar dept">{{ dept.name.substring(0, 1) }}</view>
              </template>
              <template #right>
                <view class="contact-actions">
                  <view class="contact-action" @click.stop="handleCall(dept.phone)">
                    <wd-icon name="phone" color="#1890ff" size="18" />
                  </view>
                  <view class="contact-action" @click.stop="handleEmail(dept.email)">
                    <wd-icon name="mail" color="#1890ff" size="18" />
                  </view>
                </view>
              </template>
            </wd-cell>
          </wd-cell-group>
        </view>
      </wd-tab>
    </wd-tabs>
    <!-- 加载中 -->
    <view class="loading-container" v-if="loading">
      <wd-loading color="#1890ff" />
    </view>

    <!-- 提示组件 -->
    <wd-toast />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useToast } from 'wot-design-uni'
import { getTeacherContacts } from '@/service/teacher'
import { getOrganizationList } from '@/service/organization'
import type { TeacherContact, TeacherContactQuery, TeacherContactResponse } from '@/types/teacher'
import type { Organization } from '@/types/organization'

// 搜索关键词
const searchKeyword = ref('')

// 当前激活的标签页
const activeTab = ref('teacher')

// 加载状态
const loading = ref(false)

// 分页参数
const page = ref(1)
const pageSize = ref(1000)

// 常用联系人
const frequentContacts = ref([
  { id: 1, name: '校长办公室', type: 'admin' },
  { id: 2, name: '教务处', type: 'dept' },
  { id: 3, name: '人事处', type: 'dept' },
  { id: 4, name: '王主任', type: 'teacher' },
  { id: 5, name: '李老师', type: 'teacher' },
  { id: 6, name: '张老师', type: 'teacher' },
  { id: 7, name: '后勤处', type: 'dept' },
])

// 部门列表
const departments = ref<
  {
    id: string
    name: string
    description: string
    phone: string
    email: string
    members: TeacherContact[]
  }[]
>([])

// 教师列表
const teachers = ref<TeacherContact[]>([])

// 组织机构原始数据
const organizationData = ref<Organization[]>([])

// 部门-教师映射关系
const departmentTeachersMap = ref<Map<string, TeacherContact[]>>(new Map())

// 同时加载教师列表和部门列表
const loadInitialData = async () => {
  loading.value = true
  try {
    // 同时发起两个请求
    const [orgResponse, teacherResponse] = await Promise.all([
      getOrganizationList(),
      getTeacherContacts({
        page: page.value,
        pageSize: pageSize.value,
        sortBy: 'name',
        sortOrder: 'asc',
      }),
    ])

    // 保存原始数据
    organizationData.value = orgResponse
    teachers.value = teacherResponse.items

    // 创建部门-教师映射
    createDepartmentTeacherMap(teacherResponse.items)

    // 处理部门数据
    processDepartmentData(orgResponse)
  } catch (error) {
    console.error('加载数据失败', error)
    const { show } = useToast()
    show('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 创建部门-教师映射关系
const createDepartmentTeacherMap = (teacherList: TeacherContact[]) => {
  // 创建新的映射
  const newMap = new Map<string, TeacherContact[]>()

  // 按部门名称分组教师
  teacherList.forEach((teacher) => {
    if (teacher.deptName) {
      if (!newMap.has(teacher.deptName)) {
        newMap.set(teacher.deptName, [])
      }
      newMap.get(teacher.deptName)?.push(teacher)
    }
  })

  departmentTeachersMap.value = newMap
}

// 加载部门列表
const processDepartmentData = (orgData: Organization[]) => {
  // 处理组织机构数据，筛选出jglb为3或4的机构及其子机构
  const deptList: {
    id: string
    name: string
    description: string
    phone: string
    email: string
    members: TeacherContact[]
  }[] = []

  // 递归处理组织机构及其子机构
  const processOrganization = (orgs: Organization[]) => {
    orgs.forEach((org) => {
      // 判断是否jglb为3或4的机构
      if (org.jglb === 3 || org.jglb === 4) {
        // 查找该部门的教师
        const deptMembers = departmentTeachersMap.value.get(org.name) || []

        deptList.push({
          id: org.id,
          name: org.name,
          description: org.instructions || org.remark || '', // 优先使用说明或备注，不使用机构类别名称
          phone: org.phone || '',
          email: '', // 原数据中没有email字段，暂时置空
          members: deptMembers,
        })
      }

      // 处理子机构
      if (org.children && org.children.length > 0) {
        processOrganization(org.children)
      }
    })
  }

  processOrganization(orgData)
  departments.value = deptList
}

const groupedTeachers = computed(() => {
  const groups: Record<string, TeacherContact[]> = {}
  teachers.value.forEach((teacher) => {
    // 使用pykssy作为索引，如果没有则使用首字母
    let index = teacher.pykssy ? teacher.pykssy[0] : teacher.name[0]

    // 处理特殊字符
    if (index) {
      // 去除空格
      index = index.trim()
      // 如果包含斜杠，取斜杠前的部分
      if (index.includes('/')) {
        const parts = index.split('/')
        // 如果斜杠在第一位，归类到#
        if (parts[0] === '') {
          index = '#'
        } else {
          index = parts[0]
        }
      }

      // 如果索引不是字母，则归类到#
      if (!/^[a-zA-Z]$/.test(index)) {
        index = '#'
      }
    } else {
      index = '#'
    }

    // 转换为大写
    index = index.toUpperCase()
    if (!groups[index]) {
      groups[index] = []
    }
    groups[index].push(teacher)
  })

  // 转换为 wd-index-bar 需要的格式
  return Object.entries(groups)
    .map(([index, data]) => ({
      index,
      data,
    }))
    .sort((a, b) => {
      // 特殊处理#号，让它排在最后
      if (a.index === '#') return 1
      if (b.index === '#') return -1
      return a.index.localeCompare(b.index)
    })
})

// 加载教师列表
const loadTeacherList = async () => {
  loading.value = true
  try {
    const params: TeacherContactQuery = {
      page: page.value,
      pageSize: pageSize.value,
      sortBy: 'name',
      sortOrder: 'asc',
      name: searchKeyword.value,
    }
    const res = await getTeacherContacts(params)
    teachers.value = res.items
  } catch (error) {
    console.error('加载教师列表失败', error)
    const { show } = useToast()
    show('加载教师列表失败')
  } finally {
    loading.value = false
  }
}

// 监听搜索关键词变化
watch(searchKeyword, () => {
  page.value = 1
  if (activeTab.value === 'teacher') {
    loadTeacherList()
  } else {
    // 简单过滤部门列表，不重新请求接口
    if (organizationData.value.length > 0) {
      filterDepartments()
    }
  }
})

// 监听标签页切换
watch(activeTab, (newVal) => {
  // 切换标签页时清空搜索关键词
  searchKeyword.value = ''

  if (newVal === 'department' && departments.value.length === 0) {
    // 初始加载应该已经获取了数据，不需要再重新加载
    if (organizationData.value.length === 0) {
      loadInitialData()
    }
  } else if (newVal === 'teacher' && teachers.value.length === 0) {
    // 初始加载应该已经获取了数据，不需要再重新加载
    if (teachers.value.length === 0) {
      loadInitialData()
    }
  }
})

// 根据搜索关键词过滤部门
const filterDepartments = () => {
  if (!searchKeyword.value) {
    processDepartmentData(organizationData.value) // 如果搜索关键词为空，重新处理全部数据
    return
  }

  const keyword = searchKeyword.value.toLowerCase()
  const deptList: {
    id: string
    name: string
    description: string
    phone: string
    email: string
    members: TeacherContact[]
  }[] = []

  // 递归处理组织机构及其子机构
  const processOrganization = (orgs: Organization[]) => {
    orgs.forEach((org) => {
      // 判断是否jglb为3或4的机构，且符合搜索条件
      if (
        (org.jglb === 3 || org.jglb === 4) &&
        (org.name.toLowerCase().includes(keyword) ||
          (org.instructions && org.instructions.toLowerCase().includes(keyword)) ||
          (org.remark && org.remark.toLowerCase().includes(keyword)))
      ) {
        // 查找该部门的教师
        const deptMembers = departmentTeachersMap.value.get(org.name) || []

        deptList.push({
          id: org.id,
          name: org.name,
          description: org.instructions || org.remark || '', // 优先使用说明或备注，不使用机构类别名称
          phone: org.phone || '',
          email: '', // 原数据中没有email字段，暂时置空
          members: deptMembers,
        })
      }

      // 处理子机构
      if (org.children && org.children.length > 0) {
        processOrganization(org.children)
      }
    })
  }

  processOrganization(organizationData.value)
  departments.value = deptList
}

// 处理操作
const handleCall = (phone?: string) => {
  if (!phone) {
    const { show } = useToast()
    show('电话号码不存在')
    return
  }

  uni.makePhoneCall({
    phoneNumber: phone,
    fail: () => {
      const { show } = useToast()
      show('拨打电话失败')
    },
  })
}

const handleEmail = (email?: string) => {
  if (!email) {
    const { show } = useToast()
    show('邮箱地址不存在')
    return
  }

  uni.setClipboardData({
    data: email,
    success: () => {
      const { show } = useToast()
      show('已复制邮箱地址')
    },
  })
}

const handleFrequentClick = (item: any) => {
  if (item.type === 'dept' || item.type === 'admin') {
    goToDepartmentDetail(item)
  } else {
    goToTeacherDetail(item)
  }
}

const handleAddFrequent = () => {
  const { show } = useToast()
  show('添加常用联系人功能开发中')
}

const goToDepartmentDetail = (dept: any) => {
  uni.navigateTo({
    url: `/pages/Information/contactDetail/departmentDetail?department=${encodeURIComponent(JSON.stringify(dept))}`,
  })
}

const goToTeacherDetail = (teacher: TeacherContact) => {
  uni.navigateTo({
    url: `/pages/Information/contactDetail/teacherDetail?teacher=${encodeURIComponent(JSON.stringify(teacher))}`,
  })
}

onMounted(() => {
  // 初始加载两种数据
  loadInitialData()
})
</script>

<style lang="scss" scoped>
.tabs-container {
  margin: 12px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.search-card {
  padding: 12px;
  background-color: #fff;
}

.search-box {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f7f8fa;
  border-radius: 4px;
}

.search-input {
  flex: 1;
  margin-left: 8px;
  font-size: 14px;
  background: transparent;
  border: none;
  outline: none;
}

.card {
  padding: 16px;
  margin: 12px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.card-title {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.frequent-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.frequent-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(25% - 12px);
}

.avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  margin-bottom: 8px;
  font-size: 20px;
  border-radius: 50%;
}

.avatar.admin {
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
}

.avatar.dept {
  color: #16c2c2;
  background-color: rgba(22, 194, 194, 0.1);
}

.avatar.teacher {
  color: #52c41a;
  background-color: rgba(82, 196, 26, 0.1);
}

.avatar.add {
  color: #999;
  background-color: #f5f5f5;
}

.frequent-name {
  font-size: 14px;
  color: #666;
  text-align: center;
}

.cell-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin-right: 12px;
  font-size: 16px;
  border-radius: 50%;
}

.cell-avatar.dept {
  color: #16c2c2;
  background-color: rgba(22, 194, 194, 0.1);
}

.cell-avatar.teacher {
  color: #52c41a;
  background-color: rgba(82, 196, 26, 0.1);
}

.contact-actions {
  display: flex;
}

.contact-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.teacher-list-container {
  width: 95vw;
  height: calc(100vh - 220rpx);
  margin: 24rpx;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.department-list {
  height: calc(100vh - 220rpx);
  overflow-y: auto;
}

// 确保单元格内的标题可以完整显示
:deep(.wd-cell__title) {
  flex: 1;
  width: auto;
  overflow: hidden;
}
:deep(.wd-cell__left) {
  flex: 5 !important;
}
:deep(.wd-cell__label) {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
