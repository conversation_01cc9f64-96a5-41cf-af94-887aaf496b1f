<route lang="json5">
{
  style: {
    navigationBarTitleText: '个人信息',
  },
}
</route>
<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { getTeacherInfo, saveTeacherInfo } from '@/service/teacher'
import { getCityDict } from '@/service/dict'
import type { TeacherInfo } from '@/types/teacher'
import type { DictItem } from '@/types/system'
import type { ProvinceItem } from '@/types/dict'
import { DictTypeEnum } from '@/types/system'
import { loadDictData, getDictOptions } from '@/utils/dict'
import useUpload from '@/hooks/useUpload'

const teacherInfo = ref<TeacherInfo>({} as TeacherInfo)
const idTypeDict = ref<DictItem[]>([])
const marriageDict = ref<DictItem[]>([])
const genderDict = ref<DictItem[]>([])
const politicalStatusDict = ref<DictItem[]>([])
const educationLevelDict = ref<DictItem[]>([])
const degreeDict = ref<DictItem[]>([])
const positionCategoryDict = ref<DictItem[]>([])
const professionalTitleLevelDict = ref<DictItem[]>([])
const professionalTitleDict = ref<DictItem[]>([])
const familyPlanningStatusDict = ref<DictItem[]>([])
const activeTab = ref('basic') // 当前激活的选项卡
const isEditing = ref(false) // 是否处于编辑状态
const isHelpInfoEditing = ref(false) // 是否处于帮助信息编辑状态
const editForm = ref<Partial<TeacherInfo>>({} as Partial<TeacherInfo>) // 编辑表单
const helpInfoForm = ref<Partial<TeacherInfo>>({} as Partial<TeacherInfo>) // 帮助信息编辑表单
const tempAvatarUrl = ref<string>('') // 临时头像URL

// 城市选择器相关
const originLocationColumns = ref<any[]>([])
const originLocationValue = ref<string[]>([])
const originLocationLabels = ref<string[]>([])
const cityData = ref<ProvinceItem[]>([])
// 临时记录选择路径
const tempSelectionPath = ref<{ value: string; index: number }[]>([])
// 保存原始籍贯地编码对应的标签
const originLocationNames = ref<string[]>([])

// 日期相关字段的时间戳
const birthDateTimestamp = ref<number | null>(null)
const employmentDateTimestamp = ref<number | null>(null)
const attendPartyDateTimestamp = ref<number | null>(null)

// 设置最小日期为1925年1月1日
const minDate = new Date('1925-01-01').getTime()

// 使用useUpload钩子处理头像上传
const {
  loading: uploadLoading,
  error: uploadError,
  data: uploadData,
  run: startUpload,
} = useUpload<{
  code: number
  msg: string
  time: number
  data: string
}>({
  type: 'avatar',
  teacherCode: teacherInfo.value?.teacherCode || '',
})

// 监听上传结果
watch(uploadData, (newValue) => {
  if (newValue) {
    try {
      const result = typeof newValue === 'string' ? JSON.parse(newValue) : newValue
      console.log(result.data.file.url)

      if (result.code === 1 && result.data) {
        tempAvatarUrl.value = result.data.file.url
        uni.showToast({
          title: '头像上传成功',
          icon: 'success',
        })
      } else {
        uni.showToast({
          title: result.msg || '上传失败',
          icon: 'error',
        })
      }
    } catch (error) {
      console.error('解析上传结果失败:', error)
      uni.showToast({
        title: '上传失败',
        icon: 'error',
      })
    }
  }
})

// 获取教师信息
const getInfo = async () => {
  try {
    teacherInfo.value = await getTeacherInfo()

    // 如果有籍贯地数据，尝试获取对应的名称
    if (
      Array.isArray(teacherInfo.value.originLocation) &&
      teacherInfo.value.originLocation.length > 0 &&
      cityData.value?.length > 0
    ) {
      matchOriginLocationNames()
    }
  } catch (error) {
    console.error('获取教师信息失败:', error)
  }
}

// 匹配籍贯地编码对应的名称
const matchOriginLocationNames = () => {
  originLocationNames.value = []

  if (
    !Array.isArray(teacherInfo.value.originLocation) ||
    teacherInfo.value.originLocation.length === 0 ||
    !cityData.value?.length
  ) {
    return
  }

  try {
    const codes = teacherInfo.value.originLocation

    // 尝试匹配省级
    if (codes[0]) {
      const province = cityData.value.find((p) => p.value === codes[0])
      if (province) {
        originLocationNames.value[0] = province.label

        // 尝试匹配市级
        if (codes[1] && province.children?.length) {
          const city = province.children.find((c) => c.value === codes[1])
          if (city) {
            originLocationNames.value[1] = city.label

            // 尝试匹配区县级
            if (codes[2] && city.children?.length) {
              const district = city.children.find((d) => d.value === codes[2])
              if (district) {
                originLocationNames.value[2] = district.label
              }
            }
          }
        }
      }
    }

    console.log('匹配籍贯地名称结果:', originLocationNames.value)
  } catch (error) {
    console.error('匹配籍贯地名称出错:', error)
  }
}

// 获取城市数据
const getCityData = async () => {
  try {
    cityData.value = await getCityDict()

    // 如果获取到数据，初始化第一列(省级)数据
    if (cityData.value && cityData.value.length > 0) {
      // 检查数据结构是否符合预期
      console.log(
        '城市数据结构检查:',
        '省级数量:',
        cityData.value.length,
        '第一个省级示例:',
        cityData.value[0]?.label,
        '市级示例:',
        cityData.value[0]?.children?.[0]?.label,
        '区县示例:',
        cityData.value[0]?.children?.[0]?.children?.[0]?.label,
      )

      // 只初始化第一列数据，后续列通过column-change回调加载
      originLocationColumns.value = [
        cityData.value.map((item) => ({
          value: item.value,
          label: item.label,
        })),
      ]

      // 如果有籍贯地数据，尝试获取对应的名称
      if (
        Array.isArray(teacherInfo.value.originLocation) &&
        teacherInfo.value.originLocation.length > 0
      ) {
        matchOriginLocationNames()
      }
    } else {
      console.error('城市数据为空')
      uni.showToast({
        title: '城市数据加载失败',
        icon: 'error',
      })
    }
  } catch (error) {
    console.error('获取城市数据失败:', error)
    // 错误处理，显示提示并尝试重新获取
    uni.showToast({
      title: '城市数据加载失败，请稍后重试',
      icon: 'error',
    })
  }
}

// 城市选择器列变化处理
const handleColumnChange = ({ selectedItem, index, resolve, finish }) => {
  console.log('列变化:', selectedItem, index, '当前选择值:', originLocationValue.value)

  try {
    // 记录当前选择
    tempSelectionPath.value[index] = {
      value: selectedItem.value,
      index,
    }

    // 如果是省级，加载市级数据
    if (index === 0) {
      // 查找所选省份
      const province = cityData.value.find((item) => item.value === selectedItem.value)
      console.log('选中省份:', province?.label, '是否有子数据:', province?.children?.length)

      if (province?.children && province.children.length > 0) {
        // 有市级数据，加载市级
        const cities = province.children.map((item) => ({
          value: item.value,
          label: item.label,
        }))
        console.log('加载市级数据, 数量:', cities.length)
        resolve(cities)
      } else {
        // 没有下级数据，结束选择
        console.log('省份没有市级数据，结束选择')
        finish()
      }
    }
    // 如果是市级，加载区县数据
    else if (index === 1) {
      try {
        // 获取当前选中的省份值
        const provinceValue = tempSelectionPath.value[0]?.value
        console.log('当前省份值(从临时路径):', provinceValue)

        // 找到省份对象
        const province = cityData.value.find((item) => item.value === provinceValue)
        console.log('找到省份:', province?.label)

        if (province?.children) {
          // 在该省份的城市列表中查找选中的城市
          const city = province.children.find((item) => item.value === selectedItem.value)
          console.log('找到城市:', city?.label, '是否有区县:', city?.children?.length)

          if (city?.children && city.children.length > 0) {
            // 有区县数据，加载区县
            const districts = city.children.map((item) => ({
              value: item.value,
              label: item.label,
            }))
            console.log('加载区县数据, 数量:', districts.length, '第一个区县:', districts[0]?.label)
            resolve(districts)
          } else {
            // 没有区县数据，结束选择
            console.log('城市没有区县数据，结束选择')
            finish()
          }
        } else {
          console.log('没有找到省份的城市数据，结束选择')
          finish()
        }
      } catch (err) {
        console.error('处理市级选择出错:', err)
        finish() // 出错时结束选择
      }
    } else {
      // 区县是最后一级，直接结束
      console.log('区县选择完成，结束')
      finish()
    }
  } catch (err) {
    console.error('列变化处理出错:', err)
    finish() // 出错时结束选择
  }
}

// 处理城市选择器确认
const handleOriginLocationConfirm = (event: { value: string[]; selectedItems: any[] }) => {
  console.log('城市选择确认:', event, '临时选择路径:', tempSelectionPath.value)

  // 保存选中的value值数组(编码)
  originLocationValue.value = event.value

  // 记录选中项的标签值（省市区名称）
  originLocationLabels.value = event.selectedItems.map((item) => item.label)
  console.log('选中的地址标签:', originLocationLabels.value)

  // 将选择的值转换为字符串数组用于保存
  if (Array.isArray(originLocationValue.value)) {
    editForm.value.originLocation = [...originLocationValue.value]
  } else {
    editForm.value.originLocation = []
  }

  // 添加originLocationName字段，用省市区名称，而不是编码
  // 使用"|"作为分隔符，以便与服务器格式保持一致
  ;(editForm.value as any).originLocationName = originLocationLabels.value.join('|')

  console.log('保存的籍贯地信息:', {
    originLocation: editForm.value.originLocation,
    originLocationName: (editForm.value as any).originLocationName,
  })

  // 确认后清空临时选择路径
  // tempSelectionPath.value = []
}

// 切换到编辑模式
const startEditing = () => {
  // 初始化基本信息表单
  editForm.value = {
    gender: teacherInfo.value.gender,
    IDNumber: teacherInfo.value.IDNumber,
    // 确保originLocation是字符串数组类型
    originLocation: Array.isArray(teacherInfo.value.originLocation)
      ? [...teacherInfo.value.originLocation]
      : [],
    homeAddress: teacherInfo.value.homeAddress,
    homePhone: teacherInfo.value.homePhone,
    politicalOutlookCode: teacherInfo.value.politicalOutlookCode,
  }

  // 初始化帮助信息表单
  helpInfoForm.value = {
    mobile: teacherInfo.value.mobile,
    officePhone: teacherInfo.value.officePhone,
    email: teacherInfo.value.email,
    educationalCode: teacherInfo.value.educationalCode,
    degreeCode: teacherInfo.value.degreeCode,
    marriageCode: teacherInfo.value.marriageCode,
    graduationSchool: teacherInfo.value.graduationSchool,
    graduationMajor: teacherInfo.value.graduationMajor,
    positionCategoryCode: teacherInfo.value.positionCategoryCode,
    jobTitleLevelCode: teacherInfo.value.jobTitleLevelCode,
    jobTitleCode: teacherInfo.value.jobTitleCode,
    positionName: teacherInfo.value.positionName,
    familyPlanningCode: teacherInfo.value.familyPlanningCode,
    remark: teacherInfo.value.remark,
  }

  // 初始化籍贯地选择器的值
  if (
    Array.isArray(teacherInfo.value.originLocation) &&
    teacherInfo.value.originLocation.length > 0
  ) {
    // 设置选择器的初始值为后台加载的数据
    originLocationValue.value = [...teacherInfo.value.originLocation]

    // 如果已匹配到名称，使用匹配的名称
    if (originLocationNames.value.length > 0) {
      originLocationLabels.value = [...originLocationNames.value]
    } else {
      // 否则尝试从originLocationName中获取
      if (typeof (teacherInfo.value as any).originLocationName === 'string') {
        originLocationLabels.value = (teacherInfo.value as any).originLocationName.split('|')
      } else {
        // 无法获取名称时，显示编码
        originLocationLabels.value = [...teacherInfo.value.originLocation]
      }
    }

    // 预加载所有层级的数据，以便ColPicker能够正确显示已选项
    if (cityData.value && cityData.value.length > 0) {
      // 初始化第一列（省级）数据
      originLocationColumns.value = [
        cityData.value.map((item) => ({
          value: item.value,
          label: item.label,
        })),
      ]

      // 尝试预加载第二列（市级）数据
      if (teacherInfo.value.originLocation[0]) {
        const province = cityData.value.find((p) => p.value === teacherInfo.value.originLocation[0])
        if (province?.children && province.children.length > 0) {
          originLocationColumns.value[1] = province.children.map((item) => ({
            value: item.value,
            label: item.label,
          }))

          // 尝试预加载第三列（区县级）数据
          if (teacherInfo.value.originLocation[1]) {
            const city = province.children.find(
              (c) => c.value === teacherInfo.value.originLocation[1],
            )
            if (city?.children && city.children.length > 0) {
              originLocationColumns.value[2] = city.children.map((item) => ({
                value: item.value,
                label: item.label,
              }))
            }
          }
        }
      }
    }

    console.log('编辑模式，设置籍贯地默认值:', {
      value: originLocationValue.value,
      labels: originLocationLabels.value,
      columns: originLocationColumns.value,
    })
  } else {
    originLocationLabels.value = []
    originLocationValue.value = []
  }

  // 清空临时选择路径
  tempSelectionPath.value = []

  tempAvatarUrl.value = teacherInfo.value.avatarUrl || ''

  // 将日期字符串转换为时间戳
  if (teacherInfo.value.birthDate) {
    birthDateTimestamp.value = new Date(teacherInfo.value.birthDate).getTime()
  }

  if (teacherInfo.value.employmentDate) {
    employmentDateTimestamp.value = new Date(teacherInfo.value.employmentDate).getTime()
  }

  if (teacherInfo.value.attendPartyDate) {
    attendPartyDateTimestamp.value = new Date(teacherInfo.value.attendPartyDate).getTime()
  }

  isEditing.value = true
  isHelpInfoEditing.value = true
}

// 取消编辑
const cancelEditing = () => {
  isEditing.value = false
  isHelpInfoEditing.value = false
  editForm.value = {} as Partial<TeacherInfo>
  helpInfoForm.value = {} as Partial<TeacherInfo>
  tempAvatarUrl.value = ''
  birthDateTimestamp.value = null
  employmentDateTimestamp.value = null
  attendPartyDateTimestamp.value = null
  originLocationValue.value = []
  originLocationLabels.value = []
  tempSelectionPath.value = []
}

// 保存编辑信息
const saveInfo = async () => {
  try {
    // 将时间戳转换为日期字符串
    let birthDate = ''
    if (birthDateTimestamp.value) {
      birthDate = formatDate(new Date(birthDateTimestamp.value))
    }

    let employmentDate = ''
    if (employmentDateTimestamp.value) {
      employmentDate = formatDate(new Date(employmentDateTimestamp.value))
    }

    let attendPartyDate = ''
    if (attendPartyDateTimestamp.value) {
      attendPartyDate = formatDate(new Date(attendPartyDateTimestamp.value))
    }

    // 构建base_info请求对象
    const baseInfoData: any = {
      type: 'base_info',
      rybh: teacherInfo.value.rybh || teacherInfo.value.teacherCode,
      // 保留原始不可编辑的字段
      name: teacherInfo.value.name,
      deptName: teacherInfo.value.deptName,
      establishmentTypeName: teacherInfo.value.establishmentTypeName,
      categoryName: teacherInfo.value.categoryName,
      establishmentStatusName: teacherInfo.value.establishmentStatusName,
      isDualRole: teacherInfo.value.isDualRole || '',
      isDualQualification: teacherInfo.value.isDualQualification || '',
      inStaff: teacherInfo.value.inStaff || '',

      // 可编辑的字段
      avatarUrl: tempAvatarUrl.value || teacherInfo.value.avatarUrl,
      gender: editForm.value.gender,
      birthDate,
      IDNumber: editForm.value.IDNumber,
      originLocation: editForm.value.originLocation,
      // 确保originLocationName是地区名称的拼接，使用"|"而不是"/"分隔符
      originLocationName: originLocationLabels.value.join('|'),
      homeAddress: editForm.value.homeAddress,
      homePhone: editForm.value.homePhone,
      employmentDate,
      attendPartyDate,
      politicalOutlookCode: editForm.value.politicalOutlookCode,
      politicalOutlookName: teacherInfo.value.politicalOutlookName || '',
      IDTypeCode: teacherInfo.value.IDTypeCode || '', // 保留原值但不给默认的'0'
    }

    // 构建help_info请求对象
    const helpInfoData: any = {
      type: 'help_info',
      rybh: teacherInfo.value.rybh || teacherInfo.value.teacherCode,

      // 帮助信息表单字段
      mobile: helpInfoForm.value.mobile,
      officePhone: helpInfoForm.value.officePhone,
      email: helpInfoForm.value.email,
      educationalCode: helpInfoForm.value.educationalCode,
      educationalName: getEducationLevelName(helpInfoForm.value.educationalCode || ''),
      degreeCode: helpInfoForm.value.degreeCode,
      degreeName: getDegreeName(helpInfoForm.value.degreeCode || ''),
      marriageCode: helpInfoForm.value.marriageCode,
      marriageName: getMarriageName(helpInfoForm.value.marriageCode || ''),
      graduationSchool: helpInfoForm.value.graduationSchool,
      graduationMajor: helpInfoForm.value.graduationMajor,
      positionCategoryCode: helpInfoForm.value.positionCategoryCode,
      positionCategoryName: getPositionCategoryName(helpInfoForm.value.positionCategoryCode || ''),
      jobTitleLevelCode: helpInfoForm.value.jobTitleLevelCode,
      jobTitleLevelName: getProfessionalTitleLevelName(helpInfoForm.value.jobTitleLevelCode || ''),
      jobTitleCode: helpInfoForm.value.jobTitleCode,
      jobTitleName: getProfessionalTitleName(helpInfoForm.value.jobTitleCode || ''),
      positionName: helpInfoForm.value.positionName,
      familyPlanningCode: helpInfoForm.value.familyPlanningCode,
      familyPlanningName: getFamilyPlanningStatusName(helpInfoForm.value.familyPlanningCode || ''),
      remark: helpInfoForm.value.remark,

      // 添加家庭地址和家庭电话（如果从基础信息中修改了）
      homeAddress: editForm.value.homeAddress,
      homePhone: editForm.value.homePhone,
    }

    // 先保存基本信息
    await saveTeacherInfo(baseInfoData as any)

    // 再保存帮助信息
    await saveTeacherInfo(helpInfoData as any)

    uni.showToast({
      title: '保存成功',
      icon: 'success',
    })

    isEditing.value = false
    isHelpInfoEditing.value = false

    // 重置时间戳和选择器值
    birthDateTimestamp.value = null
    employmentDateTimestamp.value = null
    attendPartyDateTimestamp.value = null
    originLocationValue.value = []
    originLocationLabels.value = []
    tempSelectionPath.value = []

    await getInfo() // 重新获取最新信息
  } catch (error) {
    console.error('保存教师信息失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'error',
    })
  }
}

// 格式化日期为YYYY-MM-DD格式
const formatDate = (date: Date): string => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 上传头像
const uploadAvatar = () => {
  if (!isEditing.value) return
  startUpload()
}

// 加载字典数据
const loadDicts = async () => {
  try {
    const dictData = await loadDictData([
      DictTypeEnum.ID_TYPE,
      DictTypeEnum.MARRIAGE_STATUS_ALT,
      DictTypeEnum.GENDER,
      DictTypeEnum.POLITICAL_STATUS,
      DictTypeEnum.EDUCATION_LEVEL,
      DictTypeEnum.DEGREE,
      DictTypeEnum.POSITION_CATEGORY,
      DictTypeEnum.PROFESSIONAL_TITLE_LEVEL,
      DictTypeEnum.PROFESSIONAL_TITLE,
      DictTypeEnum.FAMILY_PLANNING_STATUS,
    ])

    idTypeDict.value = getDictOptions(dictData[DictTypeEnum.ID_TYPE])
    marriageDict.value = getDictOptions(dictData[DictTypeEnum.MARRIAGE_STATUS_ALT])
    genderDict.value = getDictOptions(dictData[DictTypeEnum.GENDER])
    politicalStatusDict.value = getDictOptions(dictData[DictTypeEnum.POLITICAL_STATUS])
    educationLevelDict.value = getDictOptions(dictData[DictTypeEnum.EDUCATION_LEVEL])
    degreeDict.value = getDictOptions(dictData[DictTypeEnum.DEGREE])
    positionCategoryDict.value = getDictOptions(dictData[DictTypeEnum.POSITION_CATEGORY])
    professionalTitleLevelDict.value = getDictOptions(
      dictData[DictTypeEnum.PROFESSIONAL_TITLE_LEVEL],
    )
    professionalTitleDict.value = getDictOptions(dictData[DictTypeEnum.PROFESSIONAL_TITLE])
    familyPlanningStatusDict.value = getDictOptions(dictData[DictTypeEnum.FAMILY_PLANNING_STATUS])
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}

// 获取证件类型名称
const getIdTypeName = (value: string) => {
  return idTypeDict.value.find((item) => item.value === value)?.label || value
}

// 获取婚姻状况名称
const getMarriageName = (value: string) => {
  return marriageDict.value.find((item) => item.value === value)?.label || value
}

// 获取性别名称
const getGenderName = (value: number | string) => {
  const stringValue = String(value)
  return (
    genderDict.value.find((item) => item.value === stringValue)?.label ||
    (value === 1 ? '男' : '女')
  )
}

// 获取政治面貌名称
const getPoliticalStatusName = (value: string) => {
  return (
    politicalStatusDict.value.find((item) => item.value === value)?.label ||
    teacherInfo.value.politicalOutlookName ||
    '--'
  )
}

// 获取学历名称
const getEducationLevelName = (value: string) => {
  return (
    educationLevelDict.value.find((item) => item.value === value)?.label ||
    teacherInfo.value.educationalName ||
    teacherInfo.value.zgxlmc ||
    '--'
  )
}

// 获取学位名称
const getDegreeName = (value: string) => {
  return (
    degreeDict.value.find((item) => item.value === value)?.label ||
    teacherInfo.value.degreeName ||
    teacherInfo.value.zgxwmc ||
    '--'
  )
}

// 获取职务类别名称
const getPositionCategoryName = (value: string) => {
  return positionCategoryDict.value.find((item) => item.value === value)?.label || '--'
}

// 获取职称级别名称
const getProfessionalTitleLevelName = (value: string) => {
  return (
    professionalTitleLevelDict.value.find((item) => item.value === value)?.label ||
    teacherInfo.value.jobTitleLevelName ||
    teacherInfo.value.positionCategoryName ||
    '--'
  )
}

// 获取专业技术职务名称
const getProfessionalTitleName = (value: string) => {
  return (
    professionalTitleDict.value.find((item) => item.value === value)?.label ||
    teacherInfo.value.jobTitleName ||
    teacherInfo.value.jobTitleCode || // 使用jobTitleCode作为备选而不是postCategoryName
    '--'
  )
}

// 获取计划生育状况名称
const getFamilyPlanningStatusName = (value: string) => {
  return familyPlanningStatusDict.value.find((item) => item.value === value)?.label || '--'
}

// 处理证件类型确认
const handleIDTypeConfirm = (event: { value: string; selectedItems: any }) => {
  editForm.value.IDTypeCode = event.value
}

// 处理政治面貌确认
const handlePoliticalStatusConfirm = (event: { value: string; selectedItems: any }) => {
  editForm.value.politicalOutlookCode = event.value
}

// 处理婚姻状况确认
const handleMarriageConfirm = (event: { value: string; selectedItems: any }) => {
  helpInfoForm.value.marriageCode = event.value
}

// 处理学历确认
const handleEducationLevelConfirm = (event: { value: string; selectedItems: any }) => {
  helpInfoForm.value.educationalCode = event.value
}

// 处理学位确认
const handleDegreeConfirm = (event: { value: string; selectedItems: any }) => {
  helpInfoForm.value.degreeCode = event.value
}

// 处理职务类型确认
const handlePositionCategoryConfirm = (event: { value: string; selectedItems: any }) => {
  helpInfoForm.value.positionCategoryCode = event.value
}

// 处理职称级别确认
const handleProfessionalTitleLevelConfirm = (event: { value: string; selectedItems: any }) => {
  helpInfoForm.value.jobTitleLevelCode = event.value
}

// 处理职称确认
const handleProfessionalTitleConfirm = (event: { value: string; selectedItems: any }) => {
  helpInfoForm.value.jobTitleCode = event.value
}

// 处理计划生育状况确认
const handleFamilyPlanningStatusConfirm = (event: { value: string; selectedItems: any }) => {
  helpInfoForm.value.familyPlanningCode = event.value
}

// 获取籍贯地显示文本
const getOriginLocationText = () => {
  // 如果已匹配到名称，优先使用匹配的名称
  if (originLocationNames.value.length > 0) {
    return originLocationNames.value.join(' / ')
  }

  // 其次检查originLocationName字段
  if (
    Array.isArray(teacherInfo.value.originLocation) &&
    teacherInfo.value.originLocation.length > 0 &&
    typeof (teacherInfo.value as any).originLocationName === 'string'
  ) {
    // 如果存在originLocationName，显示这个值，但确保分隔符是 "/"
    return (teacherInfo.value as any).originLocationName.replace(/\|/g, ' / ')
  } else if (
    Array.isArray(teacherInfo.value.originLocation) &&
    teacherInfo.value.originLocation.length > 0
  ) {
    // 向后兼容，如果originLocation是字符串数组（老数据），则用斜杠连接
    return teacherInfo.value.originLocation.join(' / ')
  }
  return teacherInfo.value.originCountyName || '--'
}

onMounted(async () => {
  try {
    // 先加载字典和城市数据
    await Promise.all([getCityData(), loadDicts()])
    // 然后获取教师信息（这样可以正确匹配籍贯地名称）
    await getInfo()
  } catch (error) {
    console.error('初始化数据加载失败:', error)
    uni.showToast({
      title: '数据加载失败，请重试',
      icon: 'error',
    })
  }
})
</script>

<template>
  <view class="container">
    <!-- 页面标题 -->

    <!-- 个人头像和基础信息 -->
    <view class="card">
      <view class="profile-header">
        <view class="avatar-container" @click="uploadAvatar">
          <image :src="tempAvatarUrl || teacherInfo.avatarUrl" class="avatar" mode="aspectFill" />
          <view v-if="isEditing" class="avatar-upload-icon">
            <wd-icon v-if="!uploadLoading" name="camera" size="36rpx" color="#ffffff" />
            <wd-icon v-else name="refresh" size="36rpx" color="#ffffff" class="rotate" />
          </view>
          <view v-if="uploadLoading" class="avatar-loading-mask">
            <text class="upload-text">上传中...</text>
          </view>
        </view>
        <view class="basic-info">
          <text class="name">{{ teacherInfo.name || '--' }}</text>
          <text class="job-number">工号：{{ teacherInfo.teacherCode || '--' }}</text>
        </view>
        <view class="edit-button-container">
          <wd-button
            v-if="!isEditing"
            class="edit-button"
            size="small"
            type="primary"
            @click="startEditing"
          >
            编辑资料
          </wd-button>
          <view v-else class="edit-actions">
            <wd-button class="cancel-button" size="small" @click="cancelEditing">取消</wd-button>
            <wd-button class="save-button" size="small" type="primary" @click="saveInfo">
              保存
            </wd-button>
          </view>
        </view>
      </view>

      <view class="status-summary">
        <view class="status-item">
          <text class="status-value">{{ teacherInfo.establishmentStatusName || '--' }}</text>
          <text class="status-label">在职状态</text>
        </view>
        <view class="status-item">
          <text class="status-value">{{ teacherInfo.categoryName || '--' }}</text>
          <text class="status-label">类别</text>
        </view>
        <view class="status-item">
          <text class="status-value">{{ teacherInfo.deptName || '--' }}</text>
          <text class="status-label">所属院系</text>
        </view>
      </view>
    </view>

    <!-- 选项卡 -->
    <view class="tab-container">
      <view
        class="tab-item"
        :class="{ active: activeTab === 'basic' }"
        @click="activeTab = 'basic'"
      >
        <text>基本信息</text>
      </view>
      <view
        class="tab-item"
        :class="{ active: activeTab === 'family' }"
        @click="activeTab = 'family'"
      >
        <text>家庭成员</text>
      </view>
    </view>

    <!-- 基本信息内容 -->
    <view class="card" v-if="activeTab === 'basic'">
      <!-- 个人基本信息 -->
      <view class="info-group">
        <view class="info-title">个人基本信息</view>

        <view>
          <view class="info-item">
            <text class="info-label">姓名</text>
            <text class="info-value">{{ teacherInfo.name || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">性别</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ getGenderName(teacherInfo.gender) }}</text>
            </template>
            <template v-else>
              <wd-radio-group v-model="editForm.gender" inline>
                <wd-radio value="1">男</wd-radio>
                <wd-radio value="2">女</wd-radio>
              </wd-radio-group>
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">出生年月</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ teacherInfo.birthDate || '--' }}</text>
            </template>
            <template v-else>
              <wd-datetime-picker
                v-model="birthDateTimestamp"
                label-width="0"
                placeholder="请选择出生年月"
                type="date"
                :min-date="minDate"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">民族</text>
            <text class="info-value">{{ teacherInfo.ethnicGroupName || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">婚姻状况</text>
            <template v-if="!isHelpInfoEditing">
              <text class="info-value">
                {{ getMarriageName(teacherInfo.marriageCode) || '--' }}
              </text>
            </template>
            <template v-else>
              <wd-picker
                v-model="helpInfoForm.marriageCode"
                :columns="marriageDict"
                placeholder="请选择婚姻状况"
                align-right
                @confirm="handleMarriageConfirm"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">证件类型</text>
            <text class="info-value">
              {{ getIdTypeName(teacherInfo.IDTypeCode) || '--' }}
            </text>
          </view>
          <view class="info-item">
            <text class="info-label">证件号码</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ teacherInfo.IDNumber || '--' }}</text>
            </template>
            <template v-else>
              <input v-model="editForm.IDNumber" class="info-input" placeholder="请输入证件号码" />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">政治面貌</text>
            <template v-if="!isEditing">
              <text class="info-value">
                {{ getPoliticalStatusName(teacherInfo.politicalOutlookCode) }}
              </text>
            </template>
            <template v-else>
              <wd-picker
                v-model="editForm.politicalOutlookCode"
                :columns="politicalStatusDict"
                placeholder="请选择政治面貌"
                align-right
                @confirm="handlePoliticalStatusConfirm"
              />
            </template>
          </view>
        </view>
      </view>

      <!-- 工作信息 -->
      <view class="info-group">
        <view class="info-title">工作信息</view>
        <view>
          <view class="info-item">
            <text class="info-label">工号</text>
            <text class="info-value">{{ teacherInfo.teacherCode || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">部门</text>
            <text class="info-value">{{ teacherInfo.deptName || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">编制</text>
            <text class="info-value">{{ teacherInfo.establishmentTypeName || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">类别</text>
            <text class="info-value">{{ teacherInfo.categoryName || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">状况</text>
            <text class="info-value">{{ teacherInfo.establishmentStatusName || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">双肩挑</text>
            <text class="info-value">{{ teacherInfo.isDualRole || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">双师型</text>
            <text class="info-value">{{ teacherInfo.isDualQualification || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">职称级别</text>
            <template v-if="!isHelpInfoEditing">
              <text class="info-value">
                {{ getProfessionalTitleLevelName(teacherInfo.jobTitleLevelCode) }}
              </text>
            </template>
            <template v-else>
              <wd-picker
                v-model="helpInfoForm.jobTitleLevelCode"
                :columns="professionalTitleLevelDict"
                placeholder="请选择职称级别"
                align-right
                @confirm="handleProfessionalTitleLevelConfirm"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">职称</text>
            <template v-if="!isHelpInfoEditing">
              <text class="info-value">
                {{ getProfessionalTitleName(teacherInfo.jobTitleCode) }}
              </text>
            </template>
            <template v-else>
              <wd-picker
                v-model="helpInfoForm.jobTitleCode"
                :columns="professionalTitleDict"
                placeholder="请选择职称"
                align-right
                @confirm="handleProfessionalTitleConfirm"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">职务</text>
            <template v-if="!isHelpInfoEditing">
              <text class="info-value">{{ teacherInfo.positionName || '--' }}</text>
            </template>
            <template v-else>
              <input
                v-model="helpInfoForm.positionName"
                class="info-input"
                placeholder="请输入职务"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">职务类型</text>
            <template v-if="!isHelpInfoEditing">
              <text class="info-value">
                {{ getPositionCategoryName(teacherInfo.positionCategoryCode) }}
              </text>
            </template>
            <template v-else>
              <wd-picker
                v-model="helpInfoForm.positionCategoryCode"
                :columns="positionCategoryDict"
                placeholder="请选择职务类型"
                align-right
                @confirm="handlePositionCategoryConfirm"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">进本校年月</text>
            <text class="info-value">{{ teacherInfo.entryDate || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">教职工来源</text>
            <text class="info-value">{{ teacherInfo.sourceName || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">参加工作时间</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ teacherInfo.employmentDate || '--' }}</text>
            </template>
            <template v-else>
              <wd-datetime-picker
                v-model="employmentDateTimestamp"
                label-width="0"
                placeholder="请选择参加工作时间"
                type="date"
                :min-date="minDate"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">退休日期</text>
            <text class="info-value">{{ teacherInfo.retirementDate || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">离职(调出)日期</text>
            <text class="info-value">{{ teacherInfo.resignationDate || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">计划生育状况</text>
            <template v-if="!isHelpInfoEditing">
              <text class="info-value">
                {{ getFamilyPlanningStatusName(teacherInfo.familyPlanningCode) }}
              </text>
            </template>
            <template v-else>
              <wd-picker
                v-model="helpInfoForm.familyPlanningCode"
                :columns="familyPlanningStatusDict"
                placeholder="请选择计划生育状况"
                align-right
                @confirm="handleFamilyPlanningStatusConfirm"
              />
            </template>
          </view>
        </view>
      </view>

      <!-- 联系方式 -->
      <view class="info-group">
        <view class="info-title">联系方式</view>
        <view>
          <view class="info-item">
            <text class="info-label">籍贯地</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ getOriginLocationText() }}</text>
            </template>
            <template v-else>
              <wd-col-picker
                v-model="originLocationValue"
                :columns="originLocationColumns"
                label-width="0"
                placeholder="请选择籍贯地"
                align-right
                :column-change="handleColumnChange"
                @confirm="handleOriginLocationConfirm"
                title="请选择籍贯地"
                auto-complete
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">家庭地址</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ teacherInfo.homeAddress || '--' }}</text>
            </template>
            <template v-else>
              <input
                v-model="editForm.homeAddress"
                class="info-input"
                placeholder="请输入家庭地址"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">家庭电话</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ teacherInfo.homePhone || '--' }}</text>
            </template>
            <template v-else>
              <input v-model="editForm.homePhone" class="info-input" placeholder="请输入家庭电话" />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">移动电话</text>
            <template v-if="!isHelpInfoEditing">
              <text class="info-value">{{ teacherInfo.mobile || '--' }}</text>
            </template>
            <template v-else>
              <input
                v-model="helpInfoForm.mobile"
                class="info-input"
                placeholder="请输入移动电话"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">办公电话</text>
            <template v-if="!isHelpInfoEditing">
              <text class="info-value">{{ teacherInfo.officePhone || '--' }}</text>
            </template>
            <template v-else>
              <input
                v-model="helpInfoForm.officePhone"
                class="info-input"
                placeholder="请输入办公电话"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">电子邮箱</text>
            <template v-if="!isHelpInfoEditing">
              <text class="info-value">{{ teacherInfo.email || '--' }}</text>
            </template>
            <template v-else>
              <input v-model="helpInfoForm.email" class="info-input" placeholder="请输入电子邮箱" />
            </template>
          </view>
        </view>
      </view>

      <!-- 学历学位信息 -->
      <view class="info-group">
        <view class="info-title">学历学位信息</view>
        <view>
          <view class="info-item">
            <text class="info-label">学历</text>
            <template v-if="!isHelpInfoEditing">
              <text class="info-value">
                {{ getEducationLevelName(teacherInfo.educationalCode) }}
              </text>
            </template>
            <template v-else>
              <wd-picker
                v-model="helpInfoForm.educationalCode"
                :columns="educationLevelDict"
                placeholder="请选择学历"
                align-right
                @confirm="handleEducationLevelConfirm"
              />
            </template>
          </view>

          <view class="info-item">
            <text class="info-label">学位</text>
            <template v-if="!isHelpInfoEditing">
              <text class="info-value">{{ getDegreeName(teacherInfo.degreeCode) }}</text>
            </template>
            <template v-else>
              <wd-picker
                v-model="helpInfoForm.degreeCode"
                :columns="degreeDict"
                placeholder="请选择学位"
                align-right
                @confirm="handleDegreeConfirm"
              />
            </template>
          </view>

          <view class="info-item">
            <text class="info-label">毕业学校</text>
            <template v-if="!isHelpInfoEditing">
              <text class="info-value">{{ teacherInfo.graduationSchool || '--' }}</text>
            </template>
            <template v-else>
              <input
                v-model="helpInfoForm.graduationSchool"
                class="info-input"
                placeholder="请输入毕业学校"
              />
            </template>
          </view>

          <view class="info-item">
            <text class="info-label">毕业专业</text>
            <template v-if="!isHelpInfoEditing">
              <text class="info-value">{{ teacherInfo.graduationMajor || '--' }}</text>
            </template>
            <template v-else>
              <input
                v-model="helpInfoForm.graduationMajor"
                class="info-input"
                placeholder="请输入毕业专业"
              />
            </template>
          </view>
        </view>
      </view>

      <!-- 其他信息 -->
      <view class="info-group">
        <view class="info-title">其他信息</view>
        <view>
          <view class="info-item">
            <text class="info-label">参加党派时间</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ teacherInfo.attendPartyDate || '--' }}</text>
            </template>
            <template v-else>
              <wd-datetime-picker
                v-model="attendPartyDateTimestamp"
                label-width="0"
                placeholder="请选择参加党派时间"
                type="date"
                :min-date="minDate"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">政治面貌</text>
            <template v-if="!isEditing">
              <text class="info-value">
                {{ getPoliticalStatusName(teacherInfo.politicalOutlookCode) }}
              </text>
            </template>
            <template v-else>
              <wd-picker
                v-model="editForm.politicalOutlookCode"
                :columns="politicalStatusDict"
                placeholder="请选择政治面貌"
                align-right
                @confirm="handlePoliticalStatusConfirm"
              />
            </template>
          </view>
        </view>
      </view>

      <!-- 个人简介 -->
      <view class="info-group">
        <view class="info-title">个人简介</view>
        <view class="profile-content">
          <template v-if="!isHelpInfoEditing">
            <text class="profile-text">{{ teacherInfo.remark || '--' }}</text>
          </template>
          <template v-else>
            <textarea
              v-model="helpInfoForm.remark"
              class="profile-textarea"
              placeholder="请输入个人简介"
            />
          </template>
        </view>
      </view>
    </view>

    <!-- 家庭成员内容 -->
    <view class="card" v-if="activeTab === 'family'">
      <view class="info-group">
        <view class="info-title">家庭成员信息</view>
        <view class="empty-content">
          <wd-icon name="info-circle" size="48rpx" color="#909399" />
          <text class="empty-text">暂无家庭成员信息</text>
        </view>
      </view>
    </view>

    <!-- 底部固定操作区 -->
    <view v-if="isEditing" class="bottom-action-bar">
      <view class="action-buttons">
        <wd-button class="bottom-cancel-btn" size="large" @click="cancelEditing">取消</wd-button>
        <wd-button class="bottom-save-btn" size="large" type="primary" @click="saveInfo">
          保存
        </wd-button>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 32rpx;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom, 0));
  background-color: #f7f8fc;
}

.page-title {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;

  .title-text {
    font-size: 36rpx;
    font-weight: 600;
    color: #1f2329;
  }
}

.card {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  padding: 32rpx;
  margin-bottom: 32rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(31, 35, 41, 0.05);
}

.profile-header {
  display: flex;
  align-items: center;
  padding-bottom: 32rpx;
  border-bottom: 1rpx solid rgba(31, 35, 41, 0.1);

  .avatar-container {
    position: relative;
    width: 120rpx;
    height: 120rpx;
    margin-right: 32rpx;

    .avatar {
      width: 100%;
      height: 100%;
      border-radius: 60rpx;
    }

    .avatar-upload-icon {
      position: absolute;
      right: 0;
      bottom: 0;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48rpx;
      height: 48rpx;
      background-color: rgba(0, 0, 0, 0.6);
      border-radius: 24rpx;

      .rotate {
        animation: rotating 2s linear infinite;
      }
    }

    .avatar-loading-mask {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 60rpx;

      .upload-text {
        font-size: 24rpx;
        color: #ffffff;
      }
    }
  }

  .basic-info {
    flex: 1;

    .name {
      display: block;
      margin-bottom: 8rpx;
      font-size: 36rpx;
      font-weight: 600;
      color: #1f2329;
    }

    .job-number {
      font-size: 28rpx;
      color: #86909c;
    }
  }

  .edit-button-container {
    margin-left: auto;

    .edit-button {
      height: 64rpx;
      font-size: 26rpx;
      font-weight: 500;
      color: #ffffff;
      background: linear-gradient(135deg, #3a8eff 0%, #6fa6ff 100%);
      border: none;
      border-radius: 8rpx;
      box-shadow: 0 6rpx 12rpx rgba(58, 142, 255, 0.2);
    }

    .edit-actions {
      display: flex;
      gap: 16rpx;

      .cancel-button {
        height: 64rpx;
        font-size: 26rpx;
        font-weight: 500;
        color: #4e5969;
        background: #f2f3f5;
        border: none;
        border-radius: 8rpx;
      }

      .save-button {
        height: 64rpx;
        font-size: 26rpx;
        font-weight: 500;
        color: #ffffff;
        background: linear-gradient(135deg, #3a8eff 0%, #6fa6ff 100%);
        border: none;
        border-radius: 8rpx;
        box-shadow: 0 6rpx 12rpx rgba(58, 142, 255, 0.2);
      }
    }
  }
}

.status-summary {
  display: flex;
  justify-content: space-around;
  padding-top: 32rpx;
  text-align: center;

  .status-item {
    .status-value {
      display: block;
      margin-bottom: 8rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: var(--primary-color, #1989fa);
    }

    .status-label {
      font-size: 24rpx;
      color: #86909c;
    }
  }
}

.tab-container {
  display: flex;
  margin-bottom: 32rpx;
  background: #ffffff;
  border-bottom: 1rpx solid rgba(31, 35, 41, 0.1);
  border-radius: 24rpx 24rpx 0 0;

  .tab-item {
    position: relative;
    flex: 1;
    padding: 24rpx 0;
    font-size: 28rpx;
    color: #86909c;
    text-align: center;

    &.active {
      font-weight: 500;
      color: var(--primary-color, #1989fa);

      &::after {
        position: absolute;
        bottom: -1rpx;
        left: 50%;
        width: 40rpx;
        height: 6rpx;
        content: '';
        background-color: var(--primary-color, #1989fa);
        border-radius: 6rpx;
        transform: translateX(-50%);
      }
    }
  }
}

.info-group {
  margin-bottom: 40rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .info-title {
    position: relative;
    padding-left: 20rpx;
    margin-bottom: 24rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #1f2329;

    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: 6rpx;
      height: 32rpx;
      content: '';
      background-color: var(--primary-color, #1989fa);
      border-radius: 6rpx;
      transform: translateY(-50%);
    }
  }
}

.info-item {
  display: flex;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(31, 35, 41, 0.1);

  &:last-child {
    border-bottom: none;
  }

  .info-label {
    flex-shrink: 0;
    width: 180rpx;
    font-size: 28rpx;
    color: #86909c;
  }

  .info-value {
    flex: 1;
    font-size: 28rpx;
    color: #1f2329;
  }

  .info-input {
    flex: 1;
    height: 60rpx;
    padding: 0 16rpx;
    font-size: 28rpx;
    color: #1f2329;
    background-color: #f7f8fa;
    border: 1rpx solid #e5e6eb;
    border-radius: 8rpx;
  }

  .selected-location {
    margin-top: 8rpx;
    font-size: 24rpx;
    color: #3a8eff;
  }
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;

  .empty-text {
    margin-top: 16rpx;
    font-size: 28rpx;
    color: #909399;
  }
}

.profile-content {
  padding: 24rpx 0;

  .profile-text {
    font-size: 28rpx;
    line-height: 1.6;
    color: #1f2329;
  }
}

.info-group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.edit-link {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: var(--primary-color, #3a8eff);
  cursor: pointer;

  .edit-text {
    margin-left: 4rpx;
  }
}

.profile-textarea {
  box-sizing: border-box;
  width: 100%;
  height: 200rpx;
  padding: 16rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #1f2329;
  background-color: #f7f8fa;
  border: 1rpx solid #e5e6eb;
  border-radius: 8rpx;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 1;
  box-sizing: border-box;
  width: 100%;
  padding: 24rpx 32rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #e5e6eb;
  box-shadow: 0 -4rpx 16rpx rgba(31, 35, 41, 0.1);

  .action-buttons {
    display: flex;
    gap: 24rpx;

    .bottom-cancel-btn,
    .bottom-save-btn {
      flex: 1;
      height: 80rpx;
      font-size: 30rpx;
      font-weight: 500;
      border-radius: 12rpx;
    }

    .bottom-cancel-btn {
      color: #4e5969;
      background: #f2f3f5;
      border: none;
    }

    .bottom-save-btn {
      color: #ffffff;
      background: linear-gradient(135deg, #3a8eff 0%, #6fa6ff 100%);
      border: none;
      box-shadow: 0 6rpx 12rpx rgba(58, 142, 255, 0.2);
    }
  }
}

// 为底部操作栏添加安全区域内边距（适配全面屏）
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .bottom-action-bar {
    padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  }
}
</style>
