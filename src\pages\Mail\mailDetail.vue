<route lang="json5">
{
  style: {
    navigationBarTitleText: '邮件详情',
  },
}
</route>
<script setup lang="ts">
import { ref, computed } from 'vue'
import type { MailItem } from '@/types/mail'
import { getMailInfo } from '@/service/mail'

const mailData = ref<MailItem>({} as MailItem)
const isOutbox = ref(false) // 是否为发件箱

// 处理邮件内容，确保HTML内容可以正确显示
const mailContent = computed(() => {
  if (!mailData.value.content) {
    return '邮件内容为空'
  }

  // 如果内容包含HTML标签，则直接返回
  if (/<[a-z][\s\S]*>/i.test(mailData.value.content)) {
    return mailData.value.content
  }

  // 如果是纯文本，将换行符转换为<br>标签
  return mailData.value.content.replace(/\n/g, '<br>')
})

// 处理收件人显示
const recipientDisplay = computed(() => {
  // 发件箱的邮件，显示实际收件人
  if (isOutbox.value) {
    // 首先尝试使用recipients属性（发件箱专用字段）
    if (mailData.value.recipients) {
      // 处理可能的多人格式
      if (mailData.value.recipients.includes('|')) {
        const recipients = mailData.value.recipients.split(',')
        return recipients
          .map((recipient) => {
            const parts = recipient.split('|')
            return parts.length > 1 ? parts[1] : recipient
          })
          .join(', ')
      }
      return mailData.value.recipients
    }

    // 其次尝试使用recipientNames属性
    if (mailData.value.recipientNames && mailData.value.recipientNames.trim() !== '') {
      // 处理可能的多人格式
      if (mailData.value.recipientNames.includes('|')) {
        const names = mailData.value.recipientNames.split(',')
        return names
          .map((name) => {
            const parts = name.split('|')
            return parts.length > 1 ? parts[1] : name
          })
          .join(', ')
      }
      return mailData.value.recipientNames
    }

    // 再次尝试使用recipient+recipientName
    if (mailData.value.recipientName) {
      return mailData.value.recipientName
    } else if (mailData.value.recipient && mailData.value.recipient.includes('|')) {
      const parts = mailData.value.recipient.split('|')
      return parts.length > 1 ? parts[1] : mailData.value.recipient
    } else if (mailData.value.recipient) {
      return mailData.value.recipient
    }

    return '未指定收件人'
  }

  // 收件箱的邮件，显示"我"
  return '我'
})

// 处理extensionField1，解析为可点击链接
interface ExtensionLink {
  key: string
  value: string
}

const extensionLink = computed<ExtensionLink[]>(() => {
  if (!mailData.value.extensionField1) {
    return []
  }

  try {
    // 尝试解析为JSON对象
    if (typeof mailData.value.extensionField1 === 'string') {
      const parsed = JSON.parse(mailData.value.extensionField1)

      // 处理数组格式 [{name: "xxx", url: "xxx"}]
      if (Array.isArray(parsed)) {
        return parsed.map((item) => ({
          key: item.name || '扩展链接',
          value: item.url || '#',
        }))
      }

      // 处理对象格式 {key: value}
      if (typeof parsed === 'object' && parsed !== null) {
        // 返回键值对数组
        return Object.keys(parsed).map((key) => ({
          key,
          value: parsed[key],
        }))
      }
    } else if (typeof mailData.value.extensionField1 === 'object') {
      // 已经是对象，直接使用
      if (Array.isArray(mailData.value.extensionField1)) {
        return mailData.value.extensionField1.map((item) => ({
          key: item.name || '扩展链接',
          value: item.url || '#',
        }))
      }

      // 对象格式
      return Object.keys(mailData.value.extensionField1).map((key) => ({
        key,
        value: mailData.value.extensionField1[key],
      }))
    }
  } catch (e) {
    console.error('解析extensionField1失败:', e)
    // 如果不是JSON格式，尝试其他解析方式
    // 假设格式可能是 "key:value" 或其他格式
    if (typeof mailData.value.extensionField1 === 'string') {
      const parts = mailData.value.extensionField1.split(':')
      if (parts.length >= 2) {
        return [
          {
            key: parts[0],
            value: parts.slice(1).join(':'), // 合并可能包含冒号的值
          },
        ]
      }
    }
  }

  // 如果无法解析，直接返回原始值作为key和value
  return [
    {
      key: '扩展链接',
      value: String(mailData.value.extensionField1),
    },
  ]
})

// 使用onLoad生命周期函数获取路由参数
onLoad((options) => {
  console.log('onLoad options:', options)
  if (options && options.id) {
    // 检查是否有来源标识（收件箱/发件箱）
    if (options.from === 'send') {
      isOutbox.value = true
    }

    // 使用API获取邮件详情
    const mailType = isOutbox.value ? 'send' : 'in'
    uni.showLoading({ title: '加载中...' })

    getMailInfo({
      type: mailType,
      id: Number(options.id),
    })
      .then((res) => {
        // 将API返回的数据转换为组件所需的MailItem格式
        mailData.value = {
          ...mailData.value,
          id: Number(options.id),
          subject: res.subject,
          content: res.content,
          sender: res.sender,
          senderName: res.senderName,
          sendTime: res.sendTime,
          attachmentSize: res.attachmentSize,
          attachments: res.attachments,
          extensionField1: res.extensionField1,
          // 将其他需要的字段添加到mailData中
          mailInId: res.MailInId,
          viewStatus: res.ckzt === 1 ? '已读' : '未读',
          viewTime: res.cksj || '',
        }
        console.log('邮件数据加载成功:', mailData.value)
      })
      .catch((error) => {
        console.error('获取邮件详情失败:', error)
        uni.showToast({
          title: error.msg || '获取邮件详情失败',
          icon: 'none',
        })
      })
      .finally(() => {
        uni.hideLoading()
      })
  } else {
    console.error('未提供邮件ID')
    uni.showToast({
      title: '未提供邮件ID',
      icon: 'none',
    })
  }
})

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 回复邮件
const replyMail = () => {
  // 实现回复邮件功能
  uni.showToast({
    title: '回复功能开发中',
    icon: 'none',
  })
}

// 转发邮件
const forwardMail = () => {
  // 实现转发邮件功能
  uni.showToast({
    title: '转发功能开发中',
    icon: 'none',
  })
}

// 删除邮件
const deleteMail = () => {
  // 实现删除邮件功能
  uni.showToast({
    title: '删除功能开发中',
    icon: 'none',
  })
}

// 标记邮件
const markMail = () => {
  // 实现标记邮件功能
  uni.showToast({
    title: '标记功能开发中',
    icon: 'none',
  })
}
</script>

<template>
  <view class="mail-detail-container">
    <!-- 标题区域 -->
    <view class="mail-header">
      <view class="mail-title">{{ mailData.subject }}</view>
    </view>

    <!-- 发件人和收件人信息 -->
    <view class="mail-info">
      <view class="mail-info-item">
        <text class="mail-info-label">发件人：</text>
        <text class="mail-info-value">{{ mailData.senderName }}</text>
      </view>
      <view class="mail-info-item">
        <text class="mail-info-label">收件人：</text>
        <text class="mail-info-value">{{ recipientDisplay }}</text>
      </view>
      <view class="mail-date">{{ mailData.sendTime }}</view>
    </view>

    <!-- 邮件内容 -->
    <view class="mail-content">
      <!-- 使用richtext渲染HTML内容 -->
      <rich-text :nodes="mailContent" class="mail-content-rich"></rich-text>

      <!-- 也可以使用v-html渲染HTML内容（二选一，默认注释掉） -->
      <!-- <view v-html="mailContent" class="mail-content-html"></view> -->

      <!-- 附件区域 -->
      <view v-if="mailData.attachmentSize && mailData.attachmentSize > 0" class="mail-attachment">
        <view class="attachment-item">
          <wd-icon name="file-icon" class="attachment-icon"></wd-icon>
          <view class="attachment-info">
            <text class="attachment-name">附件</text>
            <text class="attachment-size">{{ (mailData.attachmentSize / 1024).toFixed(2) }}KB</text>
          </view>
          <button class="attachment-button">下载</button>
        </view>
      </view>

      <!-- 扩展链接区域 -->
      <view v-if="extensionLink && extensionLink.length > 0" class="extension-link-area">
        <view
          v-for="(link, index) in extensionLink"
          :key="index"
          class="extension-link-item"
          :class="{ 'mt-16': index > 0 }"
        >
          <wd-icon name="link" class="extension-link-icon"></wd-icon>
          <view class="extension-link-info">
            <a :href="link.value" class="extension-link">
              {{ link.key }}
            </a>
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="mail-actions">
      <view class="action-button" @click="replyMail">
        <wd-icon name="translate-bold" class="action-icon"></wd-icon>
        <text class="action-text">回复</text>
      </view>
      <view class="action-button" @click="forwardMail">
        <wd-icon name="transfer" class="action-icon"></wd-icon>
        <text class="action-text">转发</text>
      </view>
      <view class="action-button" @click="deleteMail">
        <wd-icon name="delete" class="action-icon"></wd-icon>
        <text class="action-text">删除</text>
      </view>
      <view class="action-button" @click="markMail">
        <wd-icon name="star" class="action-icon"></wd-icon>
        <text class="action-text">标记</text>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.mail-detail-container {
  box-sizing: border-box;
  min-height: 100vh;
  padding: 32rpx;
  background-color: #ffffff;
}

.mail-header {
  padding-bottom: 16rpx;
  margin-bottom: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.mail-title {
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1.4;
  color: #333333;
}

.mail-info {
  position: relative;
  padding-bottom: 16rpx;
  margin-bottom: 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.mail-info-item {
  display: flex;
  margin-bottom: 8rpx;
  font-size: 28rpx;
}

.mail-info-label {
  margin-right: 8rpx;
  color: #6b7280;
}

.mail-info-value {
  flex: 1;
  color: #333333;
}

.mail-date {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 24rpx;
  color: #6b7280;
}

.mail-content {
  padding-bottom: 24rpx;
  margin-bottom: 40rpx;
}

.mail-content-rich {
  font-size: 30rpx;
  line-height: 1.6;
  color: #333333;
}
/* v-html内容的样式 */
.mail-content-html {
  font-size: 30rpx;
  line-height: 1.6;
  color: #333333;
}
/* HTML内容的子元素样式 */
.mail-content-html p {
  margin-bottom: 16rpx;
}

.mail-content-html img {
  max-width: 100%;
  height: auto;
}

.mail-content-html a {
  color: #3b82f6;
  text-decoration: underline;
}

.mail-content-html ul,
.mail-content-html ol {
  padding-left: 40rpx;
  margin-bottom: 16rpx;
}

.mail-content-html li {
  margin-bottom: 8rpx;
}

.mail-content-html h1,
.mail-content-html h2,
.mail-content-html h3,
.mail-content-html h4,
.mail-content-html h5,
.mail-content-html h6 {
  margin-top: 24rpx;
  margin-bottom: 16rpx;
  font-weight: bold;
}

.mail-content-html table {
  width: 100%;
  margin-bottom: 16rpx;
  border-collapse: collapse;
}

.mail-content-html th,
.mail-content-html td {
  padding: 8rpx;
  border: 1rpx solid #ddd;
}

.mail-content-html blockquote {
  padding-left: 16rpx;
  margin-bottom: 16rpx;
  color: #666;
  border-left: 4rpx solid #ddd;
}

.mail-attachment {
  padding: 24rpx;
  margin-top: 32rpx;
  background-color: #f7f8fc;
  border-radius: 12rpx;
}

.attachment-item {
  display: flex;
  align-items: center;
}

.attachment-icon {
  margin-right: 16rpx;
  color: #6b7280;
}

.attachment-info {
  display: flex;
  flex: 1;
  flex-direction: column;
}

.attachment-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.attachment-size {
  font-size: 24rpx;
  color: #6b7280;
}

.attachment-button {
  padding: 8rpx 16rpx;
  font-size: 24rpx;
  color: #ffffff;
  background-color: #3b82f6;
  border: none;
  border-radius: 8rpx;
}
/* 扩展链接区域样式 */
.extension-link-area {
  padding: 24rpx;
  margin-top: 16rpx;
  background-color: #f7f8fc;
  border-radius: 12rpx;
}

.extension-link-item {
  display: flex;
  align-items: center;
}

.mt-16 {
  margin-top: 16rpx;
}

.extension-link-icon {
  margin-right: 16rpx;
  color: #3b82f6;
}

.extension-link-info {
  flex: 1;
}

.extension-link {
  font-size: 28rpx;
  color: #3b82f6;
  text-decoration: underline;
}

.mail-actions {
  display: flex;
  justify-content: space-between;
  padding-top: 24rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx;
}

.action-icon {
  margin-bottom: 8rpx;
  font-size: 40rpx;
  color: #3b82f6;
}

.action-text {
  font-size: 24rpx;
  color: #6b7280;
}
</style>
