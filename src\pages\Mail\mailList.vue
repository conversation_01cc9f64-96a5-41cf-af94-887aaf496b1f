<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '校内邮件信息',
  },
}
</route>
<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { getMailList } from '@/service/mail'
import type { MailItem, MailQuery } from '@/types/mail'
import { useUserStore } from '@/store/user'
import Pagination from '@/components/Pagination/index.vue'

// 获取用户信息
const userStore = useUserStore()

// 邮箱统计信息
const mailStats = ref({
  unreadCount: 0,
  inboxCount: 0,
  outboxCount: 0,
})

// 当前选中的选项卡
const activeTab = ref('in')

// 搜索关键词
const searchKeyword = ref('')

// 邮件列表数据
const emailList = ref<MailItem[]>([])
const total = ref(0)

// 分页相关变量
const currentPage = ref(1)
const pageSize = ref(10)

// 初始化统计信息
const initMailStats = () => {
  // 获取收件箱统计
  getMailList({
    page: 1,
    pageSize: 10,
    type: 'in',
    subject: '',
    senderName: '',
    sendTime: '',
    viewStatus: '',
    viewTime: '',
    sortBy: 'id',
    sortOrder: 'desc',
  })
    .then((inboxRes) => {
      // 更新收件箱数量
      mailStats.value.inboxCount = inboxRes.total

      // 计算未读邮件数量
      const unreadCount = inboxRes.items.filter((item) => item.viewStatus === '否').length
      mailStats.value.unreadCount = unreadCount

      // 获取发件箱统计
      return getMailList({
        page: 1,
        pageSize: 10,
        type: 'send',
        subject: '',
        senderName: '',
        sendTime: '',
        viewStatus: '',
        viewTime: '',
        sortBy: 'id',
        sortOrder: 'desc',
      })
    })
    .then((outboxRes) => {
      // 更新发件箱数量
      mailStats.value.outboxCount = outboxRes.total
    })
    .catch((error) => {
      console.error('获取邮件统计信息失败:', error)
    })
}

// 获取邮件列表
const getList = () => {
  const params: MailQuery = {
    page: currentPage.value,
    pageSize: pageSize.value,
    type: activeTab.value,
    subject: searchKeyword.value,
    sortBy: 'id',
    sortOrder: 'desc',
    senderName: '',
    sendTime: '',
    viewStatus: '',
    viewTime: '',
    recipients: '',
    wechatPushFlag: '',
  }

  // 根据不同的选项卡设置不同的请求参数
  if (activeTab.value === 'in') {
    // 收件箱参数保持不变
  } else if (activeTab.value === 'send') {
    // 发件箱参数
    params.senderName = ''
    params.viewStatus = ''
    params.viewTime = ''
  }

  getMailList(params)
    .then((res) => {
      emailList.value = res.items
      total.value = res.total

      // 只更新当前页卡对应的统计数据，不更新未读数
      if (activeTab.value === 'in') {
        mailStats.value.inboxCount = res.total
        // 重新计算未读邮件数量
        const unreadCount = res.items.filter((item) => item.viewStatus === '否').length
        if (unreadCount > 0) {
          mailStats.value.unreadCount = unreadCount
        }
      } else if (activeTab.value === 'send') {
        mailStats.value.outboxCount = res.total
      }
    })
    .catch((error) => {
      console.error('获取邮件列表失败:', error)
    })
}

// 处理页码变化
const handlePageChange = (page: number) => {
  currentPage.value = page
  getList()
}

// 获取用户头像或首字母
const getUserAvatar = () => {
  if (userStore.userInfo.avatar) {
    return userStore.userInfo.avatar
  }
  return userStore.userInfo.realname ? userStore.userInfo.realname.slice(0, 1) : '邮'
}

// 跳转到邮件详情页面
const goToMailDetail = (email: MailItem) => {
  // 如果是未读邮件，标记为已读
  if (email.viewStatus === '否') {
    // 这里可以调用接口标记为已读
    email.viewStatus = '是'

    // 重新计算未读邮件数量
    mailStats.value.unreadCount = emailList.value.filter((item) => item.viewStatus === '否').length
  }

  // 跳转到邮件详情页面，通过URL参数传递邮件ID和来源（收件箱/发件箱）
  uni.navigateTo({
    url: `/pages/Mail/mailDetail?id=${email.id}&from=${activeTab.value}`,
  })
}

// 监听选项卡切换
watch(activeTab, () => {
  currentPage.value = 1 // 切换选项卡时重置页码
  getList()
})

// 监听搜索关键词变化
watch(searchKeyword, () => {
  currentPage.value = 1 // 搜索时重置页码
  getList()
})

onShow(() => {
  getList()
})

onMounted(() => {
  // 初始化统计信息
  initMailStats()
})
</script>

<template>
  <view class="mail-container">
    <!-- 添加页面标题 -->
    <view class="page-title">校内邮件信息</view>

    <!-- 邮箱信息卡片 -->
    <view class="mail-card mb-4">
      <view class="mail-card-content p-0">
        <view class="flex items-center p-4 border-b border-gray-100">
          <view
            v-if="!userStore.userInfo.avatar"
            class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white text-xl font-bold mr-4"
          >
            {{ getUserAvatar() }}
          </view>
          <image
            v-else
            class="w-12 h-12 rounded-full mr-4"
            :src="userStore.userInfo.avatar"
            mode="aspectFill"
          ></image>
          <view class="flex-1">
            <view class="font-medium">{{ userStore.userInfo.realname }}</view>
            <view class="text-xs text-gray-500 mt-1">工号：{{ userStore.userInfo.username }}</view>
          </view>
          <view v-if="userStore.userInfo.department">
            <view class="bg-green-100 text-green-600 text-xs px-2 py-0.5 rounded-full">
              {{ userStore.userInfo.department }}
            </view>
          </view>
        </view>
        <view class="grid grid-cols-3 divide-x divide-gray-100">
          <view class="flex flex-col items-center py-3">
            <view class="text-xl font-bold text-blue-500">{{ mailStats.unreadCount }}</view>
            <view class="text-xs text-gray-500">未读邮件</view>
          </view>
          <view class="flex flex-col items-center py-3">
            <view class="text-xl font-bold text-blue-500">{{ mailStats.inboxCount }}</view>
            <view class="text-xs text-gray-500">收件数量</view>
          </view>
          <view class="flex flex-col items-center py-3">
            <view class="text-xl font-bold text-blue-500">{{ mailStats.outboxCount }}</view>
            <view class="text-xs text-gray-500">发件数量</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 邮箱选项卡 -->
    <view class="tab-container mb-4">
      <view class="tab-item" :class="{ active: activeTab === 'in' }" @click="activeTab = 'in'">
        <text>收件箱</text>
      </view>
      <view class="tab-item" :class="{ active: activeTab === 'send' }" @click="activeTab = 'send'">
        <text>发件箱</text>
      </view>
    </view>

    <!-- 搜索框 -->
    <view class="mail-search mb-4">
      <wd-icon name="search" class="mr-2 text-gray-500"></wd-icon>
      <input type="text" placeholder="搜索邮件" v-model="searchKeyword" />
    </view>

    <!-- 邮件列表 -->
    <view class="mail-list mb-4">
      <!-- 邮件列表项 -->
      <view
        v-for="email in emailList"
        :key="email.id"
        class="mail-list-item p-3"
        @click="goToMailDetail(email)"
      >
        <view class="flex items-start">
          <view
            :class="[
              'w-10 h-10 rounded-full flex items-center justify-center text-white text-sm font-bold mr-3 flex-shrink-0',
              email.viewStatusColor === 'green' ? 'bg-green-500' : 'bg-blue-500',
            ]"
          >
            {{ email.senderName.slice(0, 1) }}
          </view>
          <view class="flex-1 min-w-0">
            <view class="flex justify-between items-center">
              <view
                class="truncate pr-2"
                :class="
                  email.viewStatus === '否' && activeTab === 'in'
                    ? 'font-medium text-black'
                    : 'font-medium text-gray-700'
                "
              >
                {{ email.senderName }}
              </view>
              <view class="text-xs text-gray-500 whitespace-nowrap">{{ email.sendTime }}</view>
            </view>
            <view
              class="text-sm mt-0.5 break-words"
              :class="
                email.viewStatus === '否' && activeTab === 'in'
                  ? 'font-medium text-black'
                  : 'text-gray-700'
              "
            >
              {{ email.subject }}
            </view>
            <view class="mt-2 flex items-center justify-between">
              <view class="flex items-center">
                <view
                  v-if="email.viewStatus === '否' && activeTab === 'in'"
                  class="text-xs px-1.5 py-0.5 bg-blue-50 text-blue-500 rounded mr-2"
                >
                  未读
                </view>
                <view
                  v-else-if="activeTab === 'in'"
                  class="text-xs px-1.5 py-0.5 bg-green-50 text-green-600 rounded mr-2"
                >
                  已读
                </view>
                <view
                  class="text-xs px-1.5 py-0.5 bg-blue-50 text-blue-500 rounded"
                  v-if="email.attachmentSize > 0"
                >
                  附件
                </view>
              </view>
              <view
                v-if="email.viewStatus !== '否' && email.viewTime && activeTab === 'in'"
                class="text-xs text-gray-500"
              >
                阅读时间: {{ email.viewTime }}
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 无数据提示 -->
      <view v-if="emailList.length === 0" class="no-data">
        <view class="no-data-text">暂无邮件</view>
      </view>
    </view>

    <!-- 分页组件 -->
    <view v-if="total > 0" class="pagination-container pb-10">
      <Pagination
        :total="total"
        :page="currentPage"
        :pageSize="pageSize"
        @update:page="handlePageChange"
      />
    </view>
  </view>
</template>

<style scoped lang="scss">
.mail-container {
  box-sizing: border-box;
  min-height: 100vh;
  padding: 32rpx;
  background-color: #f7f8fc;
}

.page-title {
  margin-bottom: 30rpx;
  font-size: 44rpx;
  font-weight: 700;
}

.mail-card {
  margin-bottom: 32rpx;
  overflow: hidden;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);

  .mail-card-content {
    background: #ffffff;
  }
}

.mail-search {
  position: relative;
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  margin-bottom: 24rpx;
  background: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

  i {
    margin-right: 16rpx;
    color: #999;
  }

  input {
    flex: 1;
    font-size: 28rpx;
    background: transparent;
    border: none;
    outline: none;
  }
}

.mail-list {
  overflow: hidden;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);

  .mail-list-item {
    position: relative;
    padding: 32rpx 24rpx;
    margin-bottom: 2rpx;
    border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s;

    &:hover,
    &:active {
      background-color: rgba(0, 0, 0, 0.02);
    }

    &:last-child {
      border-bottom: none;
    }
  }
}

.break-words {
  display: -webkit-box;
  overflow: hidden;
  word-break: break-all;
  word-wrap: break-word;
  white-space: normal;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.bg-green-50 {
  background-color: rgba(5, 150, 105, 0.1);
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.mb-4 {
  margin-bottom: 32rpx;
}

.p-0 {
  padding: 0;
}

.p-3,
.p-4 {
  padding: 16rpx;
}

.border-b {
  border-bottom-style: solid;
  border-bottom-width: 1rpx;
}

.border-gray-100 {
  border-color: rgba(0, 0, 0, 0.1);
}

.border-gray-200 {
  border-color: rgba(0, 0, 0, 0.15);
}

.grid {
  display: grid;
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.divide-x > * + * {
  border-left-style: solid;
  border-left-width: 1rpx;
}

.divide-gray-100 {
  border-color: rgba(0, 0, 0, 0.1);
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.font-medium {
  font-weight: 500;
}

.font-bold {
  font-weight: 700;
}

.text-xs {
  font-size: 22rpx;
}

.text-sm {
  font-size: 28rpx;
}

.text-xl {
  font-size: 36rpx;
}

.text-white {
  color: #ffffff;
}

.text-black {
  color: #000000;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-700 {
  color: #4b5563;
}

.text-blue-500 {
  color: #3b82f6;
}

.text-red-500 {
  color: #ef4444;
}

.text-green-600 {
  color: #059669;
}

.bg-blue-500 {
  background-color: #3b82f6;
}

.bg-green-500 {
  background-color: #10b981;
}

.bg-purple-500 {
  background-color: #8b5cf6;
}

.bg-gray-400 {
  background-color: #9ca3af;
}

.bg-blue-50 {
  background-color: rgba(59, 130, 246, 0.1);
}

.bg-red-50 {
  background-color: rgba(239, 68, 68, 0.1);
}

.bg-green-100 {
  background-color: rgba(5, 150, 105, 0.1);
}

.rounded-full {
  border-radius: 9999rpx;
}

.rounded {
  border-radius: 6rpx;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.mr-1 {
  margin-right: 8rpx;
}

.mr-2 {
  margin-right: 12rpx;
}

.mr-3 {
  margin-right: 16rpx;
}

.mr-4 {
  margin-right: 24rpx;
}

.mt-0\.5 {
  margin-top: 4rpx;
}

.mt-1 {
  margin-top: 8rpx;
}

.mt-2 {
  margin-top: 12rpx;
}

.py-2 {
  padding-top: 16rpx;
  padding-bottom: 16rpx;
}

.py-3 {
  padding-top: 20rpx;
  padding-bottom: 20rpx;
}

.px-2 {
  padding-right: 12rpx;
  padding-left: 12rpx;
}

.px-4 {
  padding-right: 24rpx;
  padding-left: 24rpx;
}

.py-0\.5 {
  padding-top: 4rpx;
  padding-bottom: 4rpx;
}

.px-1\.5 {
  padding-right: 8rpx;
  padding-left: 8rpx;
}

.w-2 {
  width: 12rpx;
}

.h-2 {
  height: 12rpx;
}

.w-10 {
  width: 80rpx;
}

.h-10 {
  height: 80rpx;
}

.w-12 {
  width: 96rpx;
}

.h-12 {
  height: 96rpx;
}

.min-w-0 {
  min-width: 0;
}

.pr-2 {
  padding-right: 16rpx;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.border-b-2 {
  border-bottom-width: 2rpx !important;
}

.border-blue-500 {
  border-color: #3b82f6;
}

.tab-container {
  display: flex;
  margin-bottom: 20rpx;
  background: transparent;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);

  .tab-item {
    position: relative;
    flex: 1;
    padding: 24rpx 0;
    font-size: 28rpx;
    color: #86909c;
    text-align: center;
    transition: all 0.3s;

    &.active {
      font-weight: 500;
      color: #3b82f6;

      &::after {
        position: absolute;
        bottom: -1rpx;
        left: 50%;
        width: 40rpx;
        height: 4rpx;
        content: '';
        background-color: #3b82f6;
        border-radius: 2rpx;
        transform: translateX(-50%);
      }
    }
  }
}

.category-tabs {
  display: none;
}

.mail-category {
  display: none;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200rpx;

  .no-data-text {
    font-size: 28rpx;
    color: #999;
  }
}

.pagination-container {
  margin-bottom: 32rpx;
}
</style>
