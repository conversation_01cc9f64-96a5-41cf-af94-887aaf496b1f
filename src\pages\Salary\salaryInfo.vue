<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的工资',
  },
}
</route>

<template>
  <view class="salary-info">
    <!-- 薪资概览卡片 -->
    <view class="card salary-card">
      <view class="salary-overview">
        <view class="salary-header">
          <view>
            <!-- 日期选择器UI -->
            <view
              class="salary-date-selector"
              @click.stop="isPickerOpen ? closeDatePicker() : openDatePicker()"
            >
              <text class="salary-month">{{ currentYear }}年{{ currentMonth }}月工资单</text>
              <view class="arrow-control">
                <wd-icon :name="isPickerOpen ? 'arrow-up' : 'arrow-down'" size="14px"></wd-icon>
                <text class="control-tip">{{ isPickerOpen ? '取消' : '选择' }}</text>
              </view>
            </view>
            <text class="salary-amount">¥{{ totalSalary }}</text>
          </view>
        </view>

        <view class="salary-stats">
          <view class="stat-item">
            <text class="amount primary">¥{{ salaryData.incomeItems.应发工资 || '0' }}</text>
            <text class="label">基本工资</text>
          </view>
          <view class="stat-item">
            <text class="amount primary">¥{{ salaryData.incomeItems.应发津贴 || '0' }}</text>
            <text class="label">津贴工资</text>
          </view>
          <view class="stat-item">
            <text class="amount success">¥{{ totalSalary }}</text>
            <text class="label">总工资</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 工资趋势图 -->
    <view class="card salary-card">
      <view class="card-title">工资趋势</view>
      <view class="chart-container">
        <l-echart ref="trendChartRef"></l-echart>
      </view>
      <view class="trend-stats">
        <view class="stat-item">
          <text class="amount primary">¥{{ monthlyStats.currentMonthIncome }}</text>
          <text class="label">本月收入</text>
        </view>
        <view class="stat-item">
          <text
            class="amount"
            :class="Number(monthlyStats.diffFromLastMonth) >= 0 ? 'success' : 'danger'"
          >
            {{ Number(monthlyStats.diffFromLastMonth) >= 0 ? '+' : '' }}¥{{
              monthlyStats.diffFromLastMonth
            }}
          </text>
          <text class="label">较上月</text>
        </view>
        <view class="stat-item">
          <text class="amount">¥{{ monthlyStats.monthlyAverage }}</text>
          <text class="label">月平均</text>
        </view>
      </view>
    </view>

    <!-- 工资明细 -->
    <view class="card salary-card">
      <view class="card-title">当月工资明细</view>
      <!-- 收入项 -->
      <view class="salary-detail">
        <view class="detail-section">
          <view class="section-title success">
            <wd-icon name="check-bold" size="14px" class="section-icon"></wd-icon>
            <text>收入项</text>
          </view>
          <view class="detail-item" v-for="(item, index) in filteredIncomeItems" :key="index">
            <text class="item-label">{{ item.label }}</text>
            <text class="item-value">¥{{ item.value }}</text>
          </view>
          <view class="detail-empty" v-if="!hasIncomeItems">
            <text>暂无数据</text>
          </view>
        </view>

        <!-- 津贴项 -->
        <view class="detail-section">
          <view class="section-title success">
            <wd-icon name="check-bold" size="14px" class="section-icon"></wd-icon>
            <text>津贴项</text>
          </view>
          <view class="detail-item" v-for="(item, index) in filteredAllowanceItems" :key="index">
            <text class="item-label">{{ item.label }}</text>
            <text class="item-value">¥{{ item.value }}</text>
          </view>
          <view class="detail-empty" v-if="!hasAllowanceItems">
            <text>暂无数据</text>
          </view>
        </view>

        <!-- 扣除项 -->
        <view class="detail-section">
          <view class="section-title danger">
            <wd-icon name="close-bold" size="14px" class="section-icon"></wd-icon>
            <text>扣除项</text>
          </view>
          <view class="detail-item" v-for="(item, index) in filteredDeductItems" :key="index">
            <text class="item-label">{{ item.label }}</text>
            <text class="item-value">¥{{ item.value }}</text>
          </view>
          <view class="detail-empty" v-if="!hasDeductItems">
            <text>暂无数据</text>
          </view>
        </view>

        <!-- 年终奖项 -->
        <view class="detail-section" v-if="hasYearEndBonusItems">
          <view class="section-title primary">
            <wd-icon name="star-on" size="14px" class="section-icon"></wd-icon>
            <text>年终奖</text>
          </view>
          <view class="detail-item" v-for="(item, index) in yearEndBonusItems" :key="index">
            <text class="item-label">{{ item.label }}</text>
            <text class="item-value">¥{{ item.value }}</text>
          </view>
        </view>

        <view class="total-salary">
          <text class="total-label">实发工资</text>
          <text class="total-amount">¥{{ totalSalary }}</text>
        </view>
      </view>
    </view>

    <!-- 年度收入统计 -->
    <view class="card salary-card">
      <view class="card-title">{{ currentYearForTemplate }}年收入统计</view>
      <view class="annual-stats">
        <view class="pie-chart">
          <l-echart ref="pieChartRef"></l-echart>
        </view>
        <view class="pie-legend">
          <view class="legend-item" v-for="(item, index) in annualStats" :key="index">
            <view class="legend-color" :style="{ backgroundColor: item.color }"></view>
            <text class="legend-label">{{ item.label }}</text>
            <text class="legend-value">{{ item.percentage }}%</text>
          </view>
        </view>
      </view>
      <view class="annual-period">
        统计周期：{{ currentYearForTemplate }}年1月1日 - {{ currentYearForTemplate }}年12月31日
      </view>
    </view>

    <!-- 日期选择器 - 可控制隐藏 -->
    <view :class="{ 'hidden-picker': isPickerHidden }" ref="hiddenPickerRef">
      <wd-datetime-picker
        ref="datePickerRef"
        v-model="pickerValue"
        :min-date="minDate"
        :max-date="maxDate"
        type="year-month"
        title="选择工资月份"
        confirm-button-text="确定"
        cancel-button-text="取消"
        @confirm="handleDateConfirm"
        @cancel="handleDateCancel"
      ></wd-datetime-picker>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
import * as echarts from 'echarts'
import { getSalaryInfo, formatSalaryData } from '@/service/salary'
import type { SalaryDetail, SalaryItemEntry } from '@/types/salary'

// 图表引用
const trendChartRef = ref()
const pieChartRef = ref()
// 日期选择器引用
const datePickerRef = ref()
// 隐藏容器引用
const hiddenPickerRef = ref()
// 控制选择器是否隐藏
const isPickerHidden = ref(true)
// 控制选择器是否打开（用于显示/隐藏取消按钮）
const isPickerOpen = ref(false)
// 防止循环调用的标志
const isClosingDatePicker = ref(false)

// 工资数据
const salaryData = ref<SalaryDetail>({
  incomeItems: {},
  deductItems: {},
  baseInfo: {},
})

// 整年工资数据
const yearSalaryData = ref<Record<string, SalaryDetail>>({})

// 当前选中的月份
const currentMonth = ref(new Date().getMonth() + 1)
// 当前年份
const currentYear = ref(new Date().getFullYear())

// 日期选择器相关
const pickerValue = ref<number>(new Date().getTime())
const minDate = ref<number>(new Date(2000, 0, 1).getTime()) // 默认最小日期：2000年1月
const maxDate = ref<number>(new Date().getTime()) // 默认最大日期：当前日期

// 为模板提供的年份值
const currentYearForTemplate = computed(() => currentYear.value)

// 计算总工资（工资+津贴）
const totalSalary = computed(() => {
  const baseAmount = Number(salaryData.value.baseInfo.实发工资 || '0')
  const allowanceAmount = Number(salaryData.value.baseInfo.实发津贴 || '0')
  return (baseAmount + allowanceAmount).toFixed(2)
})

// 处理接口返回的整年工资数据，按月份组织
const processYearData = (data: Record<string, SalaryItemEntry[][]>) => {
  const monthDataMap: Record<string, SalaryDetail> = {}

  // 遍历所有数据
  Object.keys(data).forEach((key) => {
    // 数据格式示例: 2021_1_348 或 2021_2_355，表示年_月_ID
    const parts = key.split('_')
    if (parts.length >= 2) {
      const year = parts[0]
      const month = parts[1]
      const monthKey = `${year}_${month}`

      // 如果这个月份还没有数据，则初始化
      if (!monthDataMap[monthKey]) {
        monthDataMap[monthKey] = {
          incomeItems: {},
          deductItems: {},
          baseInfo: { year, month },
        }
      }

      // 格式化当前项数据
      const partialData = { [key]: data[key] }
      const formattedData = formatSalaryData(partialData)

      // 合并到对应月份的数据中
      monthDataMap[monthKey].incomeItems = {
        ...monthDataMap[monthKey].incomeItems,
        ...formattedData.incomeItems,
      }
      monthDataMap[monthKey].deductItems = {
        ...monthDataMap[monthKey].deductItems,
        ...formattedData.deductItems,
      }
      monthDataMap[monthKey].baseInfo = {
        ...monthDataMap[monthKey].baseInfo,
        ...formattedData.baseInfo,
      }
    }
  })

  return monthDataMap
}

// 日期选择器相关函数
const openDatePicker = () => {
  pickerValue.value = new Date(currentYear.value, currentMonth.value - 1).getTime()
  isPickerHidden.value = false
  isPickerOpen.value = true
  if (datePickerRef.value) {
    datePickerRef.value.open()
  }
}

const closeDatePicker = () => {
  if (isClosingDatePicker.value) return
  isClosingDatePicker.value = true
  if (datePickerRef.value) {
    datePickerRef.value.close()
  }
  setTimeout(() => {
    isPickerHidden.value = true
    isPickerOpen.value = false
    isClosingDatePicker.value = false
  }, 300)
}

const handleDateCancel = () => {
  setTimeout(() => {
    isPickerHidden.value = true
    isPickerOpen.value = false
  }, 300)
}

const handleDateConfirm = (value: { value: number }) => {
  const date = new Date(value.value)
  const selectedYear = date.getFullYear()
  const selectedMonth = date.getMonth() + 1
  const yearChanged = selectedYear !== currentYear.value

  currentYear.value = selectedYear
  currentMonth.value = selectedMonth

  if (yearChanged) {
    // 年份变化，需重新获取数据
    getSalaryDataForYear(selectedYear)
  } else {
    // 只更新月份
    updateCurrentMonthData()
  }

  closeDatePicker()
}

// 获取指定年份工资数据
const getSalaryDataForYear = async (year: number) => {
  try {
    uni.showLoading({ title: '加载中...' })

    // 获取整年数据
    const res = await getSalaryInfo({
      year,
      month: '', // 空字符串表示获取整年数据
      isMobile: 1,
    })

    // 处理数据并按月份组织
    yearSalaryData.value = processYearData(res)

    // 如果当前选中月份没有数据，自动选择最新月份
    const currentMonthKey = `${currentYear.value}_${currentMonth.value}`
    if (!yearSalaryData.value[currentMonthKey]) {
      const sortedMonths = Object.keys(yearSalaryData.value).sort().reverse()
      if (sortedMonths.length > 0) {
        const parts = sortedMonths[0].split('_')
        if (parts.length >= 2) {
          currentMonth.value = parseInt(parts[1])
          pickerValue.value = new Date(currentYear.value, currentMonth.value - 1).getTime()
        }
      } else {
        uni.showToast({
          title: `${year}年没有工资数据`,
          icon: 'none',
        })
      }
    }

    // 更新当前月份数据
    updateCurrentMonthData()

    // 更新日期选择器范围
    updateDatePickerRange()

    // 初始化图表
    setTimeout(() => {
      initTrendChart()
      initPieChart()
    }, 300)

    uni.hideLoading()
  } catch (error) {
    console.error('获取工资数据失败:', error)
    uni.showToast({
      title: '获取工资数据失败',
      icon: 'none',
    })
    uni.hideLoading()
  }
}

// 更新当前月份数据
const updateCurrentMonthData = () => {
  const currentMonthKey = `${currentYear.value}_${currentMonth.value}`

  if (yearSalaryData.value[currentMonthKey]) {
    salaryData.value = yearSalaryData.value[currentMonthKey]
  } else {
    uni.showToast({
      title: '该月份无工资数据',
      icon: 'none',
    })
  }
}

// 计算月度总工资
const calculateMonthlyTotal = (monthData: SalaryDetail) => {
  const baseAmount = Number(monthData.baseInfo.实发工资 || '0')
  const allowanceAmount = Number(monthData.baseInfo.实发津贴 || '0')
  return baseAmount + allowanceAmount
}

// 计算趋势图数据
const calculateTrendData = () => {
  const monthDataMap: Record<number, { month: string; value: number }> = {}

  // 提取所有月份数据
  Object.keys(yearSalaryData.value).forEach((monthKey) => {
    const parts = monthKey.split('_')
    if (parts.length >= 2) {
      const month = parseInt(parts[1])
      monthDataMap[month] = {
        month: `${month}月`,
        value: calculateMonthlyTotal(yearSalaryData.value[monthKey]),
      }
    }
  })

  // 按月份顺序排序
  const sortedMonthNumbers = Object.keys(monthDataMap)
    .map(Number)
    .sort((a, b) => a - b)

  // 构建数据数组
  const months: string[] = []
  const salaryValues: number[] = []

  sortedMonthNumbers.forEach((monthNum) => {
    months.push(monthDataMap[monthNum].month)
    salaryValues.push(monthDataMap[monthNum].value)
  })

  return { months, salaryValues }
}

// 计算年度统计数据
const calculateAnnualStats = () => {
  // 初始化统计数据
  let totalBase = 0
  let totalPerformance = 0
  let totalOther = 0
  let totalIncome = 0

  // 遍历每个月份的数据
  Object.values(yearSalaryData.value).forEach((monthData) => {
    // 基本工资(职岗工资+级别薪级+生活补贴)
    const baseWage =
      Number(monthData.incomeItems.职岗工资 || '0') +
      Number(monthData.incomeItems.级别薪级 || '0') +
      Number(monthData.incomeItems.生活补贴 || '0')

    // 绩效(考核津贴+在岗津贴+效益津贴)
    const performance =
      Number(monthData.incomeItems.考核津贴 || '0') +
      Number(monthData.incomeItems.在岗津贴 || '0') +
      Number(monthData.incomeItems.效益津贴 || '0')

    // 计算月总收入和其他收入
    const monthTotal = calculateMonthlyTotal(monthData)
    const other = Math.max(0, monthTotal - baseWage - performance)

    // 累加到总计中
    totalBase += baseWage
    totalPerformance += performance
    totalOther += other
    totalIncome += monthTotal
  })

  // 计算百分比
  const basePercentage = totalIncome > 0 ? Math.round((totalBase / totalIncome) * 100) : 0
  const performancePercentage =
    totalIncome > 0 ? Math.round((totalPerformance / totalIncome) * 100) : 0
  let otherPercentage = 100 - basePercentage - performancePercentage
  if (basePercentage === 0 && performancePercentage === 0) {
    otherPercentage = 0
  }

  // 返回统计结果
  return [
    {
      label: '基本工资',
      percentage: basePercentage,
      color: '#3b7cff',
      value: totalBase,
    },
    {
      label: '绩效奖金',
      percentage: performancePercentage,
      color: '#22c55e',
      value: totalPerformance,
    },
    {
      label: '其他收入',
      percentage: otherPercentage,
      color: '#f59e0b',
      value: totalOther,
    },
  ]
}

// 获取工资数据入口
const getSalaryData = async () => {
  try {
    await getSalaryDataForYear(currentYear.value)
  } catch (error) {
    console.error('获取工资数据失败:', error)
    uni.showToast({
      title: '获取工资数据失败',
      icon: 'none',
    })
  }
}

// 根据数据范围更新日期选择器的可选范围
const updateDatePickerRange = () => {
  if (Object.keys(yearSalaryData.value).length === 0) return

  const allMonthKeys = Object.keys(yearSalaryData.value).sort()
  if (allMonthKeys.length > 0) {
    // 最早的月份
    const firstParts = allMonthKeys[0].split('_')
    if (firstParts.length >= 2) {
      const firstYear = parseInt(firstParts[0])
      const firstMonth = parseInt(firstParts[1]) - 1 // JavaScript月份从0开始
      // minDate.value = new Date(firstYear, firstMonth, 1).getTime()
    }

    // 最晚的月份
    const lastParts = allMonthKeys[allMonthKeys.length - 1].split('_')
    if (lastParts.length >= 2) {
      const lastYear = parseInt(lastParts[0])
      const lastMonth = parseInt(lastParts[1]) - 1 // JavaScript月份从0开始
      // maxDate.value = new Date(lastYear, lastMonth, 1).getTime()
    }
  }
}

// 收入项数据（基本工资项目）
const incomeItems = ref([
  { label: '职岗工资', value: '0' },
  { label: '级别薪级', value: '0' },
  { label: '生活补贴', value: '0' },
  { label: '教护津贴', value: '0' },
  { label: '岗位津贴', value: '0' },
  { label: '职务津贴', value: '0' },
  { label: '地区补贴', value: '0' },
  { label: '考勤奖', value: '0' },
  { label: '特岗津贴', value: '0' },
  { label: '提租补贴', value: '0' },
  { label: '基础绩效奖', value: '0' },
  { label: '女职工卫生费', value: '0' },
  { label: '其他补', value: '0' },
])

// 津贴项数据
const allowanceItems = ref([
  { label: '考核津贴', value: '0' },
  { label: '效益津贴', value: '0' },
  { label: '在岗津贴', value: '0' },
  { label: '补系数', value: '0' },
  { label: '入岚补', value: '0' },
  { label: '文明奖', value: '0' },
  { label: '专职思政补', value: '0' },
  { label: '专职辅导员补', value: '0' },
  { label: '课题劳务费', value: '0' },
  { label: '其他贴1', value: '0' },
  { label: '其他贴2', value: '0' },
  { label: '其他贴3', value: '0' },
])

// 扣除项数据
const deductItems = ref([
  { label: '医保', value: '0' },
  { label: '失保', value: '0' },
  { label: '公积金', value: '0' },
  { label: '养老保险', value: '0' },
  { label: '职业年金', value: '0' },
  { label: '工会费', value: '0' },
  { label: '房租', value: '0' },
  { label: '水费', value: '0' },
  { label: '电费', value: '0' },
  { label: '网租', value: '0' },
  { label: '第一次扣税', value: '0' },
  { label: '补扣三险两金', value: '0' },
  { label: '其他扣1', value: '0' },
  { label: '扣工资1', value: '0' },
  { label: '其他扣2', value: '0' },
  { label: '扣工资2', value: '0' },
  { label: '第二次扣税', value: '0' },
])

// 年度统计数据
const annualStats = ref([
  { label: '基本工资', percentage: 75, color: '#3b7cff', value: 0 },
  { label: '绩效奖金', percentage: 15, color: '#22c55e', value: 0 },
  { label: '其他收入', percentage: 10, color: '#f59e0b', value: 0 },
])

// 年终奖数据计算
const yearEndBonusItems = computed(() => {
  const items = [
    { label: '补贴一', value: salaryData.value.incomeItems.补贴一 || '0' },
    { label: '补贴二', value: salaryData.value.incomeItems.补贴二 || '0' },
    { label: '补贴三', value: salaryData.value.incomeItems.补贴三 || '0' },
    { label: '年终应发', value: salaryData.value.incomeItems.年终应发 || '0' },
    { label: '其他扣(年终)', value: salaryData.value.deductItems['其他扣(年终)'] || '0' },
    { label: '扣工资(年终)', value: salaryData.value.deductItems['扣工资(年终)'] || '0' },
    { label: '年终计税', value: salaryData.value.deductItems.年终计税 || '0' },
    { label: '年终实发', value: salaryData.value.incomeItems.年终实发 || '0' },
  ]
  return items.filter((item) => Number(item.value) > 0)
})

const hasYearEndBonusItems = computed(() => yearEndBonusItems.value.length > 0)

// 添加计算属性来过滤数据
const filteredIncomeItems = computed(() => {
  return incomeItems.value.filter((item) => Number(item.value) > 0)
})

const filteredAllowanceItems = computed(() => {
  return allowanceItems.value.filter((item) => Number(item.value) > 0)
})

const filteredDeductItems = computed(() => {
  return deductItems.value.filter((item) => Number(item.value) > 0)
})

// 检查是否有数据的计算属性
const hasIncomeItems = computed(() => filteredIncomeItems.value.length > 0)
const hasAllowanceItems = computed(() => filteredAllowanceItems.value.length > 0)
const hasDeductItems = computed(() => filteredDeductItems.value.length > 0)

// 计算月度统计数据
const monthlyStats = computed(() => {
  // 当月收入
  const currentMonthIncome = totalSalary.value

  // 获取上月数据
  const lastMonthIndex = currentMonth.value - 1 || 12
  const lastMonthYear = lastMonthIndex === 12 ? currentYear.value - 1 : currentYear.value
  const lastMonthKey = `${lastMonthYear}_${lastMonthIndex}`

  // 计算与上月的差额
  let diffFromLastMonth = '0.00'
  if (yearSalaryData.value[lastMonthKey]) {
    const lastMonthTotal = calculateMonthlyTotal(yearSalaryData.value[lastMonthKey])
    const diff = Number(currentMonthIncome) - lastMonthTotal
    diffFromLastMonth = diff.toFixed(2)
  }

  // 计算月平均
  let monthlyAverage = '0.00'
  if (Object.keys(yearSalaryData.value).length > 0) {
    const totalYear = Object.values(yearSalaryData.value).reduce(
      (sum, item) => sum + calculateMonthlyTotal(item),
      0,
    )
    monthlyAverage = (totalYear / Object.keys(yearSalaryData.value).length).toFixed(2)
  }

  return {
    currentMonthIncome,
    diffFromLastMonth,
    monthlyAverage,
  }
})

// 初始化趋势图表
const initTrendChart = async () => {
  if (!trendChartRef.value) return
  const chart = await trendChartRef.value.init(echarts)

  // 使用固定颜色值
  const primaryColor = '#3b7cff'

  // 计算趋势数据
  const { months, salaryValues } = calculateTrendData()

  const option = {
    tooltip: {
      trigger: 'axis',
      confine: true,
      formatter: function (params: any) {
        const dataIndex = params[0].dataIndex
        return `${months[dataIndex]}: ¥${salaryValues[dataIndex].toFixed(2)}`
      },
    },
    grid: {
      left: 20,
      right: 20,
      bottom: 15,
      top: 40,
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: months.length > 0 ? months : ['1月', '2月', '3月', '4月', '5月', '6月'],
        axisLine: {
          lineStyle: {
            color: '#999999',
          },
        },
        axisLabel: {
          color: '#666666',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#999999',
          },
        },
        axisLabel: {
          color: '#666666',
          formatter: function (value: number) {
            return `¥${value}`
          },
        },
      },
    ],
    series: [
      {
        type: 'line',
        data: salaryValues.length > 0 ? salaryValues : [0, 0, 0, 0, 0, 0],
        itemStyle: {
          color: primaryColor,
        },
        label: {
          show: true,
          position: 'top',
          formatter: function (params: any) {
            return `¥${params.value.toFixed(2)}`
          },
        },
        lineStyle: {
          width: 3,
          shadowColor: 'rgba(0,0,0,0.2)',
          shadowBlur: 6,
        },
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
      },
    ],
  }
  chart.setOption(option)
}

// 初始化饼图
const initPieChart = async () => {
  if (!pieChartRef.value) return
  const chart = await pieChartRef.value.init(echarts)

  // 更新年度统计数据
  annualStats.value = calculateAnnualStats()

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: function (params: any) {
        const item = annualStats.value.find((item) => item.label === params.name)
        return `${params.name}: ¥${item ? item.value.toFixed(2) : 0} (${params.percent}%)`
      },
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '50%'],
        data: annualStats.value.map((item) => ({
          value: item.percentage,
          name: item.label,
          itemStyle: { color: item.color },
        })),
        label: {
          show: false,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  }
  chart.setOption(option)
}

// 更新页面数据
const updatePageData = () => {
  // 更新收入项
  incomeItems.value = incomeItems.value.map((item) => ({
    ...item,
    value: salaryData.value.incomeItems[item.label as keyof SalaryDetail['incomeItems']] || '0',
  }))

  // 更新津贴项
  allowanceItems.value = allowanceItems.value.map((item) => ({
    ...item,
    value: salaryData.value.incomeItems[item.label as keyof SalaryDetail['incomeItems']] || '0',
  }))

  // 更新扣除项
  deductItems.value = deductItems.value.map((item) => ({
    ...item,
    value: salaryData.value.deductItems[item.label as keyof SalaryDetail['deductItems']] || '0',
  }))
}

// 监听数据变化，更新页面
watch(
  () => salaryData.value,
  () => {
    updatePageData()
  },
  { deep: true },
)

// 初始化加载
onMounted(() => {
  getSalaryData()
})
</script>

<style lang="scss">
/* 在page和uni-page-body中都定义变量，确保在H5和小程序中都能正常工作 */
page,
.salary-info {
  --custom-primary: #3b7cff; // 主色调，明亮的蓝色
  --custom-success: #22c55e; // 成功色，清新的绿色
  --custom-danger: #ef4444; // 危险色，活力的红色
  --custom-warning: #f59e0b; // 警告色，温暖的橙色
  --custom-text-color: #374151; // 主要文本颜色
  --custom-text-color-secondary: #6b7280; // 次要文本颜色
  --custom-border-color: #e5e7eb; // 边框颜色
  --custom-background: #f9fafb; // 背景色
  --custom-card-background: #ffffff; // 卡片背景色
}

// 隐藏日期选择器，但保留功能
.hidden-picker {
  position: absolute;
  z-index: -1;
  width: 0;
  height: 0;
  overflow: hidden;
  opacity: 0;
}

// 修改日期选择器样式，让它更明显
.salary-date-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8rpx 16rpx;
  margin-bottom: 8rpx;
  background-color: rgba(59, 124, 255, 0.05);
  border: 1px solid rgba(59, 124, 255, 0.15);
  border-radius: 8rpx;

  .salary-month {
    display: block;
    font-size: 28rpx;
    font-weight: 500;
    color: var(--custom-text-color);
  }

  .arrow-control {
    display: flex;
    align-items: center;
    padding: 4rpx 10rpx;
    color: var(--custom-primary);
    cursor: pointer;

    &:active {
      opacity: 0.7;
    }

    .control-tip {
      margin-left: 4rpx;
      font-size: 22rpx;
    }
  }
}

// 自定义图标字体
@font-face {
  font-family: 'custom-icon';
  font-style: normal;
  font-weight: normal;
  src: url('data:font/truetype;charset=utf-8;base64,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');
  font-display: swap;
}

// 替换原有的wot-变量为自定义变量
.salary-info {
  min-height: 100vh;
  padding: 16rpx;
  background-color: var(--custom-background);
}

// 自定义卡片样式
.card {
  padding: 24rpx;
  margin-bottom: 16rpx;
  background-color: var(--custom-card-background);
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

// 卡片标题样式
.card-title {
  position: relative;
  padding-left: 20rpx;
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: var(--custom-text-color);

  &::before {
    position: absolute;
    top: 6rpx;
    left: 0;
    width: 8rpx;
    height: 28rpx;
    content: '';
    background-color: var(--custom-primary);
    border-radius: 4rpx;
  }
}

// 自定义按钮样式
.custom-button {
  width: 100%;
  height: 80rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  color: #ffffff;
  text-align: center;
  background-color: var(--custom-primary);
  border: none;
  border-radius: 40rpx;

  &:active {
    opacity: 0.9;
  }
}

// 图标样式
.icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(59, 124, 255, 0.1);
  border-radius: 50%;
}

.icon {
  font-family: 'custom-icon';
  font-size: 36rpx;
  color: var(--custom-primary);
}

.salary-card {
  margin-bottom: 24rpx;
}

.salary-overview {
  .salary-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32rpx;

    .salary-date-selector {
      display: flex;
      gap: 12rpx;
      align-items: center;
      margin-bottom: 4rpx;
    }

    .salary-month {
      display: block;
      font-size: 28rpx;
      color: var(--custom-text-color-secondary);
    }

    .salary-amount {
      display: block;
      margin-top: 8rpx;
      font-size: 48rpx;
      font-weight: 600;
    }
  }

  .salary-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 32rpx;
  }
}

.stat-item {
  text-align: center;

  .amount {
    display: block;
    font-size: 28rpx;
    font-weight: 600;

    &.primary {
      color: var(--custom-primary);
    }

    &.success {
      color: var(--custom-success);
    }
  }

  .label {
    display: block;
    margin-top: 8rpx;
    font-size: 24rpx;
    color: var(--custom-text-color-secondary);
  }
}

.chart-container {
  width: 100%;
  height: 600rpx;
  margin-bottom: 32rpx;
}

.trend-stats {
  display: flex;
  justify-content: space-between;
  padding: 0 32rpx;
}

.salary-detail {
  .detail-section {
    margin-bottom: 40rpx;

    .section-title {
      display: flex;
      align-items: center;
      margin-bottom: 24rpx;
      font-size: 28rpx;
      font-weight: 500;

      .section-icon {
        margin-right: 8rpx;
      }

      &.success {
        color: var(--custom-success);
      }

      &.danger {
        color: var(--custom-danger);
      }

      &.primary {
        color: var(--custom-primary);
      }
    }
  }

  .detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16rpx;

    .item-label {
      color: var(--custom-text-color);
    }

    .item-value {
      font-weight: 500;
    }
  }

  .total-salary {
    display: flex;
    justify-content: space-between;
    padding-top: 32rpx;
    margin-top: 32rpx;
    border-top: 2rpx dashed var(--custom-border-color);

    .total-label {
      font-size: 32rpx;
      font-weight: 500;
    }

    .total-amount {
      font-size: 32rpx;
      font-weight: 600;
      color: var(--custom-primary);
    }
  }
}

.annual-stats {
  display: flex;
  margin-bottom: 40rpx;

  .pie-chart {
    width: 400rpx;
    height: 400rpx;
  }

  .pie-legend {
    flex: 1;
    padding-top: 40rpx;
    margin-left: 40rpx;

    .legend-item {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .legend-color {
        width: 24rpx;
        height: 24rpx;
        margin-right: 16rpx;
        border-radius: 4rpx;
      }

      .legend-label {
        flex: 1;
        color: var(--custom-text-color);
      }

      .legend-value {
        font-weight: 500;
      }
    }
  }
}

.annual-period {
  font-size: 24rpx;
  color: var(--custom-text-color-secondary);
  text-align: center;
}

.detail-empty {
  padding: 20rpx 0;
  font-size: 26rpx;
  color: var(--custom-text-color-secondary);
  text-align: center;
}
</style>
