<route lang="json5">
{
  style: {
    navigationBarTitleText: '出入校记录',
  },
}
</route>
<template>
  <view
    class="transit-container bg-gray-100 p-4 pb-4 pt-2 box-border w-full"
    style="min-height: 97vh"
  >
    <!-- 日期和筛选条件 -->
    <view class="sticky-top z-10 mb-4">
      <view class="flex items-center justify-between gap-3">
        <!-- 日期选择器 -->
        <view class="w-48">
          <view
            class="date-picker-container bg-white rounded-lg px-3 py-1.5 shadow-sm flex items-center flex-1 min-w-0"
            @click="showDatePicker = true"
          >
            <wd-icon name="calendar" class="text-gray-500 mr-2 flex-shrink-0" />
            <text class="text-sm font-medium mr-2 truncate flex-1">
              {{ queryParams.passTime ? currentDate : '全部日期' }}
            </text>
            <view class="flex items-center">
              <wd-icon
                v-if="queryParams.passTime"
                name="close-circle"
                class="text-xs text-gray-500 mr-1 flex-shrink-0"
                @click.stop="clearDateFilter"
              />
              <wd-icon name="arrow-down" class="text-xs text-gray-500 flex-shrink-0" />
            </view>
          </view>
        </view>

        <!-- 出入类型筛选 -->
        <view class="flex items-center max-w-40">
          <text class="text-sm font-medium mr-2 flex-shrink-0">筛选：</text>
          <view
            class="filter-tag bg-white rounded-lg px-3 py-1.5 shadow-sm flex items-center flex-1 min-w-0"
            @click="showFilterSheet = true"
          >
            <text class="text-sm font-medium mr-2 truncate flex-1">
              {{ getSelectedFilterName() }}
            </text>
            <wd-icon name="filter" class="text-xs text-gray-500 flex-shrink-0" />
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="pagination.loading" class="flex justify-center items-center py-10">
      <wd-loading color="#3b82f6" />
    </view>

    <!-- 内容区域 -->
    <view v-else class="w-full">
      <!-- 当前状态 -->
      <!-- <view class="status-card mb-4">
        <view class="flex p-4 items-center">
          <view class="icon-container mr-4">
            <wd-icon name="check" color="#2979ff" />
          </view>
          <view class="flex-1">
            <view class="flex justify-between items-center">
              <text class="font-semibold">当前位置</text>
              <text class="text-xs text-color-desc">{{ currentStatus.updateTime }}</text>
            </view>
            <view class="flex items-center mt-1">
              <text class="location-tag mr-2">{{ currentStatus.location }}</text>
              <text class="text-sm text-color-desc">{{ currentStatus.detail }}</text>
            </view>
          </view>
        </view>
      </view> -->

      <!-- 出入校统计 -->
      <view class="stats-container mb-4">
        <view class="stats-item mr-2">
          <text class="stats-title">今日出入</text>
          <text class="stats-value">{{ accessStats.today.total }}次</text>
          <text class="stats-desc">
            {{ accessStats.today.in }}进 / {{ accessStats.today.out }}出
          </text>
        </view>
        <view class="stats-item mx-2">
          <text class="stats-title">本周出入</text>
          <text class="stats-value">{{ accessStats.week.total }}次</text>
          <text class="stats-desc">
            较上周
            <text class="text-color-primary">↓{{ Math.abs(accessStats.week.change) }}%</text>
          </text>
        </view>
        <view class="stats-item ml-2">
          <text class="stats-title">平均每日</text>
          <text class="stats-value">{{ accessStats.average.daily }}次</text>
          <text class="stats-desc">{{ accessStats.average.status }}</text>
        </view>
      </view>

      <!-- 今日出入校记录 -->
      <view class="record-list mb-4">
        <view v-if="accessRecords.length === 0" class="empty-container">
          <wd-icon name="info-circle" size="36px" class="text-gray-400 mb-3" />
          <text class="empty-text">暂无出入校记录</text>
        </view>
        <view v-else v-for="record in accessRecords" :key="record.id" class="record-item">
          <view
            :class="[
              'record-icon',
              record.entryExitType === '进' ? 'record-icon-in' : 'record-icon-out',
            ]"
          >
            <wd-icon
              :name="getRecordIcon(record.entryExitType)"
              :color="record.entryExitType === '进' ? '#2979ff' : '#ff5252'"
            />
          </view>
          <view class="flex-1">
            <view class="flex justify-between">
              <text class="font-medium">
                {{ record.entryExitType === '进' ? '进入校园' : '离开校园' }}
              </text>
              <text class="text-sm text-color-desc">{{ formatDateTime(record.passTime) }}</text>
            </view>
            <view class="flex justify-between mt-1">
              <text class="text-xs text-color-desc">{{ record.passGateName }}</text>
              <text class="text-xs text-color-desc">{{ record.passCredentialName }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 出入校趋势 -->
      <view class="chart-card mb-4">
        <view class="chart-header">本周出入校趋势</view>
        <view class="chart-content">
          <view class="chart-container">
            <l-echart ref="chartRef"></l-echart>
          </view>
        </view>
      </view>

      <!-- 异常提醒设置 -->
    </view>

    <!-- 筛选弹窗 -->
    <wd-popup v-model="showFilterSheet" position="bottom" round>
      <view class="semester-picker">
        <view class="picker-header">
          <text class="picker-title">选择筛选条件</text>
          <wd-icon name="close" size="32rpx" @click="showFilterSheet = false"></wd-icon>
        </view>
        <scroll-view class="picker-content" scroll-y>
          <view
            v-for="item in filterOptions"
            :key="item.value"
            :class="[
              'picker-item',
              {
                active: queryParams.entryExitType === item.value,
              },
            ]"
            @click="selectFilter(item.value)"
          >
            <text class="item-label">{{ item.label }}</text>
            <wd-icon
              v-if="queryParams.entryExitType === item.value"
              name="check"
              size="32rpx"
              color="#3a8eff"
            ></wd-icon>
          </view>
        </scroll-view>
      </view>
    </wd-popup>

    <!-- 日期选择弹窗 -->
    <wd-popup v-model="showDatePicker" position="bottom" closable close-on-click-modal round>
      <view class="calendar-container">
        <view class="picker-header">
          <view class="picker-title">请选择日期</view>
        </view>
        <wd-calendar-view
          v-model="selectedDate"
          type="date"
          :min-date="sixMonthsAgo"
          :max-date="today"
          @change="onDateChange"
        />
      </view>
    </wd-popup>
  </view>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeMount, computed } from 'vue'
import * as echarts from 'echarts'
import { getTransitList } from '@/service/transit'
import type { TransitQuery, TransitItem } from '@/types/transit'
import { formatDate, getWeekDay } from '@/utils/date'

// 查询参数
const queryParams = reactive<TransitQuery>({
  page: 1,
  pageSize: 10,
  entryExitType: '',
  passCredentialName: '',
  passTime: '', // 默认为空，表示不限制日期
  passGateName: '',
})

// 分页信息
const pagination = reactive({
  total: 0,
  loading: false,
})

// 日期相关数据
const currentDate = ref(formatDate(new Date(), 'yyyy年MM月dd日'))
const weekDay = ref(getWeekDay(new Date()))
const showDatePicker = ref(false)
const selectedDate = ref(new Date().getTime())
const today = new Date().getTime()
const sixMonthsAgo = new Date(new Date().setFullYear(new Date().getFullYear() - 10)).getTime()

// 出入校记录列表
const accessRecords = ref<TransitItem[]>([])

// 筛选相关
const showFilterSheet = ref(false)
const filterOptions = [
  { label: '全部', value: '' },
  { label: '进入校园', value: '进' },
  { label: '离开校园', value: '出' },
]

// 获取筛选名称
const getSelectedFilterName = () => {
  if (!queryParams.entryExitType) return '全部'
  return queryParams.entryExitType === '进' ? '进入校园' : '离开校园'
}

// 选择筛选条件
const selectFilter = (value: string) => {
  queryParams.entryExitType = value
  showFilterSheet.value = false
  getRecords()
}

// 清除日期筛选
const clearDateFilter = (e: Event) => {
  e.stopPropagation() // 阻止事件冒泡，避免触发日期选择器
  queryParams.passTime = ''
  currentDate.value = '全部日期'
  getRecords()
}

// 日期选择变更
const onDateChange = ({ value }) => {
  const date = new Date(value)
  currentDate.value = formatDate(date, 'yyyy年MM月dd日')
  weekDay.value = getWeekDay(date)

  // 更新查询参数中的日期
  queryParams.passTime = formatDate(date, 'yyyy-MM-dd')

  // 关闭日期选择弹窗并获取数据
  showDatePicker.value = false
  getRecords()
}

// 解析URL参数
const parseUrlParams = (query: Record<string, string>) => {
  if (query.entryExitType) {
    queryParams.entryExitType = query.entryExitType
  }
  if (query.passCredentialName) {
    queryParams.passCredentialName = query.passCredentialName
  }
  if (query.passTime) {
    queryParams.passTime = query.passTime
  }
  if (query.passGateName) {
    queryParams.passGateName = query.passGateName
  }
  if (query.page) {
    queryParams.page = parseInt(query.page, 10) || 1
  }
  if (query.pageSize) {
    queryParams.pageSize = parseInt(query.pageSize, 10) || 10
  }
}

// 获取出入校记录
const getRecords = async () => {
  try {
    pagination.loading = true
    const res = await getTransitList(queryParams)
    accessRecords.value = res.items
    pagination.total = res.total
    // 计算统计数据
    calculateStats()
    console.log('出入校记录:', res)

    // 在数据加载完成后初始化图表
    initChart()
  } catch (error) {
    console.error('获取出入校记录失败:', error)
  } finally {
    pagination.loading = false
  }
}

// 出入校统计数据
const accessStats = ref({
  today: {
    total: 0,
    in: 0,
    out: 0,
  },
  week: {
    total: 0,
    change: 0,
  },
  average: {
    daily: 0,
    status: '正常范围内',
  },
})

// 当前位置状态
const currentStatus = ref({
  location: '校内',
  detail: '学生在校内 - 计算机楼',
  updateTime: '15分钟前更新',
})

// 异常提醒设置
const settings = ref({
  abnormalAlert: true,
  realTimeNotification: false,
})

// 切换设置状态
const toggleSetting = (key: 'abnormalAlert' | 'realTimeNotification') => {
  settings.value[key] = !settings.value[key]
}

// 计算统计数据
const calculateStats = () => {
  if (accessRecords.value.length === 0) {
    // 没有记录时设置默认值
    accessStats.value = {
      today: { total: 0, in: 0, out: 0 },
      week: { total: 0, change: 0 },
      average: { daily: 0, status: '暂无数据' },
    }
    return
  }

  // 获取日期格式化工具
  const formatYMD = (dateStr: string) => {
    // 处理两种可能的日期格式: "2025-01-03 17:25:45" 或 "2025-01-03"
    return dateStr.split(' ')[0]
  }

  // 计算当前日期
  const today = new Date()
  const todayStr = formatDate(today, 'yyyy-MM-dd')

  // 计算一周前和两周前的日期
  const oneWeekAgo = new Date(today)
  oneWeekAgo.setDate(today.getDate() - 7)
  const oneWeekAgoStr = formatDate(oneWeekAgo, 'yyyy-MM-dd')

  const twoWeeksAgo = new Date(today)
  twoWeeksAgo.setDate(today.getDate() - 14)
  const twoWeeksAgoStr = formatDate(twoWeeksAgo, 'yyyy-MM-dd')

  // 对记录按日期分组
  const dateRecords = new Map<string, TransitItem[]>()
  accessRecords.value.forEach((record) => {
    const recordDate = formatYMD(record.passTime || record.passDate)
    if (!dateRecords.has(recordDate)) {
      dateRecords.set(recordDate, [])
    }
    dateRecords.get(recordDate)?.push(record)
  })

  // 获取所有不同的日期并排序
  const allDates = Array.from(dateRecords.keys()).sort()
  console.log('所有记录日期:', allDates)

  // 1. 今日出入统计
  const todayRecords = dateRecords.get(todayStr) || []
  accessStats.value.today.total = todayRecords.length
  accessStats.value.today.in = todayRecords.filter((item) => item.entryExitType === '进').length
  accessStats.value.today.out = todayRecords.filter((item) => item.entryExitType === '出').length

  // 2. 本周出入统计（近7天）
  let thisWeekTotal = 0
  let lastWeekTotal = 0

  allDates.forEach((date) => {
    const records = dateRecords.get(date) || []

    // 本周数据 (>= 一周前的日期)
    if (date >= oneWeekAgoStr) {
      thisWeekTotal += records.length
    }
    // 上周数据 (>= 两周前且 < 一周前)
    else if (date >= twoWeeksAgoStr && date < oneWeekAgoStr) {
      lastWeekTotal += records.length
    }
  })

  accessStats.value.week.total = thisWeekTotal

  // 3. 计算周变化率
  if (lastWeekTotal > 0) {
    const change = ((thisWeekTotal - lastWeekTotal) / lastWeekTotal) * 100
    accessStats.value.week.change = parseInt(change.toFixed(0))
  } else {
    accessStats.value.week.change = 0
  }

  // 4. 计算平均每日出入次数
  if (allDates.length > 0) {
    const totalRecords = accessRecords.value.length
    const avgDaily = totalRecords / allDates.length
    accessStats.value.average.daily = parseFloat(avgDaily.toFixed(1))

    // 根据平均值设置状态
    if (avgDaily <= 1) {
      accessStats.value.average.status = '低频出入'
    } else if (avgDaily <= 3) {
      accessStats.value.average.status = '正常范围内'
    } else {
      accessStats.value.average.status = '高频出入'
    }
  } else {
    accessStats.value.average.daily = 0
    accessStats.value.average.status = '暂无数据'
  }
}

// 格式化时间（只显示时分秒）
const formatTime = (dateTimeStr: string) => {
  if (!dateTimeStr) return ''
  const date = new Date(dateTimeStr)
  return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', second: '2-digit' })
}

// 格式化日期时间（显示年月日和时分秒）
const formatDateTime = (dateTimeStr: string) => {
  if (!dateTimeStr) return ''
  const date = new Date(dateTimeStr)
  return (
    formatDate(date, 'yyyy-MM-dd') +
    ' ' +
    date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit', second: '2-digit' })
  )
}

// 获取记录图标
const getRecordIcon = (type: string) => {
  return type === '进' ? 'check' : 'close'
}

// ECharts图表相关
const chartRef = ref()

// 图表配置
const getChartOption = () => {
  // 获取API返回数据中的日期范围
  const recordDates = new Set<string>()

  // 从记录中收集所有日期
  accessRecords.value.forEach((record) => {
    const recordDate = record.passTime?.split(' ')[0] || record.passDate
    if (recordDate) {
      recordDates.add(recordDate)
    }
  })

  // 转换为数组并排序
  const days = Array.from(recordDates).sort()
  console.log('所有记录日期:', days)

  // 如果没有记录，生成最近7天的日期作为默认值
  if (days.length === 0) {
    const today = new Date()
    for (let i = 6; i >= 0; i--) {
      const day = new Date(today)
      day.setDate(today.getDate() - i)
      days.push(formatDate(day, 'yyyy-MM-dd'))
    }
  }

  // 为每个日期生成标签
  const dayLabels = days.map((dateStr) => {
    const date = new Date(dateStr)
    const weekdayIndex = date.getDay() // 0是周日，1是周一
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
    return `${dateStr.slice(5)} ${weekdays[weekdayIndex]}` // 只显示MM-DD 周几
  })

  // 初始化数据数组
  const inData = Array(days.length).fill(0)
  const outData = Array(days.length).fill(0)

  // 统计每天的进出记录
  accessRecords.value.forEach((record) => {
    // 提取日期部分（处理不同可能的日期格式）
    const recordDate = record.passTime?.split(' ')[0] || record.passDate
    if (!recordDate) return

    // 查找日期在days数组中的索引
    const dayIndex = days.indexOf(recordDate)

    if (dayIndex !== -1) {
      // 统计进出次数
      if (record.entryExitType === '进') {
        inData[dayIndex]++
      } else if (record.entryExitType === '出') {
        outData[dayIndex]++
      }
    }
  })

  console.log('图表数据 - 日期标签:', dayLabels)
  console.log('图表数据 - 进入:', inData)
  console.log('图表数据 - 离开:', outData)

  return {
    tooltip: {
      trigger: 'axis',
      confine: true,
    },
    grid: {
      left: 20,
      right: 20,
      bottom: 15,
      top: 40,
      containLabel: true,
    },
    legend: {
      data: ['进入', '离开'],
      bottom: 0,
    },
    xAxis: [
      {
        type: 'category',
        data: dayLabels,
        axisLine: {
          lineStyle: {
            color: '#999999',
          },
        },
        axisLabel: {
          color: '#666666',
          rotate: 30,
          fontSize: 11,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#999999',
          },
        },
        axisLabel: {
          color: '#666666',
        },
      },
    ],
    series: [
      {
        name: '进入',
        type: 'bar',
        stack: 'access',
        itemStyle: {
          color: '#2979ff',
        },
        data: inData,
      },
      {
        name: '离开',
        type: 'bar',
        stack: 'access',
        itemStyle: {
          color: '#ff5252',
        },
        data: outData,
      },
    ],
  }
}

// 初始化图表
const initChart = () => {
  setTimeout(async () => {
    try {
      if (!chartRef.value) {
        console.error('图表容器不存在')
        return
      }

      console.log('开始初始化图表')
      const myChart = await chartRef.value.init(echarts)
      const option = getChartOption()
      console.log('图表配置:', option)
      myChart.setOption(option)
      console.log('图表初始化成功')
    } catch (error) {
      console.error('图表初始化失败:', error)
    }
  }, 500) // 增加延迟时间确保DOM已渲染
}

// 页面加载前解析URL参数
onBeforeMount(() => {
  // 在uni-app中，获取页面参数可以使用uni.getLaunchOptionsSync()
  // 这里暂时模拟一个空对象
  const query = {} as Record<string, string>
  if (Object.keys(query).length > 0) {
    parseUrlParams(query)
  }
})

// 初始化图表和数据
onMounted(() => {
  getRecords()
})
</script>

<style scoped lang="scss">
.transit-container {
  min-height: 97vh;
}

.sticky-top {
  // position: sticky;
  top: 0;
  z-index: 10;
  width: 100%;
  padding: 12rpx 0;
}

.filter-tag {
  transition: all 0.3s;
}

.filter-tag:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.date-picker-container {
  display: flex;
  align-items: center;
  transition: all 0.3s;
}

.date-picker-container:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.status-card {
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(41, 121, 255, 0.1);
  border-radius: 50%;
}

.location-tag {
  padding: 4rpx 16rpx;
  font-size: 24rpx;
  color: #2979ff;
  background-color: rgba(41, 121, 255, 0.1);
  border-radius: 30rpx;
}

.stats-container {
  display: flex;
}

.stats-item {
  display: flex;
  flex: 1;
  flex-direction: column;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.stats-title {
  font-size: 24rpx;
  color: #999;
}

.stats-value {
  margin: 6rpx 0;
  font-size: 36rpx;
  font-weight: 600;
}

.stats-desc {
  font-size: 24rpx;
  color: #999;
}

.record-header {
  padding: 0 10rpx;
  font-size: 28rpx;
  color: #666;
}

.record-list {
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.record-item {
  display: flex;
  padding: 20rpx;
  border-bottom: 1px solid #f5f5f5;
}

.record-item:last-child {
  border-bottom: none;
}

.record-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 70rpx;
  height: 70rpx;
  margin-right: 20rpx;
  border-radius: 50%;
}

.record-icon-in {
  background-color: rgba(41, 121, 255, 0.1);
}

.record-icon-out {
  background-color: rgba(255, 82, 82, 0.1);
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-text,
.empty-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

.chart-card {
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.chart-header {
  padding: 20rpx;
  font-size: 28rpx;
  font-weight: 500;
  border-bottom: 1px solid #f5f5f5;
}

.chart-content {
  padding: 20rpx;
}

.chart-container {
  width: 100%;
  height: 600rpx;
  background-color: #fff;
}

.settings-list {
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.settings-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 26rpx 20rpx;
  border-bottom: 1px solid #f5f5f5;
}

.settings-item:last-child {
  border-bottom: none;
}

.settings-title {
  font-size: 28rpx;
}

.text-color-desc {
  color: #999;
}

.text-color-primary {
  color: #2979ff;
}

// 底部弹窗样式
.semester-picker {
  max-height: 70vh;
  padding: 32rpx;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.picker-content {
  max-height: calc(70vh - 100rpx);
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background: #f7f8fa;
  border-radius: 12rpx;
  transition: all 0.3s;
}

.picker-item:last-child {
  margin-bottom: 0;
}

.picker-item.active {
  color: #3a8eff;
  background: rgba(58, 142, 255, 0.1);
}

.item-label {
  font-size: 28rpx;
}

.calendar-container {
  max-height: 80vh;
  padding: 32rpx 32rpx 0 32rpx;
  overflow: hidden;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;

  .picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20rpx;
  }

  .picker-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
}
</style>
