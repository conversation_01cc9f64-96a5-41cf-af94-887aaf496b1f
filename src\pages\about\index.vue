<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '我的',
  },
}
</route>

<template>
  <view class="about-container" :style="{ paddingTop: safeAreaInsets?.top + 'px' }">
    <!-- 个人资料头部 -->
    <view class="profile-header">
      <view class="profile-info">
        <view class="profile-avatar">
          <image
            v-if="userStore.userInfo.avatar"
            :src="userStore.userInfo.avatar"
            mode="aspectFill"
            @error="avatarLoadError = true"
            :class="{ hidden: avatarLoadError }"
          />
          <wd-icon v-if="!userStore.userInfo.avatar || avatarLoadError" name="user" />
        </view>
        <view class="user-info">
          <text class="profile-name">{{ userStore.userInfo.realname || '未设置姓名' }}</text>
          <text class="profile-title">
            {{ getUserRoleInfo() }}
          </text>
        </view>
      </view>
      <view class="profile-edit" @tap="navigateToUserInfo()">
        <wd-icon name="edit" size="22px" color="#fff"></wd-icon>
      </view>
    </view>

    <!-- 个人统计信息 -->
    <view class="profile-stats">
      <view class="stat-item" v-for="(stat, index) in userStats" :key="index">
        <text class="stat-value">{{ stat.value }}</text>
        <text class="stat-label">{{ stat.label }}</text>
      </view>
    </view>
    <!-- 功能模块 - 根据用户类型动态显示 -->
    <template v-for="(section, sectionIndex) in userSections" :key="sectionIndex">
      <view class="list-section">
        <view class="section-header">{{ section.title }}</view>
        <view
          class="list-item"
          v-for="(item, index) in section.items"
          :key="index"
          @tap="navigateTo(item.path)"
        >
          <view class="item-icon" :class="item.iconClass">
            <wd-icon :name="item.icon" size="20px" color="#fff"></wd-icon>
          </view>
          <view class="item-content">
            <view class="item-title">{{ item.title }}</view>
            <view class="item-subtitle">{{ item.subtitle }}</view>
          </view>
          <wd-icon name="arrow-right" size="16px" color="#C7C7CC"></wd-icon>
        </view>
      </view>
    </template>

    <!-- 应用设置 -->
    <!-- <view class="list-section">
      <view class="section-header">应用设置</view>
      <view
        class="list-item"
        v-for="(item, index) in appSettings"
        :key="index"
        @tap="navigateTo(item.path)"
      >
        <view class="item-icon" :class="item.iconClass">
          <wd-icon :name="item.icon" size="20px" color="#fff"></wd-icon>
        </view>
        <view class="item-content">
          <view class="item-title">{{ item.title }}</view>
          <view class="item-subtitle">{{ item.subtitle }}</view>
        </view>
        <wd-icon name="arrow-right" size="16px" color="#C7C7CC"></wd-icon>
      </view>
    </view> -->

    <!-- 退出登录按钮 -->
    <view class="sign-out-button" @tap="handleLogout">退出登录</view>

    <!-- 版本信息 -->
    <view class="version-info">{{ getVersionInfo() }}</view>

    <!-- 底部安全区域 -->
    <view class="safe-bottom"></view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { useMenuStore } from '@/store/menu'
import { getTeachingTaskListBySemester } from '@/service/teachingTask'
import { getTeacherInfo } from '@/service/teacher'
import type { TeacherInfo } from '@/types/teacher'

const userStore = useUserStore()
const menuStore = useMenuStore()
const { safeAreaInsets } = uni.getSystemInfoSync()
// 头像加载失败标记
const avatarLoadError = ref(false)

// 教师课程数
const teacherCourseCount = ref(0)
// 教师详细信息
const teacherDetailInfo = ref<TeacherInfo>({} as TeacherInfo)

// 获取教师教学任务数量
const fetchTeacherCourseCount = async () => {
  try {
    // 如果不是教师类型，不进行调用
    if (userStore.userInfo.userType !== 2) return

    // 调用接口获取教学任务列表，这里不需要指定学期，获取全部
    const params = {
      page: 1,
      pageSize: 1, // 仅需要total数据，减少数据传输量
    }

    const res = await getTeachingTaskListBySemester(params)
    // 更新教师课程数
    teacherCourseCount.value = res.total || 0
  } catch (error) {
    console.error('获取教师教学任务数量失败:', error)
  }
}

// 获取教师详细信息
const fetchTeacherDetailInfo = async () => {
  try {
    // 如果不是教师类型，不进行调用
    if (userStore.userInfo.userType !== 2) return

    // 调用接口获取教师详细信息
    const res = await getTeacherInfo()
    teacherDetailInfo.value = res
  } catch (error) {
    console.error('获取教师详细信息失败:', error)
  }
}

// 计算教龄
const calculateTeachingAge = () => {
  try {
    // 如果没有entryDate，返回"暂无数据"
    if (!teacherDetailInfo.value?.entryDate) {
      return '暂无数据'
    }

    // 将entryDate转换为Date对象
    const entryDate = new Date(teacherDetailInfo.value.entryDate)
    // 获取当前日期
    const currentDate = new Date()

    // 计算相差的年数
    let years = currentDate.getFullYear() - entryDate.getFullYear()

    // 处理月份和日期，如果当前月份小于入职月份，或者月份相同但日期小于入职日期，则年数减1
    if (
      currentDate.getMonth() < entryDate.getMonth() ||
      (currentDate.getMonth() === entryDate.getMonth() &&
        currentDate.getDate() < entryDate.getDate())
    ) {
      years--
    }

    return `${years}年`
  } catch (error) {
    console.error('计算教龄失败:', error)
    return '暂无数据'
  }
}

// 用户统计数据
const userStats = computed(() => {
  const userType = userStore.userInfo.userType
  if (userType === 2) {
    // 教师统计数据
    return [
      { label: '教龄', value: calculateTeachingAge() },
      { label: '任教课程', value: `${teacherCourseCount.value}门` },
      { label: '班主任班级', value: '0个' },
    ]
  } else if (userType === 1) {
    // 学生统计数据 - 使用可选链和类型断言避免类型错误
    const userInfo = userStore.userInfo as any
    return [
      { label: '已修学分', value: userInfo?.credit || '0' },
      { label: '课程数', value: userInfo?.courseCount || '0' },
      { label: '平均绩点', value: userInfo?.gpa || '0.0' },
    ]
  } else {
    // 默认统计数据
    return []
  }
})

// 教学工作
const teachingWork = [
  {
    title: '教师课表',
    subtitle: '查看我的课程安排',
    icon: 'a-rootlist',
    path: '/pages/teacher/scheduleTable',
    iconClass: 'icon-blue',
  },
  {
    title: '我的调停课',
    subtitle: '调课、停课、补课申请',
    icon: 'edit',
    path: '/pages/teacher/teaching-affairs/mediation-course',
    iconClass: 'icon-green',
  },
  {
    title: '教学日志',
    subtitle: '教学计划和授课记录',
    icon: 'note',
    path: '/pages/teacher/teaching-affairs/teaching-plan',
    iconClass: 'icon-orange',
  },
  {
    title: '教学任务',
    subtitle: '查看分配的教学任务',
    icon: 'file',
    path: '/pages/teacher/teaching-affairs/teaching-tasks',
    iconClass: 'icon-purple',
  },
]

// 教师档案
const teacherProfile = [
  {
    title: '个人信息',
    subtitle: '基本资料和教师档案',
    icon: 'user',
    path: '/pages/Information/teacherInfo',
    iconClass: 'icon-blue',
  },
  {
    title: '我的工资',
    subtitle: '工资明细和收入统计',
    icon: 'money-circle',
    path: '/pages/Salary/salaryInfo',
    iconClass: 'icon-green',
  },
  {
    title: '教工通讯录',
    subtitle: '查找同事联系方式',
    icon: 'phone',
    path: '/pages/Information/teacherContacts',
    iconClass: 'icon-orange',
  },
  {
    title: '非教学工作量',
    subtitle: '科研、行政等工作量',
    icon: 'chart-bar',
    path: '/pages/teacher/teacher-profile/non-workload',
    iconClass: 'icon-purple',
  },
]

// 行政服务
const adminServices = [
  {
    title: '校内邮件',
    subtitle: '收发校内电子邮件',
    icon: 'chat',
    path: '/pages/Mail/mailList',
    iconClass: 'icon-blue',
  },
  {
    title: '通知公告',
    subtitle: '学校和部门最新通知',
    icon: 'notification',
    path: '/pages/teacherNotice',
    iconClass: 'icon-red',
  },
  {
    title: '出入信息',
    subtitle: '校园出入记录查询',
    icon: 'arrow-right',
    path: '/pages/Transit/transitList',
    iconClass: 'icon-green',
  },
  {
    title: '教学工作量',
    subtitle: '工作量统计和核算',
    icon: 'chart-pie',
    path: '/pages/teacher/teaching-affairs/workload',
    iconClass: 'icon-orange',
  },
]

// 学生学习
const studentLearning = [
  {
    title: '我的课表',
    subtitle: '查看每周课程安排',
    icon: 'a-rootlist',
    path: '/pages/student/StudentSchedule/scheduleTable',
    iconClass: 'icon-blue',
  },
  {
    title: '我的选课',
    subtitle: '选课列表和课程信息',
    icon: 'file',
    path: '/pages/student/select-course',
    iconClass: 'icon-green',
  },
  {
    title: '日常成绩',
    subtitle: '课程日常评分和考核',
    icon: 'check-rectangle',
    path: '/pages/student/score',
    iconClass: 'icon-orange',
  },
  {
    title: '总评成绩',
    subtitle: '学期总评和绩点统计',
    icon: 'chart-bar',
    path: '/pages/student/total-score',
    iconClass: 'icon-purple',
  },
]

// 学生服务
const studentServices = [
  {
    title: '我的考勤',
    subtitle: '出勤记录和统计',
    icon: 'time',
    path: '/pages/attendance',
    iconClass: 'icon-orange',
  },
  {
    title: '我的请假',
    subtitle: '请假申请和审批状态',
    icon: 'note',
    path: '/pages/myLeave',
    iconClass: 'icon-red',
  },
  {
    title: '校内邮箱',
    subtitle: '校内邮件收发管理',
    icon: 'chat',
    path: '/pages/Mail/mailList',
    iconClass: 'icon-blue',
  },
  {
    title: '通知公告',
    subtitle: '学校和院系最新通知',
    icon: 'notification',
    path: '/pages/student/notice',
    iconClass: 'icon-green',
  },
]

// 学籍与发展
const studentDevelopment = [
  {
    title: '个人信息',
    subtitle: '基本资料和学籍信息',
    icon: 'user',
    path: '/pages/student/my-info',
    iconClass: 'icon-blue',
  },
  {
    title: '班级同学',
    subtitle: '我的班级成员信息',
    icon: 'usergroup',
    path: '/pages/student-info/my-classmates',
    iconClass: 'icon-green',
  },
  {
    title: '证书信息',
    subtitle: '技能证书和获奖情况',
    icon: 'check-bold',
    path: '/pages/my-certificate',
    iconClass: 'icon-purple',
  },
]

// 个人信息
const personalInfo = [
  {
    title: '基本信息',
    subtitle: '工号、职称、学历等',
    icon: 'user',
    path: '/pages/profile/basic',
    iconClass: 'icon-green',
  },
  {
    title: '账号安全',
    subtitle: '密码修改、手机绑定、人脸识别',
    icon: 'lock-on',
    path: '/pages/profile/security',
    iconClass: 'icon-blue',
  },
  {
    title: '消息通知',
    subtitle: '通知方式、提醒设置',
    icon: 'notification',
    path: '/pages/profile/notifications',
    iconClass: 'icon-red',
  },
]

// 应用设置
const appSettings = [
  {
    title: '主题设置',
    subtitle: '深色模式、主题颜色',
    icon: 'setting',
    path: '/pages/settings/theme',
    iconClass: 'icon-blue',
  },
  {
    title: '帮助与反馈',
    subtitle: '使用指南、问题反馈',
    icon: 'help',
    path: '/pages/settings/help',
    iconClass: 'icon-orange',
  },
  {
    title: '关于我们',
    subtitle: '版本信息、隐私政策',
    icon: 'info-circle',
    path: '/pages/about/info',
    iconClass: 'icon-purple',
  },
]

// 根据用户类型显示不同的功能部分
const userSections = computed(() => {
  const userType = userStore.userInfo.userType

  if (userType === 2) {
    // 教师用户
    return [
      { title: '教学事务', items: teachingWork },
      { title: '教师档案', items: teacherProfile },
      { title: '行政与服务', items: adminServices },
    ]
  } else if (userType === 1) {
    // 学生用户
    return [
      { title: '课业学习', items: studentLearning },
      { title: '考评服务', items: studentServices },
      { title: '学籍与发展', items: studentDevelopment },
    ]
  } else {
    // 默认用户
    return [{ title: '个人信息', items: personalInfo }]
  }
})

// 页面跳转
const navigateTo = (path: string) => {
  uni.navigateTo({ url: path })
}

// 获取用户角色信息
const getUserRoleInfo = () => {
  const userType = userStore.userInfo.userType
  if (userType === 2) {
    // 教师
    return userStore.userInfo.department || userStore.userInfo.jobName || '教师'
  } else if (userType === 1) {
    // 学生
    return userStore.userInfo.className || userStore.userInfo.department || '学生'
  } else {
    // 默认
    return userStore.userInfo.department || userStore.userInfo.roleName || '用户'
  }
}

// 跳转到用户信息页面，根据用户类型决定跳转路径
const navigateToUserInfo = () => {
  // 用户类型 1: 学生, 2: 教师
  const userType = userStore.userInfo.userType
  if (userType === 2) {
    // 教师
    navigateTo('/pages/Information/teacherInfo')
  } else if (userType === 1) {
    // 学生
    navigateTo('/pages/student/my-info')
  } else {
    // 默认路径
    navigateTo('/pages/profile/edit/index')
  }
}

// 获取版本信息
const getVersionInfo = () => {
  const userType = userStore.userInfo.userType
  if (userType === 2) {
    return '高职院教师门户 v1.0.0'
  } else if (userType === 1) {
    return '高职院学生门户 v1.0.0'
  } else {
    return '高职院校园门户 v1.0.0'
  }
}

// 退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        userStore.logout()
        menuStore.clearMenus()
        uni.reLaunch({
          url: '/pages/login/login',
        })
      }
    },
  })
}

// 页面加载时获取教师课程数量和详细信息
onMounted(() => {
  fetchTeacherCourseCount()
  fetchTeacherDetailInfo()
})
</script>

<style lang="scss" scoped>
.about-container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding-bottom: calc(env(safe-area-inset-bottom) + 16rpx);
  overflow-y: auto;
  background-color: #f5f6fa;
}

.profile-header {
  position: relative;
  // padding: 20px 15px;
  padding: 48rpx 32rpx 64rpx;
  background-color: #3a8eff;
}

.profile-info {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.profile-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  margin-right: 12px;
  overflow: hidden;
  background-color: white;
  border-radius: 50%;

  image {
    width: 100%;
    height: 100%;

    &.hidden {
      display: none;
    }
  }
}

.user-info {
  flex: 1;
}

.profile-name {
  display: block;
  margin-bottom: 2px;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.profile-title {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.profile-edit {
  position: absolute;
  top: 48rpx;
  right: 32rpx;
  z-index: 100;
  padding: 12rpx;
}

.profile-stats {
  z-index: 1;
  display: flex;
  padding: 32rpx;
  margin: -60rpx 32rpx 32rpx;
  background-color: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.stat-item {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  margin-bottom: 8rpx;
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.stat-label {
  font-size: 24rpx;
  color: #8e8e93;
}

.list-section {
  margin: 0 32rpx 32rpx;
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  border-bottom: 2rpx solid #f2f2f7;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f2f2f7;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: #f8f9fc;
  }
}

.item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72rpx;
  height: 72rpx;
  margin-right: 24rpx;
  border-radius: 16rpx;
}

.icon-blue {
  background-color: #3a8eff;
}

.icon-orange {
  background-color: #ff9500;
}

.icon-green {
  background-color: #34c759;
}

.icon-red {
  background-color: #ff3b30;
}

.icon-purple {
  background-color: #af52de;
}

.item-content {
  flex: 1;
}

.item-title {
  margin-bottom: 4rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.item-subtitle {
  font-size: 24rpx;
  color: #8e8e93;
}

.sign-out-button {
  padding: 32rpx;
  margin: 0 32rpx 32rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #ff3b30;
  text-align: center;
  background-color: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

  &:active {
    background-color: #f8f9fc;
  }
}

.version-info {
  margin-top: 32rpx;
  font-size: 24rpx;
  color: #8e8e93;
  text-align: center;
}

.safe-bottom {
  height: calc(env(safe-area-inset-bottom) + 40rpx);
}
</style>
