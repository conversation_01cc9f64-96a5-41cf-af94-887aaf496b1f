<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的考勤',
  },
}
</route>
<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { useToast } from 'wot-design-uni'
import { getStudentAttendance } from '@/service/attendance'
import { getSemesterSelect } from '@/service/system'
import type {
  AttendanceStatistics,
  CourseAttendance,
  AttendanceItem,
  ClassRanking,
  AttendanceQuery,
} from '@/types/attendance'
import type { SemesterOption } from '@/types/system'

const toast = useToast()

// 学期选择
const semesterList = ref<SemesterOption[]>([])
const currentSemester = ref('')
const semesterLoading = ref(false)

// 加载状态
const loading = ref(false)
const loadError = ref(false)

// 考勤数据
const statistics = ref<AttendanceStatistics>({
  totalCourses: 0,
  normalAttendance: 0,
  leaveCount: 0,
  absentCount: 0,
  attendanceRate: 0,
  lateRate: 0,
})

const courseList = ref<CourseAttendance[]>([])
const recordList = ref<AttendanceItem[]>([])
const rankingList = ref<ClassRanking[]>([])

// 分页
const pagination = ref({
  page: 1,
  pageSize: 10,
  total: 0,
})

// 统计图表
const attendanceChartRef = ref()

// 记录查询
const timeRange = ref('最近30天')
const timeRangeOptions = ['最近7天', '最近30天', '全部']

// 按日期分组的考勤记录
const groupedRecords = computed(() => {
  const grouped: Record<string, AttendanceItem[]> = {}

  recordList.value.forEach((record) => {
    const date = record.teachingDate
    if (!grouped[date]) {
      grouped[date] = []
    }
    grouped[date].push(record)
  })

  return grouped
})

// 日期格式化
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  if (dateStr === today.toISOString().split('T')[0]) {
    return '今天'
  } else if (dateStr === yesterday.toISOString().split('T')[0]) {
    return '昨天'
  } else {
    return `${date.getMonth() + 1}月${date.getDate()}日`
  }
}

// 获取考勤状态文本和样式
const getStatusInfo = (status: string) => {
  switch (status) {
    case '出勤':
      return { text: '出勤', textClass: 'text-green-500', badgeType: 'success' as const }
    case '迟到':
      return { text: '迟到', textClass: 'text-yellow-500', badgeType: 'warning' as const }
    case '请假':
      return { text: '请假', textClass: 'text-blue-500', badgeType: 'primary' as const }
    case '旷课':
      return { text: '旷课', textClass: 'text-red-500', badgeType: 'danger' as const }
    default:
      return { text: status || '未知', textClass: '', badgeType: 'info' as const }
  }
}

// 初始化考勤统计图表
const initAttendanceChart = () => {
  setTimeout(() => {
    if (!attendanceChartRef.value) return
    attendanceChartRef.value.init(echarts).then((chart: any) => {
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)',
          confine: true,
        },
        legend: {
          orient: 'horizontal',
          bottom: 10,
          data: ['正常出勤', '请假', '旷课'],
        },
        series: [
          {
            type: 'pie',
            radius: ['40%', '60%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2,
            },
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              {
                value: statistics.value.normalAttendance,
                name: '正常出勤',
                itemStyle: { color: '#4cd964' },
              },
              { value: statistics.value.leaveCount, name: '请假', itemStyle: { color: '#f0ad4e' } },
              {
                value: statistics.value.absentCount,
                name: '旷课',
                itemStyle: { color: '#dd524d' },
              },
            ],
          },
        ],
      }

      chart.setOption(option)
    })
  }, 300)
}

// 获取考勤记录
const fetchAttendanceRecords = async () => {
  // 如果没有选择学期，则不请求数据
  if (!currentSemester.value) return

  loading.value = true
  loadError.value = false

  try {
    const params: AttendanceQuery = {
      page: pagination.value.page,
      pageSize: pagination.value.pageSize,
      semesters: [currentSemester.value],
      sortBy: 'teachingDate',
      sortOrder: 'desc',
    }

    const res = await getStudentAttendance(params)
    recordList.value = res.items
    pagination.value.total = res.total

    // 统计数据 - 因为接口返回中没有统计数据，这里根据返回数据计算一下简单统计
    const total = recordList.value.length
    const normal = recordList.value.filter((item) => item.attendanceStatus === '出勤').length
    const leave = recordList.value.filter((item) => item.attendanceStatus === '请假').length
    const absent = recordList.value.filter((item) => item.attendanceStatus === '旷课').length
    const late = recordList.value.filter((item) => item.attendanceStatus === '迟到').length

    statistics.value = {
      totalCourses: total,
      normalAttendance: normal,
      leaveCount: leave,
      absentCount: absent,
      attendanceRate: total > 0 ? Math.round((normal / total) * 100 * 10) / 10 : 0,
      lateRate: total > 0 ? Math.round((late / total) * 100 * 10) / 10 : 0,
    }

    // 课程分组 - 简单处理，实际应该有专门接口
    const courseMap = new Map<
      string,
      {
        count: number
        total: number
        teacherName: string
        classPeriod: string
      }
    >()

    recordList.value.forEach((item) => {
      if (!courseMap.has(item.courseName)) {
        courseMap.set(item.courseName, {
          count: 1,
          total: item.classHours,
          teacherName: item.teacherName,
          classPeriod: item.classPeriod,
        })
      } else {
        const course = courseMap.get(item.courseName)!
        course.count += 1
        course.total += item.classHours
      }
    })

    const iconTypes = ['math', 'english', 'network', 'program']
    courseList.value = Array.from(courseMap.entries()).map(([name, info], index) => {
      return {
        id: index + 1,
        courseName: name,
        teacherName: info.teacherName,
        courseTime: `课节 ${info.classPeriod}`,
        finishedLessons: info.count,
        totalLessons: info.count * 2, // 简单估算
        attendanceRate: 100, // 暂时假设100%
        iconType: iconTypes[index % iconTypes.length],
      }
    })

    // 刷新图表
    nextTick(() => {
      initAttendanceChart()
    })
  } catch (error) {
    console.error('获取考勤记录失败', error)
    loadError.value = true
    toast.error('获取考勤记录失败')
  } finally {
    loading.value = false
  }
}

// 获取学年学期数据
const fetchSemesterData = async () => {
  semesterLoading.value = true
  try {
    const res = await getSemesterSelect()
    semesterList.value = res.data

    // 默认选择第一个学期
    if (res.data.length > 0) {
      currentSemester.value = res.data[0].value
    }

    // 获取考勤数据
    await fetchAttendanceRecords()
  } catch (error) {
    console.error('获取学年学期数据失败', error)
    toast.error('获取学年学期数据失败')
  } finally {
    semesterLoading.value = false
  }
}

// 切换学期
const handleSemesterChange = () => {
  fetchAttendanceRecords()
}

// 切换时间范围
const handleTimeRangeChange = () => {
  fetchAttendanceRecords()
}

onMounted(() => {
  fetchSemesterData()
})
</script>

<template>
  <view class="attendance-page">
    <view class="page-content">
      <!-- 加载中 -->
      <wd-loading type="ring" v-if="loading || semesterLoading" />

      <!-- 加载失败 -->
      <wd-status-tip
        v-if="loadError && !loading && !semesterLoading"
        type="error"
        content="加载失败"
      />

      <template v-if="!loading && !loadError && !semesterLoading">
        <!-- 学期选择 -->
        <view class="semester-select">
          <wd-picker
            v-model="currentSemester"
            :columns="semesterList"
            :column-change="false"
            @confirm="handleSemesterChange"
          >
            <view class="semester-text">
              {{
                semesterList.find((item) => item.value === currentSemester)?.label ||
                '请选择学年学期'
              }}
              <wd-icon name="arrow-down" size="14" />
            </view>
          </wd-picker>
        </view>

        <!-- 考勤统计 -->
        <view class="custom-card m-3 bg-white rounded-lg shadow-sm overflow-hidden">
          <view class="card-header flex justify-between items-center p-3">
            <text class="card-title font-bold text-lg">考勤统计</text>
            <!-- <view class="text-primary text-sm">更多</view> -->
          </view>
          <view class="p-4">
            <view class="grid grid-cols-4 gap-2 mb-4">
              <view class="flex flex-col items-center py-2 rounded-lg bg-blue-50">
                <text class="text-xl font-bold text-blue-500">{{ statistics.totalCourses }}</text>
                <text class="text-xs text-gray-500">已上课程</text>
              </view>
              <view class="flex flex-col items-center py-2 rounded-lg bg-green-50">
                <text class="text-xl font-bold text-green-500">
                  {{ statistics.normalAttendance }}
                </text>
                <text class="text-xs text-gray-500">正常出勤</text>
              </view>
              <view class="flex flex-col items-center py-2 rounded-lg bg-yellow-50">
                <text class="text-xl font-bold text-yellow-500">{{ statistics.leaveCount }}</text>
                <text class="text-xs text-gray-500">请假</text>
              </view>
              <view class="flex flex-col items-center py-2 rounded-lg bg-red-50">
                <text class="text-xl font-bold text-red-500">{{ statistics.absentCount }}</text>
                <text class="text-xs text-gray-500">旷课</text>
              </view>
            </view>

            <view class="mb-1 flex justify-between">
              <text class="text-sm font-medium">出勤率</text>
              <text class="text-sm text-green-500">{{ statistics.attendanceRate }}%</text>
            </view>
            <view class="h-2 bg-gray-200 rounded-full mb-3 overflow-hidden">
              <view
                class="h-2 bg-green-500 rounded-full"
                :style="{ width: `${statistics.attendanceRate}%` }"
              ></view>
            </view>

            <view class="mb-1 flex justify-between">
              <text class="text-sm font-medium">迟到率</text>
              <text class="text-sm text-yellow-500">{{ statistics.lateRate }}%</text>
            </view>
            <view class="h-2 bg-gray-200 rounded-full mb-3 overflow-hidden">
              <view
                class="h-2 bg-yellow-500 rounded-full"
                :style="{ width: `${statistics.lateRate}%` }"
              ></view>
            </view>

            <view class="h-400rpx">
              <l-echart ref="attendanceChartRef"></l-echart>
            </view>
          </view>
        </view>

        <!-- 课程出勤 -->
        <!-- <view class="custom-card m-3 bg-white rounded-lg shadow-sm overflow-hidden">
          <view class="card-header flex justify-between items-center p-3">
            <text class="card-title font-bold text-lg">课程出勤</text>
            <view class="flex items-center">
              <view
                class="w-6 h-6 rounded-full text-xs text-gray-500 bg-gray-200 flex items-center justify-center mr-1"
              >
                <wd-icon name="pie-chart" size="14" />
              </view>
              <view
                class="w-6 h-6 rounded-full text-xs text-white bg-blue-500 flex items-center justify-center"
              >
                <wd-icon name="list" size="14" />
              </view>
            </view>
          </view>
          <view class="p-0">
            <view class="course-list">
              <view
                v-for="course in courseList"
                :key="course.id"
                class="course-item flex items-center p-3 border-b border-gray-100 active-effect"
              >
                <view
                  class="w-10 h-10 rounded-lg flex items-center justify-center mr-3 flex-shrink-0"
                  :class="{
                    'bg-blue-100 text-blue-500': course.iconType === 'math',
                    'bg-green-100 text-green-500': course.iconType === 'english',
                    'bg-purple-100 text-purple-500': course.iconType === 'network',
                    'bg-yellow-100 text-yellow-500': course.iconType === 'program',
                  }"
                >
                  <wd-icon
                    :name="
                      course.iconType === 'math'
                        ? 'count'
                        : course.iconType === 'english'
                          ? 'translate'
                          : course.iconType === 'network'
                            ? 'link'
                            : 'computer'
                    "
                    size="24"
                  />
                </view>
                <view class="flex-1 overflow-hidden">
                  <view class="flex justify-between items-center">
                    <text class="text-base font-medium">{{ course.courseName }}</text>
                    <view
                      class="course-badge"
                      :class="{
                        'bg-green-100 text-green-600': course.attendanceRate === 100,
                        'bg-yellow-100 text-yellow-600':
                          course.attendanceRate >= 90 && course.attendanceRate < 100,
                        'bg-red-100 text-red-600': course.attendanceRate < 90,
                      }"
                    >
                      {{ course.attendanceRate }}%
                    </view>
                  </view>
                  <view class="flex justify-between mt-1">
                    <text class="text-xs text-gray-500">
                      {{ course.teacherName }} | {{ course.courseTime }}
                    </text>
                    <text class="text-xs text-gray-500">
                      已上{{ course.finishedLessons }}/{{ course.totalLessons }}
                    </text>
                  </view>
                  <view class="h-1 bg-gray-200 rounded-full mt-2 overflow-hidden">
                    <view
                      class="h-1 rounded-full"
                      :class="{
                        'bg-green-500': course.attendanceRate === 100,
                        'bg-yellow-500': course.attendanceRate >= 90 && course.attendanceRate < 100,
                        'bg-red-500': course.attendanceRate < 90,
                      }"
                      :style="{ width: `${course.attendanceRate}%` }"
                    ></view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view> -->

        <!-- 考勤记录 -->
        <view class="custom-card m-3 bg-white rounded-lg shadow-sm overflow-hidden">
          <view class="card-header flex justify-between items-center p-3">
            <text class="card-title font-bold text-lg">考勤记录</text>
            <!-- <wd-picker
              v-model="timeRange"
              :columns="timeRangeOptions"
              @confirm="handleTimeRangeChange"
            >
              <view class="text-xs text-primary flex items-center">
                {{ timeRange }}
                <wd-icon name="arrow-down" size="12" />
              </view>
            </wd-picker> -->
          </view>

          <view class="overflow-hidden">
            <template v-if="loading">
              <view class="py-8 flex justify-center items-center">
                <wd-loading size="24px" />
              </view>
            </template>

            <template v-else-if="recordList.length === 0">
              <view class="text-center p-8 text-gray-500 text-sm">暂无考勤记录</view>
            </template>

            <template v-else>
              <view class="attendance-list-container">
                <template v-for="(records, date) in groupedRecords" :key="date">
                  <view class="bg-gray-200 px-4 py-1 text-xs text-gray-600 font-medium sticky-date">
                    {{ formatDate(date) }} ({{ date }})
                  </view>
                  <view class="attendance-list">
                    <view
                      v-for="(record, index) in records"
                      :key="record.attendanceId"
                      class="attendance-item flex p-3"
                      :style="{
                        '--index': index,
                      }"
                    >
                      <!-- 左侧课节图标 -->
                      <view
                        class="class-period-icon"
                        :class="{
                          'bg-blue-100 text-blue-500': record.classPeriod.startsWith('1'),
                          'bg-green-100 text-green-500': record.classPeriod.startsWith('3'),
                          'bg-purple-100 text-purple-500': record.classPeriod.startsWith('5'),
                          'bg-yellow-100 text-yellow-500':
                            record.classPeriod.startsWith('7') ||
                            record.classPeriod.startsWith('9'),
                        }"
                      >
                        <text class="period-text">{{ record.classPeriod }}</text>
                        <text class="period-label">课节</text>
                      </view>

                      <!-- 右侧内容区 -->
                      <view class="attendance-content">
                        <!-- 标题区: 课程名 + 考勤状态 -->
                        <view class="flex justify-between items-center w-full mb-2">
                          <text class="course-title">
                            {{ record.courseName }}
                          </text>
                          <view
                            class="attendance-tag"
                            :class="{
                              'bg-green-100 text-green-600': record.attendanceStatus === '出勤',
                              'bg-yellow-100 text-yellow-600': record.attendanceStatus === '迟到',
                              'bg-blue-100 text-blue-600': record.attendanceStatus === '请假',
                              'bg-red-100 text-red-600': record.attendanceStatus === '旷课',
                              'bg-gray-100 text-gray-600': ![
                                '出勤',
                                '迟到',
                                '请假',
                                '旷课',
                              ].includes(record.attendanceStatus),
                            }"
                          >
                            {{ getStatusInfo(record.attendanceStatus).text }}
                          </view>
                        </view>

                        <!-- 详细信息区: 时间、地点、教师、周次等 -->
                        <view class="info-grid">
                          <view class="info-item">
                            <wd-icon name="time" size="12" class="info-icon" />
                            <text class="info-text">
                              {{ record.attendanceStartTime.substring(11, 16) }}-{{
                                record.attendanceEndTime.substring(11, 16)
                              }}
                            </text>
                          </view>
                          <view class="info-item">
                            <wd-icon name="location" size="12" class="info-icon" />
                            <text class="info-text">
                              {{ record.className }}
                            </text>
                          </view>
                          <view class="info-item">
                            <wd-icon name="user" size="12" class="info-icon" />
                            <text class="info-text">
                              {{ record.teacherName }}
                            </text>
                          </view>
                          <view class="info-item">
                            <wd-icon name="calendar" size="12" class="info-icon" />
                            <text class="info-text">周{{ record.weekNumber }}</text>
                          </view>

                          <!-- 备注信息 -->
                          <view
                            v-if="record.remark"
                            class="remark"
                            :class="{
                              'bg-red-50 text-red-500': record.attendanceStatus === '旷课',
                              'bg-yellow-50 text-yellow-500': record.attendanceStatus === '迟到',
                              'bg-blue-50 text-blue-500': record.attendanceStatus === '请假',
                              'bg-gray-50 text-gray-500': !['旷课', '迟到', '请假'].includes(
                                record.attendanceStatus,
                              ),
                            }"
                          >
                            {{ record.remark }}
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </template>
              </view>

              <!-- 分页 -->
              <view class="flex justify-center items-center p-4" v-if="pagination.total > 0">
                <wd-button
                  type="info"
                  size="small"
                  plain
                  @click="pagination.page > 1 && (pagination.page--, fetchAttendanceRecords())"
                  :disabled="pagination.page <= 1"
                >
                  上一页
                </wd-button>
                <text class="text-sm text-gray-500 mx-4">
                  {{ pagination.page }} / {{ Math.ceil(pagination.total / pagination.pageSize) }}
                </text>
                <wd-button
                  type="info"
                  size="small"
                  plain
                  @click="
                    pagination.page < Math.ceil(pagination.total / pagination.pageSize) &&
                      (pagination.page++, fetchAttendanceRecords())
                  "
                  :disabled="pagination.page >= Math.ceil(pagination.total / pagination.pageSize)"
                >
                  下一页
                </wd-button>
              </view>
            </template>
          </view>
        </view>

        <!-- 班级排名 -->
        <view
          class="custom-card m-3 bg-white rounded-lg shadow-sm overflow-hidden mb-12"
          v-if="rankingList.length > 0"
        >
          <view class="card-header flex justify-between items-center p-3">
            <text class="card-title font-bold text-lg">班级出勤排名</text>
            <text class="text-sm text-gray-500">42名同学</text>
          </view>
          <view class="p-3">
            <view
              v-for="student in rankingList.slice(0, 5)"
              :key="student.id"
              class="flex items-center mb-2"
              :class="{ 'bg-blue-50 p-2 rounded-lg': student.isCurrentUser }"
            >
              <view
                class="w-6 h-6 rounded-full flex items-center justify-center mr-2 text-xs"
                :class="{
                  'bg-yellow-100 text-yellow-700': student.rank === 2,
                  'bg-gray-100 text-gray-700': student.rank >= 3,
                  'bg-blue-500 text-white': student.isCurrentUser,
                }"
              >
                {{ student.rank }}
              </view>
              <view class="flex-1">
                <view class="flex items-center">
                  <text class="text-sm font-medium mr-2">
                    {{ student.name }}{{ student.isCurrentUser ? '（我）' : '' }}
                  </text>
                  <view class="flex-1 h-2 bg-gray-200 rounded-full">
                    <view
                      class="h-2 bg-green-500 rounded-full"
                      :style="{ width: `${student.attendanceRate}%` }"
                    ></view>
                  </view>
                  <text class="text-xs text-gray-500 ml-2">{{ student.attendanceRate }}%</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </template>
    </view>

    <wd-toast />
  </view>
</template>

<style scoped lang="scss">
.attendance-page {
  min-height: 100vh;
  padding-top: 12px;
  background-color: #f7f8fa;
}

.semester-select {
  padding: 0 12px 12px;
}

.semester-text {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: var(--primary-color);
}

.page-content {
  padding-bottom: 32px;
}

.h-240rpx {
  height: 240rpx;
}

.card-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.card-title {
  position: relative;
  padding-left: 12px;
}

.card-title::before {
  position: absolute;
  top: 50%;
  left: 0;
  width: 4px;
  height: 16px;
  content: '';
  background-color: #3a73f9;
  border-radius: 2px;
  transform: translateY(-50%);
}

// 考勤记录样式
.sticky-date {
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.attendance-list {
  background-color: #fff;
}

.attendance-list-container {
  animation: fadeIn 0.5s ease forwards;
}

.attendance-item {
  position: relative;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  opacity: 0;
  transition: all 0.25s;
  animation: slideInUp 0.5s ease forwards;
  animation-delay: calc(var(--index, 0) * 0.05s);

  &:active {
    background-color: #f7f7f7;
    transform: scale(0.995);
  }

  &:last-child {
    border-bottom: none;
  }
}

.attendance-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 56px;
  height: 24px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition:
    transform 0.2s,
    box-shadow 0.2s;

  &:active {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
    transform: scale(0.95);
  }
}

// 左侧课节图标样式
.class-period-icon {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  margin-right: 12px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;

  .period-text {
    font-size: 14px;
    font-weight: bold;
    transition: all 0.2s;
  }

  .period-label {
    font-size: 10px;
    opacity: 0.8;
    transition: all 0.2s;
  }
}

// 右侧内容样式优化
.attendance-content {
  flex: 1;
  overflow: hidden;

  .course-title {
    max-width: 70%;
    margin-bottom: 6px;
    overflow: hidden;
    font-size: 15px;
    font-weight: 500;
    color: #333;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
  }

  .info-item {
    display: flex;
    align-items: center;

    .info-icon {
      flex-shrink: 0;
      margin-right: 4px;
      opacity: 0.6;
    }

    .info-text {
      overflow: hidden;
      font-size: 12px;
      color: #666;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .remark {
    grid-column: span 2;
    padding: 6px 8px;
    margin-top: 6px;
    font-size: 12px;
    border-radius: 4px;
    animation: fadeIn 0.5s ease;
  }
}

// 添加列表过渡动画
.attendance-list-enter-active,
.attendance-list-leave-active {
  transition: all 0.5s ease;
}

.attendance-list-enter-from,
.attendance-list-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

// 加载动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 自定义卡片阴影效果
.custom-card {
  opacity: 0;
  transition:
    box-shadow 0.3s,
    transform 0.3s;
  animation: cardFadeIn 0.5s ease forwards;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  }

  @for $i from 1 through 5 {
    &:nth-child(#{$i}) {
      animation-delay: #{$i * 0.1}s;
    }
  }
}

@keyframes cardFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 课程出勤样式
.course-list {
  background-color: #fff;
}

.course-item {
  position: relative;
  transition: all 0.25s;
  animation: slideInUp 0.5s ease both;

  @for $i from 0 through 10 {
    &:nth-child(#{$i + 1}) {
      animation-delay: #{$i * 0.05}s;
    }
  }

  &:active {
    background-color: #f7f7f7;
    transform: scale(0.995);
  }

  &:last-child {
    border-bottom: none;
  }
}

.course-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 46px;
  height: 22px;
  padding: 0 6px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 11px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}
</style>
