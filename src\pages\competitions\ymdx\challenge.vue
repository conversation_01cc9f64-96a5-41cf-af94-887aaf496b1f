<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '闯关题',
  },
}
</route>
<template>
  <view class="required-container bg-gray-50 flex flex-col min-h-screen">
    <!-- 添加一个包裹元素，使用sticky定位 -->
    <view class="sticky top-0 left-0 right-0 z-50">
      <!-- 简化的头部 - 修改为渐变红色背景 -->
      <view class="header flex items-center justify-between p-4 py-2 ma-red">
        <view class="flex items-center">
          <wd-icon name="arrow-left" size="20px" class="mr-2 text-white" @click="goBack" />
          <view>
            <view class="text-lg font-bold text-white">闯关题</view>
            <!-- <view class="text-xs text-white opacity-80">
                {{
                  hasQuestion
                    ? `第 ${currentQuestion.level} 关 - ${isAnswered ? '已回答' : '答题进行中'}`
                    : '等待中'
                }}
              </view> -->
          </view>
        </view>
        <view class="text-right" v-if="false">
          <view class="text-lg font-bold text-white">{{ totalScore }}</view>
          <view class="text-xs text-white opacity-80">累计分数</view>
        </view>
      </view>

      <!-- 进度条 -->
    </view>

    <!-- 添加学生信息卡片 -->
    <view class="mt-3 px-6 rounded-xl">
      <view class="flex items-center justify-between">
        <view class="flex items-center">
          <wd-icon name="user-circle" size="20px" class="mr-2 text-gray-600" />
          <view class="text-base font-medium text-gray-800">
            {{ userStore.userInfo.realname || '未知姓名' }}
          </view>
        </view>
        <view class="flex items-center" v-if="userStore.userInfo.className">
          <wd-icon name="usergroup" size="20px" class="mr-2 text-gray-600" />
          <view class="text-base text-gray-800">
            {{ userStore.userInfo.className || '未知班级' }}
          </view>
        </view>
      </view>
    </view>

    <!-- 有题目时显示进度条和答题组件 -->
    <template v-if="hasQuestion">
      <view class="px-6 relative z-10 mt-4 pb-4 bg-gray-50">
        <wd-progress :percentage="progressPercentage" color="#c41e3a"></wd-progress>
        <view class="flex justify-between mt-2 text-xs text-gray-600">
          <text>已完成 {{ questionInfo.total - questionInfo.left }}/{{ questionInfo.total }}</text>
        </view>
      </view>
      <!-- 使用QuestionTimer组件 -->
      <question-timer
        ref="questionTimerRef"
        :question-type="questionType"
        :question-score="currentQuestionScore"
        :question-content="questionInfo.title"
        :initial-options="formattedOptions"
        :max-time="maxTime"
        :correct-answer="correctAnswer"
        @time-up="handleTimeUp"
        @option-selected="handleOptionSelected"
      />
      <view class="pb-20" v-if="hasQuestion && !isAnswered && hasSelectedOption"></view>
    </template>

    <!-- 无题目时显示等待界面 -->
    <template v-else>
      <view class="flex-1 flex flex-col items-center justify-center px-6">
        <view class="waiting-container flex flex-col items-center">
          <wd-icon name="time" size="80px" class="text-gray-400 mb-4" />
          <view class="text-xl font-bold text-gray-700 mb-2">等待下一题</view>
          <view class="text-sm text-gray-500 text-center mb-6">
            主持人正在准备下一道题目，请耐心等待...
          </view>
          <view class="w-full">
            <view
              class="ma-red text-white py-4 rounded-xl font-medium flex items-center justify-center"
              @tap="refreshQuestion"
            >
              <wd-icon name="refresh" class="mr-2" />
              <text>刷新</text>
            </view>
          </view>
        </view>
      </view>
    </template>

    <!-- 确认答案按钮 - 固定在底部 -->
    <view class="fixed-bottom-btn" v-if="hasQuestion && !isAnswered && hasSelectedOption">
      <view
        :class="[
          'py-4 rounded-xl font-medium flex items-center justify-center',
          hasSelectedOption && !isSubmitting ? 'ma-red text-white' : 'bg-gray-400 text-white',
        ]"
        @tap="hasSelectedOption && !isSubmitting ? nextQuestion() : null"
      >
        <wd-icon name="check" class="mr-2" />
        <text>
          {{ isSubmitting ? '提交中...' : '确认答案' }}
        </text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-overlay">
      <wd-loading color="#c41e3a" />
    </view>

    <!-- 自定义淘汰提示对话框 -->
    <custom-dialog
      ref="eliminatedDialogRef"
      :title="dialogTitle"
      :message="dialogMessage"
      confirm-button-text="确定"
      @confirm="handleEliminatedConfirm"
    />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import QuestionTimer from './components/QuestionTimer.vue'
import CustomDialog from './components/CustomDialog.vue'
import {
  getPlayerQuestionInfo,
  postPlayerAnswer,
  getPlayerScore,
} from '@/service/competitions/ymdx'
import { QuestionInfo } from '@/types/competitions/ymdx'
import { Push } from '@/static/js/push-vue'
import { navigateByCategory } from './utils'
import { useUserStore } from '@/store/user'
import { useCompetitionStore } from '@/store/competition'
import { useToast } from 'wot-design-uni'

// 获取用户信息
const userStore = useUserStore()

// 获取竞赛store
const competitionStore = useCompetitionStore()

// toast提示
const toast = useToast()

// 自定义对话框引用
const eliminatedDialogRef = ref()

// 加载状态
const loading = ref(true)

// 是否正在提交答案（防抖）
const isSubmitting = ref(false)

// 项目ID和人员编号（实际使用时应从页面参数或用户信息中获取）
const xmid = ref(competitionStore.getProjectId() || '')
const rybh = ref(userStore.userInfo.username || '') // 使用userStore中的username作为rybh的默认值

// 当前问题ID
const currentQuestionId = ref('')

// WebSocket连接
const connection = new Push({
  url: import.meta.env.VITE_WEBSOCKET_BASEURL, // 从环境变量中读取websocket地址
  app_key: '6d6d1453263346b136cfcbea80d0d1ff',
})
console.log()

// 订阅sub频道，用于接收category切换消息
const subChannel = connection.subscribe('sub')
subChannel.on('toggle_category', (message) => {
  console.log('收到toggle_category事件:', message.category)
  // 根据category跳转到对应页面
  navigateByCategory(message.category, xmid.value, rybh.value)
})

const userChannel = connection.subscribe('sub_fe_challenge')
console.log(userChannel)

// WebSocket事件处理函数
const handleNextQuestion = (payload: any) => {
  console.log('收到next_question事件:', payload)
  // 处理收到的题目信息
  if (payload) {
    // 更新问题信息
    questionInfo.value = {
      title: payload.title || '',
      opt: payload.opt || [],
      category: payload.category || '',
      time: payload.time || 0,
      total: payload.total || 0,
      left: payload.left || 0,
      status: payload.status || '',
      type: payload.type || '单选题',
    }

    // 保存当前问题ID
    currentQuestionId.value = payload.question_id || ''

    // 更新题目类型
    questionType.value = payload.type || '单选题'

    // 设置倒计时时间
    maxTime.value = payload.time || 10 // 直接使用API返回的秒数，默认为10秒
    console.log('WebSocket接收到新题目，设置倒计时时间:', maxTime.value, '秒')

    // 重置已回答状态
    isAnswered.value = false

    // 重置已选择状态
    hasSelectedOption.value = false

    // 获取最新分数
    fetchPlayerScore()

    // 使用更长的延迟，并添加多次检查，确保组件已经挂载
    const updateTimer = async (attempts = 0, maxAttempts = 10) => {
      if (attempts >= maxAttempts) {
        console.error('多次尝试后questionTimerRef仍然不可用')
        return
      }

      if (questionTimerRef.value) {
        console.log('更新组件选项:', formattedOptions.value)
        // 确保分数已更新
        await fetchPlayerScore()
        questionTimerRef.value.updateOptions(formattedOptions.value)
        // 如果计时器已经开始，重置计时器
        questionTimerRef.value.resetTimer()
        console.log('重置计时器，倒计时时间:', maxTime.value, '秒')
      } else {
        console.log(`尝试 ${attempts + 1}/${maxAttempts}: questionTimerRef不可用，300ms后重试`)
        setTimeout(() => updateTimer(attempts + 1, maxAttempts), 300)
      }
    }

    // 开始尝试更新
    setTimeout(() => updateTimer(), 500)
  }
}

// 获取最新分数
const fetchPlayerScore = async () => {
  try {
    const res = await getPlayerScore({
      xmid: xmid.value,
      rybh: rybh.value,
    })
    console.log('获取到的选手分数:', res.total_score)
    totalScore.value = res.total_score

    // 更新QuestionTimer组件中的分数
    if (questionTimerRef.value) {
      console.log('更新QuestionTimer组件中的分数:', totalScore.value)
      questionTimerRef.value.updateScore(totalScore.value)
    } else {
      console.log('QuestionTimer组件不可用，无法更新分数')
    }
  } catch (error) {
    console.error('获取选手分数失败:', error)
  }
}

const handleQuestionDone = (payload: any) => {
  console.log('收到question_done事件:', payload)
  // 处理题目完成的事件
  if (payload) {
    // 显示题目完成的提示
    toast.info('本题答题已结束')

    // 设置已回答状态
    isAnswered.value = true

    // 使用更长的延迟和多次检查，确保组件已经挂载
    const processQuestionDone = (attempts = 0, maxAttempts = 10) => {
      if (attempts >= maxAttempts) {
        console.error('多次尝试后questionTimerRef仍然不可用')
        return
      }

      if (questionTimerRef.value && payload.answer) {
        console.log('处理question_done事件，答案:', payload.answer)
        // 使用QuestionTimer组件的handleQuestionDone方法处理事件
        questionTimerRef.value.handleQuestionDone(payload)
        // 确保停止倒计时
        questionTimerRef.value.timeUp = true
        // 显示正确答案
        questionTimerRef.value.showAnswer()
      } else {
        console.log(
          `尝试 ${attempts + 1}/${maxAttempts}: questionTimerRef不可用或无答案，300ms后重试`,
        )
        setTimeout(() => processQuestionDone(attempts + 1, maxAttempts), 300)
      }
    }

    // 开始尝试处理
    setTimeout(() => processQuestionDone(), 500)
  }
}

// 是否已回答
const isAnswered = ref(false)

// 是否已选择选项
const hasSelectedOption = ref(false)

// 当前题目信息
const currentQuestion = ref({
  level: 5,
  totalLevels: 20,
})

// 问题信息
const questionInfo = ref<QuestionInfo>({
  title: '',
  opt: [],
  category: '',
  time: 0,
  total: 0,
  left: 0,
  status: '',
  type: '',
})

// 是否有题目
const hasQuestion = computed(() => {
  return questionInfo.value && questionInfo.value.opt && questionInfo.value.opt.length > 0
})

// 总分数
const totalScore = ref(25)

// 倒计时最大时间
const maxTime = ref(10)

// 问题计时器组件引用
const questionTimerRef = ref()

// 正确答案（初始为null，通过API获取）
// 对于多选题，正确答案是一个数组，包含所有正确选项的索引
const correctAnswer = ref(null)

// 当前题目类型
const questionType = ref('单选题')

// 当前题目分数
const currentQuestionScore = ref(1)

// 格式化选项，转换为组件需要的格式
const formattedOptions = computed(() => {
  if (!questionInfo.value.opt || questionInfo.value.opt.length === 0) {
    return []
  }

  return questionInfo.value.opt.map((option) => {
    // 判断题直接使用name作为text，不需要截取
    if (questionType.value === '判断题') {
      return {
        label: option.value || '',
        text: option.name || '',
        selected: false,
      }
    }

    // 其他题型（单选题、多选题）保持原有逻辑
    return {
      label: option.value || '',
      text: option.name, // 去掉选项值和点，如"A."，如果截取失败则使用完整name
      selected: false,
    }
  })
})

// 计算进度百分比
const progressPercentage = computed(() => {
  if (questionInfo.value.total === 0) return 0
  return Math.round(
    ((questionInfo.value.total - questionInfo.value.left) / questionInfo.value.total) * 100,
  )
})

// 对话框内容
const dialogTitle = ref('闯关失败')
const dialogMessage = ref('')

// 获取问题信息
const fetchQuestionInfo = async () => {
  loading.value = true
  try {
    console.log('开始获取题目信息，参数:', { xmid: xmid.value, rybh: rybh.value })

    // 从页面参数获取人员编号，项目ID从store中获取
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    // @ts-expect-error uniapp类型定义问题
    const options = currentPage?.$page?.options || {}

    // 优先使用store中的项目ID
    if (!xmid.value) {
      xmid.value = competitionStore.getProjectId() || ''
    }

    // 如果store中没有项目ID，则使用默认值
    if (!xmid.value) {
      xmid.value = '19302' // 默认值，实际应用中应从页面参数获取
    }

    rybh.value = options.rybh || userStore.userInfo.username || '' // 优先使用页面参数，其次使用用户名，最后使用空字符串

    // 获取最新分数
    await fetchPlayerScore()

    const res = await getPlayerQuestionInfo({
      xmid: xmid.value,
      rybh: rybh.value,
    })

    console.log('获取到的问题信息:', res)

    // 检查是否可以参与答题
    if (res.can_answer === false) {
      console.log('用户无法参与答题，显示淘汰提示')
      // 使用延迟显示对话框
      showEliminatedDialogWithDelay('您无法参与此活动')
      loading.value = false
      return
    }

    // 检查是否需要根据category跳转到其他页面
    if (res && res.category) {
      const category = res.category

      if (category.toLowerCase() !== 'challenge') {
        navigateByCategory(category, xmid.value, rybh.value)
        loading.value = false
        return
      }
    }

    // 检查是否有题目
    if (res.question_info && Array.isArray(res.question_info) && res.question_info.length === 0) {
      console.log('当前没有可用题目，显示等待界面')
      // 清空问题信息，触发等待界面显示
      questionInfo.value = {
        title: '',
        opt: [],
        category: '',
        time: 0,
        total: 0,
        left: 0,
        status: '',
        type: '',
      }
      return
    }

    console.log('问题选项:', res.question_info.opt)

    // 更新问题信息
    questionInfo.value = res.question_info

    // 保存当前问题ID
    currentQuestionId.value = res.current_question

    // 直接使用API返回的题目类型
    questionType.value = res.question_info.type || '单选题'

    console.log('题目类型:', questionType.value)

    // 设置倒计时时间
    maxTime.value = res.question_info.time || 10 // 直接使用API返回的秒数，默认为10秒
    console.log('API获取到题目，设置倒计时时间:', maxTime.value, '秒')

    console.log('格式化后的选项:', formattedOptions.value)

    // 使用更可靠的方式初始化组件
    const initQuestionTimer = async (attempts = 0, maxAttempts = 5) => {
      if (attempts >= maxAttempts) {
        console.error('多次尝试后questionTimerRef仍然不可用，放弃初始化')
        return
      }

      if (questionTimerRef.value) {
        // 获取最新分数
        console.log('初始化QuestionTimer组件')
        await fetchPlayerScore()
        questionTimerRef.value.updateOptions(formattedOptions.value)
        questionTimerRef.value.resetTimer()
      } else {
        console.log(`尝试 ${attempts + 1}/${maxAttempts}: 等待QuestionTimer组件挂载`)
        setTimeout(() => initQuestionTimer(attempts + 1, maxAttempts), 200)
      }
    }

    // 给组件一点时间挂载，然后尝试初始化
    setTimeout(() => initQuestionTimer(), 300)
  } catch (error) {
    console.error('获取问题信息失败', error)
    toast.error('获取题目失败')
  } finally {
    loading.value = false
  }
}

// 刷新获取题目
const refreshQuestion = () => {
  fetchQuestionInfo()
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 处理时间结束
const handleTimeUp = async () => {
  if (!isAnswered.value && hasSelectedOption.value) {
    await nextQuestion()
  }
}

// 处理选项选择
const handleOptionSelected = (index: number) => {
  // 标记已选择选项
  hasSelectedOption.value = true

  // 根据题目类型处理选项选择
  if (questionType.value === '单选题' || questionType.value === '判断题') {
    // 清除其他选中状态
    formattedOptions.value.forEach((option, i) => {
      option.selected = i === index
    })
  } else if (questionType.value === '多选题') {
    // 多选题逻辑，切换选中状态
    formattedOptions.value[index].selected = !formattedOptions.value[index].selected

    // 检查是否至少有一个选项被选中
    hasSelectedOption.value = formattedOptions.value.some((option) => option.selected)
  }

  // 更新子组件中的选项状态
  if (questionTimerRef.value) {
    questionTimerRef.value.updateOptions(formattedOptions.value)
  }
}

// 获取用户选择的答案字符串
const getUserAnswer = (): string => {
  // 获取用户选择的所有选项
  const selectedOptions = formattedOptions.value
    .filter((option) => option.selected)
    .map((option) => option.label)

  // 将选项连接成字符串，如 "A,B,C"
  return selectedOptions.join(',')
}

// 提交答案并获取结果
const submitAnswer = async (): Promise<number[]> => {
  try {
    const userAnswer = getUserAnswer()

    // 如果用户没有选择任何答案，返回空数组
    if (!userAnswer) {
      toast.warning('请选择答案')
      return []
    }

    console.log('提交答案:', {
      xmid: xmid.value,
      question_id: currentQuestionId.value,
      rybh: rybh.value,
      answer: userAnswer,
    })

    // 调用API提交答案
    const res = await postPlayerAnswer({
      xmid: xmid.value,
      question_id: currentQuestionId.value,
      rybh: rybh.value,
      answer: userAnswer,
    })

    console.log('答题结果:', res)

    // 检查is_match字段，如果为false，则显示淘汰提示对话框
    if (res.is_match === false) {
      // 使用延迟显示对话框
      showEliminatedDialogWithDelay('很遗憾，您已被淘汰')
      return []
    }

    // 根据得分判断是否正确
    const isCorrect = res.score !== '0'

    if (isCorrect) {
      // 更新总分
      totalScore.value += parseInt(res.score)
    }

    // 根据用户的选择和是否正确，返回正确答案的索引数组
    // 这里我们需要根据API的返回值来确定正确答案
    // 由于API没有直接返回正确答案，我们假设用户选择的答案如果得分不为0，则为正确答案
    const selectedIndices = formattedOptions.value
      .map((option, index) => (option.selected ? index : -1))
      .filter((index) => index !== -1)

    return isCorrect ? selectedIndices : []
  } catch (error) {
    console.error('提交答案失败', error)
    toast.error('提交答案失败')
    return []
  }
}

// 确认答案
const nextQuestion = async () => {
  // 如果正在提交中，则不处理
  if (isSubmitting.value) return

  // 先设置已回答状态
  isAnswered.value = true

  // 设置提交状态为true，防止重复点击
  isSubmitting.value = true
  toast.loading('提交答案中...')

  // 通知子组件已回答（此时还没有正确答案）
  if (questionTimerRef.value) {
    questionTimerRef.value.setAnswered()
  }

  try {
    // 提交答案并获取结果
    const correctIndices = await submitAnswer()
    toast.close()
    // correctAnswer.value = correctIndices

    // 获取到正确答案后，更新子组件中的正确答案
    if (questionTimerRef.value) {
      // 传递所有正确答案
      // questionTimerRef.value.updateCorrectAnswer(correctIndices)
      // 显示正确答案
      // questionTimerRef.value.showAnswer()
    }

    // 获取最新分数
    await fetchPlayerScore()
  } catch (error) {
    console.error('处理答题结果失败', error)
    isAnswered.value = false
    toast.error('提交答案失败，请重试')
  } finally {
    // 无论成功还是失败，都重置提交状态
    isSubmitting.value = false
  }
}

// 页面加载时获取问题信息
onMounted(() => {
  // 添加WebSocket事件监听
  userChannel.on('next_question', handleNextQuestion)
  userChannel.on('question_done', handleQuestionDone)

  // 获取题目信息
  fetchQuestionInfo()

  // 获取最新分数
  fetchPlayerScore()

  // 添加一个延迟检查，确保组件在WebSocket事件到达前已经准备好
  setTimeout(async () => {
    if (!questionTimerRef.value && hasQuestion.value) {
      console.warn('组件挂载后questionTimerRef仍然不可用，尝试重新获取题目')
      fetchQuestionInfo()
    } else if (questionTimerRef.value) {
      // 确保分数已更新到组件中
      console.log('组件已挂载，确保分数更新')
      await fetchPlayerScore()
    }
  }, 1000)
})

// 页面卸载时清理WebSocket连接和订阅
onUnmounted(() => {
  // 移除事件监听
  if (userChannel) {
    userChannel.off('next_question', handleNextQuestion)
    userChannel.off('question_done', handleQuestionDone)
  }

  if (subChannel) {
    subChannel.off('toggle_category')
  }
})

// 处理淘汰确认按钮点击
const handleEliminatedConfirm = () => {
  // 取消订阅WebSocket频道
  if (userChannel) {
    userChannel.off('next_question', handleNextQuestion)
    userChannel.off('question_done', handleQuestionDone)
  }

  if (subChannel) {
    subChannel.off('toggle_category')
  }

  // 完全取消订阅
  connection.unsubscribe('sub_fe_challenge')

  uni.navigateTo({
    url: `/pages/competitions/ymdx/index?id=${xmid.value}&eliminated=true`,
  })
}

// 显示淘汰对话框
const showEliminatedDialog = (message: string) => {
  dialogMessage.value = message
  if (eliminatedDialogRef.value) {
    eliminatedDialogRef.value.show()
  }
}

// 添加延迟显示淘汰对话框的方法
const showEliminatedDialogWithDelay = (message: string) => {
  // 完全取消订阅
  connection.unsubscribe('sub_fe_challenge')
  // 获取当前剩余时间
  const currentRemainingTime = questionTimerRef.value?.remainingTime || 0

  // 如果倒计时已经结束，直接显示对话框
  if (currentRemainingTime <= 0 || !questionTimerRef.value) {
    // 完全取消订阅
    connection.unsubscribe('sub_fe_challenge')
    showEliminatedDialog(message)
    return
  }

  console.log(`等待倒计时自然结束，剩余${currentRemainingTime}秒`)

  // 等待倒计时自然结束后再显示对话框
  setTimeout(
    () => {
      // 显示对话框
      showEliminatedDialog(message)
    },
    (currentRemainingTime + 1) * 1000,
  ) // 加1秒确保倒计时完全结束
}
</script>

<style>
.header {
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.ma-red {
  background: linear-gradient(135deg, #c41e3a 0%, #8b0000 100%);
}

.loading-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.7);
}

.waiting-container {
  width: 100%;
  padding: 40rpx;
  border-radius: 16rpx;
}

.fixed-bottom-btn {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 50;
  padding: 20rpx 40rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}
</style>
