<template>
  <view v-if="visible" class="custom-dialog-overlay" @click.stop>
    <view class="custom-dialog" @click.stop>
      <view class="custom-dialog-header">
        <text class="custom-dialog-title">{{ title }}</text>
      </view>
      <view class="custom-dialog-content">
        <text class="custom-dialog-message">{{ message }}</text>
      </view>
      <view class="custom-dialog-footer">
        <view class="custom-dialog-btn confirm-btn" @tap="handleConfirm">
          {{ confirmButtonText }}
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, defineProps, defineEmits } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: '提示',
  },
  message: {
    type: String,
    default: '',
  },
  confirmButtonText: {
    type: String,
    default: '确定',
  },
})

const emit = defineEmits(['confirm'])

const visible = ref(false)

// 显示对话框
const show = () => {
  visible.value = true
}

// 隐藏对话框
const hide = () => {
  visible.value = false
}

// 处理确认按钮点击
const handleConfirm = () => {
  hide()
  emit('confirm')
}

// 对外暴露方法
defineExpose({
  show,
  hide,
})
</script>

<style>
.custom-dialog-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
}

.custom-dialog {
  width: 80%;
  max-width: 600rpx;
  overflow: hidden;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.custom-dialog-header {
  padding: 30rpx 30rpx 20rpx;
  text-align: center;
}

.custom-dialog-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.custom-dialog-content {
  padding: 20rpx 30rpx 40rpx;
  text-align: center;
}

.custom-dialog-message {
  font-size: 28rpx;
  line-height: 1.5;
  color: #666;
}

.custom-dialog-footer {
  display: flex;
  border-top: 1rpx solid #eee;
}

.custom-dialog-btn {
  flex: 1;
  height: 90rpx;
  font-size: 30rpx;
  line-height: 90rpx;
  text-align: center;
}

.confirm-btn {
  color: #fff;
  background: linear-gradient(135deg, #c41e3a 0%, #8b0000 100%);
}
</style>
