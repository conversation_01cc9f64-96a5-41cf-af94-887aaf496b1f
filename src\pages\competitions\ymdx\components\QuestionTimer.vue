<template>
  <view class="question-timer-container">
    <!-- 倒计时器 - 修改为左右两侧布局 -->
    <view class="flex justify-between items-center pb-4 px-20">
      <!-- 左侧显示累计分数 -->
      <view class="score-display flex flex-col items-center justify-center">
        <view class="text-xl font-bold text-red-600">{{ score }}</view>
        <view class="text-xs text-gray-500">累计得分</view>
      </view>

      <!-- 右侧显示倒计时 -->
      <wd-circle
        v-model="circleProgress"
        :text="`${remainingTime}秒`"
        :size="60"
        :color="{
          '0%': '#c41e3a',
          '100%': '#8b0000',
        }"
        :layer-color="'#EBEEF5'"
        :strokeWidth="8"
        :clockwise="false"
      ></wd-circle>
    </view>

    <!-- 题目区域 -->
    <view class="flex-1 px-6">
      <view class="bg-white rounded-2xl shadow-lg p-6 mb-6 relative">
        <view class="flex items-start mb-4">
          <text class="bg-blue-100 text-blue-600 text-sm font-medium px-3 py-1 rounded-full mr-3">
            {{ questionType }}
          </text>
          <text class="bg-green-100 text-green-600 text-sm font-medium px-3 py-1 rounded-full">
            {{ questionScore }}分
          </text>
        </view>

        <view class="text-lg font-bold text-gray-800 mb-4 leading-relaxed">
          {{ questionContent }}
        </view>

        <!-- 多选题提示 -->
        <view v-if="questionType === '多选题'" class="text-xs text-gray-500 mb-3 flex items-center">
          <wd-icon name="info-circle" size="14px" class="mr-1" />
          <text>可选择多个答案</text>
        </view>

        <!-- 选项 -->
        <view class="space-y-3">
          <view
            v-for="(option, index) in options"
            :key="index"
            class="option-item border-2 rounded-xl p-4 transition-all duration-200 shadow-sm relative"
            :class="getOptionClass(option, index)"
            @tap="!isAnswered && selectOption(index)"
          >
            <view class="flex items-center">
              <view
                class="option-circle w-8 h-8 border-2 rounded-full flex items-center justify-center mr-4"
                :class="getOptionCircleClass(option, index)"
              >
                <!-- 判断题显示对错图标 -->
                <template v-if="questionType === '判断题'">
                  <wd-icon
                    :name="option.label === '正确' ? 'check' : 'close'"
                    :class="option.selected ? 'text-white' : 'text-gray-600'"
                    size="16px"
                  />
                </template>
                <!-- 其他题型判断是否为图标名称，如果是则显示图标，否则显示文本 -->
                <template v-else-if="isIconName(option.label)">
                  <wd-icon
                    :name="option.label"
                    :class="option.selected ? 'text-white' : 'text-gray-600'"
                    size="16px"
                  />
                </template>
                <text
                  v-else
                  class="option-label"
                  :class="option.selected ? 'text-white font-medium' : 'text-gray-600 font-medium'"
                >
                  {{ option.label }}
                </text>
              </view>
              <text class="text-gray-800">{{ option.text }}</text>
            </view>
          </view>
        </view>

        <!-- 磨玻璃遮罩层 - 已回答但未公布结果 -->
        <view
          v-if="isAnswered && !showCorrectAnswer && !timeUp"
          class="frosted-glass absolute inset-0 rounded-2xl flex flex-col items-center justify-center"
        >
          <wd-icon name="check-circle-filled" size="48px" class="text-gray-500 mb-4 opacity-70" />
          <view class="text-lg font-medium text-gray-700">已完成答题</view>
          <view class="text-sm text-gray-500 mt-2">{{ remainingTime }}秒后公布正确答案</view>
        </view>

        <!-- 未回答遮罩层 - 显示在倒计时结束且没有回答时 -->
        <!-- <view
          v-if="!showCorrectAnswer && !userAnswered"
          class="frosted-glass absolute inset-0 rounded-2xl flex flex-col items-center justify-center"
        >
          <wd-icon name="error-circle-filled" size="48px" class="text-red-500 mb-4 opacity-70" />
          <view class="text-lg font-medium text-red-700">未回答</view>
          <view class="text-sm text-gray-500 mt-2">正确答案：{{ getCorrectAnswerText() }}</view>
        </view> -->

        <!-- 答案信息区域 - 在倒计时结束或显示正确答案时显示 -->
        <view
          v-if="timeUp || showCorrectAnswer"
          class="answer-info mt-6 pt-4 border-t border-gray-200"
        >
          <!-- 已回答情况 -->
          <view v-if="userAnswered && isAnswerConfirmed" class="space-y-2">
            <!-- 用户答案 -->
            <view class="flex">
              <text class="text-gray-600 font-medium mr-2">您的答案：</text>
              <text
                :class="
                  isUserAnswerCorrect() ? 'text-green-600 font-bold' : 'text-red-600 font-bold'
                "
              >
                {{ getUserAnswerText() }}
              </text>
            </view>

            <!-- 正确答案 -->
            <view class="flex">
              <text class="text-gray-600 font-medium mr-2">正确答案：</text>
              <text class="text-green-600 font-bold">{{ getCorrectAnswerText() }}</text>
            </view>
          </view>
          <!-- 未回答或未确认的情况 -->
          <view v-else-if="!isAnswerConfirmed" class="">
            <view class="text-red-600 font-bold">您未回答</view>

            <text class="text-gray-600 font-medium mr-2">正确答案：</text>
            <text class="text-green-600 font-bold">{{ getCorrectAnswerText() }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

// 定义组件接收的属性
const props = defineProps({
  // 题目类型
  questionType: {
    type: String,
    default: '单选题',
  },
  // 题目分值
  questionScore: {
    type: Number,
    default: 1,
  },
  // 题目内容
  questionContent: {
    type: String,
    required: true,
  },
  // 选项数据
  initialOptions: {
    type: Array,
    required: true,
  },
  // 最大倒计时时间（秒）
  maxTime: {
    type: Number,
    default: 15,
  },
  // 正确答案索引（由父组件通过API获取）
  correctAnswer: {
    type: [Number, Array],
    default: null,
  },
})

// 定义组件的事件
const emit = defineEmits(['time-up', 'option-selected', 'answer-confirmed'])

// 复制选项数据，以便在组件内部修改
const options = ref(JSON.parse(JSON.stringify(props.initialOptions)))

// 累计分数
const score = ref(0)

// 是否已回答
const isAnswered = ref(false)
// 用户是否回答了问题（区分系统设置的isAnswered和用户实际是否回答）
const userAnswered = ref(false)
// 用户是否确认了答案（点击了确认按钮）
const isAnswerConfirmed = ref(false)
// 是否显示正确答案
const showCorrectAnswer = ref(false)
// 存储正确答案的索引（可以是单个数字或数组）
const correctAnswerIndices = ref<number[] | null>(null)

// 判断是否为图标名称
const isIconName = (label: string): boolean => {
  // 如果label包含特殊字符如A、B、C、D或√、×，则不是图标名称
  return typeof label === 'string' && !label.match(/^[A-Z√×]$/) && label.length > 1
}

// 倒计时
const remainingTime = ref(props.maxTime)
const circleProgress = computed(() => {
  // 将剩余时间转换为百分比
  return Math.round(((props.maxTime - remainingTime.value) / props.maxTime) * 100)
})

// 时间是否截止
const timeUp = ref(false)

// 定时器引用
let timer: number | null = null
// 记录开始时间和结束时间
let endTime: number = 0

// 启动定时器
const startTimer = () => {
  // 先清除可能存在的定时器，避免重复创建
  if (timer) {
    clearInterval(timer)
    timer = null
  }

  // 确保remainingTime使用最新的maxTime值
  remainingTime.value = props.maxTime

  // 计算结束时间戳
  const startTime = Date.now()
  endTime = startTime + props.maxTime * 1000

  timer = setInterval(() => {
    // 计算剩余时间（秒）
    const now = Date.now()
    const remaining = Math.max(0, Math.ceil((endTime - now) / 1000))

    // 更新剩余时间
    remainingTime.value = remaining

    if (remaining <= 0) {
      // 时间截止，确保remainingTime为0
      remainingTime.value = 0
      // 时间截止
      timeUp.value = true
      // 清除定时器
      if (timer) {
        clearInterval(timer)
        timer = null
      }
      // 通知父组件时间到了
      emit('time-up')

      // 如果已回答但未显示正确答案，时间到了就显示正确答案
      if (isAnswered.value && !showCorrectAnswer.value) {
        showCorrectAnswer.value = true
      }
    }
  }, 100) // 使用更短的间隔来更新倒计时，提高精度
}

// 组件挂载时启动定时器
onMounted(() => {
  startTimer()
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})

// 选择选项
const selectOption = (index: number) => {
  // 标记用户已回答
  userAnswered.value = true

  // 根据题目类型处理选项选择
  if (props.questionType === '多选题') {
    // 多选题：切换选中状态
    options.value[index].selected = !options.value[index].selected
  } else {
    // 单选题或判断题：只能选择一个选项
    options.value.forEach((option, i) => {
      option.selected = i === index
    })
  }

  // 通知父组件选项已选择
  emit('option-selected', index)
}

// 设置已回答状态，并接收正确答案索引
const setAnswered = (correctIndex?: number | number[]) => {
  isAnswered.value = true
  isAnswerConfirmed.value = true
  if (correctIndex !== undefined) {
    updateCorrectAnswer(correctIndex)
  }
}

// 更新正确答案（在API请求完成后调用）
const updateCorrectAnswer = (correctIndex: number | number[]) => {
  // 将正确答案转换为数组形式
  if (Array.isArray(correctIndex)) {
    correctAnswerIndices.value = correctIndex
  } else {
    correctAnswerIndices.value = [correctIndex]
  }
}

// 将字母答案转换为索引（如'A'->0, 'B'->1）
const convertAnswerToIndex = (answer: string): number[] => {
  // 处理判断题的"正确"、"错误"答案
  if (answer === '正确') return [0]
  if (answer === '错误') return [1]

  // 如果是多个字母（多选题），则拆分成数组
  if (answer.includes(',')) {
    return answer.split(',').map((letter) => {
      // 处理每个字母
      if (letter === '√') return 0
      if (letter === '×') return 1
      return letter.charCodeAt(0) - 'A'.charCodeAt(0)
    })
  } else {
    // 单个字母的情况
    const firstLetter = answer.charAt(0)
    if (firstLetter === '√') return [0]
    if (firstLetter === '×') return [1]
    return [firstLetter.charCodeAt(0) - 'A'.charCodeAt(0)]
  }
}

// 处理question_done事件
const handleQuestionDone = (eventData: {
  question_id: string
  category: string
  answer: string
}) => {
  // 设置正确答案
  const correctIndices = convertAnswerToIndex(eventData.answer)
  updateCorrectAnswer(correctIndices)

  // 设置已回答状态（系统状态，不是用户状态）
  isAnswered.value = true

  // 如果用户未点击确认按钮，则视为未选择答案
  if (!isAnswerConfirmed.value) {
    // 清除用户选择
    options.value.forEach((option) => {
      option.selected = false
    })
    userAnswered.value = false
  }

  // 显示正确答案
  showCorrectAnswer.value = true

  // 设置时间到，关闭遮罩层
  timeUp.value = true
}

// 更新选项数据
const updateOptions = (newOptions: any[]) => {
  options.value = JSON.parse(JSON.stringify(newOptions))
}

// 显示正确答案
const showAnswer = () => {
  showCorrectAnswer.value = true
}

// 获取用户选择的答案文本
const getUserAnswerText = (): string => {
  const selectedOptions = options.value
    .filter((option) => option.selected)
    .map((option) => option.label)

  return selectedOptions.join(', ') || '无'
}

// 获取正确答案文本
const getCorrectAnswerText = (): string => {
  if (!correctAnswerIndices.value || correctAnswerIndices.value.length === 0) {
    return '无'
  }

  return correctAnswerIndices.value
    .map((index) => options.value[index]?.label || '')
    .filter(Boolean)
    .join(', ')
}

// 判断用户答案是否正确
const isUserAnswerCorrect = (): boolean => {
  if (!correctAnswerIndices.value || correctAnswerIndices.value.length === 0) {
    return false
  }

  // 获取用户选择的所有索引
  const userSelectedIndices = options.value
    .map((option, index) => (option.selected ? index : -1))
    .filter((index) => index !== -1)

  // 对于单选题，直接比较
  if (props.questionType === '单选题' || props.questionType === '判断题') {
    return (
      userSelectedIndices.length === 1 &&
      correctAnswerIndices.value.includes(userSelectedIndices[0])
    )
  }

  // 对于多选题，需要比较数组
  if (props.questionType === '多选题') {
    // 长度必须相同，且每个元素都必须在正确答案中
    return (
      userSelectedIndices.length === correctAnswerIndices.value.length &&
      userSelectedIndices.every((index) => correctAnswerIndices.value!.includes(index))
    )
  }

  return false
}

// 更新分数
const updateScore = (newScore: number) => {
  score.value = newScore
}

// 暴露方法给父组件
defineExpose({
  setAnswered,
  updateCorrectAnswer,
  updateOptions,
  showAnswer,
  remainingTime,
  timeUp,
  userAnswered,
  handleQuestionDone,
  updateScore,
  resetTimer: () => {
    // 重置时间
    remainingTime.value = props.maxTime
    // 重置时间到标志
    timeUp.value = false
    // 重置已回答状态
    isAnswered.value = false
    // 重置用户回答状态
    userAnswered.value = false
    // 重置答案确认状态
    isAnswerConfirmed.value = false
    // 重置显示正确答案标志
    showCorrectAnswer.value = false
    // 清除现有定时器
    if (timer) {
      clearInterval(timer)
      timer = null
    }
    // 重新启动定时器
    startTimer()
  },
  // 添加更新剩余时间的方法
  updateRemainingTime: (time: number) => {
    remainingTime.value = time
    // 如果时间为0，设置timeUp为true
    if (time <= 0) {
      remainingTime.value = 0
      timeUp.value = true
      // 清除定时器
      if (timer) {
        clearInterval(timer)
        timer = null
      }
    }
  },
  // 添加强制停止计时器的方法
  stopTimer: () => {
    // 设置时间到标志
    timeUp.value = true
    // 确保时间归零
    remainingTime.value = 0
    // 清除定时器
    if (timer) {
      clearInterval(timer)
      timer = null
    }
  },
})

// 判断选项是否为正确答案
const isCorrectAnswer = (index: number): boolean => {
  return correctAnswerIndices.value ? correctAnswerIndices.value.includes(index) : false
}

// 获取选项样式
const getOptionClass = (option: any, index: number) => {
  if (showCorrectAnswer.value || (isAnswered.value && timeUp.value)) {
    if (isCorrectAnswer(index)) {
      return 'border-green-500 bg-green-50'
    } else if (option.selected && !isCorrectAnswer(index)) {
      return 'border-red-500 bg-red-50'
    }
    return 'border-gray-300 bg-gray-50'
  }

  if (option.selected) {
    return 'border-red-500 bg-red-50'
  }
  return 'border-gray-300 bg-gray-50 hover:bg-gray-100'
}

// 获取选项圆圈样式
const getOptionCircleClass = (option: any, index: number) => {
  if (showCorrectAnswer.value || (isAnswered.value && timeUp.value)) {
    if (isCorrectAnswer(index)) {
      return 'border-green-500 bg-green-500'
    } else if (option.selected && !isCorrectAnswer(index)) {
      return 'border-red-500 bg-red-500'
    }
    return 'border-gray-400 bg-white'
  }

  if (option.selected) {
    return 'border-red-500 bg-red-500'
  }
  return 'border-gray-400 bg-white'
}
</script>

<style>
.option-item {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.option-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  min-height: 32px;
}

.option-label {
  display: inline-block;
  line-height: 1;
  text-align: center;
}

.frosted-glass {
  z-index: 10;
  background-color: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(5px);
}

.answer-info {
  min-height: 60rpx;
}
</style>
