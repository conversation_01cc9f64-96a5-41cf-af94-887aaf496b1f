<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '一马当先',
  },
}
</route>
<template>
  <view class="competition-container flex flex-col items-center justify-center box-border">
    <!-- Logo区域 -->
    <view class="logo-animation mb-8">
      <view class="w-32 h-32 bg-white rounded-full flex items-center justify-center shadow-2xl">
        <image class="logo-img" src="/static/fjpitlogo.png" mode="aspectFit" />
      </view>
    </view>

    <!-- 应用名称 -->
    <text class="text-4xl font-bold mb-4 text-center text-white">一马当先</text>
    <text class="text-xl mb-2 text-white opacity-90">福建信息职业技术学院竞赛平台</text>
    <text class="text-sm text-white opacity-75 mb-12">马克思主义学院</text>

    <!-- 不符合参赛条件信息 -->
    <view v-if="!isEligible && !loading" class="eligibility-info mb-8">
      <view class="bg-white p-6 rounded-xl shadow-lg text-center">
        <wd-icon name="info-circle-filled" size="60px" class="text-yellow-500 mb-4" />
        <view class="text-xl font-bold text-gray-800 mb-2">{{ eligibilityMessage }}</view>
        <view class="text-gray-600 mb-4">请稍后再扫码加入</view>
      </view>
    </view>

    <!-- 淘汰信息 -->
    <view v-else-if="isEliminated" class="eliminated-info mb-8">
      <view class="bg-white p-6 rounded-xl shadow-lg text-center">
        <wd-icon name="close-circle-filled" size="60px" class="text-red-600 mb-4" />
        <view class="text-xl font-bold text-gray-800 mb-2">很遗憾，您已被淘汰</view>
        <view class="text-gray-600 mb-4">感谢您的参与，期待下次再见！</view>
        <!-- <view class="w-full mt-4">
          <view
            class="ma-red text-white py-3 rounded-xl font-medium flex items-center justify-center"
            @tap="backToHome"
          >
            <text>返回首页</text>
          </view>
        </view> -->
      </view>
    </view>

    <!-- 加载动画 -->
    <view v-else-if="loading" class="flex flex-col items-center justify-center">
      <wd-loading color="#ffffff" size="40px" />
      <view class="text-sm mt-4 text-white opacity-75">正在获取题目信息...</view>
    </view>

    <!-- 等待动画 -->
    <view v-else class="flex flex-col items-center justify-center">
      <view class="flex items-center space-x-2">
        <view class="w-2 h-2 bg-white rounded-full animate-bounce"></view>
        <view
          class="w-2 h-2 bg-white rounded-full animate-bounce"
          style="animation-delay: 0.1s"
        ></view>
        <view
          class="w-2 h-2 bg-white rounded-full animate-bounce"
          style="animation-delay: 0.2s"
        ></view>
      </view>
      <view class="text-sm mt-4 text-white opacity-75">等待比赛开始...</view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { Push } from '@/static/js/push-vue'
import { navigateByCategory } from './utils'
import { useUserStore } from '@/store/user'
import { useCompetitionStore } from '@/store/competition'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { ref, onMounted } from 'vue'
import { getPlayerQuestionInfo, checkPlayerEligibility } from '@/service/competitions/ymdx'
import { useToast } from 'wot-design-uni'
import { getUserInfo, getAccessCodes, getUserMenus } from '@/service/auth'
import { useMenuStore } from '@/store/menu'

// 获取用户信息
const userStore = useUserStore()
const menuStore = useMenuStore()
const rybh = ref(userStore.userInfo.username || '') // 使用userStore中的username作为rybh的默认值

// 获取竞赛store
const competitionStore = useCompetitionStore()
const xmid = ref('')

// toast提示
const toast = useToast()

// 加载状态
const loading = ref(false)

// 是否被淘汰
const isEliminated = ref(false)

// 用户参赛资格状态
const isEligible = ref(false)
const eligibilityMessage = ref('您不在参赛名单内/活动未开始')

// WebSocket连接
let connection: Push | null = null

// 页面加载时检查options中是否有id参数和eliminated参数
onLoad((options) => {
  console.log('页面加载时的options:', options)
  if (options && options.id) {
    xmid.value = options.id
    // 将项目ID存入store
    competitionStore.setProjectId(options.id)
    console.log('项目ID已存入store:', options.id)
  }

  // 检查是否有淘汰参数
  if (options && options.eliminated === 'true') {
    isEliminated.value = true
    console.log('用户已被淘汰')
  }
})

// 初始化用户信息和store
onMounted(async () => {
  // 检查用户是否已登录
  if (!userStore.isLogined) {
    uni.redirectTo({
      url: '/pages/login/login',
    })
    return
  }

  try {
    loading.value = true
    // 并行调用接口获取用户信息、权限和菜单
    const [userInfoRes, accessCodes, menus] = await Promise.all([
      getUserInfo(),
      getAccessCodes(),
      getUserMenus(),
    ])

    // 设置用户信息
    userStore.setUserInfo({
      ...userInfoRes.userInfo,
      // 添加缺少的store所需字段
      last_login_time: userInfoRes.userInfo.lastLoginTime || '',
      roleName: '',
      nickname: '',
      openid: '',
      className: userInfoRes.userInfo.className || '',
    })

    // 设置权限码
    userStore.setAccessCodes(accessCodes)

    // 处理菜单数据
    if (menus) {
      menuStore.setMenus(menus)
    } else {
      console.error('菜单数据格式错误:', menus)
      menuStore.setMenus([])
    }

    // 更新rybh值
    rybh.value = userStore.userInfo.username || ''

    // 检查用户参赛资格
    await checkEligibility()
  } catch (error) {
    console.error('初始化用户信息失败:', error)
    toast.error('初始化用户信息失败')
  } finally {
    loading.value = false
  }
})

// 检查用户参赛资格
const checkEligibility = async () => {
  // 如果xmid为空，则从store中获取
  if (!xmid.value) {
    xmid.value = competitionStore.getProjectId() || ''
  }

  // 如果仍然为空，则使用默认值
  if (!xmid.value) {
    console.error('项目ID不存在')
    isEligible.value = false
    eligibilityMessage.value = '项目信息获取失败'
    return
  }

  try {
    console.log('开始检查参赛资格，参数:', { xmid: xmid.value, rybh: rybh.value })

    const res = await checkPlayerEligibility({
      xmid: xmid.value,
      rybh: rybh.value,
    })

    console.log('检查参赛资格结果:', res)
    isEligible.value = res.can_answer

    if (!res.can_answer && res.message) {
      eligibilityMessage.value = res.message
    }

    // 只有当用户符合参赛资格时才建立WebSocket连接
    if (res.can_answer) {
      initWebSocket()
      fetchQuestionInfo()
    }
  } catch (error) {
    console.error('检查参赛资格失败', error)
    isEligible.value = false
    eligibilityMessage.value = '系统错误，请稍后再试'
  }
}

// 初始化WebSocket连接
const initWebSocket = () => {
  // 只有当用户符合参赛资格且未被淘汰时才建立连接
  if (!isEligible.value || isEliminated.value) return

  connection = new Push({
    url: import.meta.env.VITE_WEBSOCKET_BASEURL, // 从环境变量中读取websocket地址
    app_key: '6d6d1453263346b136cfcbea80d0d1ff',
  })

  // 订阅sub频道，用于接收category切换消息
  const subChannel = connection.subscribe('sub')
  subChannel.on('toggle_category', (message) => {
    // 如果用户已被淘汰，不进行任何操作
    if (isEliminated.value) return

    // 如果xmid.value为空，则从store中获取
    if (!xmid.value) {
      xmid.value = competitionStore.getProjectId()
    }
    console.log('收到toggle_category事件:', message.category)
    if (connection) {
      connection.disconnect()
      connection = null
    }
    // 根据category跳转到对应页面
    navigateByCategory(message.category, xmid.value, rybh.value)
  })
}

// 页面显示时获取问题信息并跳转
onShow(() => {
  // 如果用户已被淘汰或不符合参赛资格，不进行任何操作
  if (isEliminated.value || !isEligible.value) return

  // 获取问题信息
  fetchQuestionInfo()
})

// 获取问题信息
const fetchQuestionInfo = async () => {
  // 如果用户不符合参赛资格，不获取问题信息
  if (!isEligible.value) return

  // 如果xmid为空，则从store中获取
  if (!xmid.value) {
    xmid.value = competitionStore.getProjectId() || ''
  }

  // 如果仍然为空，则使用默认值
  if (!xmid.value) {
    console.error('项目ID不存在')
    return
  }

  loading.value = true
  try {
    console.log('开始获取题目信息，参数:', { xmid: xmid.value, rybh: rybh.value })

    const res = await getPlayerQuestionInfo({
      xmid: xmid.value,
      rybh: rybh.value,
    })

    console.log('获取到的问题信息:', res)

    // 检查是否需要根据category跳转到其他页面
    if (res && res.category) {
      const category = res.category
      console.log('获取到的category:', category)

      // 根据category跳转到对应页面
      navigateByCategory(category, xmid.value, rybh.value)
    }
  } catch (error) {
    console.error('获取问题信息失败', error)
    toast.error('获取题目信息失败')
  } finally {
    loading.value = false
  }
}

// 返回首页
const backToHome = () => {
  // 这里可以根据需要跳转到实际的首页
  uni.reLaunch({
    url: '/pages/home/<USER>',
  })
}
</script>

<style lang="scss">
page {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

.competition-container {
  box-sizing: border-box;
  width: 100%;
  height: 100vh;
  padding: 0 20rpx;
  overflow-x: hidden;
  background: linear-gradient(135deg, #c41e3a 0%, #8b0000 100%);
}

.logo-animation {
  animation: pulse 2s infinite;
}

.eliminated-info,
.eligibility-info {
  width: 90%;
  max-width: 600rpx;
}

.ma-red {
  background: linear-gradient(135deg, #c41e3a 0%, #8b0000 100%);
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
</style>
