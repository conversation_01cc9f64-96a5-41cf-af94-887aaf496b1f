<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '抢答题',
  },
}
</route>
<template>
  <view class="rush-page flex flex-col bg-gray-50 h-screen">
    <!-- 添加一个包裹元素，使用sticky定位 -->
    <view class="sticky top-0 left-0 right-0 z-50">
      <!-- 简化的头部 - 修改为渐变红色背景 -->
      <view class="header flex items-center justify-between p-4 py-2 ma-red">
        <view class="flex items-center">
          <wd-icon name="arrow-left" size="20px" class="mr-2 text-white" @click="goBack" />
          <view>
            <view class="text-lg font-bold text-white">抢答题</view>
            <!-- <view class="text-xs text-white opacity-80">
              {{
                hasQuestion ? `第 ${currentQuestion.level} 题 - ${questionStatus}` : '等待题目中'
              }}
            </view> -->
          </view>
        </view>
        <view class="text-right" v-if="false">
          <view class="text-lg font-bold text-white">{{ totalScore }}</view>
          <view class="text-xs text-white opacity-80">累计分数</view>
        </view>
      </view>

      <!-- 题目状态 -->
      <view class="px-4 py-3 bg-white shadow-sm" v-if="!isAnswering">
        <view class="flex items-center justify-between">
          <view class="flex items-center">
            <view class="w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></view>
            <text class="text-sm font-medium text-gray-700">{{ questionStatusText }}</text>
          </view>
          <view class="text-right" v-if="hasQuestion && shouldShowTimer">
            <text class="text-lg font-bold text-orange-600">{{ remainingTime }}</text>
            <view class="text-xs text-gray-500">剩余秒数</view>
          </view>
        </view>
      </view>
    </view>
    <!-- 添加学生信息卡片 -->
    <view class="mt-3 px-6 pb-2 rounded-xl">
      <view class="flex items-center justify-between">
        <view class="flex items-center">
          <wd-icon name="user-circle" size="20px" class="mr-2 text-gray-600" />
          <view class="text-base font-medium text-gray-800">
            {{ userStore.userInfo.realname || '未知姓名' }}
          </view>
        </view>
        <view class="flex items-center" v-if="userStore.userInfo.className">
          <wd-icon name="usergroup" size="20px" class="mr-2 text-gray-600" />
          <view class="text-base text-gray-800">
            {{ userStore.userInfo.className || '未知班级' }}
          </view>
        </view>
      </view>
    </view>

    <!-- 等待提示 - 修改为与required.vue一致的样式 -->
    <view v-if="!hasQuestion" class="flex-1 flex flex-col items-center justify-center px-6">
      <view class="waiting-container flex flex-col items-center">
        <wd-icon name="time" size="80px" class="text-gray-400 mb-4" />
        <view class="text-xl font-bold text-gray-700 mb-2">等待下一题</view>
        <view class="text-sm text-gray-500 text-center mb-6">
          主持人正在准备下一道题目，请耐心等待...
        </view>
        <view class="w-full">
          <view
            class="ma-red text-white py-4 rounded-xl font-medium flex items-center justify-center"
            @tap="refreshQuestion"
          >
            <wd-icon name="refresh" class="mr-2" />
            <text>刷新</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 题目显示区 -->
    <view class="px-4 py-3" v-if="hasQuestion && !isAnswering">
      <view class="bg-white rounded-2xl shadow-lg p-4">
        <view class="flex items-center justify-between mb-3">
          <view class="flex items-center">
            <text
              class="bg-orange-100 text-orange-600 text-sm font-medium px-3 py-1 rounded-full mr-3"
            >
              {{ questionInfo.type || '抢答题' }}
            </text>
            <text class="bg-green-100 text-green-600 text-sm font-medium px-3 py-1 rounded-full">
              1分
            </text>
          </view>
          <text class="text-sm text-gray-500">题目 #{{ currentQuestion.level }}</text>
        </view>

        <text class="text-lg font-bold text-gray-800 mb-4 leading-relaxed">
          {{ questionInfo.title || '加载中...' }}
        </text>

        <!-- 答案显示区域 -->
        <view
          v-if="answerInfo.showAnswer"
          class="mt-4 p-3 bg-green-50 rounded-lg border border-green-200"
        >
          <view class="flex items-center">
            <wd-icon name="check-bold" size="20px" class="text-green-600 mr-2" />
            <text class="text-base font-bold text-green-700">正确答案</text>
          </view>
          <text class="text-base text-green-800 mt-2 block">{{ answerInfo.answer }}</text>
        </view>
      </view>
    </view>

    <!-- 抢答区域 -->
    <view
      class="flex-1 flex flex-col items-center justify-center px-4"
      v-if="hasQuestion && !answerInfo.showAnswer && !isAnswering"
    >
      <!-- 抢答按钮 - 添加动态类绑定 -->
      <view
        :class="[
          'rush-button w-40 h-40 rounded-full flex flex-col items-center justify-center text-white mb-4',
          isButtonDisabled ? 'bg-gray-400' : 'ma-red',
        ]"
        @click="handleRushAnswer"
      >
        <wd-icon name="pointing-hand" class="text-3xl mb-2"></wd-icon>
        <text class="text-xl font-bold">抢答</text>
        <text class="text-xs opacity-80 mt-1">
          {{
            isButtonDisabled
              ? questionInfo.status === QuestionStatus.WAITING_OPEN
                ? '准备中'
                : questionInfo.status === QuestionStatus.DONE
                  ? '本题已结束'
                  : '他人答题中'
              : '点击抢答'
          }}
        </text>
      </view>

      <!-- 抢答提示 -->
      <view class="text-center mb-4">
        <text class="text-gray-600 text-sm">
          {{
            isButtonDisabled
              ? questionInfo.status === QuestionStatus.WAITING_OPEN
                ? '题目准备中，请等待'
                : questionInfo.status === QuestionStatus.DONE
                  ? '本题已结束'
                  : '有人正在答题中，请等待'
              : '题目读完后即可抢答'
          }}
        </text>
        <!-- <view class="text-gray-500 text-xs mt-1">抢答成功后有20秒答题时间</view> -->
      </view>
    </view>

    <!-- 答题计时器组件 - 在抢答成功后显示 -->
    <view v-if="isAnswering" class="flex-1 pb-20">
      <QuestionTimer
        ref="questionTimerRef"
        :questionType="questionInfo.type || '单选题'"
        :questionScore="1"
        :questionContent="questionInfo.title || ''"
        :initialOptions="formattedOptions"
        :maxTime="answerTimeLimit"
        @time-up="handleTimeUp"
        @option-selected="handleOptionSelected"
      />
    </view>

    <!-- 答案结果区域 - 仅在非答题状态且有答案时显示 -->
    <view
      class="flex-1 flex flex-col items-center justify-center px-4"
      v-if="hasQuestion && answerInfo.showAnswer && !isAnswering"
    >
      <view class="text-center mb-4">
        <view class="text-xl font-bold text-green-700 mb-2">本题已结束</view>
        <view class="text-gray-600">请等待下一题开始</view>
      </view>
      <view
        class="ma-red text-white py-4 px-8 rounded-xl font-medium flex items-center justify-center mt-4"
        @tap="refreshQuestion"
      >
        <wd-icon name="refresh" class="mr-2" />
        <text>刷新状态</text>
      </view>
    </view>

    <!-- 确认答案按钮 - 固定在底部 -->
    <view class="fixed-bottom-btn" v-if="isAnswering && !answerInfo.showAnswer">
      <view
        :class="[
          'text-white py-4 rounded-xl font-medium flex items-center justify-center',
          hasSelectedAnswer && !isSubmitting ? 'ma-red' : 'bg-gray-400',
        ]"
        @tap="hasSelectedAnswer && !isSubmitting ? confirmAnswer() : null"
      >
        <wd-icon name="check" class="mr-2" />
        <text>
          {{ isSubmitting ? '提交中...' : hasSelectedAnswer ? '确认答案' : '请选择答案' }}
        </text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { Push } from '@/static/js/push-vue'
import {
  getPlayerQuestionInfo,
  postPlayerAnswer,
  postPlayerHandleQuick,
  getPlayerScore,
} from '@/service/competitions/ymdx'
import type {
  PlayerQuestionInfoData,
  PlayerQuestionInfoParams,
  QuestionInfo,
  PlayerHandleQuickParams,
} from '@/types/competitions/ymdx'
import { QuestionStatus } from '@/types/competitions/ymdx'
import { useToast } from 'wot-design-uni'
import { navigateByCategory } from './utils'
import QuestionTimer from './components/QuestionTimer.vue'
import { useUserStore } from '@/store/user'
import { useCompetitionStore } from '@/store/competition'

// 获取用户信息
const userStore = useUserStore()

// 获取竞赛store
const competitionStore = useCompetitionStore()

// toast提示
const toast = useToast()

// 项目ID和人员编号，实际应用中应从页面参数或全局状态获取
const xmid = ref<string | number>(competitionStore.getProjectId() || '')
const rybh = ref<string | number>(userStore.userInfo.username || '') // 使用userStore中的username作为rybh的默认值

// 问题信息
const questionInfo = ref<QuestionInfo>({
  title: '',
  opt: [],
  category: '',
  time: 0,
  total: 0,
  left: 0,
  type: '',
  status: '',
})

// 是否正在提交答案（防抖）
const isSubmitting = ref(false)

// 是否有问题
const hasQuestion = computed(() => {
  return currentQuestionId.value !== '0' && currentQuestionId.value !== 0
})

// 当前题目信息
const currentQuestion = ref({
  level: 0,
  totalLevels: 0,
})

// 当前问题ID
const currentQuestionId = ref<string | number>('0')

// 总分数
const totalScore = ref(0)

// 剩余时间
const remainingTime = ref(0)

// 问题状态
const questionStatus = ref('准备中')
const questionStatusText = ref('等待题目中，请耐心等待')

// 答案信息
const answerInfo = ref({
  answer: '',
  showAnswer: false,
})

// 是否正在答题状态
const isAnswering = ref(false)

// 答题时间限制
const answerTimeLimit = ref(20)

// 问题计时器引用
const questionTimerRef = ref()

// 用户选择的答案
const selectedOptions = ref<number[]>([])

// 判断按钮是否禁用（准备中或答题中状态）
const isButtonDisabled = computed(() => {
  return (
    questionInfo.value.status === QuestionStatus.DOING ||
    questionInfo.value.status === QuestionStatus.WAITING_OPEN ||
    questionInfo.value.status === QuestionStatus.DONE
  )
})

// 判断是否显示计时器（只在答题中和已结束状态显示）
const shouldShowTimer = computed(() => {
  return questionInfo.value.status === QuestionStatus.DOING
})

// 判断是否已选择答案
const hasSelectedAnswer = computed(() => {
  return selectedOptions.value.length > 0
})

// 计时器
let timer: number | null = null

// 格式化选项数据
const formattedOptions = computed(() => {
  if (!questionInfo.value.opt || !Array.isArray(questionInfo.value.opt)) {
    return []
  }

  return questionInfo.value.opt.map((option, index) => {
    // 根据选项类型生成标签
    let label = ''

    // 检查选项是否是对象格式
    if (typeof option === 'object' && option !== null) {
      // 如果是对象，使用value属性作为标签
      label = option.value || String.fromCharCode(65 + index)

      // 如果是判断题，使用√和×
      if (questionInfo.value.type === '判断题' && index < 2) {
        label = index === 0 ? '√' : '×'
      }

      return {
        label,
        text: option.name, // 移除前缀如"A."
        selected: false,
      }
    } else {
      // 如果是字符串，使用字母作为标签
      label = String.fromCharCode(65 + index) // A, B, C, D...

      // 如果是判断题，使用√和×
      if (questionInfo.value.type === '判断题' && index < 2) {
        label = index === 0 ? '√' : '×'
      }

      return {
        label,
        text: String(option),
        selected: false,
      }
    }
  })
})

// WebSocket连接
const connection = new Push({
  url: import.meta.env.VITE_WEBSOCKET_BASEURL, // 从环境变量中读取websocket地址
  app_key: '6d6d1453263346b136cfcbea80d0d1ff',
})

const subChannel = connection.subscribe('sub')
subChannel.on('toggle_category', (message) => {
  console.log(message.category)
  // 根据category跳转到对应页面
  navigateByCategory(message.category, xmid.value, rybh.value)
})
const userChannel = connection.subscribe('sub_fe_quick')

// 添加抢答模式的WebSocket事件监听
userChannel.on('open_quick', (message) => {
  console.log('收到开放抢答推送:', message)
  if (message && message.question_id) {
    // 更新当前问题ID
    currentQuestionId.value = message.question_id
    // 更新问题状态为"抢答中"
    questionStatus.value = '抢答中'
    questionStatusText.value = '题目已显示，可以抢答'
    // 刷新题目信息
    refreshQuestion()
  }
})

userChannel.on('begin_answer', (message) => {
  console.log('收到开始答题推送:', message)
  if (message && message.question_id) {
    // 更新问题状态为"答题中"
    questionInfo.value.status = QuestionStatus.DOING
    updateQuestionStatus(QuestionStatus.DOING)

    // 如果是当前用户抢答成功
    if (message.rybh === rybh.value) {
      toast.success('抢答成功')
      // 切换到答题状态，显示QuestionTimer组件
      isAnswering.value = true

      console.log(remainingTime.value)

      // 设置答题时间限制
      answerTimeLimit.value = remainingTime.value

      // 重置选项状态
      selectedOptions.value = []

      // 先清除全局定时器，避免与QuestionTimer组件的定时器重复运行
      if (timer) {
        clearInterval(timer)
        timer = null
      }

      // 如果QuestionTimer组件已挂载，重置计时器
      setTimeout(() => {
        if (questionTimerRef.value) {
          fetchPlayerScore()
          questionTimerRef.value.resetTimer()
          questionTimerRef.value.updateOptions(formattedOptions.value)
        }
      }, 300)

      // 不再调用startCountdown，让QuestionTimer组件管理答题时间
    } else {
      // 如果是其他用户抢答成功
      toast.info(`编号${message.rybh}的选手抢答成功`)
    }
  }
})

// 添加新题目推送事件监听
userChannel.on('next_question', (message) => {
  console.log('收到新题目推送:', message)
  if (message) {
    // 更新问题信息
    console.log(message.time < 0 ? 20 : message.time)

    questionInfo.value = {
      title: message.title || '',
      opt: message.opt || [],
      category: message.category || '',
      time: message.time < 0 ? 20 : message.time,
      total: message.total || 0,
      left: message.left || 0,
      type: message.type,
      status: QuestionStatus.WAITING_OPEN, // 默认为抢答中状态
    }

    // 更新当前问题ID，如果没有提供，则使用当前时间戳作为临时ID
    if (message.question_id) {
      currentQuestionId.value = message.question_id
    } else {
      currentQuestionId.value = Date.now().toString()
    }

    // 更新题目级别
    currentQuestion.value.level = questionInfo.value.total - questionInfo.value.left + 1
    currentQuestion.value.totalLevels = questionInfo.value.total

    // 更新剩余时间
    console.log(questionInfo.value.time < 0 ? 20 : questionInfo.value.time)
    remainingTime.value = questionInfo.value.time < 0 ? 20 : questionInfo.value.time

    // 更新问题状态
    updateQuestionStatus(QuestionStatus.WAITING_OPEN)

    // 重置答案信息
    answerInfo.value = {
      answer: '',
      showAnswer: false,
    }

    // 重置答题状态
    isAnswering.value = false
    selectedOptions.value = []

    // 重置提交状态
    isSubmitting.value = false

    // 只在非答题状态时开始倒计时
    if (!isAnswering.value) {
      // 清除可能存在的计时器
      if (timer) {
        clearInterval(timer)
        timer = null
      }
      startCountdown()
    }

    // 显示提示
    toast.success('收到新题目')
  }
})

// 添加倒计时结束结果推送事件监听
userChannel.on('question_done', (message) => {
  console.log('收到题目结束推送:', message)
  if (message) {
    // 更新问题状态为已结束
    questionInfo.value.status = QuestionStatus.DONE
    updateQuestionStatus(QuestionStatus.DONE)

    // 保存答案信息（确保多选题答案完整保存）
    answerInfo.value = {
      answer: message.answer || '',
      showAnswer: true,
    }

    // 重置提交状态
    isSubmitting.value = false

    // 停止倒计时
    if (timer) {
      clearInterval(timer)
      timer = null
    }

    // 如果QuestionTimer组件存在且正在答题中，停止其内部计时器
    if (isAnswering.value && questionTimerRef.value) {
      // 确保只调用一次stopTimer
      questionTimerRef.value.stopTimer()

      // 调用处理函数
      handleQuestionDone({
        question_id: String(currentQuestionId.value),
        category: questionInfo.value.category,
        answer: message.answer || '',
      })
    } else {
      // 如果不在答题状态，直接处理答案
      handleQuestionDone({
        question_id: String(currentQuestionId.value),
        category: questionInfo.value.category,
        answer: message.answer || '',
      })
    }

    // 显示答案提示
    toast.info(`本题答案: ${message.answer}`)
  }
})

// 获取最新分数
const fetchPlayerScore = async () => {
  try {
    const res = await getPlayerScore({
      xmid: xmid.value,
      rybh: rybh.value,
    })
    console.log('获取到的选手分数:', res.total_score)
    totalScore.value = res.total_score

    // 更新QuestionTimer组件中的分数
    if (questionTimerRef.value) {
      questionTimerRef.value.updateScore(totalScore.value)
    }
  } catch (error) {
    console.error('获取选手分数失败:', error)
  }
}

// 处理question_done事件
const handleQuestionDone = (eventData: {
  question_id: string
  category: string
  answer: string
}) => {
  // 设置答案信息
  answerInfo.value = {
    answer: eventData.answer || '',
    showAnswer: true,
  }

  // 重置提交状态
  isSubmitting.value = false

  // 停止倒计时
  if (timer) {
    clearInterval(timer)
    timer = null
  }

  // 获取最新分数
  fetchPlayerScore()

  // 如果正在答题，将答案传递给QuestionTimer组件
  if (isAnswering.value && questionTimerRef.value) {
    // 使用更长的延迟和多次检查，确保组件已经挂载
    const processQuestionDone = (attempts = 0, maxAttempts = 10) => {
      if (attempts >= maxAttempts) {
        console.error('多次尝试后questionTimerRef仍然不可用')
        return
      }

      if (questionTimerRef.value && eventData.answer) {
        try {
          // 直接使用QuestionTimer组件的handleQuestionDone方法处理事件
          questionTimerRef.value.handleQuestionDone(eventData)
          // 确保停止倒计时
          questionTimerRef.value.timeUp = true
          // 停止内部计时器
          questionTimerRef.value.stopTimer()

          // 如果用户没有选择答案，设置为未回答状态
          if (selectedOptions.value.length === 0) {
            questionTimerRef.value.userAnswered = false
          }
        } catch (error) {
          console.error('处理答案时出错:', error)
        }
      } else {
        console.log(
          `尝试 ${attempts + 1}/${maxAttempts}: questionTimerRef不可用或无答案，300ms后重试`,
        )
        setTimeout(() => processQuestionDone(attempts + 1, maxAttempts), 300)
      }
    }

    // 开始尝试处理
    setTimeout(() => processQuestionDone(), 500)
  }
}

// 刷新获取题目
const refreshQuestion = () => {
  // 重置答题状态
  isAnswering.value = false

  // 重置答案信息
  answerInfo.value = {
    answer: '',
    showAnswer: false,
  }

  // 重置选项
  selectedOptions.value = []

  // 重置提交状态
  isSubmitting.value = false

  // 获取最新题目信息
  initQuestionInfo()
}

// 更新问题状态
const updateQuestionStatus = (status: string) => {
  switch (status) {
    case QuestionStatus.WAITING_OPEN:
      questionStatus.value = '准备中'
      questionStatusText.value = '题目准备中，等待开始抢答'
      break
    case QuestionStatus.WAITING_QUICK:
      questionStatus.value = '抢答中'
      questionStatusText.value = '题目已显示，可以抢答'
      break
    case QuestionStatus.DOING:
      questionStatus.value = '答题中'
      questionStatusText.value = '有人正在答题中，请等待'
      break
    case QuestionStatus.DONE:
      questionStatus.value = '已结束'
      questionStatusText.value = '本题已结束，等待下一题'
      break
    default:
      questionStatus.value = '准备中'
      questionStatusText.value = '题目准备中，请稍候'
  }
}

// 开始倒计时
const startCountdown = () => {
  // 清除之前的计时器
  if (timer) {
    clearInterval(timer)
  }

  // 设置新的计时器
  timer = setInterval(() => {
    // 如果已经确认答案或答题已结束，停止倒计时
    if (
      isAnswering.value &&
      questionTimerRef.value &&
      (questionTimerRef.value.isAnswerConfirmed || questionTimerRef.value.timeUp)
    ) {
      if (timer) {
        clearInterval(timer)
        timer = null
      }
      return
    }

    if (remainingTime.value > 0) {
      remainingTime.value--

      // 同步更新QuestionTimer组件的倒计时（如果在答题状态）
      if (isAnswering.value && questionTimerRef.value) {
        // 如果用户已经确认答案或答题已结束，停止倒计时
        if (questionTimerRef.value.isAnswerConfirmed || questionTimerRef.value.timeUp) {
          if (timer) {
            clearInterval(timer)
            timer = null
          }
          return
        }
        console.log(remainingTime.value, '倒计时')

        questionTimerRef.value.updateRemainingTime(remainingTime.value)
      }
    } else {
      // 时间到，清除计时器
      if (timer) {
        clearInterval(timer)
        timer = null
      }

      // 如果在答题状态且用户尚未确认答案，自动提交答案
      if (
        isAnswering.value &&
        questionTimerRef.value &&
        !questionTimerRef.value.isAnswerConfirmed
      ) {
        confirmAnswer()
      }
    }
  }, 1000) as unknown as number
}

// 处理抢答
const handleRushAnswer = async () => {
  // 如果是答题中状态，显示提示并返回
  if (isButtonDisabled.value) {
    toast.warning(
      questionInfo.value.status === QuestionStatus.WAITING_OPEN
        ? '题目准备中，请等待'
        : questionInfo.value.status === QuestionStatus.DONE
          ? '题目已结束'
          : '有人正在答题中，请等待',
    )
    return
  }

  try {
    toast.loading('抢答中...')

    const params: PlayerHandleQuickParams = {
      xmid: xmid.value,
      question_id: currentQuestionId.value,
      rybh: rybh.value,
    }

    const res = await postPlayerHandleQuick(params)

    if (res && res.flag) {
      toast.success('抢答成功')
      // 切换到答题状态，显示QuestionTimer组件
      isAnswering.value = true
      // 设置答题时间限制
      answerTimeLimit.value = remainingTime.value

      // 重置选项状态
      selectedOptions.value = []

      // 重置提交状态
      isSubmitting.value = false

      // 先清除全局定时器，避免与QuestionTimer组件的定时器重复运行
      if (timer) {
        clearInterval(timer)
        timer = null
      }

      // 如果QuestionTimer组件已挂载，重置计时器
      if (questionTimerRef.value) {
        questionTimerRef.value.resetTimer()
        questionTimerRef.value.updateOptions(formattedOptions.value)
      }

      // 不再调用startCountdown，让QuestionTimer组件管理答题时间
    } else {
      toast.error('抢答失败，请重试')
    }
  } catch (error) {
    console.error('抢答失败:', error)
    toast.error('抢答失败，请重试')
  }
}

// 处理选项选择
const handleOptionSelected = (index: number) => {
  // 根据题目类型处理选择
  if (questionInfo.value.type === '多选题') {
    // 多选题：切换选中状态
    const optionIndex = selectedOptions.value.indexOf(index)
    if (optionIndex > -1) {
      selectedOptions.value.splice(optionIndex, 1)
    } else {
      selectedOptions.value.push(index)
    }
  } else {
    // 单选题：只能选择一个
    selectedOptions.value = [index]
  }

  // 更新子组件中的选项状态
  if (questionTimerRef.value) {
    const updatedOptions = formattedOptions.value.map((option, i) => {
      return {
        ...option,
        selected: selectedOptions.value.includes(i),
      }
    })
    questionTimerRef.value.updateOptions(updatedOptions)
  }
}

// 获取用户选择的答案字符串
const getUserAnswer = (): string => {
  // 将选项索引转换为字母答案（A, B, C...）
  const selectedLabels = selectedOptions.value.map((index) => {
    // 获取选项对象
    const option = questionInfo.value.opt[index]

    // 如果选项是对象，使用value属性
    if (typeof option === 'object' && option !== null && option.value) {
      return option.value
    }

    // 否则，使用默认逻辑
    // 如果是判断题，使用√和×
    if (questionInfo.value.type === '判断题' && index < 2) {
      return index === 0 ? '√' : '×'
    }
    // 否则使用字母
    return String.fromCharCode(65 + index)
  })

  // 将选项连接成字符串，如 "A,B,C"
  return selectedLabels.join(',')
}

// 处理时间到
const handleTimeUp = () => {
  // 时间到，自动提交答案
  confirmAnswer()
}

// 确认答案
const confirmAnswer = async () => {
  // 如果不在答题状态或已经在提交中，则不处理
  if (!isAnswering.value || isSubmitting.value) return

  try {
    // 设置提交状态为true，防止重复点击
    isSubmitting.value = true
    toast.loading('提交答案中...')

    const userAnswer = getUserAnswer()

    // 如果用户没有选择任何答案
    if (!userAnswer) {
      toast.warning('请选择答案')
      toast.close()
      isSubmitting.value = false // 重置提交状态
      return
    }

    // 调用API提交答案
    const res = await postPlayerAnswer({
      xmid: xmid.value,
      question_id: currentQuestionId.value,
      rybh: rybh.value,
      answer: userAnswer,
    })

    console.log('答题结果:', res)
    toast.close()

    // 设置QuestionTimer组件的已回答状态
    if (questionTimerRef.value) {
      questionTimerRef.value.setAnswered()
      questionTimerRef.value.isAnswerConfirmed = true
      // 停止内部计时器
      questionTimerRef.value.stopTimer()

      // 如果已经有正确答案，则显示
      if (answerInfo.value.showAnswer && answerInfo.value.answer) {
        // 使用handleQuestionDone方法处理答案显示
        questionTimerRef.value.handleQuestionDone({
          question_id: String(currentQuestionId.value),
          category: questionInfo.value.category,
          answer: answerInfo.value.answer,
        })
      }
    }

    // 停止倒计时，防止重复提交
    if (timer) {
      clearInterval(timer)
      timer = null
    }

    // 根据得分判断是否正确
    const isCorrect = res.score !== '0'

    // 获取最新分数
    fetchPlayerScore()

    // 不需要在此处重置答题状态，等待question_done事件处理
    // 用户可以继续在QuestionTimer组件中查看自己的答案
  } catch (error) {
    console.error('提交答案失败:', error)
    toast.error('提交答案失败，请重试')
    // 发生错误时也需要重置提交状态，允许用户重新提交
    isSubmitting.value = false
  }
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 页面加载时初始化
onMounted(() => {
  initQuestionInfo()

  // 获取初始分数
  fetchPlayerScore()
})

// 页面卸载时清除计时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})

// 初始化获取问题信息
const initQuestionInfo = async () => {
  try {
    // 显示加载提示
    toast.loading('加载题目中...')

    // 从页面参数获取人员编号，项目ID从store中获取
    const pages = getCurrentPages()
    const currentPage = pages[pages.length - 1]
    // @ts-expect-error uniapp类型定义问题
    const options = currentPage?.$page?.options || {}

    // 优先使用store中的项目ID
    if (!xmid.value) {
      xmid.value = competitionStore.getProjectId() || ''
    }

    // 如果store中没有项目ID，则使用默认值
    if (!xmid.value) {
      xmid.value = '19302' // 默认值，实际应用中应从页面参数获取
    }

    rybh.value = options.rybh || userStore.userInfo.username || '' // 优先使用页面参数，其次使用用户名，最后使用空字符串

    const params: PlayerQuestionInfoParams = {
      xmid: xmid.value,
      rybh: rybh.value,
    }

    const res = await getPlayerQuestionInfo(params)

    // 检查是否需要根据category跳转到其他页面
    if (res && res.category) {
      const category = res.category

      // 如果category不是quick，则跳转到对应页面
      if (category.toLowerCase() !== 'quick') {
        navigateByCategory(category, xmid.value, rybh.value)
        toast.close()
        return
      }
    }

    // 更新问题信息
    if (res && res.question_info) {
      questionInfo.value = res.question_info
      currentQuestionId.value = res.current_question

      // 更新题目级别
      currentQuestion.value.level = res.question_info.total - res.question_info.left + 1
      currentQuestion.value.totalLevels = res.question_info.total

      // 更新剩余时间
      remainingTime.value =
        res.question_info.time < 20
          ? remainingTime.value
            ? remainingTime.value
            : 20
          : res.question_info.time

      // 更新问题状态
      updateQuestionStatus(res.question_info.status)

      // 重置提交状态

      // 只在非答题状态时开始倒计时
      if (!isAnswering.value) {
        // 清除可能存在的计时器
        if (timer) {
          clearInterval(timer)
          timer = null
        }
        startCountdown()
      }
    } else {
      // 没有题目信息
      currentQuestionId.value = '0'
      questionStatusText.value = '等待题目中，请耐心等待'
    }

    toast.close()
  } catch (error) {
    console.error('获取问题信息失败:', error)
    toast.error('获取题目失败，请重试')
    // 重置提交状态，确保即使出错也能重新操作
    isSubmitting.value = false
  }
}
</script>

<style lang="scss">
.header {
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.ma-red {
  background: linear-gradient(135deg, #c41e3a 0%, #8b0000 100%);
}

.rush-button {
  box-shadow: 0 0 30px rgba(196, 30, 58, 0.5);
  animation: pulse-rush 1.5s infinite;
}
/* 禁用状态下停止动画并改变阴影 */
.bg-gray-400 {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
  animation: none;
}

.waiting-container {
  width: 100%;
  padding: 40rpx;
  border-radius: 16rpx;
}

.fixed-bottom-btn {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 50;
  padding: 20rpx 40rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

@keyframes pulse-rush {
  0%,
  100% {
    box-shadow: 0 0 30px rgba(196, 30, 58, 0.5);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 40px rgba(196, 30, 58, 0.8);
    transform: scale(1.05);
  }
}
</style>
