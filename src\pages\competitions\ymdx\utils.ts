/**
 * 页面导航工具函数
 */
import { useToast } from 'wot-design-uni'

/**
 * 根据category跳转到对应页面
 * @param category 分类名称 (must/quick/challenge)
 * @param xmid 项目ID
 * @param rybh 人员编号
 */
export const navigateByCategory = (
  category: string,
  xmid: string | number,
  rybh: string | number,
): void => {
  if (!category) return

  // 获取当前页面路径
  const pages = getCurrentPages()

  const currentPage = pages[pages.length - 1]
  // @ts-expect-error uniapp类型定义问题
  const currentPath = currentPage?.$page?.route || ''

  let targetPath = ''

  switch (category.toLowerCase()) {
    case 'must':
      targetPath = '/pages/competitions/ymdx/required'
      break
    case 'quick':
      targetPath = '/pages/competitions/ymdx/rush'
      break
    case 'challenge':
      targetPath = '/pages/competitions/ymdx/challenge'
      break
    default:
      return
  }

  // 构建完整的URL，包含参数
  const url = `${targetPath}?xmid=${xmid}&rybh=${rybh}`

  // 如果当前已经在目标页面，不需要跳转
  if (currentPath === targetPath.substring(1)) {
    console.log('已经在目标页面，无需跳转')
    return
  }

  // 跳转到对应页面
  console.log(`跳转到页面: ${url}`)
  uni.redirectTo({
    url,
    fail: (err) => {
      console.error('页面跳转失败:', err)
    },
  })
}
