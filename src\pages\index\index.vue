<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '首页',
  },
}
</route>
<template>
  <view class="home-container">
    <!-- 头部用户信息 -->
    <view class="header">
      <view class="header-bg"></view>
      <view class="header-user">
        <view class="user-avatar">
          <image
            v-if="userStore.userInfo.avatar"
            :src="userStore.userInfo.avatar"
            mode="aspectFill"
            @error="avatarLoadError = true"
            :class="{ hidden: avatarLoadError }"
          />
          <wd-icon v-if="!userStore.userInfo.avatar || avatarLoadError" name="user" />
        </view>
        <view class="user-info">
          <text class="user-name">{{ userStore.userInfo.realname || '未登录' }}</text>
          <view v-if="userStore.userInfo.department" class="user-role">
            {{ userStore.userInfo.department }}
            <template v-if="userStore.userInfo.userType === 2 && userStore.userInfo.jobName">
              · {{ userStore.userInfo.jobName }}
            </template>
          </view>
        </view>
      </view>
      <view class="header-stats">
        <view class="stat-item" @tap="navigateToTodoApproval">
          <view class="stat-value">{{ totalTodoCount }}</view>
          <view class="stat-label">待审批</view>
        </view>
        <view class="stat-item" @tap="navigateToMyApply">
          <view class="stat-value">{{ myApplyTotal }}</view>
          <view class="stat-label">待处理</view>
        </view>
        <view class="stat-item" @tap="navigateToUnreadMail">
          <view class="stat-value">{{ unreadMailCount }}</view>
          <view class="stat-label">未读消息</view>
        </view>
      </view>
    </view>

    <!-- 快速功能区 -->
    <view class="quick-actions">
      <view class="section-header">
        <view class="section-title">常用功能</view>
        <view class="section-more" @tap="navigateToApplicationCenter">
          全部
          <wd-icon name="arrow-right" />
        </view>
      </view>
      <!-- 有最近访问记录时显示最近功能 -->
      <view v-if="recentMenus.length > 0" class="quick-grid">
        <view
          v-for="menu in recentMenus.slice(0, 8)"
          :key="menu.id"
          class="quick-item"
          @tap="navigateToMenu(menu)"
        >
          <view class="quick-icon" :style="{ background: colorMap[menu.color] }">
            <view
              :class="[
                `${menu.icon.indexOf('i-mdi-') !== -1 ? '' : 'i-carbon-'}${menu.icon}`,
                'text-white',
                'icon-size',
              ]"
            ></view>
          </view>
          <view class="quick-title">{{ menu.title }}</view>
        </view>
      </view>
    </view>

    <!-- 今日课程表 -->
    <view class="schedule">
      <view class="section-header">
        <view class="section-title">今日课程</view>
        <view class="schedule-date" @click="navigateToSchedule">
          {{ currentDate }}
          <wd-icon name="arrow-right" size="24rpx" color="#999999"></wd-icon>
        </view>
      </view>
      <view class="schedule-list" v-if="todayCourses.length > 0">
        <view
          class="schedule-item"
          v-for="(course, index) in todayCourses"
          :key="index"
          @tap="showCourseSelection(course)"
        >
          <view class="schedule-time">
            <view v-if="course.xqmc" class="campus-name">{{ course.xqmc }}</view>
            <view class="time-range">{{ course.timeRange }}</view>
            <view class="time-period">{{ course.timePeriod }}</view>
          </view>
          <view class="schedule-content">
            <view class="course-name">
              <text v-if="course.skfsmc" class="teach-method">{{ course.skfsmc }}</text>
              {{ course.name }}
              <text v-if="course.fdjs && userStore.userInfo.userType !== 1" class="course-fdjs">
                {{ course.fdjs }}
              </text>
            </view>
            <view class="course-info" v-if="userStore.userInfo.userType !== 1">
              {{ course.info }}
              <text v-if="course.studentCount" class="student-count">
                ({{ course.studentCount }}人)
              </text>
            </view>
            <view class="course-info" v-else>
              {{ course.info }}({{ course.studentCount }}人)
              <text v-if="course.teacherName" class="student-count">
                {{ course.teacherName }}
              </text>
            </view>
            <view class="course-location">
              <wd-icon name="location" class="location-icon" />
              {{ course.location }}
            </view>
          </view>
        </view>
      </view>
      <view class="schedule-empty" v-else>
        <view class="empty-text">今日暂无课程安排</view>
      </view>
    </view>

    <!-- 通知区域 -->
    <view class="notifications">
      <view class="section-header">
        <view class="section-title">通知公告</view>
        <view class="section-more" @tap="navigateToNoticeList">
          更多
          <wd-icon name="arrow-right" />
        </view>
      </view>
      <view class="notice-list">
        <view v-if="noticeList.length > 0">
          <view
            v-for="notice in noticeList"
            :key="notice.id"
            class="notice-item"
            @tap="viewNoticeDetail(notice)"
          >
            <view class="notice-icon" :style="{ backgroundColor: notice.iconBgColor }">
              <wd-icon :name="notice.icon" :color="notice.iconColor" />
            </view>
            <view class="notice-content">
              <view class="notice-title-wrapper">
                <text class="notice-title" :class="{ 'unread-title': notice.isUnread }">
                  {{ notice.title }}
                </text>
              </view>
              <view class="notice-footer">
                <text class="notice-info">{{ notice.department }}</text>
                <view class="notice-footer-right">
                  <text class="notice-time">{{ notice.publishTime }}</text>
                  <text v-if="notice.isUnread" class="notice-badge">未读</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view v-else class="empty-notice">
          <wd-icon name="info-circle" size="64rpx" color="#d1d5db"></wd-icon>
          <text class="empty-text">暂无通知</text>
        </view>
      </view>
    </view>

    <!-- 我的申请 -->
    <view class="apply-section">
      <view class="section-header">
        <view class="section-title">我的申请</view>
        <view class="section-more" @tap="navigateToMyApply">
          查看全部
          <wd-icon name="arrow-right" />
        </view>
      </view>
      <view class="apply-list">
        <view
          class="apply-item"
          v-for="item in myApplyList.slice(0, 3)"
          :key="item.id"
          @tap="handleApplyItem(item)"
        >
          <view class="apply-left flex-1">
            <view
              class="apply-status w-3 h-3 mr-2"
              :class="getApplyStatusClass(item.process_status)"
            ></view>
            <view class="apply-content flex-1">
              <view class="apply-title">{{ item.form_title }}</view>
              <view class="apply-time">申请时间：{{ item.create_time }}</view>
            </view>
          </view>
          <view class="apply-right w-12" @tap="handleApplyItem(item)">查看</view>
        </view>
        <view v-if="myApplyList.length === 0" class="empty-apply">
          <wd-icon name="info-circle" size="64rpx" color="#d1d5db"></wd-icon>
          <text class="empty-text">暂无申请记录</text>
        </view>
      </view>
    </view>
    <!-- 待办任务 -->
    <view class="tasks">
      <view class="section-header">
        <view class="section-title">待办事项</view>
        <view class="section-more" @tap="navigateToAllTodoList">
          查看全部
          <wd-icon name="arrow-right" />
        </view>
      </view>
      <view class="task-list">
        <view
          class="task-item"
          v-for="item in todoList.slice(0, 3)"
          :key="item.id"
          @tap="handleTodoItem(item)"
        >
          <view class="task-left flex-1">
            <view class="task-status w-3 h-3 mr-2" :class="getStatusClass(item)"></view>
            <view class="task-content flex-1">
              <view class="task-title">{{ item.title }}</view>
              <view class="task-time">申请时间：{{ item.applyTime }}</view>
            </view>
          </view>
          <view class="task-right w-12" @tap="handleTodoItem(item)">去处理</view>
        </view>
        <view v-if="todoList.length === 0" class="empty-task">
          <wd-icon name="info-circle" size="64rpx" color="#d1d5db"></wd-icon>
          <text class="empty-text">暂无待办事项</text>
        </view>
      </view>
    </view>

    <!-- 课程选择popup -->
    <wd-popup v-model="showCoursePopup" position="bottom" round class="fixed z-1000">
      <view class="course-detail-popup mb-5">
        <view class="popup-header">
          <text class="popup-title">请选择进行授课确认的课程</text>
          <wd-icon name="close" size="32rpx" @click="showCoursePopup = false"></wd-icon>
        </view>
        <scroll-view class="popup-content" scroll-y>
          <view v-if="originalCourses.length === 0" class="empty-tip">没有找到相关课程数据</view>
          <view v-else class="course-items">
            <view
              v-for="(course, index) in originalCourses"
              :key="index"
              class="course-detail-item"
              @tap="handleCourseSelect(course)"
            >
              <view class="detail-header">
                <text class="detail-title">{{ course.course + '-' + course.className }}</text>
                <view v-if="course.skfsmc" class="detail-teach-method">{{ course.skfsmc }}</view>
              </view>
              <view class="detail-info">
                <view v-if="course.fdjs" class="info-row">
                  <wd-icon name="user-talk" size="32rpx" color="#666666"></wd-icon>
                  <text>辅导教师：{{ course.fdjs }}</text>
                </view>
                <view class="info-row">
                  <wd-icon name="time" size="32rpx" color="#666666"></wd-icon>
                  <text>
                    星期{{ ['日', '一', '二', '三', '四', '五', '六'][course.DayIndex % 7] }}，第{{
                      course.sectionsShow ||
                      course.sections ||
                      `${course.startNode}-${course.endNode}节`
                    }}
                  </text>
                </view>
                <view class="info-row">
                  <wd-icon name="calendar" size="32rpx" color="#666666"></wd-icon>
                  <text>{{ course.date }}</text>
                </view>
                <view class="info-row">
                  <wd-icon name="user" size="32rpx" color="#666666"></wd-icon>
                  <text>{{ course.teacherName }}</text>
                </view>
                <view class="info-row">
                  <wd-icon name="location" size="32rpx" color="#666666"></wd-icon>
                  <text>
                    {{ course.xqmc ? course.xqmc + ' - ' : ''
                    }}{{ course.spaceName || '未安排教室' }}
                  </text>
                </view>
                <view class="info-row">
                  <wd-icon name="usergroup" size="32rpx" color="#666666"></wd-icon>
                  <text>学生人数：{{ course.studentCount }}人</text>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store/user'
import { useRecentMenuStore } from '@/store/recentMenu'
import { ref, computed, onMounted, watch } from 'vue'
import { getSemesterList, getSemesterConfig } from '@/service/semester'
import { getSchedule } from '@/service/schedule'
import { getSectionConfig } from '@/service/section'
import { getNoticeList } from '@/service/notice'
import { getTodoWorkflowList, getWorkflowList4new } from '@/service/workflow'
import { getApplyList } from '@/service/apply'
import { getMailList } from '@/service/mail'
import type { SemesterOption, SemesterConfig } from '@/types/semester'
import type { ScheduleItem } from '@/types/schedule'
import type { SectionInfo } from '@/types/section'
import type { NoticeItem } from '@/types/notice'
import type { WorkflowItem } from '@/types/workflow'
import type { ApplyListQuery, ApplyListResponse, ApplyListItem } from '@/types/apply'
import type { MailQuery } from '@/types/mail'

// 获取用户信息
const userStore = useUserStore()
// 获取最近访问菜单信息
const recentMenuStore = useRecentMenuStore()
// 头像加载失败标记
const avatarLoadError = ref(false)

// 获取最近访问的菜单列表(按访问时间排序)
const recentMenus = computed(() => {
  return recentMenuStore.getRecentMenusByTime
})

// 获取最常用的菜单列表(按访问次数排序)
const mostUsedMenus = computed(() => {
  return recentMenuStore.getRecentMenusByCount
})

// 颜色映射(与application/index.vue保持一致)
const colorMap: Record<string, string> = {
  blue: 'linear-gradient(135deg, #0a84ff, #0055d4)',
  green: 'linear-gradient(135deg, #34c759, #248a3d)',
  orange: 'linear-gradient(135deg, #ff9500, #c93400)',
  red: 'linear-gradient(135deg, #ff3b30, #c90c00)',
  purple: 'linear-gradient(135deg, #5e5ce6, #3634a3)',
  teal: 'linear-gradient(135deg, #5ac8fa, #0071a4)',
  yellow: 'linear-gradient(135deg, #ffcc00, #d68e00)',
  pink: 'linear-gradient(135deg, #ff2d55, #b3002d)',
}

// 菜单导航
const navigateToMenu = (menu: any) => {
  if (!menu.path) return

  // 处理路径末尾的斜杠 - 去掉URL结尾的斜杠
  let processedPath = menu.path
  if (processedPath.endsWith('/')) {
    processedPath = processedPath.slice(0, -1)
  }

  // 如果是外部链接
  if (processedPath.startsWith('http')) {
    // 处理外部链接
    uni.navigateTo({
      url: `/pages/webview/index?url=${encodeURIComponent(processedPath)}`,
    })
    return
  }

  // TabBar页面列表 - 与pages.config.ts中的tabBar配置保持一致
  const tabBarPages = [
    '/index/index',
    '/application/index',
    '/workflow/index',
    '/Mail/mailList',
    '/about/about',
  ]

  // 检查是否是TabBar页面
  if (tabBarPages.includes(processedPath)) {
    // 使用switchTab跳转到TabBar页面
    uni.switchTab({
      url: `/pages${processedPath}`,
      fail: (err) => {
        console.error('TabBar页面跳转失败:', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none',
        })
      },
    })
  } else {
    // 内部非TabBar页面导航使用navigateTo
    uni.navigateTo({
      url: `/pages${processedPath}`,
      fail: (err) => {
        console.error('导航失败:', err)
        uni.showToast({
          title: '页面不存在',
          icon: 'none',
        })
      },
    })
  }
}

// 跳转到应用中心
const navigateToApplicationCenter = () => {
  uni.switchTab({
    url: '/pages/application/index',
  })
}

// ----- 通知相关逻辑 -----
// 通知列表数据
const noticeList = ref<NoticeItem[]>([])
const totalNoticeCount = ref(0) // 通知总数

// 获取通知列表（首页只显示最新的几条）
const getNotices = async () => {
  try {
    const params = {
      page: 1,
      pageSize: 3, // 首页只显示3条最新通知
    }

    const res = await getNoticeList(params)

    noticeList.value = res.items.map((item) => ({
      id: item.id,
      title: item.title,
      content: item.content,
      department: item.publisherDeptName,
      publishTime: item.publishTime,
      type: item.columnName,
      isImportant: false,
      isUnread: item.readCount === 0,
      icon: getIconName(item.columnName),
      iconBgColor: getIconBgColor(item.columnName),
      iconColor: getIconColor(item.columnName),
      category: item.columnName,
      categoryColor: getCategoryColor(item.columnName),
    }))
    totalNoticeCount.value = res.total
  } catch (error) {
    console.error('获取通知列表失败:', error)
  }
}

// 查看通知详情
const viewNoticeDetail = (notice: NoticeItem) => {
  // 将通知数据存储到localStorage
  uni.setStorageSync('NOTICE_DETAIL_DATA', JSON.stringify(notice))

  // 跳转到通知详情页面
  uni.navigateTo({
    url: `/pages/noticeDetail?id=${notice.id}`,
  })
}

// 跳转到通知列表页
const navigateToNoticeList = () => {
  uni.navigateTo({
    url: '/pages/student/notice',
  })
}

// 根据通知类型获取图标名称
const getIconName = (type: string) => {
  switch (type) {
    case '教务通知':
      return 'warning'
    case '学工通知':
      return 'setting'
    case '活动通知':
      return 'calendar'
    case '就业通知':
      return 'user'
    case '资源通知':
      return 'document'
    case '医疗通知':
      return 'star'
    case '通知公告':
      return 'notification-filled'
    default:
      return 'notification'
  }
}

// 根据通知类型获取图标底色
const getIconBgColor = (type: string) => {
  switch (type) {
    case '教务通知':
      return '#e0f2fe' // 浅蓝色背景
    case '学工通知':
      return '#e0e7ff' // 浅靛蓝色背景
    case '活动通知':
      return '#dcfce7' // 浅绿色背景
    case '就业通知':
      return '#f3e8ff' // 浅紫色背景
    case '资源通知':
      return '#fef9c3' // 浅黄色背景
    case '医疗通知':
      return '#d1fae5' // 浅绿色背景
    case '通知公告':
      return '#e2f2ff' // 浅蓝色背景
    default:
      return '#e2e8f0' // 浅灰色背景
  }
}

// 根据通知类型获取图标颜色
const getIconColor = (type: string) => {
  switch (type) {
    case '教务通知':
      return '#0284c7' // 深蓝色
    case '学工通知':
      return '#4f46e5' // 深靛蓝色
    case '活动通知':
      return '#16a34a' // 深绿色
    case '就业通知':
      return '#9333ea' // 深紫色
    case '资源通知':
      return '#ca8a04' // 黄色
    case '医疗通知':
      return '#10b981' // 绿色
    case '通知公告':
      return '#007aff' // 蓝色
    default:
      return '#64748b' // 深灰色
  }
}

// 根据通知类型获取分类颜色
const getCategoryColor = (type: string) => {
  switch (type) {
    case '教务通知':
      return 'blue'
    case '学工通知':
      return 'indigo'
    case '活动通知':
      return 'green'
    case '就业通知':
      return 'purple'
    case '资源通知':
      return 'yellow'
    case '医疗通知':
      return 'green'
    case '通知公告':
      return 'blue'
    default:
      return 'gray'
  }
}

// ----- 课程表相关逻辑 -----
// 今日课程数据
const todayCourses = ref<any[]>([])
// 当前日期
const currentDate = ref<string>('')
// 当前星期
const currentDay = ref<string>('')
// 学期选项
const semesterOptions = ref<SemesterOption[]>([])
// 当前学期
const currentSemesterOption = ref<SemesterOption | null>(null)
// 学期配置
const semesterConfig = ref<SemesterConfig | null>(null)
// 课节配置
const sectionConfig = ref<SectionInfo[]>([])
// 节次配置加载状态
const sectionConfigLoaded = ref<boolean>(false)

// 获取学年学期选项
const getSemesterOptions = async (): Promise<void> => {
  try {
    const res = await getSemesterList()
    // 对学期列表进行倒序排序
    semesterOptions.value = res.semesters.sort((a, b) => {
      return b.value.localeCompare(a.value)
    })

    // 默认选择当前学期
    const currSemester = res.semesters.find((item: SemesterOption) => item.isCurrent)
    if (currSemester) {
      currentSemesterOption.value = currSemester
      // 获取默认学期的配置
      await getSemesterWeeks(currSemester.value)
    } else if (res.semesters.length > 0) {
      currentSemesterOption.value = res.semesters[0]
      // 获取默认学期的配置
      await getSemesterWeeks(res.semesters[0].value)
    }
  } catch (error) {
    console.error('获取学年学期选项失败:', error)
  }
}

// 获取学期配置
const getSemesterWeeks = async (semester: string): Promise<void> => {
  try {
    const res = await getSemesterConfig(semester)
    semesterConfig.value = res.semestersConfig

    // 检查当前学期是否为小学期
    const isXxq = currentSemesterOption.value?.isCurrent && currentSemesterOption.value?.isXxq

    // 计算当前周次
    const calculatedWeek = calculateCurrentWeek(res.semestersConfig, isXxq)

    // 获取节次配置（根据学期的季节制度）
    if (currentSemesterOption.value?.seasonal) {
      await fetchSectionConfig(currentSemesterOption.value.seasonal as '夏令制' | '冬令制')
    } else {
      await fetchSectionConfig('冬令制') // 默认使用冬令制
    }

    // 加载今日课程
    await loadTodayCourses(semester, calculatedWeek, isXxq)
  } catch (error) {
    console.error('获取学期配置失败:', error)
  }
}

// 获取课节时间配置
const fetchSectionConfig = async (seasonal: '夏令制' | '冬令制' = '冬令制'): Promise<void> => {
  if (sectionConfig.value.length > 0 && sectionConfig.value[0].seasonal === seasonal) {
    console.log('使用已缓存的课节配置')
    return
  }

  console.log('加载课节配置:', seasonal)
  try {
    const res = await getSectionConfig()
    // 根据 campus 和 seasonal 过滤配置
    sectionConfig.value = res.sectionConfig.filter(
      (config) =>
        config.campus === '' && // 默认校区为空
        config.seasonal === seasonal,
    )
    sectionConfigLoaded.value = true
  } catch (error) {
    console.error('获取课节时间配置失败:', error)
  }
}

// 当前季节制度
const currentSeasonal = computed<'夏令制' | '冬令制'>(
  () => (currentSemesterOption.value?.seasonal as '夏令制' | '冬令制') || '冬令制',
)

// 监听季节制度变化
watch(currentSeasonal, (newSeasonal) => {
  console.log('季节制度变化:', newSeasonal)
  fetchSectionConfig(newSeasonal)
})

// 计算当前周次
const calculateCurrentWeek = (config: SemesterConfig, isXxq: boolean = false): number => {
  // 根据是否为小学期选择不同的开始日期和总周数
  const startDateStr = isXxq ? config.xxqStartDate : config.startDate
  const totalWeeks = isXxq ? config.xxqWeek : config.totalWeeks

  const startDate = new Date(startDateStr)
  const today = new Date()

  // 先调整学期起始日期到最近的周一
  const firstDayOfWeek = startDate.getDay() // 0是周日，1-6是周一到周六
  const adjustDays = firstDayOfWeek === 0 ? -6 : 1 - firstDayOfWeek
  startDate.setDate(startDate.getDate() + adjustDays)

  // 计算两个日期之间的天数差
  const diffTime = today.getTime() - startDate.getTime()
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

  // 计算当前是第几周
  const weekNumber = Math.floor(diffDays / 7) + 1

  // 如果计算得到的周次超出总周数，返回最后一周
  // 如果计算得到的周次小于1，返回第一周
  if (weekNumber > totalWeeks) {
    return totalWeeks
  } else if (weekNumber < 1) {
    return 1
  }

  return weekNumber
}

// 格式化日期为 "2025年5月15日 星期三" 格式
const formatCurrentDate = (): void => {
  const today = new Date()
  const year = today.getFullYear()
  const month = today.getMonth() + 1
  const date = today.getDate()

  const weekDayMap = ['日', '一', '二', '三', '四', '五', '六']
  const weekDay = weekDayMap[today.getDay()]

  currentDate.value = `${year}年${month}月${date}日 星期${weekDay}`
  currentDay.value = `周${weekDay}`
}

// ----- 课程选择popup相关逻辑 -----
// popup显示状态
const showCoursePopup = ref(false)
// 原始课程列表数据（不合并）
const originalCourses = ref<ScheduleItem[]>([])

// 显示课程选择popup
const showCourseSelection = (course?: any) => {
  // 获取今天是星期几
  const today = new Date()
  const dayOfWeek = today.getDay() === 0 ? 7 : today.getDay() // 将周日从0转为7

  // 如果点击了特定课程，并且有原始课程数据
  if (course && course.originalCourses && course.originalCourses.length > 0) {
    // 如果只有一个课程（没有合班），直接跳转
    if (course.originalCourses.length === 1) {
      handleCourseSelect(course.originalCourses[0])
      return
    }

    // 否则显示选择弹窗
    originalCourses.value = course.originalCourses
    showCoursePopup.value = true
    return
  }

  // 如果已经有数据，检查是否只有一个课程
  if (originalCourses.value.length === 1) {
    handleCourseSelect(originalCourses.value[0])
    return
  } else if (originalCourses.value.length > 1) {
    showCoursePopup.value = true
    return
  }

  // 如果当前学期选项存在，获取课程数据
  if (currentSemesterOption.value) {
    // 检查当前学期是否为小学期
    const isXxq = currentSemesterOption.value.isCurrent && currentSemesterOption.value.isXxq

    // 计算当前周次
    const week = semesterConfig.value ? calculateCurrentWeek(semesterConfig.value, isXxq) : 1

    // 获取课程数据
    getSchedule(currentSemesterOption.value.value, week, isXxq ? 1 : 0)
      .then((res) => {
        // 过滤出今天的课程，不进行合并
        originalCourses.value = res.list.filter((course) => course.DayIndex === dayOfWeek)

        // 如果只有一个课程，直接跳转
        if (originalCourses.value.length === 1) {
          handleCourseSelect(originalCourses.value[0])
        } else if (originalCourses.value.length > 1) {
          showCoursePopup.value = true
        } else {
          uni.showToast({
            title: '今日暂无课程',
            icon: 'none',
          })
        }
      })
      .catch((error) => {
        console.error('获取课程数据失败:', error)
        uni.showToast({
          title: '获取课程数据失败',
          icon: 'none',
        })
      })
  } else {
    uni.showToast({
      title: '未找到当前学期信息',
      icon: 'none',
    })
  }
}

// 处理课程选择
const handleCourseSelect = (course: ScheduleItem) => {
  console.log('选择课程:', course)
  if (course.teachingTaskId === 0) {
    /* uni.showToast({
      title: '页面跳转失败',
      icon: 'none',
    }) */
    return
  }
  // 关闭popup
  showCoursePopup.value = false
  // 根据用户类型决定跳转路径
  if (userStore.userInfo.userType === 1) {
    // 学生用户，跳转到学习计划确认页面
    uni.navigateTo({
      url: `/pages/student/study-plan/confirm?id=${course.id}`,
      success: () => {
        console.log('跳转成功，学习计划确认页面')
      },
      fail: (error) => {
        console.error('跳转失败:', error)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none',
        })
      },
    })
  } else {
    // 教师用户，跳转到教学日志编辑页面
    uni.navigateTo({
      url: `/pages/teacher/teaching-affairs/teaching-journal-edit?id=${course.id}`,
      success: () => {
        console.log('跳转成功，课程ID:', course.id)
      },
      fail: (error) => {
        console.error('跳转失败:', error)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none',
        })
      },
    })
  }
}

// 获取课程时间显示
const getTimeDisplay = (course: ScheduleItem) => {
  const startNode = Number(course.startNode)
  const endNode = Number(course.endNode)

  // 从节次配置中获取时间范围
  if (sectionConfigLoaded.value && sectionConfig.value.length > 0) {
    // 查找开始节次的配置
    const startSectionConfig = sectionConfig.value.find((config) => config.section === startNode)
    // 查找结束节次的配置
    const endSectionConfig = sectionConfig.value.find((config) => config.section === endNode)

    if (startSectionConfig && endSectionConfig) {
      // 去掉秒数显示，只保留小时和分钟
      const startTime = startSectionConfig.startTime.substring(0, 5)
      const endTime = endSectionConfig.endTime.substring(0, 5)
      return `${startTime}-${endTime} ${course.sectionsShow || course.sections || `${startNode}-${endNode}节`}`
    }
  }

  // 如果找不到配置，使用默认时间映射
  let timeRange = ''
  if (startNode === 1 && endNode === 2) timeRange = '08:00-09:40'
  else if (startNode === 3 && endNode === 4) timeRange = '10:00-11:40'
  else if (startNode === 5 && endNode === 6) timeRange = '14:00-15:40'
  else if (startNode === 7 && endNode === 8) timeRange = '16:00-17:40'
  else if (startNode === 9 && endNode === 10) timeRange = '19:00-20:40'
  else timeRange = `第${startNode}-${endNode}节`

  return `${timeRange} ${course.sectionsShow || course.sections || `${startNode}-${endNode}节`}`
}

// 获取今日课程
const loadTodayCourses = async (
  semester: string,
  week: number,
  isXxq: boolean = false,
): Promise<void> => {
  try {
    const res = await getSchedule(semester, week, isXxq ? 1 : 0)
    console.log(res)
    // 获取今天是星期几
    const today = new Date()
    const dayOfWeek = today.getDay() === 0 ? 7 : today.getDay() // 将周日从0转为7

    // 保存原始课程数据，用于popup显示
    originalCourses.value = res.list.filter((course) => course.DayIndex === dayOfWeek)

    // 过滤出今天的课程
    const todaySchedules = res.list.filter((course) => course.DayIndex === dayOfWeek)

    // 使用Map按mergeTaskId和上课时间段对课程进行分组
    const mergedCourses = new Map<string, Array<ScheduleItem>>()
    todaySchedules.forEach((course) => {
      // 使用mergeTaskId和上课时间段作为key
      const key = `${course.mergeTaskId}-${course.startNode}-${course.endNode}`
      if (!mergedCourses.has(key)) {
        mergedCourses.set(key, [course])
      } else {
        mergedCourses.get(key)?.push(course)
      }
    })

    // 转换为显示格式
    todayCourses.value = Array.from(mergedCourses.values())
      .map((courses) => {
        const course = courses[0] // 获取第一个课程的基本信息

        // 计算节次显示
        const startNode = Number(course.startNode)
        const endNode = Number(course.endNode)
        let timePeriod = ''
        const timeRange = `${course.jckssj.substring(0, 5)}-${course.jcjssj.substring(0, 5)}`

        // 根据节次区分时间段
        if (startNode <= 2) timePeriod = '上午'
        else if (startNode <= 4) timePeriod = '上午'
        else if (startNode <= 6) timePeriod = '下午'
        else if (startNode <= 8) timePeriod = '下午'
        else timePeriod = '晚上'

        // 合并班级信息
        let className = course.className
        if (courses.length > 1) {
          className = courses.map((c) => c.className).join('、')
        }

        // 处理学生人数信息
        let studentCount = course.studentCount || 0
        if (courses.length > 1) {
          // 如果有多个班级，累加学生人数
          studentCount = courses.reduce((total, c) => total + (c.studentCount || 0), 0)
        }

        return {
          timeRange,
          // 使用API返回的sectionsShow而不是拼接节次
          timePeriod: `${timePeriod} ${course.sectionsShow || course.sections || `${startNode}-${endNode}节`}`,
          name: course.course,
          // 如果有辅导教师，在课程名称后添加辅导教师信息
          fdjs: course.fdjs ? `辅导：${course.fdjs}` : '',
          // 授课方式
          skfsmc: course.skfsmc || '',
          // 校区名称
          xqmc: course.xqmc || '',
          // 如果是学生用户(userType为1)，不显示班级信息
          info: className,
          teacherName: course.teacherName,
          // 添加学生人数信息
          studentCount,
          location: course.spaceName,
          // 保存原始课程数据，用于点击时显示
          originalCourses: courses,
        }
      })
      // 按开始节次排序
      .sort((a, b) => {
        const getNodeNumber = (str: string) => {
          const match = str.match(/(\d+)-\d+/)
          return match ? parseInt(match[1]) : 0
        }
        return getNodeNumber(a.timePeriod) - getNodeNumber(b.timePeriod)
      })

    // 更新当前日期显示
    formatCurrentDate()
  } catch (error) {
    console.error('获取今日课程失败:', error)
  }
}

// 跳转到课程表页面
const navigateToSchedule = () => {
  // 根据用户类型决定跳转到不同的课程表页面
  if (userStore.userInfo.userType === 1) {
    // 学生用户，跳转到学生课程表页面
    uni.navigateTo({
      url: '/pages/student/StudentSchedule/scheduleTable',
    })
  } else {
    // 教师用户，跳转到教师课程表页面
    uni.navigateTo({
      url: '/pages/teacher/scheduleTable',
    })
  }
}

// ----- 待办事项相关逻辑 -----
// 待办事项列表数据
const todoList = ref<WorkflowItem[]>([])
const totalTodoCount = ref(0) // 待办总数
// 我的申请列表数据
const myApplyList = ref<ApplyListItem[]>([]) // 我发起的申请列表
const myApplyTotal = ref(0) // 我发起的申请总数

// 获取待办事项列表
const getTodoList = async () => {
  try {
    const res = await getWorkflowList4new() // 首页只显示3条最新待办
    todoList.value = res.items
    totalTodoCount.value = res.total
  } catch (error) {
    console.error('获取待办事项列表失败:', error)
  }
}

// 获取我的申请列表
const getMyApplyList = async () => {
  try {
    const applyParams: ApplyListQuery = {
      page: 1,
      pageSize: 3, // 首页显示3条最新申请
      sortBy: 'create_time',
      sortOrder: 'desc',
    }

    const res = await getApplyList(applyParams)
    console.log(res.items)

    myApplyList.value = res.items
    myApplyTotal.value = res.total
  } catch (error) {
    console.error('获取我的申请列表失败:', error)
  }
}

// 查看待办详情或去处理
const handleTodoItem = (item: WorkflowItem) => {
  // 跳转到待办处理页面
  uni.navigateTo({
    url: `/pages/workflow/approve?id=${item.id}&code=${item.code || ''}`,
  })
}

// 跳转到所有待办页面
const navigateToAllTodoList = () => {
  uni.switchTab({
    url: '/pages/workflow/index',
  })
}

// 获取状态样式类名
const getStatusClass = (item: WorkflowItem) => {
  // 根据process_status判断
  if (item.process_status === 0) {
    // 审批中的任务视为紧急
    return 'urgent'
  } else {
    // 其他状态视为普通
    return 'normal'
  }
}

// 未读邮件数量
const unreadMailCount = ref(0)

// 获取未读邮件数量
const getUnreadMailCount = async () => {
  try {
    const params: MailQuery = {
      page: 1,
      pageSize: 10,
      type: 'in',
      subject: '',
      sortBy: 'id',
      sortOrder: 'desc',
      senderName: '',
      sendTime: '',
      viewStatus: '',
      viewTime: '',
      recipients: '',
      wechatPushFlag: '',
    }

    const res = await getMailList(params)
    // 计算未读邮件数量
    unreadMailCount.value = res.items.filter((item) => item.viewStatus === '否').length
  } catch (error) {
    console.error('获取未读邮件数量失败:', error)
  }
}

// 页面加载时初始化
onMounted(() => {
  // 直接加载数据，自动登录由中间页面处理
  initPageData()
})

// 跳转到待我审批页面
const navigateToTodoApproval = () => {
  // 使用本地存储来保存要显示的标签页索引
  uni.setStorageSync('WORKFLOW_ACTIVE_TAB', 0)
  uni.switchTab({
    url: '/pages/workflow/index',
  })
}

// 跳转到我发起的申请页面
const navigateToMyApply = () => {
  // 使用本地存储来保存要显示的标签页索引
  uni.setStorageSync('WORKFLOW_ACTIVE_TAB', 1)
  uni.switchTab({
    url: '/pages/workflow/index',
  })
}

// 处理申请项点击事件
const handleApplyItem = (item: ApplyListItem) => {
  // 跳转到申请详情页面
  uni.navigateTo({
    url: `/pages/workflow/detail?id=${item.id}`,
  })
}

// 获取申请状态样式类
const getApplyStatusClass = (status: string) => {
  switch (status) {
    case '0':
      return 'bg-orange-400' // 待审批 - 橙色
    case '1':
      return 'bg-green-400' // 已审批 - 绿色
    case '2':
      return 'bg-red-400' // 已拒绝 - 红色
    default:
      return 'bg-gray-400' // 默认 - 灰色
  }
}

// 跳转到未读邮件页面
const navigateToUnreadMail = () => {
  uni.switchTab({
    url: '/pages/Mail/mailList',
  })
}

/**
 * 初始化页面数据
 */
const initPageData = () => {
  // 如果用户已登录，根据用户类型初始化默认菜单
  if (userStore.isLogined && userStore.userInfo.userType) {
    recentMenuStore.initDefaultMenus(userStore.userInfo.userType)
  }

  // 获取学期信息和今日课程
  getSemesterOptions()

  // 设置当前日期
  formatCurrentDate()

  // 获取默认课节时间配置(冬令制)
  fetchSectionConfig('冬令制')

  // 获取通知列表
  getNotices()

  // 获取待办事项列表
  getTodoList()

  // 获取我的申请列表总数
  getMyApplyList()

  // 获取未读邮件数量
  getUnreadMailCount()
}
</script>

<style lang="scss" scoped>
.home-container {
  padding-bottom: 70px;
  background-color: #f2f5f8;
}
/* 头部用户信息 */
.header {
  position: relative;
  padding: 20px 15px;
  overflow: hidden;
  color: white;
  background: linear-gradient(135deg, #0a84ff, #0055d4);
  border-radius: 0 0 15px 15px;
}

.header-bg {
  position: absolute;
  top: 0;
  right: 0;
  width: 150px;
  height: 150px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(30%, -30%);
}

.header-user {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  margin-right: 12px;
  overflow: hidden;
  font-size: 20px;
  color: #0a84ff;
  background-color: white;
  border-radius: 50%;

  image {
    width: 100%;
    height: 100%;
  }

  .hidden {
    display: none;
  }
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.user-role {
  margin-top: 2px;
  font-size: 14px;
  opacity: 0.8;
}

.header-stats {
  position: relative;
  z-index: 1;
  display: flex;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

.stat-item {
  position: relative;
  flex: 1;
  padding: 0 10px;
  text-align: center;

  &:not(:last-child)::after {
    position: absolute;
    top: 20%;
    right: 0;
    width: 1px;
    height: 60%;
    content: '';
    background-color: rgba(255, 255, 255, 0.2);
  }
}

.stat-value {
  margin-bottom: 3px;
  font-size: 18px;
  font-weight: 600;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}
/* 快速功能区 */
.quick-actions {
  padding: 15px;
  margin: 20px 15px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.section-more {
  font-size: 14px;
  color: #999;
}

.quick-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.quick-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.quick-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  margin-bottom: 8px;
  font-size: 22px;
  color: #0a84ff;
  background-color: #f2f7ff;
  border-radius: 12px;
}

.quick-title {
  font-size: 12px;
  color: #666;
}
/* 今日课程表 */
.schedule {
  padding: 15px;
  margin: 20px 15px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.schedule-date {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #666;

  .wd-icon {
    margin-left: 4rpx;
  }
}

.schedule-list {
  margin-top: 10px;
}

.schedule-item {
  display: flex;
  padding-bottom: 12px;
  margin-bottom: 12px;
  border-bottom: 1px solid #f2f2f7;

  &:last-child {
    padding-bottom: 0;
    margin-bottom: 0;
    border-bottom: none;
  }
}

.schedule-time {
  min-width: 60px;
  margin-right: 15px;
  text-align: center;
}

.campus-name {
  padding: 2px 6px;
  margin-bottom: 3px;
  font-size: 12px;
  color: #666;
  text-align: center;
  background-color: #f0f7ff;
  border-radius: 4px;
}

.time-range {
  font-size: 13px;
  color: #999;
}

.time-period {
  display: inline-block;
  padding: 2px 8px;
  margin-top: 4px;
  font-size: 12px;
  color: #666;
  background-color: #f2f2f7;
  border-radius: 4px;
}

.teach-method {
  padding: 2px 6px;
  margin-top: 4px;
  font-size: 12px;
  color: #666;
  text-align: center;
  background-color: #f2f7f2;
  border-radius: 4px;
}

.schedule-content {
  flex: 1;
}

.course-name {
  margin-bottom: 3px;
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.course-info {
  margin-bottom: 5px;
  font-size: 13px;
  color: #999;
}

.course-location {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #666;
}

.location-icon {
  margin-right: 5px;
  font-size: 14px;
  color: #007aff;
}
/* 通知区域 */
.notifications {
  padding: 15px;
  margin: 20px 15px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.notice-list {
  margin-top: 10px;
}

.notice-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid #f2f2f7;

  &:last-child {
    padding-bottom: 0;
    border-bottom: none;
  }

  &:first-child {
    padding-top: 0;
  }
}

.notice-icon {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  margin-right: 12px;
  font-size: 18px;
  border-radius: 10px;

  &.announcement {
    color: #ff9500;
    background-color: #ffe8cc;
  }

  &.event {
    color: #ff3b30;
    background-color: #ffe2e5;
  }

  &.news {
    color: #007aff;
    background-color: #e2f2ff;
  }
}

.notice-content {
  flex: 1;
}

.notice-title-wrapper {
  display: flex;
  margin-bottom: 3px;
}

.notice-title {
  display: -webkit-box;
  flex: 1;
  overflow: hidden;
  font-size: 15px;
  font-weight: 500;
  line-height: 1.3;
  color: #333;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.unread-title {
  font-weight: 600;
  color: #000;
}

.notice-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 12px;
  color: #aaa;
}

.notice-footer-right {
  display: flex;
  align-items: center;
}

.notice-info {
  color: #666;
}

.notice-time {
  margin-right: 6px;
  color: #aaa;
}

.notice-badge {
  flex-shrink: 0;
  padding: 1px 6px;
  font-size: 10px;
  line-height: 1.5;
  color: white;
  background-color: #ff3b30;
  border-radius: 10px;
}

.empty-notice {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120rpx;
  margin-top: 20rpx;

  .empty-text {
    margin-top: 12rpx;
    font-size: 28rpx;
    color: #999;
  }
}
/* 待办任务 */
.tasks {
  padding: 15px;
  margin: 20px 15px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.task-list {
  margin-top: 10px;
}

.task-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f2f2f7;

  &:last-child {
    padding-bottom: 0;
    border-bottom: none;
  }

  &:first-child {
    padding-top: 0;
  }
}

.task-left {
  display: flex;
  align-items: center;
}

.task-status {
  border-radius: 50%;

  &.urgent {
    background-color: #ff3b30;
  }

  &.normal {
    background-color: #ffcc00;
  }
}

.task-content {
  display: flex;
  flex-direction: column;
}

.task-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.task-time {
  margin-top: 3px;
  font-size: 12px;
  color: #aaa;
}

.task-right {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #007aff;
  text-align: center;
}

.schedule-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 120rpx;
  margin-top: 20rpx;

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

.empty-task {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120rpx;
  margin-top: 20rpx;

  .empty-text {
    margin-top: 12rpx;
    font-size: 28rpx;
    color: #999;
  }
}
/* 我的申请 */
.apply-section {
  padding: 15px;
  margin: 20px 15px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.apply-list {
  margin-top: 10px;
}

.apply-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #f2f2f7;

  &:last-child {
    border-bottom: none;
  }

  &:first-child {
    padding-top: 0;
  }
}

.apply-left {
  display: flex;
  align-items: center;
}

.apply-status {
  border-radius: 50%;

  &.bg-orange-400 {
    background-color: #fb923c;
  }

  &.bg-green-400 {
    background-color: #4ade80;
  }

  &.bg-red-400 {
    background-color: #f87171;
  }

  &.bg-gray-400 {
    background-color: #9ca3af;
  }
}

.apply-content {
  display: flex;
  flex-direction: column;
}

.apply-title {
  font-size: 15px;
  font-weight: 500;
  color: #333;
}

.apply-time {
  margin-top: 3px;
  font-size: 12px;
  color: #aaa;
}

.apply-right {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #007aff;
  text-align: center;
}

.empty-apply {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120rpx;

  .empty-text {
    margin-top: 10rpx;
    font-size: 28rpx;
    color: #999;
  }
}
/* 课程选择popup样式 */
.course-detail-popup {
  box-sizing: border-box;
  max-height: 70vh;
  padding: 32rpx;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;
  }

  .popup-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  .popup-content {
    max-height: calc(70vh - 100rpx);
  }

  .course-items {
    .course-detail-item {
      padding: 24rpx;
      margin-bottom: 24rpx;
      background: #f7f8fa;
      border-radius: 12rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .detail-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 16rpx;
        margin-bottom: 16rpx;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      }

      .detail-title {
        flex: 1;
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
      }

      .detail-teach-method {
        padding: 2px 6px;
        font-size: 22rpx;
        color: #666;
        background-color: #f2f7f2;
        border-radius: 4px;
      }

      .detail-info {
        margin-bottom: 16rpx;
      }

      .info-row {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;
        font-size: 26rpx;
        color: #666;

        &:last-child {
          margin-bottom: 0;
        }

        .wd-icon {
          margin-right: 12rpx;
        }
      }
    }
  }

  .empty-tip {
    padding: 32rpx 0;
    font-size: 28rpx;
    color: #999;
    text-align: center;
  }
}

.course-fdjs {
  margin-left: 6px;
  font-size: 13px;
  font-weight: normal;
  color: #666;
}

.student-count {
  margin-left: 4px;
  font-size: 12px;
  color: #666;
}
</style>
