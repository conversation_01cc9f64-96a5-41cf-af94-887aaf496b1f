<route lang="json5">
{
  style: {
    navigationBarTitleText: '请假详情',
  },
}
</route>

<template>
  <view class="leave-detail bg-gray-100 min-h-screen">
    <!-- 加载状态 -->
    <view v-if="loading" class="p-8 flex flex-col items-center justify-center">
      <view class="loading-spinner"></view>
      <text class="text-gray-500 mt-2">加载中...</text>
    </view>

    <template v-else-if="leaveDetail">
      <!-- 请假信息卡片 -->
      <view class="custom-card bg-white rounded-lg overflow-hidden mb-4 mx-4 mt-4">
        <view class="card-header flex justify-between items-center p-3">
          <text class="card-title font-bold text-lg">请假信息</text>
          <view
            class="badge"
            :class="{
              'badge-warning': getApprovalStatus().status === '审批中',
              'badge-success': getApprovalStatus().status === '已批准',
              'badge-danger': getApprovalStatus().status === '已拒绝',
            }"
          >
            {{ getApprovalStatus().status }}
          </view>
        </view>
        <view class="p-4">
          <view class="info-item flex mb-3">
            <text class="label w-24 text-gray-500">请假类型</text>
            <text>{{ leaveDetail.leaveTypeName }}</text>
          </view>
          <view class="info-item flex mb-3">
            <text class="label w-24 text-gray-500">请假时间</text>
            <text>{{ leaveDetail.leaveTime }}</text>
          </view>
          <view class="info-item flex mb-3">
            <text class="label w-24 text-gray-500">请假时长</text>
            <text>{{ leaveDetail.leaveHours }} 小时</text>
          </view>
          <view class="info-item flex mb-3">
            <text class="label w-24 text-gray-500">请假原因</text>
            <text>{{ leaveDetail.leaveReason }}</text>
          </view>
          <view class="info-item flex mb-3">
            <text class="label w-24 text-gray-500">申请时间</text>
            <text>{{ leaveDetail.requestTime }}</text>
          </view>
          <view class="info-item flex mb-3">
            <text class="label w-24 text-gray-500">是否出校</text>
            <text>{{ leaveDetail.isApplyForExitCampus === 1 ? '是' : '否' }}</text>
          </view>
          <view v-if="leaveDetail.remark" class="info-item flex mb-3">
            <text class="label w-24 text-gray-500">备注</text>
            <text>{{ leaveDetail.remark }}</text>
          </view>
        </view>
      </view>

      <!-- 审批进度卡片 -->
      <view class="custom-card bg-white rounded-lg overflow-hidden mb-4 mx-4">
        <view class="card-header flex justify-between items-center p-3">
          <text class="card-title font-bold text-lg">审批流程</text>
        </view>
        <view class="p-4">
          <view class="timeline">
            <!-- 申请 -->
            <view class="timeline-item flex">
              <view class="timeline-dot-wrapper flex flex-col items-center mr-4">
                <view class="timeline-dot bg-blue-500">
                  <wd-icon name="message" color="#FFFFFF" size="14px" />
                </view>
                <view class="timeline-line"></view>
              </view>
              <view class="timeline-content flex-1">
                <view class="font-medium">提交申请</view>
                <view class="text-xs text-gray-500">{{ leaveDetail.requestTime }}</view>
                <view class="text-sm mt-1">{{ leaveDetail.studentName }} 提交了请假申请</view>
              </view>
            </view>

            <!-- 辅导员审批 -->
            <view class="timeline-item flex">
              <view class="timeline-dot-wrapper flex flex-col items-center mr-4">
                <view
                  class="timeline-dot"
                  :class="{
                    'bg-green-500': leaveDetail.approval1 === 1,
                    'bg-yellow-500': leaveDetail.approval1 === 0,
                    'bg-red-500': leaveDetail.approval1 === 2,
                  }"
                >
                  <wd-icon
                    :name="
                      leaveDetail.approval1 === 1
                        ? 'check'
                        : leaveDetail.approval1 === 0
                          ? 'time'
                          : 'close'
                    "
                    color="#FFFFFF"
                    size="14px"
                  />
                </view>
                <view class="timeline-line" v-if="leaveDetail.approval1 !== 0"></view>
              </view>
              <view class="timeline-content flex-1">
                <view class="font-medium">辅导员审批</view>
                <view class="text-xs text-gray-500" v-if="leaveDetail.approval1 !== 0">
                  {{ leaveDetail.update_time ? formatTimestamp(leaveDetail.update_time) : '' }}
                </view>
                <view
                  class="text-sm mt-1"
                  :class="{
                    'text-green-500': leaveDetail.approval1 === 1,
                    'text-gray-500': leaveDetail.approval1 === 0,
                    'text-red-500': leaveDetail.approval1 === 2,
                  }"
                >
                  {{
                    leaveDetail.approval1 === 1
                      ? '已批准'
                      : leaveDetail.approval1 === 0
                        ? '审批中'
                        : '已拒绝'
                  }}
                </view>
              </view>
            </view>

            <!-- 系主任审批 (仅当需要二级审批时显示) -->
            <view class="timeline-item flex" v-if="leaveDetail.leaveHours > 24">
              <view class="timeline-dot-wrapper flex flex-col items-center mr-4">
                <view
                  class="timeline-dot"
                  :class="{
                    'bg-green-500': leaveDetail.approval2 === 1,
                    'bg-yellow-500': leaveDetail.approval2 === 0,
                    'bg-red-500': leaveDetail.approval2 === 2,
                  }"
                >
                  <wd-icon
                    :name="
                      leaveDetail.approval2 === 1
                        ? 'check'
                        : leaveDetail.approval2 === 0
                          ? 'time'
                          : 'close'
                    "
                    color="#FFFFFF"
                    size="14px"
                  />
                </view>
              </view>
              <view class="timeline-content flex-1">
                <view class="font-medium">系主任审批</view>
                <view class="text-xs text-gray-500" v-if="leaveDetail.approval2 !== 0">
                  {{ leaveDetail.update_time ? formatTimestamp(leaveDetail.update_time) : '' }}
                </view>
                <view
                  class="text-sm mt-1"
                  :class="{
                    'text-green-500': leaveDetail.approval2 === 1,
                    'text-gray-500': leaveDetail.approval2 === 0,
                    'text-red-500': leaveDetail.approval2 === 2,
                  }"
                >
                  {{
                    leaveDetail.approval2 === 1
                      ? '已批准'
                      : leaveDetail.approval2 === 0
                        ? '审批中'
                        : '已拒绝'
                  }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 附件卡片 (仅当有附件时显示) -->
      <view
        class="custom-card bg-white rounded-lg overflow-hidden mb-4 mx-4"
        v-if="leaveDetail.attachmentList"
      >
        <view class="card-header flex justify-between items-center p-3">
          <text class="card-title font-bold text-lg">附件</text>
        </view>
        <view class="p-4">
          <view class="flex flex-wrap">
            <view class="attachment-item" @click="previewImage(leaveDetail.attachmentList)">
              <image
                :src="leaveDetail.attachmentList"
                mode="aspectFill"
                class="w-24 h-24 rounded-lg"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 操作按钮区域 -->
      <view class="action-buttons p-4 flex space-x-4" v-if="canCancel">
        <button class="ios-button flex-1 secondary" @click="goBack">返回</button>
        <button class="ios-button flex-1 danger" @click="showCancelConfirm">取消请假</button>
      </view>
    </template>

    <view v-else class="p-8 text-center">
      <text class="text-gray-500">未找到请假记录</text>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { getLeaveDetail, cancelLeaveApplication } from '@/service/leave'
import type { LeaveRecord } from '@/types/leave'

// 获取页面参数
const query = defineProps({
  id: {
    type: [Number, String],
    default: 0,
  },
})

// 请假详情数据
const leaveDetail = ref<LeaveRecord | null>(null)
// 加载状态
const loading = ref(true)

// 是否可以取消请假
const canCancel = computed(() => {
  if (!leaveDetail.value) return false
  // 只有审批中的请假可以取消
  return (
    leaveDetail.value.approval1 === 0 ||
    (leaveDetail.value.approval1 === 1 && leaveDetail.value.approval2 === 0)
  )
})

// 获取请假详情
const fetchLeaveDetail = async () => {
  if (!query.id) {
    uni.showToast({
      title: '请假ID不能为空',
      icon: 'none',
    })
    return
  }

  try {
    loading.value = true
    const res = await getLeaveDetail(Number(query.id))
    leaveDetail.value = res.data
    loading.value = false
  } catch (error) {
    console.error(error)
    loading.value = false
    uni.showToast({
      title: '获取请假详情失败',
      icon: 'none',
    })
  }
}

// 获取审批状态
const getApprovalStatus = () => {
  if (!leaveDetail.value) return { status: '未知', color: 'info' }

  if (
    leaveDetail.value.approval1 === 0 ||
    (leaveDetail.value.approval1 === 1 &&
      leaveDetail.value.approval2 === 0 &&
      leaveDetail.value.leaveHours > 24)
  ) {
    return { status: '审批中', color: 'warning' }
  } else if (
    leaveDetail.value.approval1 === 1 &&
    (leaveDetail.value.approval2 === 1 ||
      (leaveDetail.value.approval2 === 0 && leaveDetail.value.leaveHours <= 24))
  ) {
    return { status: '已批准', color: 'success' }
  } else {
    return { status: '已拒绝', color: 'danger' }
  }
}

// 预览图片
const previewImage = (url: string) => {
  uni.previewImage({
    urls: [url],
    current: url,
  })
}

// 格式化时间戳
const formatTimestamp = (timestamp: number) => {
  const date = new Date(timestamp * 1000)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}:${String(date.getSeconds()).padStart(2, '0')}`
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 显示取消确认弹窗
const showCancelConfirm = () => {
  uni.showModal({
    title: '确认取消',
    content: '确定要取消这条请假申请吗？',
    success: async (res) => {
      if (res.confirm) {
        await handleCancelLeave()
      }
    },
  })
}

// 处理取消请假
const handleCancelLeave = async () => {
  if (!leaveDetail.value) return

  try {
    await cancelLeaveApplication(leaveDetail.value.id)
    uni.showToast({
      title: '取消成功',
      icon: 'success',
    })
    // 返回列表页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } catch (error) {
    console.error(error)
    uni.showToast({
      title: '取消失败',
      icon: 'none',
    })
  }
}

// 页面加载时获取请假详情
onMounted(() => {
  fetchLeaveDetail()
})
</script>

<style>
.card-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.card-title {
  position: relative;
  padding-left: 12px;
}

.card-title::before {
  position: absolute;
  top: 50%;
  left: 0;
  width: 4px;
  height: 16px;
  content: '';
  background-color: #3a73f9;
  border-radius: 2px;
  transform: translateY(-50%);
}
/* 标签样式 */
.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  padding: 0 8px;
  font-size: 10px;
  font-weight: 500;
  line-height: 1;
  border-radius: 10px;
}

.badge-success {
  color: #67c23a;
  background-color: #f0f9eb;
}

.badge-warning {
  color: #e6a23c;
  background-color: #fdf6ec;
}

.badge-danger {
  color: #f56c6c;
  background-color: #fef0f0;
}

.badge-info {
  color: #909399;
  background-color: #f4f4f5;
}
/* 按钮样式 */
.ios-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 10px;
  font-size: 15px;
  font-weight: 500;
  color: white;
  text-align: center;
  background-color: #3a73f9;
  border-radius: 6px;
}

.ios-button.secondary {
  color: #333;
  background-color: #f5f5f5;
}

.ios-button.danger {
  background-color: #f56c6c;
}
/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-top-color: #3a73f9;
  border-radius: 50%;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
/* 时间线样式 */
.timeline-item {
  margin-bottom: 16px;
}

.timeline-dot-wrapper {
  width: 24px;
}

.timeline-dot {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  border-radius: 50%;
}

.timeline-line {
  flex: 1;
  width: 2px;
  background-color: #e0e0e0;
}
/* 附件样式 */
.attachment-item {
  margin-right: 8px;
  margin-bottom: 8px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
}
</style>
