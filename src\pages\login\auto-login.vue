<route lang="json5">
{
  style: {
    navigationBarTitleText: '自动登录',
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="auto-login-container">
    <view class="login-logo">
      <image src="/static/logo.png" mode="aspectFit" />
    </view>
    <view class="login-title">系统登录</view>
    <view class="login-status">
      {{ loginMessage }}
    </view>
    <view class="loading-container">
      <wd-loading size="42px" color="#007aff" />
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { useMenuStore } from '@/store/menu'
import { refreshTokenByWechat, getUserInfo, getAccessCodes, getUserMenus } from '@/service/auth'
import type { MenuItem } from '@/store/menu'
import type { IUserInfo, IRefreshTokenByWechatParams, IGetUserInfoResponse } from '@/types/auth'

// 获取用户信息
const userStore = useUserStore()
const menuStore = useMenuStore()

// 登录状态信息
const loginMessage = ref<string>('正在自动登录...')
const loginSuccess = ref<boolean>(false)

// 目标页面信息
const targetPath = ref<string>('/pages/index/index') // 默认跳转到首页
const targetQuery = ref<Record<string, string>>({})

// 认证服务配置
const AUTH_BASEURL = import.meta.env.VITE_AUTH_BASEURL as string
const AUTH_ENDPOINT = import.meta.env.VITE_AUTH_ENDPOINT as string

/**
 * 从参数获取token和uid，进行自动登录
 */
const handleAutoLogin = async (token: string, uid: number) => {
  try {
    loginMessage.value = '正在验证登录信息...'

    // 调用refreshTokenByWechat接口获取新token
    const params: IRefreshTokenByWechatParams = {
      token,
      uid,
    }
    const refreshRes = await refreshTokenByWechat(params)

    // 保存token信息
    userStore.setTokenInfo({
      token: refreshRes.token,
      refresh_token: '', // 接口没有返回refresh_token，设置为空字符串
    })

    loginMessage.value = '正在加载用户信息...'

    // 获取用户信息
    let userInfoRes: IGetUserInfoResponse
    try {
      userInfoRes = await getUserInfo()
    } catch (error) {
      console.error('获取用户信息失败:', error)
      uni.reLaunch({
        url: '/pages/login/login',
      })
      return // 提前返回，不继续执行后面的代码
    }

    // 并行调用其他接口
    const [accessCodes, menus] = await Promise.all([getAccessCodes(), getUserMenus()])

    // 设置用户信息、权限和菜单
    userStore.setUserInfo({
      ...userInfoRes.userInfo,
      // 添加缺少的store所需字段
      last_login_time: userInfoRes.userInfo.lastLoginTime || '',
      roleName: '',
      nickname: '',
      openid: '',
      className: '',
    })
    userStore.setAccessCodes(accessCodes)

    // 处理菜单数据
    if (menus) {
      menuStore.setMenus(menus)
    } else {
      console.error('菜单数据格式错误:', menus)
      menuStore.setMenus([])
    }

    loginMessage.value = '登录成功，即将跳转...'
    loginSuccess.value = true

    // 登录成功后跳转到目标页面
    setTimeout(() => {
      navigateToTargetPage()
    }, 1000)
  } catch (error: any) {
    console.error('自动登录失败:', error)
    loginMessage.value = `登录失败: ${error?.msg || '未知错误'}`

    // 清除重定向信息
    userStore.clearRedirectInfo()

    // 登录失败后3秒跳转到登录页
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/login/login',
      })
    }, 3000)
  }
}

/**
 * 获取登录凭证
 * 如果URL中没有token和uid参数，则跳转到认证服务器
 */
const getLoginCredentials = () => {
  loginMessage.value = '正在跳转到认证服务...'

  // 在跳转前将重定向信息保存到store
  const redirectInfo = {
    path: targetPath.value,
    query: targetQuery.value,
  }

  // 保存重定向信息到store（不强制覆盖已有值）
  userStore.setRedirectInfo(redirectInfo, false)

  // 构建认证服务器URL
  const authUrl = `${AUTH_BASEURL}${AUTH_ENDPOINT}`

  // 根据平台不同使用不同的跳转方式
  // #ifdef H5
  // H5环境下直接使用location跳转
  window.location.href = authUrl
  // #endif

  // #ifdef MP-WEIXIN
  // 微信小程序环境下使用小程序接口跳转到认证服务
  uni.navigateToMiniProgram({
    appId: '', // 认证小程序的appId
    path: 'pages/auth/index', // 认证小程序的页面路径
    success(res) {
      console.log('跳转到认证小程序成功', res)
    },
    fail(err) {
      console.error('跳转到认证小程序失败', err)
      loginMessage.value = '跳转认证失败，请稍后重试'

      // 跳转失败后3秒跳转到登录页
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/login/login',
        })
      }, 3000)
    },
  })
  // #endif

  // #ifndef H5 || MP-WEIXIN
  // 其他环境下使用通用的webview打开
  uni.navigateTo({
    url: `/pages/webview/index?url=${encodeURIComponent(authUrl)}`,
    fail(err) {
      console.error('打开WebView失败', err)
      loginMessage.value = '打开认证页面失败，请稍后重试'

      // 跳转失败后3秒跳转到登录页
      setTimeout(() => {
        uni.reLaunch({
          url: '/pages/login/login',
        })
      }, 3000)
    },
  })
  // #endif

  // 因为跳转后这里的代码不会继续执行，所以不需要返回值
  return new Promise(() => {}) // 返回一个永不完成的promise，因为页面会被重定向
}

/**
 * 解析URL参数
 */
const parseUrlParams = (url: string) => {
  const params: Record<string, string> = {}
  const queryString = url.split('?')[1]
  if (!queryString) return params

  const paramPairs = queryString.split('&')
  paramPairs.forEach((pair) => {
    const [key, value] = pair.split('=')
    if (key && value) {
      params[key] = decodeURIComponent(value)
    }
  })
  return params
}

/**
 * 跳转到目标页面
 */
const navigateToTargetPage = () => {
  // 获取目标路径
  const path = targetPath.value || '/pages/index/index'

  // 构建查询参数字符串
  let queryStr = ''
  const queryParams = targetQuery.value || {}
  // 移除token和uid参数
  delete queryParams.token
  delete queryParams.uid

  // 构建查询参数
  const queryPairs = Object.entries(queryParams).map(([k, v]) => `${k}=${encodeURIComponent(v)}`)
  if (queryPairs.length > 0) {
    queryStr = `?${queryPairs.join('&')}`
  }

  // 判断是否是tabBar页面
  const tabBarPages = [
    '/pages/index/index',
    '/pages/application/index',
    '/pages/workflow/index',
    '/pages/Mail/mailList',
    '/pages/about/about',
  ]

  const fullPath = `${path}${queryStr}`

  // 清除重定向信息
  userStore.clearRedirectInfo()

  if (tabBarPages.includes(path)) {
    // 使用switchTab跳转到TabBar页面
    uni.switchTab({
      url: fullPath,
      fail: (err) => {
        console.error('TabBar页面跳转失败:', err)
        // 失败时尝试使用reLaunch
        uni.reLaunch({
          url: fullPath,
        })
      },
    })
  } else {
    uni.preloadPage({ url: '/pages/index/index' })
    uni.preloadPage({ url: fullPath })
    // 非TabBar页面，先将首页添加到导航历史中
    uni.switchTab({
      url: '/pages/index/index',
      success: () => {
        // 添加导航历史后，立即跳转到目标页面
        setTimeout(() => {
          uni.navigateTo({
            url: fullPath,
            fail: (err) => {
              console.error('页面跳转失败:', err)
              // 失败时跳转到首页
              uni.switchTab({
                url: '/pages/index/index',
              })
            },
          })
        }, 10)
      },
      fail: (err) => {
        console.error('添加导航历史失败:', err)
        // 失败时直接使用reLaunch跳转到目标页面
        uni.reLaunch({
          url: fullPath,
          fail: (redirectErr) => {
            console.error('页面跳转失败:', redirectErr)
            // 失败时跳转到首页
            uni.switchTab({
              url: '/pages/index/index',
            })
          },
        })
      },
    })
  }
}

onMounted(async () => {
  // 获取当前页面的参数（可能包含token和uid）
  const currentPages = getCurrentPages()
  const currentPage = currentPages[currentPages.length - 1]
  // 使用类型断言解决options属性访问问题
  const options = (currentPage as any)?.options || {}

  // 判断URL参数中是否有token和uid
  const token = options.token || ''
  const uid = options.uid || ''

  // 如果URL中没有token和uid，检查query参数（适用于H5环境）
  let hasLoginParams = !!(token && uid)

  // #ifdef H5
  if (!hasLoginParams) {
    // 检查URL中的查询参数（H5环境）
    const urlParams = new URLSearchParams(window.location.search)
    const urlToken = urlParams.get('token')
    const urlUid = urlParams.get('uid')

    // 如果URL中有token和uid，使用这些值
    if (urlToken && urlUid) {
      hasLoginParams = true
      await handleAutoLogin(urlToken, parseInt(urlUid))
      return
    }

    // 检查hash中的参数
    const hashParams = window.location.hash.split('?')[1]
    if (hashParams) {
      const hashUrlParams = new URLSearchParams(hashParams)
      const hashToken = hashUrlParams.get('token')
      const hashUid = hashUrlParams.get('uid')

      // 如果hash中有token和uid，使用这些值
      if (hashToken && hashUid) {
        hasLoginParams = true
        await handleAutoLogin(hashToken, parseInt(hashUid))
        return
      }
    }
  }
  // #endif

  // 如果URL中有token和uid，说明是从认证服务器跳转回来的，可以恢复重定向信息并进行自动登录
  if (hasLoginParams) {
    // 从store中恢复重定向信息
    if (userStore.redirectInfo.path) {
      console.log('从store恢复重定向信息:', userStore.redirectInfo)
      targetPath.value = userStore.redirectInfo.path
      targetQuery.value = userStore.redirectInfo.query
    }

    // 进行自动登录
    await handleAutoLogin(token, parseInt(uid.toString()))
    return
  }

  // 如果URL中没有token和uid，说明是首次进入页面，尝试从URL参数获取重定向信息
  try {
    // 获取URL参数中的redirect_path和redirect_query
    if (options.redirect_path) {
      console.log(options.redirect_path, 'options.redirect_path')
      targetPath.value = options.redirect_path
    }
    if (options.redirect_query) {
      try {
        targetQuery.value = JSON.parse(decodeURIComponent(options.redirect_query))
      } catch (error) {
        console.error('解析重定向查询参数失败:', error)
        targetQuery.value = {}
      }
    }
  } catch (error) {
    console.error('解析URL参数失败:', error)
  }

  // 没有token和uid，跳转到认证服务器获取
  getLoginCredentials()
})
</script>

<style lang="scss" scoped>
.auto-login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 0 30px;
  background-color: #ffffff;
}

.login-logo {
  margin-bottom: 30px;

  image {
    width: 120px;
    height: 120px;
  }
}

.login-title {
  margin-bottom: 20px;
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.login-status {
  margin-bottom: 30px;
  font-size: 16px;
  color: #666;
  text-align: center;
}

.loading-container {
  margin-top: 20px;
}
</style>
