<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '登录',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="login-container">
    <!-- 顶部背景装饰 -->
    <view class="login-header">
      <image
        class="login-bg"
        src="/static/login-bg.jpg"
        mode="aspectFill"
        style="filter: blur(2px)"
      />
      <view class="overlay-gradient"></view>

      <!-- 标题内容 -->
      <view class="header-content">
        <view class="school-logo">
          <image class="logo-img" src="/static/fjpitlogo.png" mode="aspectFit" />
        </view>
        <view class="logo-title">福信智慧教务</view>
        <view class="logo-subtitle">服务师生 · 提升效率</view>
      </view>

      <!-- 标签切换区域 -->
      <view class="tab-buttons">
        <view
          class="tab-button"
          :class="{ active: activeTab === 'account' }"
          @tap="switchTab('account')"
        >
          账号密码登录
        </view>
        <view class="tab-button" :class="{ active: activeTab === 'sms' }" @tap="switchTab('sms')">
          短信验证码登录
        </view>
      </view>
    </view>

    <!-- 登录表单内容区 -->
    <view class="login-form">
      <!-- 账号密码登录标签内容 -->
      <view v-if="activeTab === 'account'" class="tab-content">
        <!-- 用户名输入框 -->
        <view class="input-group">
          <view class="input-icon">
            <wd-icon name="user" size="40rpx" color="#999"></wd-icon>
          </view>
          <input
            v-model="formData.username"
            class="input-control"
            type="text"
            placeholder="请输入工号/学号"
            @input="validateUsername"
          />
          <view class="input-icon-right" v-if="formData.username">
            <wd-icon
              name="close"
              size="40rpx"
              color="#999"
              @click="formData.username = ''"
            ></wd-icon>
          </view>
        </view>
        <text v-if="errors.username" class="error-tip">{{ errors.username }}</text>

        <!-- 密码输入框 -->
        <view class="input-group">
          <view class="input-icon">
            <wd-icon name="lock-on" size="40rpx" color="#999"></wd-icon>
          </view>
          <input
            v-model="formData.password"
            class="input-control"
            type="text"
            :password="!showPassword"
            placeholder="请输入密码"
            @input="validatePassword"
          />
          <view class="input-icon-right">
            <wd-icon
              :name="showPassword ? 'view' : 'eye-close'"
              size="40rpx"
              color="#999"
              @click="togglePasswordVisibility"
            ></wd-icon>
          </view>
        </view>
        <text v-if="errors.password" class="error-tip">{{ errors.password }}</text>

        <!-- 验证码区域 -->
        <block v-if="captchaFlag">
          <view class="input-group">
            <view class="input-icon">
              <wd-icon name="picture" size="40rpx" color="#999"></wd-icon>
            </view>
            <input
              v-model="formData.captcha"
              class="input-control"
              type="text"
              placeholder="请输入验证码"
              :maxlength="4"
              @input="validateCaptcha"
            />
            <image
              class="verification-code"
              :src="captchaImage"
              @tap="refreshCaptcha"
              mode="aspectFit"
            />
          </view>
          <text v-if="errors.captcha" class="error-tip">{{ errors.captcha }}</text>
        </block>

        <!-- 登录按钮 -->
        <button
          class="btn-login"
          :loading="loading"
          :disabled="loading || hasErrors"
          @tap="handleSubmit"
        >
          {{ loading ? '登录中...' : '登录' }}
        </button>
      </view>

      <!-- 短信验证码登录标签内容 -->
      <view v-if="activeTab === 'sms'" class="tab-content">
        <!-- 手机号输入框 -->
        <view class="input-group">
          <view class="input-icon">
            <wd-icon name="phone" size="40rpx" color="#999"></wd-icon>
          </view>
          <input
            v-model="smsForm.phone"
            class="input-control"
            type="text"
            placeholder="请输入手机号"
          />
        </view>

        <!-- 验证码输入框 -->
        <view class="input-group">
          <view class="input-icon">
            <wd-icon name="keywords" size="40rpx" color="#999"></wd-icon>
          </view>
          <input
            v-model="smsForm.code"
            class="input-control"
            type="text"
            placeholder="请输入验证码"
          />
          <button class="verification-code-btn" @tap="showFeatureNotAvailable">获取验证码</button>
        </view>

        <!-- 登录按钮 -->
        <button class="btn-login" @tap="showFeatureNotAvailable">登录</button>
      </view>

      <!-- 服务条款 -->
      <view class="service-agreement">
        登录即代表您同意
        <text class="agreement-link" @tap="showAgreement('service')">《服务条款》</text>
        和
        <text class="agreement-link" @tap="showAgreement('privacy')">《隐私政策》</text>
      </view>

      <!-- 底部版权信息 -->
      <view class="footer-copyright">© 2025 福信智慧教学管理平台 版本号:V1.0.0</view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import { useMenuStore } from '@/store/menu'
import {
  getUserInfo,
  getAccessCodes,
  getUserMenus,
  getCaptchaFlag,
  getCaptcha,
  login,
} from '@/service/auth'
import type { MenuItem } from '@/store/menu'
import type { IUserInfo, ILoginRequest } from '@/types/auth'

const userStore = useUserStore()
const menuStore = useMenuStore()
const loading = ref(false)
const captchaFlag = ref(false)
const captchaImage = ref('')
const captchaUuid = ref('')
const showPassword = ref(false)
const activeTab = ref('account') // 默认激活账号密码登录标签
const windowHeight = ref('100vh')

// 账号密码登录表单数据
const formData = reactive({
  username: '',
  password: '',
  captcha: '',
  remember: false,
})

// 短信登录表单数据
const smsForm = reactive({
  phone: '',
  code: '',
})

// 表单错误信息
const errors = reactive({
  username: '',
  password: '',
  captcha: '',
})

// 切换标签
const switchTab = (tab: string) => {
  if (tab === 'sms') {
    showFeatureNotAvailable()
    return
  }
  activeTab.value = tab
}

// 功能未开放提示
const showFeatureNotAvailable = () => {
  uni.showToast({
    title: '功能暂未开放',
    icon: 'none',
    duration: 2000,
  })
}

// 显示协议
const showAgreement = (type: 'service' | 'privacy') => {
  const title = type === 'service' ? '服务条款' : '隐私政策'
  uni.navigateTo({
    url: `/pages/agreement/index?type=${type}&title=${title}`,
  })
}

// 表单验证
const validateUsername = () => {
  if (!formData.username) {
    errors.username = '请输入用户名'
  } else if (!/^[a-zA-Z0-9_]{4,16}$/.test(formData.username)) {
    errors.username = '用户名必须是4-16位字母、数字或下划线'
  } else {
    errors.username = ''
  }
}

const validatePassword = () => {
  if (!formData.password) {
    errors.password = '请输入密码'
  } else if (formData.password.length < 6) {
    errors.password = '密码长度不能小于6位'
  } else {
    errors.password = ''
  }
}

const validateCaptcha = () => {
  if (captchaFlag.value) {
    if (!formData.captcha) {
      errors.captcha = '请输入验证码'
    } else if (formData.captcha.length !== 4) {
      errors.captcha = '验证码必须是4位'
    } else {
      errors.captcha = ''
    }
  }
}

// 是否有错误
const hasErrors = computed(() => {
  return Object.values(errors).some((error) => error !== '')
})

// 检查是否启用图形验证码
onMounted(() => {
  // 获取设备高度
  const sysInfo = uni.getSystemInfoSync()
  windowHeight.value = `${sysInfo.windowHeight}px`

  // 检查用户是否已登录，如果已登录则跳转到首页
  if (userStore.isLogined) {
    uni.switchTab({
      url: '/pages/index/index',
    })
    return
  }

  // 检查是否启用验证码
  checkCaptchaFlag()
})

// 检查是否启用验证码
const checkCaptchaFlag = async () => {
  try {
    const res = await getCaptchaFlag()
    captchaFlag.value = res.flag
    if (captchaFlag.value) {
      refreshCaptcha()
    }
  } catch (error) {
    console.error('获取验证码配置失败:', error)
  }
}

// 刷新验证码
const refreshCaptcha = async () => {
  try {
    const res = await getCaptcha()
    captchaImage.value = res.base64
    captchaUuid.value = res.uuid
  } catch (error) {
    console.error('获取验证码失败:', error)
  }
}

// 切换密码显示/隐藏
const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value
}

// 提交表单
const handleSubmit = async () => {
  // 验证所有字段
  validateUsername()
  validatePassword()
  validateCaptcha()

  if (hasErrors.value) return

  try {
    loading.value = true

    const loginData: ILoginRequest = {
      username: formData.username,
      password: formData.password,
      ...(captchaFlag.value
        ? {
            captcha: formData.captcha,
            captcha_uuid: captchaUuid.value,
          }
        : {}),
    }

    // 1. 登录获取token
    const loginRes = await login(loginData)

    // eslint-disable-next-line camelcase
    const { token, refresh_token, ...userInfo } = loginRes.userInfo

    // 2. 先存储token
    // eslint-disable-next-line camelcase
    userStore.setTokenInfo({ token, refresh_token })

    // 3. 并行调用其他接口
    const [userInfoRes, accessCodes, menus] = await Promise.all([
      getUserInfo(),
      getAccessCodes(),
      getUserMenus(),
    ])

    // 4. 设置用户信息、权限和菜单
    userStore.setUserInfo({
      ...userInfoRes.userInfo,
      // 添加缺少的store所需字段
      last_login_time: userInfoRes.userInfo.lastLoginTime || '',
      roleName: '',
      nickname: '',
      openid: '',
      className: userInfoRes.userInfo.className || '',
    })
    userStore.setAccessCodes(accessCodes)

    // 处理菜单数据
    if (menus) {
      menuStore.setMenus(menus)
    } else {
      console.error('菜单数据格式错误:', menus)
      menuStore.setMenus([])
    }

    uni.showToast({
      title: '登录成功',
      icon: 'success',
    })

    // 跳转到首页
    uni.switchTab({
      url: '/pages/index/index',
    })
  } catch (error: any) {
    console.error('登录失败:', error)
    uni.showToast({
      title: error?.msg || '登录失败',
      icon: 'error',
    })
    if (captchaFlag.value) {
      refreshCaptcha()
    }
  } finally {
    loading.value = false
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  flex-direction: column;
  height: v-bind(windowHeight); /* 使用动态获取的设备高度 */
  overflow: hidden; /* 防止内容溢出导致滚动 */
  background-color: #f8f9fa;
}

.login-header {
  position: relative;
  height: 600rpx;
  min-height: 460rpx; /* 确保最小高度 */
  overflow: hidden;

  .login-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .overlay-gradient {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 140rpx;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.3));
  }

  .header-content {
    position: absolute;
    top: 100rpx;
    left: 0;
    box-sizing: border-box;
    width: 100%;
    padding: 0 40rpx;
    text-align: center;
  }

  .school-logo {
    position: relative;
    width: 160rpx;
    height: 160rpx;
    margin: 0 auto 30rpx;
    overflow: hidden;
    background-color: #fff;
    backdrop-filter: blur(2rpx); /* 添加1px的背景模糊效果 */
    border: 4rpx solid white; /* 添加2px的白色描边 */
    border-radius: 50%;
    box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
  }

  .logo-img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 50%;
    /* 确保内部图片也完全圆形 */
  }

  .logo-title {
    font-size: 52rpx;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
  }

  .logo-subtitle {
    margin-top: 16rpx;
    font-size: 32rpx;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
  }
}

.tab-buttons {
  position: absolute;
  bottom: 0;
  left: 0;
  display: flex;
  width: 100%;
  height: 100rpx;
  background-color: rgba(255, 255, 255, 1);
  backdrop-filter: blur(10rpx);
  border-radius: 32rpx 32rpx 0 0;
}

.tab-button {
  position: relative;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  height: 100rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #666;
}

.tab-button.active {
  color: #007aff;
}

.tab-button.active::after {
  position: absolute;
  bottom: 0;
  left: 30%;
  width: 40%;
  height: 6rpx;
  content: '';
  background-color: #007aff;
  border-radius: 6rpx 6rpx 0 0;
}

.login-form {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  background-color: #fff;
  border-radius: 0 0 32rpx 32rpx;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.tab-content {
  display: block;
  flex: 1; /* 使内容区域可伸缩 */
  padding: 60rpx 40rpx 30rpx;
}

.input-group {
  position: relative;
  margin-bottom: 40rpx;
}

.input-control {
  box-sizing: border-box;
  width: 100%;
  height: 96rpx;
  padding: 0 96rpx 0 96rpx;
  font-size: 32rpx;
  background-color: #f8f9fc;
  border: 1px solid #ebedf5;
  border-radius: 16rpx;
}

.input-icon {
  position: absolute;
  top: 50%;
  left: 30rpx;
  z-index: 1;
  transform: translateY(-50%);
}

.input-icon-right {
  position: absolute;
  top: 50%;
  right: 30rpx;
  z-index: 1;
  transform: translateY(-50%);
}

.verification-code {
  position: absolute;
  top: 50%;
  right: 20rpx;
  z-index: 1;
  width: 200rpx;
  height: 76rpx;
  border-radius: 8rpx;
  transform: translateY(-50%);
}

.verification-code-btn {
  position: absolute;
  top: 50%;
  right: 20rpx;
  z-index: 1;
  padding: 10rpx 20rpx;
  font-size: 28rpx;
  color: #007aff;
  background: none;
  border: none;
  transform: translateY(-50%);
}

.error-tip {
  display: block;
  margin: -20rpx 0 40rpx 30rpx;
  font-size: 24rpx;
  color: #ff4d4f;
}

.btn-login {
  width: 100%;
  height: 96rpx;
  margin-top: 60rpx;
  font-size: 36rpx;
  font-weight: 500;
  color: white;
  background-color: #007aff;
  border: none;
  border-radius: 16rpx;
}

.btn-login[disabled] {
  opacity: 0.6;
}

.service-agreement {
  margin-top: 40rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

.agreement-link {
  color: #007aff;
}

.footer-copyright {
  padding: 30rpx 0;
  margin-top: auto; /* 保持在底部 */
  font-size: 24rpx;
  color: #999;
  text-align: center;
}
</style>
