<route lang="json5">
{
  style: {
    navigationBarTitleText: '证书信息',
  },
}
</route>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { getStudentCertificates } from '@/service/certificate'
import type { CertificateItem } from '@/types/certificate'
import { loadDictData, getDictLabel, getDictOptions } from '@/utils/dict'
import type { DictData } from '@/types/system'

// 定义类型
interface Certificate {
  id: number
  name: string
  issueDate: string
  certNumber: string
  icon: string
  iconBg: string
  validityType: string
  certLevel: string
  score?: number
  organization?: string
  // 新增字段
  certificateType: string
  certificateTypeName: string
  certificateLevel: string
  certificateLevelName: string
  issuingUnit: string
  levelCode: number
  levelName: string
  remark: string
  certificateCategory?: string // 证书分类
}

// 证书分类字典
const certCategoryDict = ref<DictData[]>([])

// 证书数据加载状态
const loading = ref(false)
// 系统证书数据
const systemCertificates = ref<CertificateItem[]>([])

// 默认证书数据（当API没有返回数据时使用）
const defaultCertificates: Certificate[] = []

// 已获得的证书数据
const certificates = ref<Certificate[]>(defaultCertificates)

// 显示证书详情弹窗
const showCertDetail = ref(false)
// 当前选中的证书
const selectedCert = ref<Certificate | null>(null)

// 证书图标映射
const certIconMap: Record<string, string> = {
  // 类型图标
  language: 'language',
  medal: 'medal',
  professional: 'certificate',
  vocational: 'document',
  creative: 'growth',
  laptop: 'laptop',
  default: 'certificate',

  // 根据关键词映射
  英语: 'language',
  日语: 'language',
  德语: 'language',
  法语: 'language',
  俄语: 'language',
  韩语: 'language',
  计算机: 'laptop',
  软件: 'laptop',
  编程: 'code',
  维修: 'tools',
  创新: 'growth',
  创业: 'growth',
  职业: 'document',
}

// 根据证书名称和类型获取合适的图标
const getCertIcon = (certName: string, certType: string): string => {
  // 按语言类型匹配
  if (
    certName.includes('英语') ||
    certName.includes('日语') ||
    certName.includes('德语') ||
    certName.includes('法语') ||
    certName.includes('俄语') ||
    certName.includes('韩语')
  ) {
    return 'language'
  }

  // 按计算机类型匹配
  if (certName.includes('计算机') || certName.includes('软件') || certName.includes('编程')) {
    return 'laptop'
  }

  // 按维修类型匹配
  if (certName.includes('维修') || certName.includes('电工')) {
    return 'tools'
  }

  // 按创新创业类型匹配
  if (certName.includes('创新') || certName.includes('创业')) {
    return 'growth'
  }

  // 按职业类型匹配
  if (certName.includes('职业') || certType === '职业资格证书') {
    return 'document'
  }

  // 默认图标
  return 'certificate'
}

// 将系统证书转换为前端显示格式
const convertSystemCertificate = (cert: CertificateItem): Certificate => {
  // 获取合适的图标
  const icon = getCertIcon(cert.certificateName, cert.certificateTypeName)

  // 选择图标背景色
  let iconBg = 'bg-blue-100 text-blue-600'

  if (icon === 'language') {
    iconBg = 'bg-red-100 text-red-600'
  } else if (icon === 'growth') {
    iconBg = 'bg-green-100 text-green-600'
  } else if (icon === 'document') {
    iconBg = 'bg-purple-100 text-purple-600'
  } else if (icon === 'laptop') {
    iconBg = 'bg-blue-100 text-blue-600'
  } else if (icon === 'tools') {
    iconBg = 'bg-yellow-100 text-yellow-600'
  }

  return {
    id: cert.id,
    name: cert.certificateName,
    issueDate: cert.issueDate,
    certNumber: cert.certificateNumber || '暂无',
    icon,
    iconBg,
    validityType: '',
    certLevel: '',
    organization: cert.issuingUnit || '',
    // 新增字段
    certificateType: cert.certificateType || '',
    certificateTypeName: cert.certificateTypeName || '',
    certificateLevel: cert.certificateLevel || '',
    certificateLevelName: cert.certificateLevelName || '',
    issuingUnit: cert.issuingUnit || '',
    levelCode: cert.levelCode || 0,
    levelName: cert.levelName || '',
    remark: cert.remark || '',
    certificateCategory: cert.certificateCategory || '',
  }
}

// 根据证书分类统计数量
const certCategoryStats = computed(() => {
  // 如果没有加载证书数据或字典数据，返回空数组
  if (certificates.value.length === 0 || certCategoryDict.value.length === 0) {
    return []
  }

  // 统计每个分类的证书数量
  const stats: {
    category: string
    count: number
    label: string
    bgColor: string
    textColor: string
  }[] = []
  const colorMap: Record<number, { bg: string; text: string }> = {
    0: { bg: 'bg-blue-50', text: 'text-blue-500' },
    1: { bg: 'bg-green-50', text: 'text-green-500' },
    2: { bg: 'bg-purple-50', text: 'text-purple-500' },
    3: { bg: 'bg-red-50', text: 'text-red-500' },
    4: { bg: 'bg-orange-50', text: 'text-orange-500' },
    5: { bg: 'bg-yellow-50', text: 'text-yellow-600' },
  }

  // 获取字典中所有的分类
  certCategoryDict.value.forEach((dict, index) => {
    const category = dict.dictValue
    const label = dict.dictLabel
    const count = certificates.value.filter((cert) => cert.certificateCategory === category).length
    // 只显示有证书的分类
    if (count > 0) {
      const colorIndex = index % Object.keys(colorMap).length
      stats.push({
        category,
        count,
        label,
        bgColor: colorMap[colorIndex].bg,
        textColor: colorMap[colorIndex].text,
      })
    }
  })

  // 如果有未分类的证书，也统计出来
  const uncategorizedCount = certificates.value.filter((cert) => !cert.certificateCategory).length
  if (uncategorizedCount > 0) {
    stats.push({
      category: '',
      count: uncategorizedCount,
      label: '其他',
      bgColor: 'bg-gray-50',
      textColor: 'text-gray-500',
    })
  }

  return stats
})

// 计算统计面板的布局类型
const statsPanelClass = computed(() => {
  // 总数量：基础的"已获得"项 + 分类统计项
  const totalItems = 1 + certCategoryStats.value.length

  if (totalItems === 1) {
    return 'stats-grid-1' // 只有一个项目时的全宽布局
  } else if (totalItems === 2) {
    return 'stats-grid-2'
  } else if (totalItems === 3) {
    return 'stats-grid-3'
  } else if (totalItems === 4) {
    return 'stats-grid-4'
  } else {
    // 超过4个项目时使用自适应布局
    return 'stats-grid-auto'
  }
})

// 获取系统证书数据
const fetchSystemCertificates = async () => {
  try {
    loading.value = true
    const res = await getStudentCertificates()
    systemCertificates.value = res.items || []

    // 如果有系统证书数据，转换为前端展示格式
    if (systemCertificates.value.length > 0) {
      certificates.value = systemCertificates.value.map(convertSystemCertificate)
    }
  } catch (error) {
    console.error('获取证书数据失败:', error)
    // 如果获取失败，使用默认数据
    certificates.value = defaultCertificates
  } finally {
    loading.value = false
  }
}

// 加载证书分类字典
const loadCertCategoryDict = async () => {
  try {
    const dictResult = await loadDictData(['DM_ZSLBDM'])
    certCategoryDict.value = dictResult.DM_ZSLBDM || []
  } catch (error) {
    console.error('加载证书分类字典失败:', error)
    certCategoryDict.value = []
  }
}

// 查看证书详情
const viewCertDetail = (cert: Certificate) => {
  selectedCert.value = cert
  showCertDetail.value = true
}

// 关闭证书详情弹窗
const closeCertDetail = () => {
  showCertDetail.value = false
  selectedCert.value = null
}

// 获取分类名称
const getCategoryLabel = (category: string) => {
  return getDictLabel(certCategoryDict.value, category) || '未分类'
}

// 页面加载时获取证书数据
onMounted(async () => {
  await loadCertCategoryDict()
  await fetchSystemCertificates()
})
</script>

<template>
  <view class="cert-container">
    <!-- 证书统计 -->
    <view class="cert-stats-card">
      <view class="cert-stats-content">
        <!-- 证书分类统计 -->
        <view class="cert-category-stats" :class="statsPanelClass">
          <!-- 已获得证书总数 -->
          <view class="cert-category-item bg-blue-50">
            <view class="cert-category-item-content">
              <text class="cert-category-count text-blue-500">{{ certificates.length }}</text>
              <text class="cert-category-label">已获得</text>
            </view>
          </view>

          <!-- 各分类证书数量 -->
          <view
            class="cert-category-item"
            v-for="(stat, index) in certCategoryStats"
            :key="index"
            :class="stat.bgColor"
          >
            <view class="cert-category-item-content">
              <text class="cert-category-count" :class="stat.textColor">{{ stat.count }}</text>
              <text class="cert-category-label">{{ stat.label }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="cert-loading">
      <wd-loading type="outline" color="#1989fa" />
      <text class="cert-loading-text">加载中...</text>
    </view>

    <!-- 证书列表 -->
    <view class="cert-list-container" v-else>
      <!-- 统一的证书列表 -->
      <view class="cert-list-section-header">全部证书</view>

      <view
        class="cert-list-item"
        v-for="cert in certificates"
        :key="cert.id"
        @click="viewCertDetail(cert)"
      >
        <view class="cert-item-content">
          <view class="cert-icon-container" :class="cert.iconBg">
            <view :class="['i-carbon-' + cert.icon, 'cert-icon']"></view>
          </view>
          <view class="cert-info">
            <view class="cert-header">
              <text class="cert-name">{{ cert.name }}</text>
              <view
                class="cert-badge"
                :class="cert.remark.includes('优秀') ? 'badge-excellent' : 'badge-good'"
              >
                {{ cert.remark }}
              </view>
            </view>
            <view class="cert-detail-rows">
              <view class="cert-detail-row" v-if="cert.issuingUnit">
                <wd-icon name="user-avatar" size="12px" color="#999" />
                <text class="cert-detail-text">颁发机构：{{ cert.issuingUnit }}</text>
              </view>
              <view class="cert-detail-row">
                <wd-icon name="time" size="12px" color="#999" />
                <text class="cert-detail-text">获得日期：{{ cert.issueDate }}</text>
              </view>
              <view class="cert-detail-row">
                <wd-icon name="note" size="12px" color="#999" />
                <text class="cert-detail-text">证书编号：{{ cert.certNumber }}</text>
              </view>
            </view>
            <view class="cert-action-row">
              <text class="cert-action">查看</text>
            </view>
            <view class="cert-tags">
              <!-- 证书分类标签 -->
              <text v-if="cert.certificateCategory" class="cert-tag cert-tag-category">
                {{ getCategoryLabel(cert.certificateCategory) }}
              </text>
              <!-- 证书类型标签 -->
              <text class="cert-tag cert-tag-type" v-if="cert.certificateTypeName">
                {{ cert.certificateTypeName }}
              </text>
              <!-- 证书等级标签 -->
              <text class="cert-tag cert-tag-level-name" v-if="cert.certificateLevelName">
                {{ cert.certificateLevelName }}
              </text>
              <!-- 级别名称标签 -->
              <text class="cert-tag cert-tag-level" v-if="cert.levelName">
                {{ cert.levelName }}
              </text>
              <!-- 其他标签 -->
              <text class="cert-tag cert-tag-validity" v-if="cert.validityType">
                {{ cert.validityType }}
              </text>
              <text class="cert-tag cert-tag-score" v-if="cert.score">分数：{{ cert.score }}</text>
            </view>
          </view>
        </view>
      </view>

      <view v-if="certificates.length === 0" class="cert-empty-tip">
        <text>暂无证书数据</text>
      </view>
    </view>

    <!-- 证书详情弹窗 -->
    <wd-popup v-model="showCertDetail" position="center" round>
      <view class="cert-detail-popup">
        <view class="cert-detail-content">
          <view class="cert-detail-icon-container">
            <view class="cert-detail-icon-bg" :class="selectedCert?.iconBg">
              <view :class="['i-carbon-' + (selectedCert?.icon || ''), 'cert-detail-icon']"></view>
            </view>
            <view class="cert-detail-status-icon">
              <view class="i-carbon-check cert-detail-status-icon-inner"></view>
            </view>
          </view>

          <text class="cert-detail-name">{{ selectedCert?.name }}</text>
          <text v-if="selectedCert?.issuingUnit" class="cert-detail-org">
            {{ selectedCert?.issuingUnit }}
          </text>

          <view class="cert-detail-info-card">
            <!-- 证书分类信息 -->
            <view class="cert-detail-info-item" v-if="selectedCert?.certificateCategory">
              <text class="cert-detail-info-label">证书分类</text>
              <text class="cert-detail-info-value">
                {{ getCategoryLabel(selectedCert.certificateCategory) }}
              </text>
            </view>
            <view class="cert-detail-info-item" v-if="selectedCert?.issuingUnit">
              <text class="cert-detail-info-label">颁发机构</text>
              <text class="cert-detail-info-value">{{ selectedCert?.issuingUnit }}</text>
            </view>
            <view class="cert-detail-info-item" v-if="selectedCert?.certificateTypeName">
              <text class="cert-detail-info-label">证书类型</text>
              <text class="cert-detail-info-value">{{ selectedCert?.certificateTypeName }}</text>
            </view>
            <view class="cert-detail-info-item" v-if="selectedCert?.certificateLevelName">
              <text class="cert-detail-info-label">证书等级</text>
              <text class="cert-detail-info-value">{{ selectedCert?.certificateLevelName }}</text>
            </view>
            <view class="cert-detail-info-item" v-if="selectedCert?.levelName">
              <text class="cert-detail-info-label">级别名称</text>
              <text class="cert-detail-info-value">{{ selectedCert?.levelName }}</text>
            </view>
            <view class="cert-detail-info-item">
              <text class="cert-detail-info-label">证书编号</text>
              <text class="cert-detail-info-value">{{ selectedCert?.certNumber }}</text>
            </view>
            <view class="cert-detail-info-item">
              <text class="cert-detail-info-label">获得日期</text>
              <text class="cert-detail-info-value">{{ selectedCert?.issueDate }}</text>
            </view>
            <view v-if="selectedCert?.validityType" class="cert-detail-info-item">
              <text class="cert-detail-info-label">有效期</text>
              <text class="cert-detail-info-value">{{ selectedCert?.validityType }}</text>
            </view>
            <view class="cert-detail-info-item">
              <text class="cert-detail-info-label">成绩评定</text>
              <text
                class="cert-detail-info-value"
                :class="
                  selectedCert?.remark.includes('优秀') ? 'text-green-500' : 'text-orange-500'
                "
              >
                {{ selectedCert?.remark
                }}{{ selectedCert?.score ? '（' + selectedCert.score + '分）' : '' }}
              </text>
            </view>
          </view>

          <!-- 证书详情介绍部分已隐藏 -->

          <view class="cert-detail-actions">
            <wd-button type="info" block @click="closeCertDetail">关闭</wd-button>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<style scoped lang="scss">
/* 预先定义所有可能用到的图标类，确保UnoCSS能够识别 */
.i-carbon-medal,
.i-carbon-language,
.i-carbon-certificate,
.i-carbon-document,
.i-carbon-growth,
.i-carbon-tools,
.i-carbon-laptop,
.i-carbon-code,
.i-carbon-star,
.i-carbon-star-on,
.i-carbon-check-circle {
  /* 这些类会被UnoCSS识别和处理 */
}

.cert-container {
  min-height: 100vh;
  padding: 20rpx;
  background-color: #f5f5f5;
}

// 加载状态
.cert-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
}

.cert-loading-text {
  margin-top: 20rpx;
  font-size: 26rpx;
  color: #666;
}

// 证书统计卡片样式
.cert-stats-card {
  padding: 20rpx;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.cert-stats-content {
  padding: 10rpx;
}

// 证书分类统计样式
.cert-category-stats {
  display: grid;
  gap: 10rpx;
  margin-top: 15rpx;

  // 只有1个项目时的布局（全宽）
  &.stats-grid-1 {
    grid-template-columns: 1fr;
  }

  // 只有2个项目时的布局
  &.stats-grid-2 {
    grid-template-columns: repeat(2, 1fr);
  }

  // 有3个项目时的布局
  &.stats-grid-3 {
    grid-template-columns: repeat(3, 1fr);
  }

  // 有4个项目时的布局
  &.stats-grid-4 {
    grid-template-columns: repeat(4, 1fr);
  }

  // 默认为自适应布局（超过4个时）
  &.stats-grid-auto {
    grid-template-columns: repeat(auto-fill, minmax(120rpx, 1fr));
  }
}

.cert-category-item {
  padding: 15rpx 10rpx;
  text-align: center;
  border-radius: 12rpx;
}

.cert-category-item-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.cert-category-count {
  margin-bottom: 4rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.cert-category-label {
  font-size: 24rpx;
  color: #666;
}

// 证书列表样式
.cert-list-container {
  margin-bottom: 30rpx;
}

.cert-list-section-header {
  padding: 15rpx 30rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #666;
  background-color: #f2f2f2;
}

.cert-list-item {
  padding: 20rpx;
  background-color: #fff;
  border-bottom: 1px solid #eee;
}

.cert-item-content {
  display: flex;
}

.cert-icon-container {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  border-radius: 12rpx;
}

.cert-icon {
  width: 40rpx;
  height: 40rpx;
  font-size: 40rpx;
}

.cert-info {
  flex: 1;
}

.cert-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.cert-name {
  font-size: 28rpx;
  font-weight: 500;
}

.cert-badge {
  padding: 2rpx 10rpx;
  font-size: 22rpx;
  border-radius: 8rpx;

  &.badge-excellent {
    color: #4caf50;
    background-color: #e1f3e1;
  }

  &.badge-good {
    color: #ff9800;
    background-color: #fff2e0;
  }
}

.cert-detail-rows {
  margin-bottom: 10rpx;
}

.cert-detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
}

.cert-detail-text {
  margin-left: 8rpx;
  font-size: 22rpx;
  color: #999;
}

.cert-action-row {
  display: flex;
  justify-content: flex-end;
  margin-top: 10rpx;
  margin-bottom: 10rpx;
}

.cert-action {
  font-size: 24rpx;
  color: #1989fa;
}

.cert-tags {
  display: flex;
  flex-wrap: wrap;
}

.cert-tag {
  padding: 2rpx 10rpx;
  margin-right: 10rpx;
  margin-bottom: 4rpx;
  font-size: 22rpx;
  border-radius: 100rpx;

  &.cert-tag-type {
    color: #607d8b;
    background-color: #eceff1;
  }

  &.cert-tag-level {
    color: #1989fa;
    background-color: #e6f1fc;
  }

  &.cert-tag-validity {
    color: #4caf50;
    background-color: #e8f5e9;
  }

  &.cert-tag-score {
    color: #9c27b0;
    background-color: #f0e6fc;
  }

  &.cert-tag-level-name {
    color: #ff5722;
    background-color: #fff3e0;
  }

  &.cert-tag-category {
    color: #2196f3;
    background-color: #e3f2fd;
  }
}

// 证书空状态提示
.cert-empty-tip {
  padding: 40rpx 20rpx;
  text-align: center;
  background-color: #fff;
  border-radius: 12rpx;

  text {
    display: block;
    font-size: 26rpx;
    line-height: 1.8;
    color: #999;
  }
}

// 证书详情弹窗样式
.cert-detail-popup {
  box-sizing: border-box;
  width: 650rpx;
  max-height: 80vh;
  padding: 30rpx;
  overflow-y: auto;
}

.cert-detail-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.cert-detail-icon-container {
  position: relative;
  margin-bottom: 20rpx;
}

.cert-detail-icon-bg {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 10rpx;
  border-radius: 50%;
}

.cert-detail-icon {
  width: 60rpx;
  height: 60rpx;
  font-size: 60rpx;
}

.cert-detail-status-icon {
  position: absolute;
  right: -10rpx;
  bottom: -10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  background-color: #4caf50;
  border-radius: 50%;
}

.cert-detail-status-icon-inner {
  width: 20rpx;
  height: 20rpx;
  font-size: 20rpx;
  color: #fff;
}

.cert-detail-name {
  margin-bottom: 6rpx;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
}

.cert-detail-org {
  margin-bottom: 20rpx;
  font-size: 26rpx;
  color: #666;
}

.cert-detail-info-card {
  width: 100%;
  padding: 20rpx;
  margin-bottom: 20rpx;
  background-color: #f5f9ff;
  border-radius: 12rpx;
}

.cert-detail-info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.cert-detail-info-label {
  font-size: 26rpx;
  color: #666;
}

.cert-detail-info-value {
  font-size: 26rpx;
  font-weight: 500;
}

.cert-detail-actions {
  display: flex;
  gap: 20rpx;
  justify-content: space-between;
  width: 100%;

  :deep(.wd-button) {
    flex: 1;
  }
}
</style>
