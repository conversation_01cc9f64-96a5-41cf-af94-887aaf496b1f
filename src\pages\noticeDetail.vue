<route lang="json5">
{
  style: {
    navigationBarTitleText: '通知详情',
  },
}
</route>
<script setup lang="ts">
import { ref, computed } from 'vue'
import type { NoticeItem } from '@/types/notice'

// 通知详情数据
const noticeData = ref<NoticeItem>({} as NoticeItem)

// 处理通知内容，确保HTML内容可以正确显示
const noticeContent = computed(() => {
  if (!noticeData.value.content) {
    return '通知内容为空'
  }

  // 如果内容包含HTML标签，则进行处理
  if (/<[a-z][\s\S]*>/i.test(noticeData.value.content)) {
    // 移除可能导致文本不换行的样式，处理text-indent过大的情况
    const processedContent = noticeData.value.content
      .replace(/text-wrap:\s*nowrap/g, 'text-wrap: normal')
      .replace(/white-space:\s*nowrap/g, 'white-space: normal')
      // 处理过大的缩进值，限制最大缩进为屏幕宽度的30%
      .replace(/(text-indent:\s*)(\d{3,})px/g, function (match, p1, p2) {
        const value = parseInt(p2)
        return value > 100 ? `${p1}30%` : match
      })
    return processedContent
  }

  // 如果是纯文本，将换行符转换为<br>标签
  return noticeData.value.content.replace(/\n/g, '<br>')
})

// 使用onLoad生命周期函数获取路由参数
onLoad((options) => {
  console.log('onLoad options:', options)
  if (options && options.id) {
    // 从localStorage中获取通知数据
    const noticeDetailStr = uni.getStorageSync('NOTICE_DETAIL_DATA')
    if (noticeDetailStr) {
      const allNoticeData = JSON.parse(noticeDetailStr)
      // 确保ID匹配
      if (allNoticeData.id === Number(options.id)) {
        noticeData.value = allNoticeData
        console.log('通知数据加载成功:', noticeData.value)
      } else {
        console.error('通知ID不匹配')
        uni.showToast({
          title: '通知数据不匹配',
          icon: 'none',
        })
      }
    } else {
      console.error('找不到通知数据')
      uni.showToast({
        title: '找不到通知数据',
        icon: 'none',
      })
    }
  } else {
    console.error('未提供通知ID')
    uni.showToast({
      title: '未提供通知ID',
      icon: 'none',
    })
  }
})

// 分享通知
const shareNotice = () => {
  uni.showToast({
    title: '分享功能开发中',
    icon: 'none',
  })
}

// 收藏通知
const favoriteNotice = () => {
  uni.showToast({
    title: '收藏功能开发中',
    icon: 'none',
  })
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}
</script>

<template>
  <view class="notice-detail-container">
    <view class="notice-content-wrapper">
      <!-- 标题区域 -->
      <view class="notice-header">
        <view class="notice-title">{{ noticeData.title }}</view>
      </view>

      <!-- 发布信息 -->
      <view class="notice-info">
        <view class="notice-info-row">
          <text class="notice-info-label">发布单位：</text>
          <text class="notice-info-value">{{ noticeData.department }}</text>
        </view>
        <view class="notice-info-row">
          <text class="notice-info-label">发布时间：</text>
          <text class="notice-info-value">{{ noticeData.publishTime }}</text>
        </view>
        <view class="notice-tags" v-if="noticeData.type || noticeData.isImportant">
          <text v-if="noticeData.isImportant" class="notice-tag importance-tag">重要通知</text>
          <text
            v-if="noticeData.type"
            class="notice-tag"
            :class="[
              'bg-' + noticeData.categoryColor + '-50',
              'text-' + noticeData.categoryColor + '-600',
            ]"
          >
            {{ noticeData.type }}
          </text>
        </view>
      </view>

      <!-- 通知内容 -->
      <view class="notice-content">
        <!-- 使用richtext渲染HTML内容 -->
        <rich-text :nodes="noticeContent" class="notice-content-rich"></rich-text>
      </view>

      <!-- 操作按钮 -->
      <view class="notice-actions">
        <view class="action-button" @click="shareNotice">
          <wd-icon name="share" class="action-icon"></wd-icon>
          <text class="action-text">分享</text>
        </view>
        <view class="action-button" @click="favoriteNotice">
          <wd-icon name="star" class="action-icon"></wd-icon>
          <text class="action-text">收藏</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.notice-detail-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
  background-color: #ffffff;
}

.notice-content-wrapper {
  box-sizing: border-box;
  padding: 32rpx;
  margin: 0 auto;
}

.notice-header {
  padding-bottom: 24rpx;
  margin-bottom: 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.notice-title {
  font-size: 36rpx;
  font-weight: bold;
  line-height: 1.4;
  color: #333333;
}

.notice-info {
  padding-bottom: 24rpx;
  margin-bottom: 32rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.notice-info-row {
  display: flex;
  margin-bottom: 12rpx;
  font-size: 28rpx;
}

.notice-info-label {
  margin-right: 8rpx;
  color: #6b7280;
}

.notice-info-value {
  flex: 1;
  color: #333333;
}

.notice-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 16rpx;
}

.notice-tag {
  padding: 6rpx 16rpx;
  margin-right: 16rpx;
  margin-bottom: 8rpx;
  font-size: 24rpx;
  border-radius: 999rpx;
}

.importance-tag {
  color: #ffffff;
  background-color: #ef4444;
}

.notice-content {
  padding-bottom: 40rpx;
  margin-bottom: 40rpx;
  overflow-x: hidden; // 添加水平方向溢出隐藏
}

.notice-content-rich {
  max-width: 100%;
  font-size: 30rpx;
  line-height: 1.6;
  color: #333333;
  word-break: break-word;
  overflow-wrap: break-word;
  /* 添加全局覆盖样式，确保内容自动换行 */
  :deep(span),
  :deep(p),
  :deep(div),
  :deep(strong),
  :deep(em),
  :deep(b),
  :deep(i),
  :deep(u),
  :deep(s),
  :deep(sup),
  :deep(sub) {
    box-sizing: border-box !important;
    max-width: 100% !important;
    text-wrap: wrap !important;
    word-break: break-word !important;
    white-space: normal !important;
  }
  /* 处理缩进过大的问题 */
  :deep([style*='text-indent']) {
    text-indent: 2em !important; // 统一设置适当的缩进
  }
  /* 限制富文本内部所有元素的宽度 */
  :deep(*) {
    box-sizing: border-box !important;
    max-width: 100% !important;
  }
  /* 确保图片等媒体元素不超出容器 */
  :deep(img),
  :deep(image),
  :deep(video) {
    display: block !important;
    max-width: 100% !important;
    height: auto !important;
    margin: 12rpx auto !important;
  }
  /* 表格自适应样式 */
  :deep(table) {
    width: 100% !important;
    word-break: break-word !important;
    table-layout: fixed !important;
    border-collapse: collapse !important;
  }

  :deep(td),
  :deep(th) {
    box-sizing: border-box !important;
    max-width: 100% !important;
    padding: 8rpx !important;
    word-break: break-word !important;
    overflow-wrap: break-word !important;
    border: 1rpx solid #e5e7eb !important;
  }
}

.notice-actions {
  display: flex;
  justify-content: space-around;
  padding-top: 24rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 32rpx;
}

.action-icon {
  margin-bottom: 8rpx;
  font-size: 40rpx;
  color: #3b82f6;
}

.action-text {
  font-size: 24rpx;
  color: #6b7280;
}
/* 样式类复用 */
.bg-blue-50 {
  background-color: rgba(59, 130, 246, 0.05);
}

.bg-green-50 {
  background-color: rgba(5, 150, 105, 0.05);
}

.bg-purple-50 {
  background-color: rgba(139, 92, 246, 0.05);
}

.bg-yellow-50 {
  background-color: rgba(234, 179, 8, 0.05);
}

.bg-indigo-50 {
  background-color: rgba(99, 102, 241, 0.05);
}

.bg-gray-50 {
  background-color: rgba(100, 116, 139, 0.05);
}

.text-blue-600 {
  color: #2563eb;
}

.text-green-600 {
  color: #059669;
}

.text-purple-600 {
  color: #7c3aed;
}

.text-yellow-600 {
  color: #ca8a04;
}

.text-indigo-600 {
  color: #4f46e5;
}

.text-gray-600 {
  color: #475569;
}
</style>
