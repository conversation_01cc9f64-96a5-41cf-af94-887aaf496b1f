<template>
  <view>
    <view class="mt-xl">
      <web-view :src="finalUrl" :webview-styles="webviewStyles"></web-view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

// 获取环境变量
const pdfBaseUrl = import.meta.env.VITE_PDF_VIEWERURL || ''

// 定义页面参数类型
interface PageOptions {
  url: string
  title?: string
  pdfreview?: string
}

// 定义数据
const url = ref('')
const title = ref('浏览')
const isPdfReview = ref(false)
const webviewStyles = ref({
  progress: {
    color: '#FF3333',
  },
})

// 计算最终的URL
const finalUrl = computed(() => {
  if (isPdfReview.value) {
    // 如果是PDF预览模式
    return `${pdfBaseUrl}?url=${encodeURIComponent(window.btoa(processUrl(url.value)))}`
  } else {
    // 非PDF预览模式，直接使用url
    return url.value
  }
})

// 处理URL，如果不包含http则添加当前location路径
function processUrl(inputUrl: string): string {
  if (inputUrl.includes('http')) {
    return inputUrl
  } else {
    // 在不同平台获取当前路径
    let result = inputUrl

    // #ifdef H5
    const currentLocation = document.location.origin
    result = `${currentLocation}${inputUrl.startsWith('/') ? '' : '/'}${inputUrl}`
    // #endif

    return result
  }
}

// 页面加载
onLoad((option: PageOptions) => {
  url.value = option.url
  isPdfReview.value = option.pdfreview === 'true' || option.pdfreview === '1'

  if (option.title) {
    title.value = option.title
    uni.setNavigationBarTitle({
      title: option.title,
    })
  }
})
</script>

<style></style>
