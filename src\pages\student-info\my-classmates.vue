<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的班级同学',
  },
}
</route>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { getMyClassmates } from '@/service/student'
import type {
  ClassmateInfo,
  ClassInfo,
  LetterGroupedClassmates,
  StudentDisplay,
} from '@/types/student'
import { useToast } from 'wot-design-uni'
import { useUserStore } from '@/store/user'

const toast = useToast()
const loading = ref(false)
const userStore = useUserStore()

// 登录学生的学号（模拟，实际应从用户状态获取）
const currentStudentCode = ref('2023011837')

// 班级信息数据
const classInfo = ref<ClassInfo>({
  className: '',
  shortName: '',
  headTeacher: '张明辉',
  monitor: '',
  studentCount: 0,
  groupCount: 0,
})

// 同学列表原始数据
const classmatesData = ref<ClassmateInfo[]>([])

// 获取随机颜色
function getRandomColor(): string {
  const colors = [
    '#1989fa',
    '#07c160',
    '#ff9500',
    '#f56c6c',
    '#8e8e93',
    '#5856d6',
    '#ff2d55',
    '#a0cfff',
    '#67c23a',
    '#e6a23c',
  ]
  return colors[Math.floor(Math.random() * colors.length)]
}

// 获取头像显示文字
function getAvatarText(name: string): string {
  return name.length > 2 ? name.slice(-2) : name
}

// 过滤器状态
const sortByName = ref(false)
const sortBySeatNum = ref(true)

// 切换排序方式
const toggleSort = (type: 'name' | 'seat') => {
  if (type === 'name') {
    sortByName.value = true
    sortBySeatNum.value = false
  } else {
    sortByName.value = false
    sortBySeatNum.value = true
  }
}

// 字母索引的同学列表数据
const classmatesList = computed<LetterGroupedClassmates[]>(() => {
  if (!classmatesData.value || classmatesData.value.length === 0) return []

  // 转换学生数据
  const students: StudentDisplay[] = classmatesData.value.map((student) => ({
    id: student.studentCode,
    name: student.studentName,
    avatarText: getAvatarText(student.studentName),
    avatarColor: getRandomColor(),
    isMe: student.studentCode === userStore.userInfo.username,
    sex: student.sex,
    phone: student.phone,
    seatNum: student.seatNum,
  }))

  // 如果是按座号排序，直接返回单个分组
  if (sortBySeatNum.value) {
    // 按座号排序，null值排在最后
    const sortedStudents = students.sort((a, b) => {
      if (a.seatNum === null && b.seatNum === null) return a.name.localeCompare(b.name)
      if (a.seatNum === null) return 1
      if (b.seatNum === null) return -1
      return a.seatNum - b.seatNum
    })

    return [
      {
        letter: '座号',
        students: sortedStudents,
      },
    ]
  }

  // 按姓名排序时使用字母分组
  const groupedByLetter: Record<string, StudentDisplay[]> = {}
  students.forEach((student) => {
    const firstLetter = student.name.charAt(0).toLocaleUpperCase()
    if (!groupedByLetter[firstLetter]) {
      groupedByLetter[firstLetter] = []
    }
    groupedByLetter[firstLetter].push(student)
  })

  // 返回按字母排序的分组
  return Object.keys(groupedByLetter)
    .sort()
    .map((letter) => ({
      letter,
      students: groupedByLetter[letter].sort((a, b) => a.name.localeCompare(b.name)),
    }))
})

// 搜索关键字
const searchKeyword = ref('')

// 过滤后的同学列表
const filteredClassmatesList = computed(() => {
  if (!searchKeyword.value) return classmatesList.value

  const keyword = searchKeyword.value.toLowerCase()

  return classmatesList.value
    .map((section) => {
      return {
        letter: section.letter,
        students: section.students.filter(
          (student) =>
            student.name.toLowerCase().includes(keyword) ||
            student.id.toLowerCase().includes(keyword),
        ),
      }
    })
    .filter((section) => section.students.length > 0)
})

// 初始化班级信息
function initClassInfo() {
  if (classmatesData.value.length > 0) {
    const firstStudent = classmatesData.value[0]
    classInfo.value.className = firstStudent.className
    classInfo.value.shortName = firstStudent.className.substring(0, 2)
    classInfo.value.studentCount = classmatesData.value.length
  }
}

// 获取班级同学数据
async function fetchClassmatesData() {
  loading.value = true
  try {
    const res = await getMyClassmates()
    classmatesData.value = res
    initClassInfo()
  } catch (error) {
    console.error('获取同学数据失败:', error)
    toast.error('获取同学信息失败')
  } finally {
    loading.value = false
  }
}

// 打电话
function callStudent(phone: string) {
  if (!phone) {
    toast.warning('该同学未设置电话号码')
    return
  }

  uni.makePhoneCall({
    phoneNumber: phone,
    fail: () => {
      toast.warning('拨打电话失败')
    },
  })
}

// 发消息
function messageStudent(studentCode: string) {
  toast.info('即将开发消息功能')
  // 这里可以跳转到聊天页面
  // uni.navigateTo({ url: `/pages/chat/index?studentCode=${studentCode}` })
}

onMounted(() => {
  fetchClassmatesData()
})
</script>

<template>
  <view class="container">
    <!-- 内容区域 -->
    <view class="content">
      <wd-loading text="加载中..." v-if="loading" />

      <template v-if="!loading">
        <!-- 班级信息 -->
        <view class="ios-card class-info-card">
          <view class="ios-card-content">
            <view class="flex items-center">
              <view class="class-logo">
                <text>{{ classInfo.shortName }}</text>
              </view>
              <view class="class-info">
                <view class="class-name">{{ classInfo.className }}</view>
                <view class="class-teacher">班主任：{{ classInfo.headTeacher }}</view>
                <view class="class-stats">
                  <text class="stat-badge">{{ classInfo.studentCount }}名同学</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 搜索栏 -->
        <view class="search-input-wrap">
          <wd-icon name="search" size="16" :color="'#8e8e93'" class="mr-2" />
          <wd-input
            v-model="searchKeyword"
            placeholder="搜索同学姓名、学号"
            clearable
            custom-class="search-input"
            no-border
          />
        </view>

        <!-- 同学列表 -->
        <view class="section">
          <view class="section-title-row">
            <text>同学列表</text>
            <view class="sort-buttons">
              <view class="sort-button" :class="{ active: sortByName }" @click="toggleSort('name')">
                <wd-icon
                  name="sort-alpha-up"
                  size="12"
                  :color="sortByName ? '#007aff' : '#8e8e93'"
                  class="mr-1"
                />
                姓名
              </view>
              <view
                class="sort-button"
                :class="{ active: sortBySeatNum }"
                @click="toggleSort('seat')"
              >
                <wd-icon
                  name="sort-numeric-down"
                  size="12"
                  :color="sortBySeatNum ? '#007aff' : '#8e8e93'"
                  class="mr-1"
                />
                座号
              </view>
            </view>
          </view>

          <view class="classmates-list" v-if="filteredClassmatesList.length > 0">
            <block v-for="section in filteredClassmatesList" :key="section.letter">
              <!-- 字母索引 -->
              <view class="letter-index">
                {{ section.letter }}
              </view>

              <!-- 学生列表 -->
              <view
                v-for="student in section.students"
                :key="student.id"
                class="student-item clickable"
                hover-class="hover-item"
              >
                <view class="flex items-center">
                  <view
                    class="student-avatar"
                    :class="{ 'avatar-highlight': student.isMe }"
                    :style="{ backgroundColor: student.avatarColor }"
                  >
                    <text class="avatar-text">{{ student.avatarText }}</text>
                  </view>
                  <view class="flex-1">
                    <view class="flex justify-between">
                      <view class="flex items-center">
                        <text class="student-name">{{ student.name }}</text>
                        <text class="gender-tag" :class="student.sex === 1 ? 'male' : 'female'">
                          {{ student.sex === 1 ? '男' : '女' }}
                        </text>
                      </view>
                      <text class="student-id">{{ student.id }}</text>
                    </view>
                    <view class="flex justify-between mt-1">
                      <view class="student-tags">
                        <text v-if="student.seatNum" class="student-tag seat-tag-sm">
                          {{ student.seatNum }}号
                        </text>
                        <text v-if="student.isMe" class="student-tag me-tag-sm">我</text>
                      </view>
                      <view class="student-actions">
                        <wd-button
                          type="success"
                          size="small"
                          circle
                          icon="phone"
                          custom-style=""
                          @click="callStudent(student.phone)"
                        />
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </block>
          </view>

          <view v-else class="empty-state">
            <wd-icon name="search" size="48" :color="'#c8c9cc'" />
            <text class="empty-text">未找到匹配的同学</text>
          </view>
        </view>
      </template>
    </view>

    <wd-toast />
  </view>
</template>

<style scoped lang="scss">
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.content {
  padding: 30rpx;
}
/* 卡片组件 */
.ios-card {
  margin-bottom: 30rpx;
  overflow: hidden;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.ios-card-content {
  padding: 24rpx;
}
/* 班级信息卡片 */
.class-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  font-weight: bold;
  color: white;
  background-color: #1989fa;
  border-radius: 16rpx;
}

.class-info {
  flex: 1;
}

.class-name {
  font-size: 32rpx;
  font-weight: bold;
}

.class-teacher {
  margin-top: 6rpx;
  font-size: 24rpx;
  color: #666;
}

.class-stats {
  display: flex;
  margin-top: 10rpx;
}

.stat-badge {
  padding: 4rpx 12rpx;
  margin-right: 12rpx;
  font-size: 20rpx;
  color: #1989fa;
  background-color: #e8f3ff;
  border-radius: 30rpx;
}
/* 搜索栏 */
.ios-search {
  padding: 20rpx 30rpx;
  margin-bottom: 30rpx;
  background-color: #f2f2f7;
  border-radius: 20rpx;
}

.search-input-wrap {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
}

.search-input {
  flex: 1;
}
/* 页面部分 */
.section {
  margin-bottom: 30rpx;
}

.section-title {
  margin-bottom: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #6d6d72;
}

.section-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #6d6d72;
}
/* 排序按钮 */
.sort-buttons {
  display: flex;
  font-size: 24rpx;
}

.sort-button {
  display: flex;
  align-items: center;
  padding: 6rpx 12rpx;
  margin-left: 16rpx;
  font-size: 24rpx;
  color: #8e8e93;
}

.sort-button.active {
  color: #007aff;
}
/* 同学列表 */
.classmates-list {
  margin-bottom: 30rpx;
  overflow: hidden;
  background-color: #fff;
  border-radius: 20rpx;
}

.letter-index {
  padding: 8rpx 30rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #6d6d72;
  background-color: #f2f2f7;
}

.student-item {
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #e5e5ea;
}

.clickable {
  position: relative;
}

.hover-item {
  background-color: #f9f9f9;
}

.student-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  border-radius: 50%;

  .avatar-text {
    font-size: 28rpx;
    font-weight: 500;
    color: #fff;
  }
}

.avatar-highlight {
  border: 4rpx solid #ff976a;
}

.student-name {
  font-size: 28rpx;
  font-weight: 500;
}

.student-id {
  font-size: 22rpx;
  color: #8e8e93;
}

.student-tags {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.student-tag {
  padding: 4rpx 12rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
  font-size: 20rpx;
  border-radius: 30rpx;
}

.seat-tag-sm {
  color: #8e8e93;
  background-color: #f2f2f7;
}

.me-tag-sm {
  color: #ff9500;
  background-color: #fff7e8;
}

.student-actions {
  display: flex;
}
/* 辅助类 */
.mr-1 {
  margin-right: 5rpx;
}

.mr-2 {
  margin-right: 10rpx;
}

.mt-1 {
  margin-top: 5rpx;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}
/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  background-color: #fff;
  border-radius: 20rpx;
}

.empty-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #969799;
}

.gender-tag {
  padding: 2rpx 12rpx;
  margin-left: 12rpx;
  font-size: 20rpx;
  border-radius: 20rpx;

  &.male {
    color: #1989fa;
    background-color: #e8f3ff;
  }

  &.female {
    color: #ff2d55;
    background-color: #ffebf0;
  }
}
</style>
