<script setup lang="ts">
import { PropType } from 'vue'

interface Course {
  time: string
  period: string
  name: string
  teacher: string
  location: string
  className: string
  dayOfWeek: number
  section: number[]
}

interface DayData {
  day: string
  date: string
  courses: Course[]
}

defineProps({
  courseData: {
    type: Array as PropType<DayData[]>,
    required: true,
  },
})

// 导出事件
const emit = defineEmits(['view-course-detail'])

// 查看课程详情
const viewCourseDetail = (course: Course, day: string, date: string) => {
  // 传递更多信息以便精确识别课程
  emit('view-course-detail', {
    name: course.name,
    dayOfWeek: course.dayOfWeek,
    section: course.section,
    className: course.className,
    day,
    date,
  })
}
</script>

<template>
  <view class="schedule-content">
    <view v-for="(dayData, index) in courseData" :key="index">
      <view class="day-title">{{ dayData.day }}（{{ dayData.date }}）</view>

      <view v-if="dayData.courses.length > 0">
        <view
          v-for="(course, cIndex) in dayData.courses"
          :key="cIndex"
          class="course-list-item"
          @click="viewCourseDetail(course, dayData.day, dayData.date)"
        >
          <view class="course-time">
            <view class="course-hour">{{ course.time }}</view>
            <view class="course-period">{{ course.period }}</view>
          </view>
          <view class="course-details">
            <view class="list-course-name">{{ course.name }}</view>
            <view class="course-info-row">
              <wd-icon name="user" size="32rpx" color="#666666"></wd-icon>
              {{ course.teacher }}
            </view>
            <view class="course-info-row">
              <wd-icon name="location" size="32rpx" color="#666666"></wd-icon>
              {{ course.location }}
            </view>
            <view class="course-info-row">
              <wd-icon name="usergroup" size="32rpx" color="#666666"></wd-icon>
              {{ course.className }}
            </view>
          </view>
        </view>
      </view>

      <view v-else class="empty-schedule">
        <view class="empty-icon">
          <wd-icon
            :name="dayData.day === '周六' || dayData.day === '周日' ? 'calendar' : 'list'"
            size="80rpx"
            :color="dayData.day === '周六' || dayData.day === '周日' ? '#c0c4cc' : '#a0c4e8'"
          ></wd-icon>
        </view>
        <view class="empty-text">
          {{
            dayData.day === '周六' || dayData.day === '周日'
              ? '休息日，没有课程安排'
              : '今天没有课程安排'
          }}
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
// 课表内容
.schedule-content {
  box-sizing: border-box;
  width: 100%;
}

.day-title {
  display: flex;
  align-items: center;
  margin: 24rpx 0 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;

  &::after {
    flex: 1;
    height: 1rpx;
    margin-left: 16rpx;
    content: '';
    background-color: rgba(142, 142, 147, 0.2);
  }

  &:first-child {
    margin-top: 0;
  }
}

.course-list-item {
  display: flex;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;

  &:active {
    background-color: #f9f9ff;
    transform: translateY(2rpx);
  }
}

.course-time {
  width: 120rpx;
  padding-right: 24rpx;
  margin-right: 24rpx;
  text-align: center;
  border-right: 1rpx solid #ebedf5;
}

.course-hour {
  margin-bottom: 4rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.course-period {
  font-size: 24rpx;
  color: #666666;
}

.course-details {
  flex: 1;
  min-width: 0; // 防止溢出
}

.list-course-name {
  margin-bottom: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.course-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  color: #666666;
  word-break: break-all; // 允许长文本换行

  &:last-child {
    margin-bottom: 0;
  }

  // 为图标和文字添加间距
  .wd-icon {
    flex-shrink: 0;
    margin-right: 12rpx;
  }
}

.empty-schedule {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 0;
  margin-bottom: 16rpx;
  background-color: transparent;
  border-radius: 16rpx;
}

.empty-icon {
  margin-bottom: 16rpx;

  .wd-icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.empty-text {
  font-size: 28rpx;
  line-height: 1.4;
  color: #909399;
  text-align: center;
}
</style>
