<script lang="ts" setup>
import { ref, onMounted, computed, PropType, watch, defineEmits } from 'vue'
import { getSectionConfig } from '@/service/section'
import type { SectionInfo } from '@/types/section'

interface Course {
  day: string
  date: string
  dayOfWeek: number
  name: string
  location: string
  teacher: string
  time: string
  period: string
  section: number[]
  className: string
  color?: string
}

interface TimeSlot {
  number: number
  startTime: string
  endTime: string
}

// 接收父组件传递的数据
const props = defineProps({
  scheduleData: {
    type: Array as PropType<Course[]>,
    default: () => [],
  },
  weekDays: {
    type: Array as PropType<Array<{ day: string; date: string; dayIndex: number }>>,
    default: () => [],
  },
  sectionTimes: {
    type: Array as PropType<
      Array<{
        section: number[]
        time: string
        sectionName?: string
        startTime?: string
        endTime?: string
        remark?: string
      }>
    >,
    default: () => [],
  },
  seasonal: {
    type: String as PropType<'夏令制' | '冬令制'>,
    required: true,
  },
  sectionConfig: {
    type: Array as PropType<SectionInfo[]>,
    default: () => [],
  },
})

// 定义事件
const emit = defineEmits(['view-course-detail', 'config-loaded'])

// 课节时间配置
const currentSeasonal = ref<'夏令制' | '冬令制'>(props.seasonal)

// 监听 seasonal 变化
watch(
  () => props.seasonal,
  (newSeasonal) => {
    console.log('课表组件季节切换:', newSeasonal)
    currentSeasonal.value = newSeasonal
  },
)

// 时间槽配置 - 支持使用从父组件传递的sectionTimes数据
const timeSlots = computed(() => {
  // 优先使用父组件传递的sectionTimes数据
  if (props.sectionTimes && props.sectionTimes.length > 0) {
    console.log('使用从父组件传递的sectionTimes数据')
    return props.sectionTimes.map((item) => ({
      number: item.section[0],
      startTime: item.startTime || item.time.split('-')[0],
      endTime: item.endTime || item.time.split('-')[1],
      sectionName: item.sectionName || `第${item.section[0]}节`,
      timePeriod: item.remark || '',
    }))
  }

  // 如果没有sectionTimes数据，则使用sectionConfig数据
  if (!props.sectionConfig.length) return []

  return []
})

// 星期代码映射
const dayKeyMap = computed<Record<string, string>>(() => {
  const map: Record<string, string> = {}
  if (props.weekDays && props.weekDays.length > 0) {
    props.weekDays.forEach((day, index) => {
      map[day.day] = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'][index]
    })
  } else {
    // 默认映射
    map['周一'] = 'mon'
    map['周二'] = 'tue'
    map['周三'] = 'wed'
    map['周四'] = 'thu'
    map['周五'] = 'fri'
    map['周六'] = 'sat'
    map['周日'] = 'sun'
  }
  return map
})

// 处理后的课程数据
const courses = computed(() => {
  if (!props.scheduleData || props.scheduleData.length === 0) return []

  // 增加更多颜色选项
  const colorNames = [
    'color-1',
    'color-2',
    'color-3',
    'color-4',
    'color-5',
    'color-6',
    'color-7',
    'color-8',
    'color-9',
    'color-10',
    'color-11',
    'color-12',
    'color-13',
    'color-14',
    'color-15',
  ]

  // 使用位置+课程+教师的组合作为颜色分配依据
  const courseColorMap = new Map()

  return props.scheduleData.map((course) => {
    // 创建一个唯一的颜色键，结合多个因素
    // 优先考虑位置（星期+节次）和课程名称组合
    const colorKey = `${course.day}-${course.section[0]}-${course.name.substring(0, 2)}`

    // 如果还没有分配颜色，随机分配一个
    if (!courseColorMap.has(colorKey)) {
      // 使用简单的字符串哈希算法
      let hashCode = 0
      for (let i = 0; i < colorKey.length; i++) {
        hashCode = (hashCode << 5) - hashCode + colorKey.charCodeAt(i)
        hashCode = hashCode & hashCode // 转换为32位整数
      }
      const colorIndex = Math.abs(hashCode) % colorNames.length
      courseColorMap.set(colorKey, colorNames[colorIndex])
    }

    // 将课程数据转换为组件需要的格式
    return {
      day: dayKeyMap.value[course.day] || 'mon',
      start: course.section[0],
      duration: course.section[1] - course.section[0] + 1,
      name: course.name,
      location: course.location || '',
      teacher: course.teacher || '',
      color: courseColorMap.get(colorKey),
      className: course.className,
      time: course.time,
    }
  })
})

// 课程详情
const showCourseDetail = (course) => {
  // 传递更多信息以便精确识别课程
  emit('view-course-detail', {
    name: course.name,
    day: course.day,
    section: [course.start, course.start + course.duration - 1],
    className: course.className,
  })
}

// 获取单元格的课程
const getCellCourses = (day: string, timeSlot: number) => {
  return courses.value.filter((course) => course.day === day && course.start === timeSlot)
}

// 计算课程的样式
const getCourseStyle = (course) => {
  // 调整高度计算，留出足够空间显示课程名称(4行)、地址(2行)和班级(2行)
  const height = 110 * course.duration - 6
  const top = 3

  return {
    height: `${height}rpx`,
    top: `${top}rpx`,
    zIndex: '1',
  }
}

// 高亮今天
const highlightToday = () => {
  const now = new Date()
  const day = now.getDay() // 0周日，1-6周一到周六
  const dayMap = {
    1: 'mon',
    2: 'tue',
    3: 'wed',
    4: 'thu',
    5: 'fri',
    6: 'sat',
    0: 'sun',
  }
  return {
    day: dayMap[day],
    date: `${now.getMonth() + 1}月${now.getDate()}日`,
  }
}

// 判断是否是今天的单元格
const isTodayCell = (day, dayDate) => {
  const today = highlightToday()
  return day === today.day && dayDate === today.date
}

onMounted(() => {
  highlightToday()
})
</script>

<template>
  <view class="schedule-container">
    <view class="schedule-table">
      <!-- 表头 -->
      <view class="schedule-header">
        <view class="time-cell">节次</view>
        <!-- 动态生成星期表头 -->
        <view
          v-for="(day, index) in weekDays"
          :key="index"
          class="day-header"
          :class="{
            weekend: index > 4,
          }"
        >
          <view class="day-name">{{ day.day }}</view>
          <view class="day-date">{{ day.date }}</view>
        </view>
      </view>

      <!-- 表格内容 -->
      <view class="schedule-body">
        <view v-for="slot in timeSlots" :key="slot.number" class="schedule-row">
          <!-- 时间单元格，显示节次编号和时间 -->
          <view class="time-cell">
            <view class="slot-number">{{ slot.sectionName }}</view>
            <view class="slot-time">
              {{ slot.startTime }}
              <br />
              {{ slot.endTime }}
            </view>
          </view>

          <!-- 课程单元格 -->
          <view
            v-for="(day, index) in weekDays"
            :key="index"
            class="day-cell"
            :class="{
              weekend: index > 4,
              today: isTodayCell(dayKeyMap[day.day], day.date),
            }"
          >
            <view
              v-for="(course, courseIndex) in getCellCourses(dayKeyMap[day.day], slot.number)"
              :key="`${dayKeyMap[day.day]}-${slot.number}-${courseIndex}`"
              class="class-item"
              :class="course.color"
              @click="showCourseDetail(course)"
              :style="getCourseStyle(course)"
            >
              <view class="course-content">
                <view class="class-name">{{ course.name }}</view>
                <view class="class-location">{{ course.location }}</view>
                <view class="class-class">{{ course.className }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style>
.schedule-container {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #f9fafb;
  border-radius: 24rpx;
}

.schedule-table {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: white;
  border-radius: 24rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
}

.schedule-header {
  position: sticky;
  top: 0;
  z-index: 1;
  box-sizing: border-box;
  display: flex;
  width: 100%;
}

.time-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  min-width: 80rpx;
  padding: 8rpx 0;
  font-weight: 600;
  color: #3b82f6;
  text-align: center;
  background-color: #dbeafe;
}

.slot-number {
  font-size: 24rpx;
  font-weight: 600;
}

.slot-time {
  margin-top: 4rpx;
  font-size: 18rpx;
  font-weight: normal;
}

.day-header {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 60rpx;
  padding: 12rpx 0;
  font-weight: 600;
  color: white;
  text-align: center;
  background-color: #3b82f6;
}

.day-header.weekend {
  background-color: #6b7280;
}

.day-header.today {
  background-color: #60a5fa;
  box-shadow: 0 0 8rpx rgba(59, 130, 246, 0.5);
}

.day-name {
  font-size: 24rpx;
  font-weight: 600;
}

.day-date {
  margin-top: 2rpx;
  font-size: 20rpx;
  font-weight: 400;
  opacity: 0.9;
}

.schedule-body {
  display: flex;
  flex: 1;
  flex-direction: column;
  width: 100%;
  overflow-y: auto;
}

.schedule-row {
  display: flex;
  width: 100%;
  height: 110rpx;
}

.day-cell {
  position: relative;
  box-sizing: border-box;
  flex: 1;
  min-width: 60rpx;
  height: 110rpx;
  padding: 0;
}

.day-cell.weekend {
  background-color: rgba(254, 243, 199, 0.2);
}

.day-cell.today {
  background-color: rgba(219, 234, 254, 0.2);
}

.class-item {
  position: absolute;
  left: 6%;
  z-index: 1;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 88%;
  padding: 4rpx 6rpx;
  overflow: hidden;
  font-size: 20rpx;
  border-style: solid;
  border-width: 1rpx;
  border-radius: 8rpx;
}

.course-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.class-name {
  display: -webkit-box;
  overflow: hidden;
  font-size: 20rpx;
  font-weight: 600;
  line-height: 22rpx;
  text-align: center;
  word-break: break-all;
  white-space: normal;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}

.class-location {
  display: -webkit-box;
  margin-top: 2rpx;
  overflow: hidden;
  font-size: 18rpx;
  line-height: 20rpx;
  text-align: center;
  word-break: break-all;
  white-space: normal;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.class-class {
  margin-top: 2rpx;
  overflow: hidden;
  font-size: 16rpx;
  line-height: 18rpx;
  text-align: center;
  word-break: break-all;
  white-space: normal;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
/* 课程颜色 */
.color-1 {
  color: #1e40af;
  background-color: #dbeafe;
  border-color: #93c5fd;
}

.color-2 {
  color: #831843;
  background-color: #fce7f3;
  border-color: #f9a8d4;
}

.color-3 {
  color: #065f46;
  background-color: #ecfdf5;
  border-color: #6ee7b7;
}

.color-4 {
  color: #92400e;
  background-color: #fef3c7;
  border-color: #fcd34d;
}

.color-5 {
  color: #5b21b6;
  background-color: #ede9fe;
  border-color: #c4b5fd;
}

.color-6 {
  color: #9a3412;
  background-color: #ffedd5;
  border-color: #fdba74;
}

.color-7 {
  color: #4b5563;
  background-color: #f3f4f6;
  border-color: #d1d5db;
}
/* 新增颜色 */
.color-8 {
  color: #1d4ed8;
  background-color: #e0f2fe;
  border-color: #7dd3fc;
}

.color-9 {
  color: #7c2d12;
  background-color: #fef2f2;
  border-color: #fca5a5;
}

.color-10 {
  color: #3730a3;
  background-color: #eef2ff;
  border-color: #a5b4fc;
}

.color-11 {
  color: #155e75;
  background-color: #ecfeff;
  border-color: #67e8f9;
}

.color-12 {
  color: #115e59;
  background-color: #f0fdfa;
  border-color: #5eead4;
}

.color-13 {
  color: #3f6212;
  background-color: #f7fee7;
  border-color: #bef264;
}

.color-14 {
  color: #854d0e;
  background-color: #fffbeb;
  border-color: #fcd34d;
}

.color-15 {
  color: #713f12;
  background-color: #fef9c3;
  border-color: #fde047;
}
</style>
