<route lang="json5">
{
  style: {
    navigationBarTitleText: '学生课表',
  },
}
</route>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { getSemesterList, getSemesterConfig } from '@/service/semester'
import type { SemesterOption, SemesterConfig } from '@/types/semester'
import { getSchedule, getScheduleTime } from '@/service/schedule'
import type { ScheduleItem, ScheduleTimeItem } from '@/types/schedule'
import { useUserStore } from '@/store/user'
import { getSectionConfig } from '@/service/section'
import type { SectionInfo } from '@/types/section'

// 导入视图组件
import ListScheduleView from './components/ListScheduleView.vue'
import TableScheduleView from './components/TableScheduleView.vue'
import { listToTableData, tableToListData } from './components/ScheduleDataConverter'

// 获取用户信息
const userStore = useUserStore()
const userName = computed(() => userStore.userInfo.realname || userStore.userInfo.nickname || '我')

// 学期选择
const currentSemester = ref<string>('')
const showSemesterPicker = ref<boolean>(false)
const semesterOptions = ref<SemesterOption[]>([])

// 学期配置相关
const semesterConfig = ref<SemesterConfig | null>(null)
const currentWeek = ref<number>()
const weekOptions = ref<Array<{ week: number; date: string }>>([])

// 表格视图相关
const sectionConfigLoaded = ref<boolean>(false)
const sectionConfig = ref<SectionInfo[]>([])

// 课程详情弹窗相关
const showCourseDetailPopup = ref<boolean>(false)
const selectedCourseName = ref<string>('')
const originalCourseData = ref<ScheduleItem[]>([])
const rawScheduleData = ref<ScheduleItem[]>([]) // 存储原始API返回的课程数据

// 新增：课程时间表数据
const scheduleTimeData = ref<Record<string, ScheduleTimeItem>>({})
// 新增：是否已加载课程时间表
const scheduleTimeLoaded = ref<boolean>(false)

// 获取学年学期选项
const getSemesterOptions = (): void => {
  getSemesterList()
    .then((res) => {
      // 对学期列表进行倒序排序
      semesterOptions.value = res.semesters.sort((a, b) => {
        // 按照学期值（如：2024-2025-1）进行倒序排序
        return b.value.localeCompare(a.value)
      })

      // 默认选择当前学期
      const currentSemesterOption = res.semesters.find((item: SemesterOption) => item.isCurrent)
      if (currentSemesterOption) {
        currentSemester.value = currentSemesterOption.label
        // 获取默认学期的周数配置
        getSemesterWeeks(currentSemesterOption.value)
      } else if (res.semesters.length > 0) {
        currentSemester.value = res.semesters[0].label
        // 获取默认学期的周数配置
        getSemesterWeeks(res.semesters[0].value)
      }
    })
    .catch((error: Error) => {
      console.error('获取学年学期选项失败:', error)
      uni.showToast({
        title: '获取学年学期选项失败',
        icon: 'none',
      })
    })
}

// 计算当前周次
const calculateCurrentWeek = (config: SemesterConfig, isXxq: boolean = false): number => {
  // 根据是否为小学期选择不同的开始日期和总周数
  const startDateStr = isXxq ? config.xxqStartDate : config.startDate
  const totalWeeks = isXxq ? config.xxqWeek : config.totalWeeks

  const startDate = new Date(startDateStr)
  const today = new Date()

  // 先调整学期起始日期到最近的周一，与generateWeekOptions保持一致
  const firstDayOfWeek = startDate.getDay() // 0是周日，1-6是周一到周六
  const adjustDays = firstDayOfWeek === 0 ? -6 : 1 - firstDayOfWeek // 如果是周日，往前推6天；否则，往前推 (firstDayOfWeek - 1) 天
  startDate.setDate(startDate.getDate() + adjustDays)

  // 计算两个日期之间的天数差
  const diffTime = today.getTime() - startDate.getTime()
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

  // 计算当前是第几周
  const weekNumber = Math.floor(diffDays / 7) + 1

  // 如果计算得到的周次超出总周数，返回最后一周
  // 如果计算得到的周次小于1，返回第一周
  if (weekNumber > totalWeeks) {
    return totalWeeks
  } else if (weekNumber < 1) {
    return 1
  }

  return weekNumber
}

// 获取学期配置并生成周次选项
const getSemesterWeeks = async (semester: string): Promise<void> => {
  try {
    const res = await getSemesterConfig(semester)
    semesterConfig.value = res.semestersConfig

    // 检查当前学期是否为小学期
    const currentSemesterOption = semesterOptions.value.find(
      (item) => item.label === currentSemester.value,
    )
    const isXxq = currentSemesterOption?.isCurrent && currentSemesterOption?.isXxq

    // 生成周次选项
    generateWeekOptions(res.semestersConfig, isXxq)

    // 计算当前周次
    const calculatedWeek = calculateCurrentWeek(res.semestersConfig, isXxq)
    // 获取当前周次的课程表数据
    await getScheduleData(semester, calculatedWeek, isXxq)
    setTimeout(() => {
      selectWeek(calculatedWeek)
    }, 1)
  } catch (error) {
    console.error('获取学期配置失败:', error)
    uni.showToast({
      title: '获取学期配置失败',
      icon: 'none',
    })
  }
}

// 生成周次选项
const generateWeekOptions = (config: SemesterConfig, isXxq: boolean = false): void => {
  const options: Array<{ week: number; date: string }> = []

  // 根据是否为小学期选择不同的开始日期和总周数
  const startDateStr = isXxq ? config.xxqStartDate : config.startDate
  const totalWeeks = isXxq ? config.xxqWeek : config.totalWeeks

  const startDate = new Date(startDateStr)

  // 先调整学期起始日期到最近的周一
  const firstDayOfWeek = startDate.getDay() // 0是周日，1-6是周一到周六
  const adjustDays = firstDayOfWeek === 0 ? -6 : 1 - firstDayOfWeek // 如果是周日，往前推6天；否则，往前推 (firstDayOfWeek - 1) 天
  startDate.setDate(startDate.getDate() + adjustDays)

  // 生成每一周的日期范围
  for (let i = 0; i < totalWeeks; i++) {
    // 计算当前周的开始日期和结束日期
    const weekStart = new Date(startDate)
    weekStart.setDate(startDate.getDate() + i * 7) // 从调整后的开始日期计算每周的周一

    const weekEnd = new Date(weekStart)
    weekEnd.setDate(weekStart.getDate() + 6) // 周一+6天 = 周日

    // 格式化日期
    const startStr = `${weekStart.getMonth() + 1}/${weekStart.getDate()}`
    const endStr = `${weekEnd.getMonth() + 1}/${weekEnd.getDate()}`

    options.push({
      week: i + 1,
      date: `${startStr}-${endStr}`,
    })
  }

  weekOptions.value = options
}

// 获取课程表数据
const getScheduleData = async (
  semester: string,
  week: number,
  isXxq: boolean = false,
): Promise<void> => {
  try {
    const res = await getSchedule(semester, week, isXxq ? 1 : 0)
    // 保存原始课程数据
    rawScheduleData.value = res.list
    // 将接口返回的数据转换为列表格式
    const listData = tableToListData(res.list, weekDays.value, sectionTimes.value)
    courseData.value = listData
  } catch (error) {
    console.error('获取课程表失败:', error)
    uni.showToast({
      title: '获取课程表失败',
      icon: 'none',
    })
  }
}

// 选择周次
const selectWeek = (week: number): void => {
  currentWeek.value = week
  // 获取当前学期的课程表数据
  const currentSemesterOption = semesterOptions.value.find(
    (item) => item.label === currentSemester.value,
  )
  if (currentSemesterOption) {
    const isXxq = currentSemesterOption.isCurrent && currentSemesterOption.isXxq
    getScheduleData(currentSemesterOption.value, week, isXxq)
    fetchScheduleTime()
  }
}

// 选择学期
const selectSemester = (semester: SemesterOption): void => {
  currentSemester.value = semester.label
  showSemesterPicker.value = false
  // 获取学期配置并生成周次选项
  getSemesterWeeks(semester.value)
}

// 时间段信息
const sectionTimes = ref([])

// 星期信息
const weekDays = computed(() => {
  if (!semesterConfig.value) return []

  // 检查当前学期是否为小学期
  const currentSemesterOption = semesterOptions.value.find(
    (item) => item.label === currentSemester.value,
  )
  const isXxq = currentSemesterOption?.isCurrent && currentSemesterOption?.isXxq

  // 根据是否为小学期选择不同的开始日期
  const startDateStr = isXxq ? semesterConfig.value.xxqStartDate : semesterConfig.value.startDate
  const startDate = new Date(startDateStr)

  // 先调整学期起始日期到最近的周一，与generateWeekOptions保持一致
  const firstDayOfWeek = startDate.getDay() // 0是周日，1-6是周一到周六
  const adjustDays = firstDayOfWeek === 0 ? -6 : 1 - firstDayOfWeek
  startDate.setDate(startDate.getDate() + adjustDays)

  // 计算当前周的起始日期
  const weekStart = new Date(startDate)
  weekStart.setDate(startDate.getDate() + (currentWeek.value - 1) * 7)

  // 定义星期顺序（周一到周日）
  const weekOrder = [1, 2, 3, 4, 5, 6, 7] // 1代表周一，7代表周日
  const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

  // 按照固定顺序生成周一到周日的数据
  return weekOrder.map((order, index) => {
    // 计算这一周的这一天的日期
    const date = new Date(weekStart)
    date.setDate(weekStart.getDate() + index)

    // 格式化日期
    const month = date.getMonth() + 1
    const day = date.getDate()

    return {
      day: dayNames[index], // 保持原有的周一到周日顺序
      date: `${month}月${day}日`,
      dayIndex: order,
      realDay: dayNames[index], // 实际星期几就是当前索引对应的星期
    }
  })
})

// 课程数据 - 列表格式（主数据源）
const courseData = ref<
  Array<{
    day: string
    date: string
    courses: Array<{
      time: string
      period: string
      name: string
      teacher: string
      location: string
      className: string
      dayOfWeek: number
      section: number[]
    }>
  }>
>([])

// 课程数据 - 表格格式（从列表格式转换而来）
const scheduleData = computed(() => {
  return listToTableData(courseData.value)
})

// 查看课程详情
const viewCourseDetail = (courseInfo) => {
  // 如果是字符串，则是从汇总区域点击的，显示所有同名课程
  if (typeof courseInfo === 'string') {
    selectedCourseName.value = courseInfo
    originalCourseData.value = rawScheduleData.value.filter((item) => item.course === courseInfo)
    // console.log(originalCourseData.value.);
    // 如果只有一个课程，直接跳转处理
    if (originalCourseData.value.length === 1) {
      handleCourseSelect(originalCourseData.value[0])
      return
    }
  } else {
    // 从课表区域点击的，显示特定课程
    selectedCourseName.value = courseInfo.name

    // 对点击的课程信息进行日志记录，便于调试
    console.log('点击的课程信息:', courseInfo)

    // 根据传入的信息精确筛选课程
    originalCourseData.value = rawScheduleData.value.filter((item) => {
      // 课程名称必须匹配
      if (item.course !== courseInfo.name) return false

      // 星期对应关系
      const dayMapping = {
        mon: 1,
        tue: 2,
        wed: 3,
        thu: 4,
        fri: 5,
        sat: 6,
        sun: 7,
      }

      // 如果有day信息（英文简写），转换为DayIndex进行匹配
      if (courseInfo.day && dayMapping[courseInfo.day]) {
        if (item.DayIndex !== dayMapping[courseInfo.day]) return false
      }

      // 如果有dayOfWeek信息，则必须匹配
      if (courseInfo.dayOfWeek && item.DayIndex !== courseInfo.dayOfWeek) return false

      // 如果有day信息（星期几的中文表示），也要匹配
      if (courseInfo.day && typeof courseInfo.day === 'string' && courseInfo.day.startsWith('周')) {
        const dayIndex = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日'].indexOf(
          courseInfo.day,
        )
        if (dayIndex > 0 && item.DayIndex !== dayIndex) return false
      }

      // 如果有section信息（节次范围），则检查是否有重叠
      if (courseInfo.section && courseInfo.section.length) {
        const startNode = Number(item.startNode)
        const endNode = Number(item.endNode)
        const courseStart = courseInfo.section[0]
        const courseEnd =
          courseInfo.section.length > 1 ? courseInfo.section[1] : courseInfo.section[0]

        // 检查节次范围是否有重叠
        if (!(startNode <= courseEnd && endNode >= courseStart)) return false
      }

      // 如果有className信息，检查是否匹配
      // className可能是多个班级合并的字符串，只要有一个匹配即可
      if (courseInfo.className) {
        // 如果courseInfo.className包含多个班级（用、分隔）
        if (courseInfo.className.includes('、')) {
          // 分割成数组，检查item.className是否在其中之一
          const classNames = courseInfo.className.split('、')
          if (!classNames.includes(item.className)) return false
        } else {
          // 单个班级直接比较
          if (item.className !== courseInfo.className) return false
        }
      }
      console.log(item)

      return true
    })

    // 如果只有一个课程，直接跳转处理
    if (originalCourseData.value.length === 1) {
      handleCourseSelect(originalCourseData.value[0])
      return
    }
  }

  // 如果有多个课程，显示弹窗让用户选择
  showCourseDetailPopup.value = true
}

// 视图切换
const currentView = ref('table') // 'list' or 'table'

// 页面参数处理函数
const handlePageParams = () => {
  // 获取页面参数
  const pages = getCurrentPages()
  const page = pages[pages.length - 1]
  // @ts-expect-error uniapp类型定义问题
  const params = page.options || {}

  // 如果URL参数中有view=list，就设置默认视图为list
  if (params.view === 'list') {
    currentView.value = 'list'
    console.log('根据URL参数设置默认视图为列表视图')
  }
}

const switchView = (view) => {
  currentView.value = view
  console.log('切换视图:', view, '课节配置加载状态:', sectionConfigLoaded.value)
}

// 获取课程图标
const getCourseIcon = (courseName: string): { icon: string; collection: string } => {
  const icons: Record<string, { icon: string; collection: string }> = {
    // 编程类课程
    数据库: { icon: 'database', collection: 'carbon' },
    Java: { icon: 'application-development', collection: 'carbon' },
    Web: { icon: 'html', collection: 'carbon' },
    Python: { icon: 'logo-python', collection: 'carbon' },
    C语言: { icon: 'letter-c', collection: 'mdi' },
    'C++': { icon: 'language-cpp', collection: 'mdi' },
    算法: { icon: 'flow', collection: 'carbon' },
    编程: { icon: 'code', collection: 'carbon' },

    // 理科类课程
    数学: { icon: 'calculation', collection: 'carbon' },
    物理: { icon: 'chart-radar', collection: 'carbon' },
    化学: { icon: 'chemistry', collection: 'carbon' },
    生物: { icon: 'microscope', collection: 'mdi' },

    // 文科类课程
    语文: { icon: 'book', collection: 'carbon' },
    英语: { icon: 'language', collection: 'carbon' },
    历史: { icon: 'document-sentiment', collection: 'carbon' },
    地理: { icon: 'earth', collection: 'carbon' },
    政治: { icon: 'group', collection: 'carbon' },

    // 艺术类课程
    音乐: { icon: 'music', collection: 'carbon' },
    美术: { icon: 'brush', collection: 'mdi' },
    舞蹈: { icon: 'activity', collection: 'carbon' },

    // 体育类课程
    体育: { icon: 'run', collection: 'carbon' },
    篮球: { icon: 'basketball', collection: 'mdi' },
    足球: { icon: 'soccer', collection: 'mdi' },

    // 实践类课程
    实验: { icon: 'chemistry', collection: 'carbon' },
    实训: { icon: 'tools', collection: 'carbon' },
    实习: { icon: 'workspace', collection: 'carbon' },
    项目: { icon: 'task', collection: 'carbon' },

    // 其他常见课程
    讲座: { icon: 'presentation', collection: 'carbon' },
    研讨: { icon: 'forum', collection: 'carbon' },
    辅导: { icon: 'help', collection: 'carbon' },
    考试: { icon: 'exam-mode', collection: 'carbon' },
  }

  // 尝试精确匹配
  if (courseName && icons[courseName]) {
    return icons[courseName]
  }

  // 尝试包含匹配
  for (const [key, value] of Object.entries(icons)) {
    if (courseName?.includes(key)) {
      return value
    }
  }

  // 默认图标
  return { icon: 'notebook', collection: 'carbon' }
}
// 获取课程上课时间
const getCourseDays = (courses: Array<any>): string => {
  if (!courses?.length) return ''

  // 使用Set去重，避免同一天重复显示
  const uniqueDays = new Set(courses.map((course) => course.dayOfWeek))

  const dayMap: Record<number, string> = {
    1: '周一',
    2: '周二',
    3: '周三',
    4: '周四',
    5: '周五',
    6: '周六',
    7: '周日',
  }

  return Array.from(uniqueDays)
    .sort((a, b) => a - b) // 按照周几排序
    .map((day) => dayMap[day])
    .join('、')
}

// 处理课程汇总数据
const summaryData = computed(() => {
  if (!courseData.value?.length) return []

  // 1. 过滤出有课的数据
  const validCourses = courseData.value.filter((item) => item.courses?.length > 0)

  // 2. 按课程名称分组
  const courseGroups = validCourses.reduce(
    (groups, item) => {
      // 遍历每天的课程
      item.courses.forEach((course) => {
        const courseName = course.name
        if (!courseName) return

        // 如果是新课程，初始化
        if (!groups[courseName]) {
          groups[courseName] = {
            name: courseName,
            className: course.className,
            location: course.location,
            totalHours: 0,
            courses: [],
            // 用于去重的课程唯一标识集合
            uniqueCourseIds: new Set<string>(),
          }
        }

        // 创建课程唯一标识（日期+节次+班级）
        // 使用course.period或其他可用属性代替section.join
        const courseUniqueId = `${item.date}_${course.period || course.time}_${course.className}`

        // 如果这个课程标识还没有被记录，则计数+1
        if (!groups[courseName].uniqueCourseIds.has(courseUniqueId)) {
          groups[courseName].uniqueCourseIds.add(courseUniqueId)
          groups[courseName].totalHours += 1
        }

        // 将课程添加到对应分组
        groups[courseName].courses.push(course)
      })

      return groups
    },
    {} as Record<string, any>,
  )

  // 3. 转换为数组并删除临时使用的uniqueCourseIds属性
  return Object.values(courseGroups).map((group) => {
    // 使用类型断言确保TypeScript不会报错
    const { uniqueCourseIds, ...rest } = group as {
      uniqueCourseIds: Set<string>
      [key: string]: any
    }
    return rest
  })
})

// 更新导航栏标题
const updateNavbarTitle = () => {
  uni.setNavigationBarTitle({
    title: `${userName.value}的课程表`,
  })
}

// 获取课节时间配置
const fetchSectionConfig = async (seasonal: '夏令制' | '冬令制' = '冬令制'): Promise<void> => {
  if (sectionConfig.value.length > 0 && sectionConfig.value[0].seasonal === seasonal) {
    console.log('使用已缓存的课节配置')
    return
  }

  console.log('父组件加载课节配置:', seasonal)
  try {
    const res = await getSectionConfig()
    // 根据 campus 和 seasonal 过滤配置
    sectionConfig.value = res.sectionConfig.filter(
      (config) =>
        config.campus === '' && // 默认校区为空
        config.seasonal === seasonal,
    )
    sectionConfigLoaded.value = true
  } catch (error) {
    console.error('获取课节时间配置失败:', error)
    uni.showToast({
      title: '获取课节时间配置失败',
      icon: 'none',
    })
  }
}

// 处理课程选择
const handleCourseSelect = (course: ScheduleItem) => {
  console.log('选择课程:', course)
  if (course.teachingTaskId === 0) {
    /* uni.showToast({
      title: '页面跳转失败',
      icon: 'none',
    }) */
    return
  }
  // 关闭popup
  showCourseDetailPopup.value = false

  // 跳转到教学日志编辑页面，并传递课程id参数
  uni.navigateTo({
    url: `/pages/student/study-plan/confirm?id=${course.id}`,
    success: () => {
      console.log('跳转成功，课程ID:', course.id)
    },
    fail: (error) => {
      console.error('跳转失败:', error)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none',
      })
    },
  })
}
// 当前学期选项
const currentSemesterOption = computed(() =>
  semesterOptions.value.find((item) => item.label === currentSemester.value),
)

// 当前季节制度
const currentSeasonal = computed<'夏令制' | '冬令制'>(
  () => (currentSemesterOption.value?.seasonal as '夏令制' | '冬令制') || '冬令制',
)

// 监听季节制度变化
watch(currentSeasonal, (newSeasonal) => {
  console.log('季节制度变化:', newSeasonal)
  fetchSectionConfig(newSeasonal)
})

// 新增：获取课程时间表
const fetchScheduleTime = async (): Promise<void> => {
  try {
    const res = await getScheduleTime(currentWeek.value)
    scheduleTimeData.value = res
    scheduleTimeLoaded.value = true
    console.log('获取课程时间表成功:', res)

    // 将获取的数据转换为组件需要的格式
    processScheduleTimeData()
  } catch (error) {
    console.error('获取课程时间表失败:', error)
    uni.showToast({
      title: '获取课程时间表失败',
      icon: 'none',
    })
  }
}

// 新增：处理课程时间表数据
const processScheduleTimeData = (): void => {
  if (!scheduleTimeLoaded.value || !Object.keys(scheduleTimeData.value).length) return

  // 将课程时间表数据转换为组件需要的格式
  const timeData = Object.values(scheduleTimeData.value)

  // 按节次排序
  timeData.sort((a, b) => a.jcdm - b.jcdm)

  // 转换为组件需要的格式
  sectionTimes.value = timeData.map((item) => ({
    section: [Number(item.jcdm)], // 节次数组，这里只有一个元素
    time: `${item.jcskkssj.substring(0, 5)}-${item.jcskjssj.substring(0, 5)}`, // 格式化为 "08:00-08:45"
    sectionName: item.jcmc, // 节次名称，如"第1节"
    startTime: item.jcskkssj.substring(0, 5), // 开始时间
    endTime: item.jcskjssj.substring(0, 5), // 结束时间
    remark: item.remark, // 备注，如"上午"
  }))

  console.log('处理后的课程时间表数据:', sectionTimes.value)
}

// 初始化
onMounted(() => {
  // 设置导航栏标题
  updateNavbarTitle()

  // 获取页面参数并处理
  handlePageParams()

  // 获取学年学期选项列表
  getSemesterOptions()

  // 获取默认课节时间配置(冬令制)
  fetchSectionConfig('冬令制')

  // 新增：获取课程时间表
  // fetchScheduleTime()
})

// 监听当前周次变化，自动滚动（无需额外操作，scrollIntoView属性会处理）
watch(currentWeek, (newWeek) => {
  console.log('当前周次变更为:', newWeek)
})

// 监听用户名变化，更新标题
watch(userName, (newName) => {
  updateNavbarTitle()
})
</script>

<template>
  <view class="container">
    <!-- 顶部操作栏 -->
    <view class="top-action-bar">
      <!-- 学期选择 -->
      <view class="semester-select" @click="showSemesterPicker = true">
        <text class="semester-label">学期:</text>
        <text class="semester-text">
          {{ currentSemester
          }}{{
            currentSemesterOption?.isCurrent && currentSemesterOption?.isXxq ? '（小学期）' : ''
          }}
        </text>
        <view class="i-carbon-chevron-down" style="font-size: 20rpx; color: #666666"></view>
      </view>
      <!-- 视图切换按钮 -->
      <view class="view-toggle">
        <view
          :class="['view-toggle-btn', { active: currentView === 'table' }]"
          @click="switchView('table')"
        >
          <view
            class="i-carbon-calendar"
            :style="{ fontSize: '20rpx', color: currentView === 'table' ? '#3a8eff' : '#666666' }"
          ></view>
          <text class="toggle-text">表格</text>
        </view>
        <view
          :class="['view-toggle-btn', { active: currentView === 'list' }]"
          @click="switchView('list')"
        >
          <view
            class="i-carbon-list"
            :style="{ fontSize: '20rpx', color: currentView === 'list' ? '#3a8eff' : '#666666' }"
          ></view>
          <text class="toggle-text">列表</text>
        </view>
      </view>
    </view>

    <!-- 学期选择器弹窗 -->
    <wd-popup v-model="showSemesterPicker" position="bottom" round>
      <view class="semester-picker">
        <view class="picker-header">
          <text class="picker-title">选择学期</text>
          <view
            class="i-carbon-close"
            style="font-size: 32rpx"
            @click="showSemesterPicker = false"
          ></view>
        </view>
        <scroll-view class="picker-content" scroll-y>
          <view
            v-for="item in semesterOptions"
            :key="item.value"
            :class="['picker-item', { active: currentSemester === item.label }]"
            @click="selectSemester(item)"
          >
            <view class="item-info">
              <text class="item-label">
                {{ item.label }}{{ item.isCurrent && item.isXxq ? '（小学期）' : '' }}
              </text>
              <text v-if="item.seasonal" class="item-seasonal">{{ item.seasonal }}</text>
            </view>
            <view
              v-if="currentSemester === item.label"
              class="i-carbon-checkmark"
              style="font-size: 32rpx; color: #3a8eff"
            ></view>
          </view>
        </scroll-view>
      </view>
    </wd-popup>

    <!-- 周次选择器 -->
    <view class="week-view-container">
      <view class="week-scroller">
        <scroll-view
          class="week-selector"
          scroll-x
          :show-scrollbar="false"
          :scroll-into-view="`week-${currentWeek}`"
          scroll-with-animation
        >
          <view class="week-items-container">
            <view
              v-for="item in weekOptions"
              :key="item.week"
              :id="`week-${item.week}`"
              :class="['week-item', { active: currentWeek === item.week }]"
              @click="selectWeek(item.week)"
            >
              <text class="week-number">第{{ item.week }}周</text>
              <text class="week-date">{{ item.date }}</text>
            </view>
          </view>
        </scroll-view>
      </view>
    </view>

    <!-- 课表内容 - 列表视图 -->
    <ListScheduleView
      v-if="currentView === 'list'"
      :course-data="courseData"
      @view-course-detail="viewCourseDetail"
    />

    <!-- 课表内容 - 表格视图 -->
    <TableScheduleView
      v-if="currentView === 'table'"
      :schedule-data="scheduleData"
      :week-days="weekDays"
      :section-times="sectionTimes"
      :seasonal="currentSeasonal"
      :sectionConfig="sectionConfig"
      @view-course-detail="viewCourseDetail"
    />

    <!-- 本周课程汇总 -->
    <view class="card course-summary">
      <view class="card-title">本周课程汇总</view>
      <view class="summary-list">
        <view
          v-for="course in summaryData"
          :key="course.name"
          class="list-item"
          @click="viewCourseDetail(course.name)"
        >
          <view class="course-icon">
            <view
              :class="[
                `i-${getCourseIcon(course.name).collection}-${getCourseIcon(course.name).icon}`,
              ]"
              style="font-size: 40rpx; color: #3a8eff"
            ></view>
          </view>
          <view class="course-content">
            <view class="course-name">{{ course.name }}</view>
            <view class="course-info">
              {{ course.className }} | {{ course.location }} | {{ course.totalHours }}学时/周
            </view>
          </view>
          <view class="course-days">
            <text class="days-text">{{ getCourseDays(course.courses) }}</text>
          </view>
        </view>
      </view>
      <view v-if="!summaryData.length" class="empty-tip">本周暂无课程安排</view>
    </view>

    <!-- 课程详情弹窗 -->
    <wd-popup v-model="showCourseDetailPopup" position="bottom" round>
      <view class="course-detail-popup mb-5">
        <view class="popup-header">
          <text class="popup-title">请选择进行授课确认的课程</text>
          <view
            class="i-carbon-close"
            style="font-size: 32rpx"
            @click="showCourseDetailPopup = false"
          ></view>
        </view>
        <scroll-view class="popup-content" scroll-y>
          <view v-if="originalCourseData.length === 0" class="empty-tip">没有找到相关课程数据</view>
          <view v-else class="course-items">
            <view
              v-for="(item, index) in originalCourseData"
              :key="index"
              class="course-detail-item"
              @tap="handleCourseSelect(item)"
            >
              <view class="detail-header">
                <text class="detail-title">{{ selectedCourseName + '-' + item.className }}</text>
              </view>
              <view class="detail-info">
                <view class="info-row">
                  <view class="i-carbon-time" style="font-size: 32rpx; color: #666666"></view>
                  <text>
                    星期{{ ['日', '一', '二', '三', '四', '五', '六'][item.DayIndex % 7] }}，第{{
                      item.sectionsShow
                    }}节
                  </text>
                </view>
                <view class="info-row">
                  <view class="i-carbon-calendar" style="font-size: 32rpx; color: #666666"></view>
                  <text>{{ item.date }}</text>
                </view>
                <view class="info-row">
                  <view class="i-carbon-user" style="font-size: 32rpx; color: #666666"></view>
                  <text>{{ item.teacherName }}</text>
                </view>
                <view class="info-row">
                  <view class="i-carbon-location" style="font-size: 32rpx; color: #666666"></view>
                  <text>{{ item.spaceName || '未安排教室' }}</text>
                </view>
                <view class="info-row">
                  <view class="i-carbon-group" style="font-size: 32rpx; color: #666666"></view>
                  <text>学生人数：{{ item.studentCount }}人</text>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </wd-popup>
  </view>
</template>

<style scoped lang="scss">
.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 24rpx 24rpx 32rpx;
  overflow-x: hidden;
  background-color: #f5f7fa;
}

.card {
  padding: 24rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.03);
}

// 顶部操作栏
.top-action-bar {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 4rpx 0;
  margin-bottom: 8rpx;

  [class^='i-'] {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
}

// 学期选择
.semester-select {
  display: flex;
  align-items: center;
  max-width: 65%;
  padding: 6rpx 12rpx;
  margin-right: 12rpx;
  background: #f7f8fa;
  border-radius: 10rpx;
  transition: all 0.3s;

  &:active {
    background: #f0f2f5;
  }

  .semester-label {
    margin-right: 4rpx;
    font-size: 22rpx;
    color: #666;
  }

  .semester-text {
    font-size: 24rpx;
    font-weight: 500;
    color: #333;
  }

  .wd-icon {
    margin-left: 4rpx;
  }
}

// 视图切换按钮
.view-toggle {
  display: flex;
  align-items: center;
  width: 160rpx;
  height: 42rpx;
  margin-left: 12rpx;
  overflow: hidden;
  background-color: #f0f2f5;
  border-radius: 21rpx;
}

.view-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 42rpx;
  padding: 0 6rpx;
  transition: all 0.3s;

  &.active {
    color: #3a8eff;
    background-color: #ffffff;
    border-radius: 21rpx;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
    transform: translateY(-1rpx);
  }

  .toggle-text {
    margin-left: 2rpx;
    font-size: 20rpx;
    color: #666;
  }
}

// 学期选择器样式
.semester-picker {
  max-height: 60vh;
  padding: 32rpx;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;

  .picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32rpx;
  }

  .picker-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  .picker-content {
    max-height: calc(60vh - 100rpx);
  }

  .picker-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx;
    margin-bottom: 16rpx;
    background: #f7f8fa;
    border-radius: 12rpx;
    transition: all 0.3s;

    &:last-child {
      margin-bottom: 0;
    }

    &.active {
      color: #3a8eff;
      background: rgba(58, 142, 255, 0.1);
    }

    .item-info {
      display: flex;
      flex: 1;
      flex-direction: column;
    }

    .item-label {
      margin-bottom: 4rpx;
      font-size: 28rpx;
    }

    .item-seasonal {
      font-size: 24rpx;
      color: #666;
    }

    &.active .item-seasonal {
      color: #3a8eff;
    }
  }

  .picker-header [class^='i-'] {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32rpx;
    height: 32rpx;
  }

  .picker-item [class^='i-'] {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32rpx;
    height: 32rpx;
  }
}

// 周次选择器
.week-view-container {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 12rpx;
  overflow-x: hidden;
}

.week-scroller {
  flex: 1;
  width: 100%;
  overflow: hidden;
}

.week-selector {
  box-sizing: border-box;
  width: 100%;
  padding: 12rpx;
  white-space: nowrap;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.week-items-container {
  display: inline-flex;
  width: max-content;
  padding: 6rpx 0;
}

.week-item {
  display: inline-flex;
  flex-direction: column;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 150rpx;
  height: 100rpx;
  margin-right: 12rpx;
  background: #f7f8fa;
  border-radius: 10rpx;
  transition: all 0.3s;

  &:last-child {
    margin-right: 0;
  }

  &.active {
    color: #3a8eff;
    background: rgba(58, 142, 255, 0.1);
    box-shadow: 0 4rpx 8rpx rgba(41, 121, 255, 0.1);
    transform: translateY(-2rpx);
  }

  .week-number {
    margin-bottom: 4rpx;
    font-size: 26rpx;
    font-weight: 600;
  }

  .week-date {
    padding: 0 6rpx;
    font-size: 22rpx;
    color: #666666;
  }

  &.active .week-date {
    color: #3a8eff;
  }
}

// 本周课程汇总样式
.course-summary {
  margin-top: 24rpx;
  .card-title {
    margin-bottom: 24rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  .summary-list {
    .list-item {
      display: flex;
      align-items: center;
      padding: 24rpx;
      margin-bottom: 16rpx;
      background: #f7f8fa;
      border-radius: 12rpx;
      transition: all 0.3s;

      &:last-child {
        margin-bottom: 0;
      }

      &:active {
        background: #f0f2f5;
      }

      .course-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80rpx;
        height: 80rpx;
        margin-right: 24rpx;
        background: rgba(58, 142, 255, 0.1);
        border-radius: 12rpx;

        [class^='i-'] {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40rpx;
          height: 40rpx;
        }
      }

      .course-content {
        flex: 1;
        margin-right: 24rpx;

        .course-name {
          margin-bottom: 8rpx;
          font-size: 28rpx;
          font-weight: 500;
          color: #333;
        }

        .course-info {
          font-size: 24rpx;
          color: #666;
        }
      }

      .course-days {
        min-width: 120rpx;
        max-width: 180rpx;

        .days-text {
          display: -webkit-box;
          overflow: hidden;
          font-size: 24rpx;
          color: #3a8eff;
          text-align: center;
          text-overflow: ellipsis;
          word-break: break-all;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
        }
      }
    }
  }

  .empty-tip {
    padding: 32rpx 0;
    font-size: 28rpx;
    color: #999;
    text-align: center;
  }
}

// 课程详情弹窗样式
.course-detail-popup {
  box-sizing: border-box;
  max-height: 70vh;
  padding: 32rpx;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;
  }

  .popup-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  .popup-content {
    max-height: calc(70vh - 100rpx);
  }

  .course-items {
    .course-detail-item {
      padding: 24rpx;
      margin-bottom: 24rpx;
      background: #f7f8fa;
      border-radius: 12rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .detail-header {
        padding-bottom: 16rpx;
        margin-bottom: 16rpx;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      }

      .detail-title {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
      }

      .detail-info {
        margin-bottom: 16rpx;
      }

      .info-row {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;
        font-size: 26rpx;
        color: #666;

        &:last-child {
          margin-bottom: 0;
        }

        [class^='i-'] {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32rpx;
          height: 32rpx;
          margin-right: 12rpx;
        }
      }

      .action-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 12rpx;
        padding-top: 16rpx;
        border-top: 1px solid rgba(0, 0, 0, 0.05);

        .action-btn {
          display: flex;
          align-items: center;
          padding: 8rpx 16rpx;
          font-size: 24rpx;
          color: #3a8eff;
          background: rgba(58, 142, 255, 0.1);
          border-radius: 8rpx;

          [class^='i-'] {
            margin-right: 8rpx;
          }
        }
      }
    }
  }

  .empty-tip {
    padding: 32rpx 0;
    font-size: 28rpx;
    color: #999;
    text-align: center;
  }
}
</style>
