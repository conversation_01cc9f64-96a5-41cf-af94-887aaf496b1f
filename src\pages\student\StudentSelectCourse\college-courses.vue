<route lang="json5">
{
  style: {
    navigationBarTitleText: '院级选课',
  },
}
</route>
<script setup lang="ts">
import { computed, onMounted, ref, onBeforeUnmount } from 'vue'
import { useUserStore } from '@/store/user'
import {
  getAvailableCourseList,
  getSelectCourseSetting,
  selectCourse as selectCourseApi,
  getSelectCourseScores,
} from '@/service/selectCourse'
import { getDictLabel, loadDictData } from '@/utils/dict'
import { useMessage } from 'wot-design-uni'
import Pagination from '@/components/Pagination/index.vue'
import type { DictData } from '@/types/system'
import type {
  AvailableCourseItem,
  AvailableCourseQuery,
  SelectCourseParams,
  SelectCourseSettingResponse,
  SelectCourseScoreQuery,
  StudentCourseItem,
} from '@/types/selectCourse'
import * as echarts from 'echarts'

// 初始化消息框组件
const message = useMessage()

// 用户信息
const userStore = useUserStore()
const userInfo = computed(() => ({
  name: userStore.userInfo.realname,
  major: userStore.userInfo.department,
  grade: userStore.userInfo.className,
}))

// 课程总数
const total = ref(0)

// 课程分类字典
const courseCategoryDict = ref<DictData[]>([])
// 授课方式字典
const teachingMethodDict = ref<DictData[]>([])
// 成绩信息字典
const scoreInfoDict = ref<DictData[]>([])
// 当前选中的课程分类
const selectedCourseCategory = ref('')

// 搜索相关
const searchType = ref('courseName') // 默认搜索课程名称
const searchValue = ref('') // 搜索关键词
const searchOptions = [
  { value: 'courseName', label: '课程名称' },
  { value: 'leaderTeacherName', label: '教师姓名' },
]
// 处理搜索类型变更
const handleSearchTypeChange = (value: string) => {
  searchType.value = value
}
// 处理搜索
const handleSearch = () => {
  // 重置页码
  queryParams.value.page = 1
  // 清空之前的搜索条件
  queryParams.value.courseName = ''
  queryParams.value.leaderTeacherName = ''

  // 设置当前搜索条件
  if (searchType.value === 'courseName') {
    queryParams.value.courseName = searchValue.value
  } else if (searchType.value === 'leaderTeacherName') {
    queryParams.value.leaderTeacherName = searchValue.value
  }

  // 重新获取数据
  fetchAvailableCourses()
}
// 清空搜索
const clearSearch = () => {
  searchValue.value = ''
  queryParams.value.courseName = ''
  queryParams.value.leaderTeacherName = ''
  fetchAvailableCourses()
}

// 图表相关
const chartRef = ref()
const chartData = ref<Array<{ value: number; name: string; color: string }>>([])

// 添加图表弹窗相关变量
const showChartDetailDialog = ref(false)
const showChartTip = ref(false)
const sortedChartData = computed(() => {
  // 按照数量从大到小排序
  return [...chartData.value].sort((a, b) => b.value - a.value)
})

// 计算已通过的公选课门数
const passedCourseCount = computed(() => {
  return chartData.value.reduce((total, item) => total + item.value, 0)
})

// 获取授课方式颜色
const getTeachingMethodColor = (methodCode: string) => {
  if (!methodCode) return 'text-gray-600'

  // 根据不同的授课方式代码返回不同的颜色类
  switch (methodCode) {
    case '1': // 假设1代表线上授课
      return 'text-purple-600 font-medium'
    case '2': // 假设2代表线下授课
      return 'text-green-600 font-medium'
    case '3': // 假设3代表混合授课
      return 'text-orange-600 font-medium'
    default:
      return 'text-blue-600 font-medium'
  }
}

// 加载状态
const loading = ref(false)

// 显示可选课程的开关
const showAvailableOnly = ref(false)
// 当前选中的标签
const activeTab = ref('single')

// 选课摘要信息
const courseSummary = ref({
  totalAllowed: 0, // 可选门数
  totalSelected: 0, // 已选门数
  remainingCount: 0, // 剩余可选门数
  startTime: '', // 选课开始时间
  endTime: '', // 选课截止时间
  useCampusTime: false, // 是否使用校区选课时间
})

// 通知内容
const notice = ref({
  title: '选课通知',
  content: '',
  time: '',
  xnxqszxx: null as any,
})

// 已选课程ID集合，用于判断课程是否已选
const selectedCourseIds = ref<Set<number>>(new Set())

// 查询参数
const queryParams = ref<AvailableCourseQuery>({
  optype: 'list',
  page: 1,
  pageSize: 1000,
  semesters: '', // 初始为空，将通过接口获取真实的学年学期信息
  courseCategory: '',
  courseCategoryName: '',
  courseName: '',
  leaderTeacherName: '',
  className: '',
  courseTotalHours: '',
  weekHours: '',
  creditHour: '',
  startWeek: '',
  teachingInfo: '',
  siteName: '',
  limitCount: '',
  selectedCount: '',
  campusName: '',
  check: false, // 添加 check 参数，默认为 false，表示查看全部
})

// 可选课程列表
const availableCourses = ref<AvailableCourseItem[]>([])

// 选课状态标签
const enrollmentStatusLabel = ref<string>('')

// 倒计时相关数据
const countdownTime = ref({
  days: 0,
  hours: 0,
  minutes: 0,
  seconds: 0,
})
const countdownTimer = ref<number | null>(null)
const remainingSeconds = ref(0)
// 添加倒计时类型：'start' 表示距离开始，'end' 表示距离结束
const countdownType = ref<'start' | 'end'>('end')

// 计算倒计时时间
const calculateCountdown = () => {
  if (remainingSeconds.value <= 0) {
    // 倒计时结束，清除定时器
    if (countdownTimer.value) {
      clearInterval(countdownTimer.value)
      countdownTimer.value = null

      // 如果是开始倒计时结束，需要重新获取数据以获取结束时间倒计时
      if (countdownType.value === 'start') {
        // 更新选课状态标签为"我要选课"，表示选课已开始
        enrollmentStatusLabel.value = '我要选课'

        // 如果在测试模式下，启动结束倒计时
        if (testMode.value) {
          startCountdown(testSettings.value.jssj, 'end')
        } else {
          // 重新获取数据
          fetchAvailableCourses()
        }

        // 显示选课开始提示
        uni.showToast({
          title: '选课已开始',
          icon: 'success',
          duration: 2000,
        })
      } else {
        // 结束倒计时结束，显示选课已结束
        enrollmentStatusLabel.value = '选课已结束'
        // 显示选课结束提示
        uni.showToast({
          title: '选课已结束',
          icon: 'none',
          duration: 2000,
        })

        // 如果不在测试模式下，刷新数据
        if (!testMode.value) {
          fetchAvailableCourses()
        }
      }
    }
    return
  }

  // 计算天、时、分、秒
  const days = Math.floor(remainingSeconds.value / (24 * 60 * 60))
  const hours = Math.floor((remainingSeconds.value % (24 * 60 * 60)) / (60 * 60))
  const minutes = Math.floor((remainingSeconds.value % (60 * 60)) / 60)
  const seconds = Math.floor(remainingSeconds.value % 60)

  // 更新倒计时数据
  countdownTime.value = {
    days,
    hours,
    minutes,
    seconds,
  }

  // 减少剩余秒数
  remainingSeconds.value--
}

// 跳转到退选页面
const navigateToDropCourses = () => {
  uni.navigateTo({
    url: '/pages/student/StudentSelectCourse/drop-courses',
    success: () => {
      console.log('成功跳转到退选页面')
    },
    fail: (err) => {
      console.error('跳转到退选页面失败:', err)
      uni.showToast({
        title: '跳转失败，请稍后再试',
        icon: 'none',
      })
    },
  })
}

// 启动倒计时
const startCountdown = (seconds: number, type: 'start' | 'end' = 'end') => {
  // 清除可能存在的定时器
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }

  // 设置倒计时类型
  countdownType.value = type

  // 设置剩余秒数
  remainingSeconds.value = seconds

  // 立即计算一次
  calculateCountdown()

  // 设置定时器，每秒更新一次
  countdownTimer.value = setInterval(calculateCountdown, 1000)

  // 更新测试设置中的倒计时秒数
  if (testMode.value) {
    if (type === 'start') {
      testSettings.value.kssj = seconds
    } else {
      testSettings.value.jssj = seconds
    }
  }

  console.log(`启动${type === 'start' ? '开始' : '结束'}倒计时:`, seconds)
}

// 切换仅查看可选课程
const toggleAvailableOnly = () => {
  // 直接将状态设置为相反值
  showAvailableOnly.value = !showAvailableOnly.value

  // 设置 check 参数
  queryParams.value.check = showAvailableOnly.value ? 1 : false

  // 重新获取数据
  fetchAvailableCourses()

  // 添加提示信息，让用户知道筛选状态已改变
  uni.showToast({
    title: showAvailableOnly.value ? '已筛选出可选课程' : '显示全部课程',
    icon: 'none',
    duration: 1500,
  })

  // 添加调试信息
  console.log('切换仅看可选状态:', showAvailableOnly.value)
  console.log('check参数:', queryParams.value.check)
}

// 更新已选课程ID集合
const updateSelectedCourseIds = (settingRes: SelectCourseSettingResponse) => {
  // 清空现有的已选课程ID集合
  selectedCourseIds.value.clear()

  // 从xsxkxx字段获取已选课程ID
  if (settingRes.xsxkxx && Array.isArray(settingRes.xsxkxx) && settingRes.xsxkxx.length > 0) {
    settingRes.xsxkxx.forEach((item: any) => {
      selectedCourseIds.value.add(item)
    })
  }

  console.log('已选课程IDs:', Array.from(selectedCourseIds.value))
}

// 初始化图表
const initChart = async () => {
  if (!chartRef.value || chartData.value.length === 0) return

  setTimeout(async () => {
    const myChart = await chartRef.value.init(echarts)

    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
        confine: true,
      },
      legend: {
        show: false,
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 4,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '12',
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: chartData.value.map((item) => ({
            value: item.value,
            name: item.name,
            itemStyle: {
              color: item.color,
            },
          })),
        },
      ],
    }

    myChart.setOption(option)
  }, 300)
}

// 关闭图表详情弹窗
const closeChartDetailDialog = () => {
  showChartDetailDialog.value = false
}

// 课程列表
const courses = computed(() => {
  if (!availableCourses.value || availableCourses.value.length === 0) {
    return []
  }

  // 全局选课未开放标志
  const isEnrollmentClosed =
    enrollmentStatusLabel.value === '选课未开放' || enrollmentStatusLabel.value === '选课未开始'

  return availableCourses.value.map((course) => {
    // 判断课程分类以设置背景色和文字颜色
    let typeBgColor = 'bg-blue-100'
    let typeTextColor = 'text-blue-700'

    if (
      course.courseCategoryName.includes('人工智能') ||
      course.courseCategoryName.includes('智能教育')
    ) {
      typeBgColor = 'bg-red-100'
      typeTextColor = 'text-red-700'
    } else if (
      course.courseCategoryName.includes('艺术') ||
      course.courseCategoryName.includes('美育')
    ) {
      typeBgColor = 'bg-green-100'
      typeTextColor = 'text-green-700'
    } else if (course.courseCategoryName.includes('其他')) {
      typeBgColor = 'bg-yellow-100'
      typeTextColor = 'text-yellow-700'
    } else if (
      course.courseCategoryName.includes('人文') ||
      course.courseCategoryName.includes('社科')
    ) {
      typeBgColor = 'bg-red-100'
      typeTextColor = 'text-red-700'
    } else if (
      course.courseCategoryName.includes('自然') ||
      course.courseCategoryName.includes('科学')
    ) {
      typeBgColor = 'bg-yellow-100'
      typeTextColor = 'text-yellow-700'
    } else if (
      course.courseCategoryName.includes('计算机') ||
      course.courseCategoryName.includes('信息')
    ) {
      typeBgColor = 'bg-blue-100'
      typeTextColor = 'text-blue-700'
    }

    // 判断课程是否已选
    const isSelected = selectedCourseIds.value.has(course.selectCourseId)

    // 课程状态：
    // - closed: 选课未开放
    // - ended: 课程已满
    // - selected: 已选课程
    // - available: 可选课程
    let status = 'available'

    // 优先检查选课是否全局关闭
    if (isEnrollmentClosed) {
      status = 'closed'
    }
    // 然后检查是否已选
    else if (isSelected) {
      status = 'selected'
    }
    // 最后检查是否已满
    else if (course.selectedCount >= course.maxCount && course.maxCount) {
      status = 'ended'
    }

    return {
      startWeek: course.startWeek,
      className: course.className,
      id: course.id,
      selectCourseId: course.selectCourseId,
      type: course.courseCategoryName,
      typeBgColor,
      typeTextColor,
      name: course.courseName,
      status,
      isSelected,
      campus: course.campusName || '全校',
      credit: course.creditHour,
      teacher: course.leaderTeacherName,
      selected: course.selectedCount,
      limit: course.maxCount || 0,
      teachingMode: course.type || 'MOOC',
      time: course.teachingInfo || '',
      totalHours: course.courseTotalHours || '0', // 添加总学时
      teachingMethodCode: course.teachingMethod, // 使用teachingMethod属性作为授课方式代码
      teachingMethod: course.teachingMethod, // 添加teachingMethod属性以保持一致性
      courseCategory: course.courseCategory, // 添加课程类别编码
      // 添加学年学期信息
      studyYear: course.studyYear || '', // 添加学年信息
      studyTerm: course.studyTerm || 0, // 添加学期信息
    }
  })
})

// 过滤后的课程列表
const filteredCourses = computed(() => {
  console.log('计算filteredCourses，showAvailableOnly:', showAvailableOnly.value)

  // 直接返回 API 返回的课程列表，不再在前端进行筛选
  return courses.value
})

// 获取可选课程数据
const fetchAvailableCourses = async () => {
  loading.value = true
  try {
    // 获取选课设置信息
    const settingRes = await getSelectCourseSetting()

    // 如果不在测试模式下，才更新选课状态标签
    if (!testMode.value) {
      // 获取选课状态标签
      enrollmentStatusLabel.value = settingRes.label || ''
      console.log('选课状态标签:', enrollmentStatusLabel.value)
    }

    // 更新已选课程ID集合
    updateSelectedCourseIds(settingRes)

    // 获取图表数据
    if (settingRes.chartData && Array.isArray(settingRes.chartData)) {
      chartData.value = settingRes.chartData
      // 在下一个渲染周期初始化图表
      setTimeout(() => {
        initChart()
      }, 100)
    }

    // 更新选课摘要信息
    if (settingRes.xnxqszxx) {
      const { xn, xq, rxkxkkssj, rxkxkjssj, rxkxsxxsl } = settingRes.xnxqszxx
      const rxkxsyxs = settingRes.rxkxsyxs || 0

      // 更新查询参数中的学期信息，使用接口返回的学年和学期
      queryParams.value.semesters = `${xn}|${xq}`
      console.log('更新学期参数:', queryParams.value.semesters)

      // 更新通知内容（保留原通知内容，但不再使用）
      notice.value.content = `以下为${xn}学年第${xq}学期你可选择的院级选课列表[选课时间: ${rxkxkkssj} 至 ${rxkxkjssj}]。你共可选 ${rxkxsxxsl || 0} 门，你已选 ${rxkxsyxs || 0} 门。`
      notice.value.time = new Date().toLocaleString()
      // 更新notice中的xnxqszxx数据
      notice.value.xnxqszxx = settingRes.xnxqszxx

      // 更新选课摘要信息
      courseSummary.value.totalAllowed = rxkxsxxsl || 0
      courseSummary.value.totalSelected = rxkxsyxs
      courseSummary.value.remainingCount = Math.max(0, (rxkxsxxsl || 0) - rxkxsyxs)

      // 如果不在测试模式下，才更新开始和结束时间
      if (!testMode.value) {
        // 设置开始和结束时间
        courseSummary.value.startTime = rxkxkkssj || ''
        courseSummary.value.endTime = rxkxkjssj || ''
        courseSummary.value.useCampusTime = false
      }
    }

    // 处理学生开放时间信息 - 只从xskfsjxx获取数据
    if (
      !testMode.value && // 只在非测试模式下更新
      settingRes.xskfsjxx &&
      Array.isArray(settingRes.xskfsjxx) &&
      settingRes.xskfsjxx.length > 0
    ) {
      // 尝试获取第一个元素的信息
      const firstItem = settingRes.xskfsjxx[0] as any
      if (firstItem) {
        if (firstItem.kfkssj) {
          courseSummary.value.startTime = firstItem.kfkssj
          courseSummary.value.useCampusTime = true
        }
        if (firstItem.kfjssj) {
          courseSummary.value.endTime = firstItem.kfjssj
          courseSummary.value.useCampusTime = true
        }
      }
    } else if (
      !testMode.value && // 只在非测试模式下更新
      settingRes.xskfsjxx &&
      !Array.isArray(settingRes.xskfsjxx)
    ) {
      // 如果是对象，直接使用
      const item = settingRes.xskfsjxx as any
      if (item) {
        if (item.kfkssj) {
          courseSummary.value.startTime = item.kfkssj
          courseSummary.value.useCampusTime = true
        }
        if (item.kfjssj) {
          courseSummary.value.endTime = item.kfjssj
          courseSummary.value.useCampusTime = true
        }
      }
    }

    // 如果不在测试模式下，才获取并启动倒计时
    if (!testMode.value) {
      // 判断是否有开始时间倒计时
      if (settingRes.kssj !== undefined && settingRes.kssj > 0) {
        // 有开始时间倒计时，启动开始倒计时
        startCountdown(settingRes.kssj, 'start')
        console.log('启动开始倒计时:', settingRes.kssj)
      }
      // 判断是否有结束时间倒计时
      else if (settingRes.jssj !== undefined && settingRes.jssj > 0) {
        // 启动结束倒计时
        startCountdown(settingRes.jssj, 'end')
        console.log('启动结束倒计时:', settingRes.jssj)
      }
    }

    // 更新课程分类参数
    queryParams.value.courseCategory = selectedCourseCategory.value

    // 确保学期参数不为空再获取课程列表
    if (!queryParams.value.semesters) {
      console.warn('学期参数为空，无法获取课程列表')
      loading.value = false
      return
    }

    // 获取可选课程列表
    const res = await getAvailableCourseList(queryParams.value)
    availableCourses.value = res.items || []
    total.value = res.total || 0
  } finally {
    loading.value = false
  }
}

// 添加自定义选课确认对话框的状态
const showCourseConfirmDialog = ref(false)
const currentSelectCourse = ref<AvailableCourseItem | null>(null)
// 添加确认按钮加载状态
const confirmLoading = ref(false)

// 选课
const handleSelectCourse = (courseId: number, courseName: string) => {
  // 找到对应的课程对象
  const course = availableCourses.value.find((item) => item.id === courseId)

  if (!course) {
    console.error('未找到课程信息')
    return
  }

  // 设置当前选中的课程并显示确认对话框
  currentSelectCourse.value = course
  showCourseConfirmDialog.value = true
}

// 查看课程详情（复用选课确认对话框）
const handleViewCourse = (courseId: number, courseName: string) => {
  // 找到对应的课程对象
  const course = availableCourses.value.find((item) => item.id === courseId)

  if (!course) {
    console.error('未找到课程信息')
    return
  }

  // 设置当前选中的课程并显示确认对话框
  currentSelectCourse.value = course
  showCourseConfirmDialog.value = true
}

// 确认选课
const confirmSelectCourse = async () => {
  if (!currentSelectCourse.value || confirmLoading.value) return

  confirmLoading.value = true
  try {
    const params: SelectCourseParams = {
      xkxxid: currentSelectCourse.value.id.toString(),
      optype: 'select',
      xktjid: 0,
    }
    await selectCourseApi(params)

    // 选课成功后刷新列表
    uni.showToast({
      title: '选课成功',
      icon: 'success',
    })
    fetchAvailableCourses()

    // 关闭对话框
    showCourseConfirmDialog.value = false
  } catch (error) {
    console.error('选课失败:', error)
    uni.showToast({
      title: error.msg || '选课失败',
      icon: 'none',
    })
  } finally {
    confirmLoading.value = false
  }
}

// 取消选课
const cancelSelectCourse = () => {
  showCourseConfirmDialog.value = false
  currentSelectCourse.value = null
  console.log('用户取消选课')
}

// 获取授课方式文本
const getTeachingMethodText = (methodCode: string) => {
  if (!methodCode || !teachingMethodDict.value) return '暂无'

  const method = teachingMethodDict.value.find((item) => item.dictValue === methodCode)
  return method ? method.dictLabel : '暂无'
}

const changeTab = (tab: string) => {
  activeTab.value = tab
}

// 切换课程分类
const changeCourseCategory = (category: string) => {
  selectedCourseCategory.value = category
  fetchAvailableCourses()
}

// 格式化时间
const formatDateTime = (dateTimeStr: string) => {
  if (!dateTimeStr) return ''

  // 简单格式化，保留日期和时间，去掉秒
  try {
    const dateTime = new Date(dateTimeStr)
    return `${dateTime.getFullYear()}年${dateTime.getMonth() + 1}月${dateTime.getDate()}日 ${dateTime.getHours()}:${String(dateTime.getMinutes()).padStart(2, '0')}`
  } catch (e) {
    return dateTimeStr
  }
}

// 格式化日期时间为input datetime-local可用的格式
const formatDateTimeForInput = (date: Date) => {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  return `${year}-${month}-${day}T${hours}:${minutes}`
}

// 处理页面变化
const handlePageChange = (newPage: number) => {
  queryParams.value.page = newPage
  fetchAvailableCourses()
}

// 页面加载时获取数据
onMounted(() => {
  // 加载课程分类字典和授课方式字典
  loadDicts()

  // 获取可选课程数据
  fetchAvailableCourses()
})

// 在组件销毁前清除定时器
onBeforeUnmount(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
})

// 测试模式
const testMode = ref(false)
const testSettings = ref({
  label: '选课未开放', // 可选值：'选课未开放', '我要选课', '选课已结束'
  startTime: '',
  endTime: '',
  kssj: 60, // 开始倒计时秒数
  jssj: 120, // 结束倒计时秒数
  countdownType: 'start', // 'start' 或 'end'
})
// 切换测试模式
const toggleTestMode = () => {
  testMode.value = !testMode.value
  if (testMode.value) {
    // 初始化测试设置 - 使用2025年的固定日期
    testSettings.value.label = '选课未开放'
    testSettings.value.startTime = '2025-05-28T11:16'
    testSettings.value.endTime = '2025-05-29T11:16'
    testSettings.value.kssj = 60 // 默认60秒开始倒计时
    testSettings.value.jssj = 120 // 默认120秒结束倒计时
    testSettings.value.countdownType = 'start'

    // 自动应用测试设置
    applyTestSettings()
  }
}

// 应用测试设置
const applyTestSettings = () => {
  // 设置选课状态标签
  enrollmentStatusLabel.value = testSettings.value.label

  // 设置选课时间
  courseSummary.value.startTime = testSettings.value.startTime.replace('T', ' ')
  courseSummary.value.endTime = testSettings.value.endTime.replace('T', ' ')

  // 清除现有倒计时
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }

  // 根据选择的倒计时类型启动倒计时
  if (testSettings.value.countdownType === 'start') {
    startCountdown(testSettings.value.kssj, 'start')
  } else {
    startCountdown(testSettings.value.jssj, 'end')
  }

  // 显示提示
  uni.showToast({
    title: '测试设置已应用',
    icon: 'success',
    duration: 2000,
  })
}

// 添加选课成绩相关变量
const courseScores = ref<StudentCourseItem[]>([])
const courseScoresTotal = ref(0)
const courseScoresLoading = ref(false)
const courseScoresQuery = ref<SelectCourseScoreQuery>({
  optype: 'selectScore',
  page: 1,
  pageSize: 10,
  sortBy: 'id',
  sortOrder: 'desc',
})

// 处理图表点击
const handleChartClick = () => {
  showChartDetailDialog.value = true
  showChartTip.value = false

  // 加载选课成绩数据
  fetchCourseScores()
}

// 获取选课成绩数据
const fetchCourseScores = async () => {
  courseScoresLoading.value = true
  try {
    const res = await getSelectCourseScores(courseScoresQuery.value)
    courseScores.value = res.items || []
    courseScoresTotal.value = res.total || 0
  } catch (error) {
    console.error('获取选课成绩数据失败:', error)
    uni.showToast({
      title: '获取成绩数据失败',
      icon: 'none',
    })
  } finally {
    courseScoresLoading.value = false
  }
}

// 处理成绩列表分页变化
const handleScorePageChange = (newPage: number) => {
  courseScoresQuery.value.page = newPage
  fetchCourseScores()
}

// 格式化成绩显示
const formatScore = (score: number | string | undefined): string => {
  if (score === undefined || score === null || score === '') {
    return '无成绩'
  }

  // 转换为数字进行比较
  const scoreNum = Number(score)

  // 如果是负数，从字典中查找对应的文本
  if (scoreNum < 0) {
    const dictLabel = getDictLabel(scoreInfoDict.value, String(scoreNum))
    return dictLabel || `${score}`
  }

  // 正常成绩直接返回
  return `${score}`
}

// 获取成绩显示样式
const getScoreClass = (score: number | string | undefined): string => {
  if (score === undefined || score === null || score === '') {
    return 'bg-gray-200 text-gray-600'
  }

  const scoreNum = Number(score)

  if (scoreNum < 0) {
    return 'bg-orange-100 text-orange-600'
  }

  return scoreNum >= 60 ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
}

// 加载字典数据
const loadDicts = async () => {
  try {
    const dicts = await loadDictData(['DM_XKKCFLDM', 'DM_XKSKFSDM', 'DM_CJXXDM'])
    courseCategoryDict.value = dicts.DM_XKKCFLDM || []
    teachingMethodDict.value = dicts.DM_XKSKFSDM || []
    scoreInfoDict.value = dicts.DM_CJXXDM || []
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}
</script>

<template>
  <!-- 添加MessageBox组件 -->

  <!-- 图表详情弹窗 -->
  <view v-if="showChartDetailDialog" class="course-confirm-dialog">
    <view class="dialog-mask" @click="closeChartDetailDialog"></view>
    <view class="dialog-container" style="max-height: 80vh">
      <view class="dialog-header">
        <text class="dialog-title">已通过公选课详情</text>
        <wd-icon name="close" size="20px" class="close-icon" @click="closeChartDetailDialog" />
      </view>
      <view class="dialog-content">
        <view class="chart-detail-summary mb-3">
          <text class="text-lg font-medium">共通过</text>
          <text class="text-xl font-bold text-blue-600">{{ passedCourseCount }}</text>
          <text class="text-lg font-medium">门公选课</text>
        </view>

        <!-- 图表数据统计 -->
        <view class="chart-detail-list mb-4">
          <view v-for="(item, index) in sortedChartData" :key="index" class="chart-detail-item">
            <view class="chart-detail-color" :style="{ backgroundColor: item.color }"></view>
            <view class="chart-detail-name">{{ item.name }}</view>
            <view class="chart-detail-value">{{ item.value }}门</view>
          </view>
        </view>

        <!-- 分割线 -->
        <view class="border-t border-gray-200 my-4"></view>

        <!-- 课程成绩列表标题 -->
        <view class="flex justify-between items-center mb-3">
          <text class="font-medium text-base">课程成绩详情</text>
        </view>

        <!-- 加载状态 -->
        <view v-if="courseScoresLoading" class="flex justify-center items-center py-5">
          <wd-loading color="#007aff" />
        </view>

        <!-- 成绩列表 -->
        <view v-else-if="courseScores.length > 0" class="course-scores-list">
          <view
            v-for="course in courseScores"
            :key="course.id"
            class="course-score-item bg-white mb-3 rounded-lg overflow-hidden border border-gray-100"
          >
            <view class="p-3">
              <view class="flex justify-between items-start mb-2">
                <view class="course-info-container">
                  <view class="course-heading">
                    <text
                      :class="[
                        'course-type-tag',
                        'px-2',
                        'py-1',
                        'rounded',
                        'text-xs',
                        'font-medium',
                        'whitespace-nowrap',
                        course.courseCategory === '7'
                          ? 'bg-red-100 text-red-700'
                          : course.courseCategory === '2'
                            ? 'bg-green-100 text-green-700'
                            : course.courseCategory === '3'
                              ? 'bg-yellow-100 text-yellow-700'
                              : 'bg-blue-100 text-blue-700',
                      ]"
                    >
                      【{{ course.courseCategoryName }}】
                    </text>
                    <text class="font-medium text-base course-name ml-1">
                      {{ course.courseName }}
                    </text>
                  </view>
                </view>
                <!-- 成绩显示 -->
                <view
                  class="ml-2 p-1 px-3 rounded-lg text-sm font-medium whitespace-nowrap flex-shrink-0"
                  :class="getScoreClass(course.cj)"
                >
                  {{ formatScore(course.cj) }}
                </view>
              </view>

              <!-- 班级信息 -->
              <view class="grid grid-cols-2 gap-2 text-sm mb-2">
                <view class="flex items-center">
                  <wd-icon name="usergroup" class="text-gray-400 mr-1" />
                  <text>{{ course.className }}</text>
                </view>
              </view>

              <!-- 校区和学分信息 -->
              <view class="grid grid-cols-2 gap-2 text-sm mb-2">
                <view class="flex items-center">
                  <wd-icon name="location" class="text-gray-400 mr-1" />
                  <text>{{ course.siteName || '全校' }}</text>
                </view>
                <view class="flex items-center">
                  <wd-icon name="books" class="text-gray-400 mr-1" />
                  <text>学分: {{ course.creditHour }}</text>
                </view>
              </view>

              <view class="grid grid-cols-2 gap-2 text-sm mb-2">
                <view class="flex items-center">
                  <wd-icon name="calendar" class="text-gray-400 mr-1" />
                  <text>学期: {{ course.studyYear }}（{{ course.studyTerm }}）</text>
                </view>
                <view class="flex items-center">
                  <wd-icon name="hourglass" class="text-gray-400 mr-1" />
                  <text>总学时: {{ course.courseTotalHours }}学时</text>
                </view>
              </view>

              <!-- 教师和选课人数信息 -->
              <view class="grid grid-cols-2 gap-2 text-sm mb-2">
                <view class="flex items-center">
                  <wd-icon name="user" class="text-gray-400 mr-1" />
                  <text>教师: {{ course.leaderTeacherName }}</text>
                </view>

                <view class="flex items-center">
                  <wd-icon name="time" class="text-gray-400 mr-1" />
                  <text>授课时间: {{ course.teachingInfo || '暂无' }}</text>
                </view>
              </view>

              <!-- 授课方式和授课时间 -->
              <!-- <view class="grid grid-cols-2 gap-2 text-sm">
                <view class="flex items-center">
                  <wd-icon name="laptop" class="text-gray-400 mr-1" />
                  <text>
                    授课方式:
                    <text :class="[getTeachingMethodColor(course.teachingMethod)]">
                      {{ getDictLabel(teachingMethodDict, course.teachingMethod) || course.type }}
                    </text>
                  </text>
                </view>
              </view>
 -->
              <!-- 授课场地信息 -->
              <view class="grid grid-cols-1 gap-2 text-sm mt-2 pt-2 border-t border-gray-100">
                <view class="flex items-center">
                  <wd-icon name="location" class="text-gray-400 mr-1" />
                  <text>起始周: {{ course.startWeek || '暂无' }}</text>
                </view>
                <view class="flex items-center">
                  <wd-icon name="location" class="text-gray-400 mr-1" />
                  <text>教学场地: {{ course.siteName || '暂无' }}</text>
                </view>
              </view>
            </view>
          </view>

          <!-- 分页组件 -->
          <view v-if="courseScoresTotal > courseScoresQuery.pageSize" class="px-4 pb-4">
            <Pagination
              :page="courseScoresQuery.page"
              :page-size="courseScoresQuery.pageSize"
              :total="courseScoresTotal"
              @update:page="handleScorePageChange"
            />
          </view>
        </view>

        <!-- 无数据状态 -->
        <view v-else class="p-10 text-center text-gray-500">
          <wd-icon name="empty" size="60" color="#d1d1d6" />
          <view class="mt-2">暂无成绩数据</view>
        </view>
      </view>
      <view class="dialog-footer">
        <view class="confirm-btn" @click="closeChartDetailDialog">关闭</view>
      </view>
    </view>
  </view>

  <!-- 自定义选课确认对话框 -->
  <view v-if="showCourseConfirmDialog && currentSelectCourse" class="course-confirm-dialog">
    <view class="dialog-mask" @click="cancelSelectCourse"></view>
    <view class="dialog-container">
      <view class="dialog-header">
        <text class="dialog-title">
          {{
            enrollmentStatusLabel === '选课未开放' || enrollmentStatusLabel === '选课未开始'
              ? '课程详情'
              : '确认选课'
          }}
        </text>
        <wd-icon name="close" size="20px" class="close-icon" @click="cancelSelectCourse" />
      </view>
      <view class="dialog-content">
        <!-- 选课信息 -->
        <view class="info-section">
          <view class="section-title">选课信息</view>
          <view class="course-main-info">
            <text class="teacher-name">{{ currentSelectCourse.leaderTeacherName }}</text>
            <text class="course-name">{{ currentSelectCourse.courseName }}</text>
            <text class="class-name">({{ currentSelectCourse.className }})</text>
          </view>
        </view>

        <!-- 课程信息 -->
        <view class="info-section">
          <view class="section-title">课程信息</view>
          <view class="info-table">
            <view class="table-row">
              <view class="table-cell label">类别:</view>
              <view class="table-cell value">
                <text class="category-tag">{{ currentSelectCourse.courseCategoryName }}</text>
              </view>
            </view>
            <view class="table-row">
              <view class="table-cell label">总学时:</view>
              <view class="table-cell value">{{ currentSelectCourse.courseTotalHours }}</view>
              <view class="table-cell label">周学时:</view>
              <view class="table-cell value">{{ currentSelectCourse.weekHours }}</view>
            </view>
            <view class="table-row">
              <view class="table-cell label">学分:</view>
              <view class="table-cell value">
                <text class="credit-value">{{ currentSelectCourse.creditHour }}</text>
              </view>
            </view>
            <view class="table-row">
              <view class="table-cell label">学年学期:</view>
              <view class="table-cell value">
                <text class="semester-tag">
                  {{ currentSelectCourse.studyYear }}学年第{{ currentSelectCourse.studyTerm }}学期
                </text>
              </view>
            </view>
          </view>
        </view>

        <!-- 授课时间 -->
        <view class="info-section">
          <view class="section-title">授课时间</view>
          <view class="section-content">
            {{ currentSelectCourse.startWeek || '暂无' }}
            {{ currentSelectCourse.teachingInfo || '暂无' }}
          </view>
        </view>

        <!-- 开放校区 -->
        <view class="info-section">
          <view class="section-title">开放校区</view>
          <view class="info-table">
            <view class="table-row">
              <view class="table-cell label">校区:</view>
              <view class="table-cell value">
                <text class="campus-tag">{{ currentSelectCourse.campusName || '暂无' }}</text>
              </view>
            </view>
            <view class="table-row">
              <view class="table-cell label">授课方式:</view>
              <view class="table-cell value">
                <text
                  :class="[
                    'teaching-method',
                    getTeachingMethodColor(currentSelectCourse.teachingMethod),
                  ]"
                >
                  {{
                    getDictLabel(teachingMethodDict, currentSelectCourse.teachingMethod) ||
                    currentSelectCourse.type
                  }}
                </text>
              </view>
            </view>
          </view>
        </view>

        <!-- 授课场地 -->
        <view class="info-section">
          <view class="section-title">授课场地</view>
          <view class="section-content">{{ currentSelectCourse.siteName || '暂无' }}</view>
        </view>

        <!-- 课程说明 -->
        <view class="info-section">
          <view class="section-title">课程说明</view>
          <view class="section-content course-remark">
            {{ currentSelectCourse.remark || '暂无课程说明' }}
          </view>
        </view>
      </view>
      <view class="dialog-footer">
        <view class="cancel-btn" @click="cancelSelectCourse">
          {{
            enrollmentStatusLabel === '选课未开放' || enrollmentStatusLabel === '选课未开始'
              ? '关闭'
              : '取消'
          }}
        </view>
        <view
          class="confirm-btn"
          :class="{ 'confirm-btn-loading': confirmLoading }"
          @click="confirmSelectCourse"
        >
          <wd-icon v-if="confirmLoading" name="refresh" size="16px" class="loading-icon" />
          <text>{{ confirmLoading ? '选课中...' : '确认选课' }}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 选课状态与筛选区域 -->
  <view class="bg-white p-3 sticky z-10 pb-1">
    <!-- 选课状态提示 -->
    <view
      v-if="enrollmentStatusLabel && enrollmentStatusLabel != '我要选课'"
      :class="[
        'px-3 py-1 rounded-lg flex items-center mb-2',
        enrollmentStatusLabel === '选课未开放' || enrollmentStatusLabel === '选课未开始'
          ? 'bg-orange-50 text-orange-700'
          : 'bg-green-50 text-green-700',
      ]"
    >
      <wd-icon
        :name="
          enrollmentStatusLabel === '选课未开放' || enrollmentStatusLabel === '选课未开始'
            ? 'info-circle'
            : 'check-circle'
        "
        class="mr-2"
        size="18px"
      />
      <view class="flex flex-col">
        <view class="flex items-center">
          <text class="font-medium text-sm">{{ enrollmentStatusLabel }}</text>
          <!-- 在选课未开放状态下显示开始时间 -->
          <view
            v-if="enrollmentStatusLabel === '选课未开放' || enrollmentStatusLabel === '选课未开始'"
            class="text-xs ml-2 flex items-center"
          >
            <wd-icon name="time" size="12px" class="mr-1" />
            <text>开始时间：{{ formatDateTime(courseSummary.startTime) }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 选课摘要信息 -->
    <view class="mb-2">
      <view class="flex justify-between items-center">
        <view class="text-blue-600 text-xs flex items-center mr-2">
          <wd-icon name="time" size="14px" class="mr-1" />
          截止时间：
          <text>{{ formatDateTime(courseSummary.endTime) }}</text>
        </view>

        <!-- 仅看可选开关 -->
        <view class="flex items-center">
          <view
            class="flex items-center bg-gray-100 rounded-full px-2 py-1"
            :class="[showAvailableOnly ? 'bg-blue-100' : '']"
            @click="toggleAvailableOnly"
          >
            <text
              class="text-xs mr-1"
              :class="[showAvailableOnly ? 'text-blue-600 font-medium' : 'text-gray-600']"
            >
              仅看可选
            </text>
            <view class="custom-switch-small" :class="{ 'switch-active': showAvailableOnly }">
              <view class="switch-thumb-small"></view>
            </view>
          </view>
        </view>
      </view>

      <!-- 选课统计卡片 -->
      <view class="grid grid-cols-3 gap-2 mt-2">
        <view class="stat-card-small bg-blue-50 border border-blue-100 border-solid">
          <text class="stat-label">可选门数</text>
          <text class="stat-value text-blue-600">{{ courseSummary.totalAllowed }}</text>
        </view>
        <view class="stat-card-small bg-green-50 border border-green-100 border-solid">
          <text class="stat-label">已选门数</text>
          <text class="stat-value text-green-600">{{ courseSummary.totalSelected }}</text>
        </view>
        <view class="stat-card-small bg-purple-50 border border-purple-100 border-solid">
          <text class="stat-label">剩余门数</text>
          <text class="stat-value text-purple-600">{{ courseSummary.remainingCount }}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 倒计时区域 -->
  <view v-if="countdownTimer || chartData.length > 0" class="flex flex-row gap-3 mx-3 mb-2 mt-1">
    <!-- 倒计时部分 -->
    <view
      v-if="countdownTimer"
      class="countdown-container px-3 pb-2 rounded-lg bg-blue-50 border border-blue-100 border-solid flex-1 flex flex-col justify-center"
    >
      <view class="flex items-center justify-center">
        <wd-icon name="hourglass" class="text-blue-600 mr-2" />
        <text class="text-blue-600 font-medium">
          {{ countdownType === 'start' ? '距离选课开始还有:' : '距离选课结束还有:' }}
        </text>
      </view>
      <view class="flex justify-center mt-2">
        <view class="countdown-box">
          <text class="countdown-number">{{ countdownTime.days }}</text>
          <text class="countdown-label">天</text>
        </view>
        <view class="countdown-box">
          <text class="countdown-number">{{ countdownTime.hours }}</text>
          <text class="countdown-label">小时</text>
        </view>
        <view class="countdown-box">
          <text class="countdown-number">{{ countdownTime.minutes }}</text>
          <text class="countdown-label">分钟</text>
        </view>
        <view class="countdown-box">
          <text class="countdown-number">{{ countdownTime.seconds }}</text>
          <text class="countdown-label">秒</text>
        </view>
      </view>
    </view>

    <!-- 课程分类饼图 -->
    <view
      v-if="chartData.length > 0"
      class="chart-container flex-1 relative"
      @click="handleChartClick"
      @touchstart="showChartTip = true"
      @touchend="showChartTip = false"
    >
      <view class="text-center text-sm text-blue-600 font-medium mb-2">
        已通过公选课{{ passedCourseCount }}门
      </view>
      <l-echart ref="chartRef" class="chart-content"></l-echart>
      <!-- 添加点击提示 -->
      <view v-if="showChartTip" class="chart-click-tip">
        <wd-icon name="view" size="12px" class="mr-1" />
        <text>点击查看详情</text>
      </view>
    </view>
  </view>

  <!-- 添加通知栏 -->
  <wd-notice-bar
    v-if="notice.xnxqszxx?.rxkxksm"
    scrollable
    prefix="warn-bold"
    :text="notice.xnxqszxx.rxkxksm"
    class="mx-3 mt-3"
  ></wd-notice-bar>

  <!-- 前往退选按钮 - 单独成行更加显眼 -->
  <view
    class="flex items-center justify-center bg-orange-100 rounded-lg py-1 mt-3 text-orange-600 mx-3"
    @click="navigateToDropCourses"
  >
    <wd-icon name="delete-thin" size="" class="mr-2" />
    <text class="font-small">前往退选课程</text>
  </view>
  <!-- 搜索框区域 -->
  <view class="bg-white py-2 px-3 mt-1">
    <view class="flex items-center">
      <!-- 搜索类型选择器 -->
      <view class="search-type-selector mr-2">
        <picker
          :range="searchOptions"
          range-key="label"
          :value="searchOptions.findIndex((item) => item.value === searchType)"
          @change="(e) => handleSearchTypeChange(searchOptions[e.detail.value].value)"
        >
          <view
            class="picker-view flex items-center justify-center text-sm py-2 px-3 bg-gray-100 rounded-lg"
          >
            <text>{{ searchOptions.find((item) => item.value === searchType)?.label }}</text>
            <wd-icon name="chevron-down" size="14px" class="ml-1" />
          </view>
        </picker>
      </view>

      <!-- 搜索输入框 -->
      <view class="search-input-container flex-1 flex items-center bg-gray-100 rounded-lg px-3">
        <input
          v-model="searchValue"
          class="search-input flex-1 py-2 text-sm"
          :placeholder="`请输入${searchType === 'courseName' ? '课程名称' : '教师姓名'}`"
          confirm-type="search"
          @confirm="handleSearch"
        />
        <wd-icon
          v-if="searchValue"
          name="close-circle"
          size="16px"
          class="text-gray-400 mr-1"
          @click="clearSearch"
        />
        <wd-icon name="search" size="16px" class="text-blue-600" @click="handleSearch" />
      </view>
    </view>
  </view>

  <!-- 课程分类选择器 -->
  <view v-if="courseCategoryDict.length > 0" class="bg-white py-2 px-3 mt-1">
    <view class="flex overflow-x-auto py-1 no-scrollbar">
      <view
        class="flex-none px-3 py-1 mr-2 rounded-full text-center transition-all duration-300 text-sm"
        :class="!selectedCourseCategory ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'"
        @click="changeCourseCategory('')"
      >
        全部
      </view>
      <view
        v-for="item in courseCategoryDict"
        :key="item.dictValue"
        class="flex-none px-3 py-1 mr-2 rounded-full text-center transition-all duration-300 text-sm"
        :class="
          selectedCourseCategory === item.dictValue
            ? 'bg-blue-500 text-white'
            : 'bg-gray-100 text-gray-600'
        "
        @click="changeCourseCategory(item.dictValue)"
      >
        {{ item.dictLabel }}
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view v-if="loading" class="flex justify-center items-center py-10">
    <wd-loading color="#007aff" />
  </view>

  <!-- 课程列表 -->
  <view v-else class="mt-2 bg-white">
    <view v-if="filteredCourses.length === 0" class="p-10 text-center text-gray-500">
      <wd-icon name="empty" size="60" color="#d1d1d6" />
      <view class="mt-2">
        {{ showAvailableOnly ? '没有可选课程' : '暂无符合条件的课程' }}
      </view>
    </view>
    <view
      v-for="course in filteredCourses"
      :key="course.id"
      class="p-4 border-b border-gray-200 last:border-b-0 border-b-solid"
    >
      <view class="flex justify-between items-start mb-2">
        <view class="course-info-container">
          <view class="course-heading">
            <text
              :class="[
                'course-type-tag',
                'px-2',
                'py-1',
                'rounded',
                'text-xs',
                'font-medium',
                'whitespace-nowrap',
                course.typeBgColor,
                course.typeTextColor,
              ]"
            >
              【{{ course.type }}】
            </text>
            <text class="font-medium text-lg course-name">{{ course.name }}</text>
          </view>
        </view>
        <!-- 课程已选状态 -->
        <view
          v-if="course.status === 'selected'"
          class="ml-2 p-2 px-4 bg-green-500 text-white rounded-lg text-sm font-medium whitespace-nowrap flex-shrink-0"
        >
          已选
        </view>
        <!-- 课程已满状态 -->
        <view
          v-else-if="course.status === 'ended'"
          class="ml-2 p-2 px-4 bg-gray-300 text-gray-600 rounded-lg text-sm font-medium whitespace-nowrap flex-shrink-0"
        >
          已满
        </view>
        <!-- 选课未开放状态 -->
        <view
          v-else-if="course.status === 'closed'"
          class="ml-2 p-2 px-4 bg-orange-300 text-orange-700 rounded-lg text-sm font-medium whitespace-nowrap flex-shrink-0 cursor-pointer"
          @click="handleViewCourse(course.id, course.name)"
        >
          查看
        </view>
        <!-- 可选状态 -->
        <view
          v-else
          class="ml-2 p-2 px-4 bg-blue-500 text-white rounded-lg text-sm font-medium whitespace-nowrap flex-shrink-0"
          @click="handleSelectCourse(course.id, course.name)"
        >
          立即选课
        </view>
      </view>

      <view class="grid grid-cols-2 gap-2 text-sm mb-2">
        <view class="flex items-center">
          <wd-icon name="usergroup" class="text-gray-400 mr-1" />
          <text>{{ course.className }}</text>
        </view>
        <view class="flex items-center">
          <wd-icon name="list" class="text-gray-400 mr-1" />
          <text>起始周: {{ course.startWeek }}</text>
        </view>
      </view>
      <view class="grid grid-cols-2 gap-2 text-sm mb-2">
        <view class="flex items-center">
          <wd-icon name="location" class="text-gray-400 mr-1" />
          <text>{{ course.campus }}</text>
        </view>
        <view class="flex items-center">
          <wd-icon name="books" class="text-gray-400 mr-1" />
          <text>学分: {{ course.credit }}</text>
        </view>
      </view>

      <view class="grid grid-cols-2 gap-2 text-sm mb-2">
        <view class="flex items-center">
          <wd-icon name="calendar" class="text-gray-400 mr-1" />
          <text>学期: {{ course.studyYear }}（{{ course.studyTerm }}）</text>
        </view>
        <view class="flex items-center">
          <wd-icon name="hourglass" class="text-gray-400 mr-1" />
          <text>总学时: {{ course.totalHours }}学时</text>
        </view>
      </view>

      <view class="grid grid-cols-2 gap-2 text-sm mb-2">
        <view class="flex items-center">
          <wd-icon name="user" class="text-gray-400 mr-1" />
          <text>教师: {{ course.teacher }}</text>
        </view>
        <view class="flex items-center">
          <wd-icon name="usergroup" class="text-gray-400 mr-1" />
          <text>
            已选:
            <text
              :class="[
                course.selected > 0
                  ? course.selected >= course.limit && course.limit > 0
                    ? 'text-red-500 font-medium'
                    : 'text-blue-500 font-medium'
                  : 'text-gray-500',
              ]"
            >
              {{ course.selected }}
            </text>
            人 / 限: {{ course.limit || '不限' }}人
          </text>
        </view>
      </view>

      <view class="grid grid-cols-2 gap-2 text-sm">
        <view class="flex items-center">
          <wd-icon name="laptop" class="text-gray-400 mr-1" />
          <text>
            授课方式:
            <text :class="[getTeachingMethodColor(course.teachingMethod)]">
              {{ getDictLabel(teachingMethodDict, course.teachingMethod) || course.type }}
            </text>
          </text>
        </view>
        <view class="flex items-center">
          <wd-icon name="time" class="text-gray-400 mr-1" />
          <text>授课时间: {{ course.time }}</text>
        </view>
      </view>
    </view>

    <!-- 分页组件 -->
    <view v-if="filteredCourses.length > 0" class="px-4 pb-4">
      <Pagination
        v-model:page="queryParams.page"
        :page-size="queryParams.pageSize"
        :total="total"
        @update:page="handlePageChange"
      />
    </view>
  </view>
</template>

<style scoped lang="scss">
.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.last\:border-b-0:last-child {
  border-bottom-width: 0;
}

.course-info-container {
  flex: 1;
  min-width: 0;
  padding-right: 8rpx;
}

.course-heading {
  display: flex;
  flex-wrap: wrap;
  align-items: baseline;
}

.course-type-tag {
  float: left;
  margin-right: 8rpx;
  margin-bottom: 4rpx;
}

.course-name {
  word-break: break-word;
}

.no-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

// 搜索框样式
.search-type-selector {
  min-width: 120rpx;
}

.picker-view {
  height: 40rpx;
  line-height: 72rpx;
}

.search-input-container {
  height: 72rpx;
}

.search-input {
  height: 72rpx;
  font-size: 28rpx;
  line-height: 72rpx;
  background-color: transparent;
  border: none;
  outline: none;
}

// 自定义开关样式
.custom-switch {
  position: relative;
  width: 40px;
  height: 20px;
  background-color: #e5e5ea;
  border-radius: 10px;
  transition: all 0.3s;
}

// 小型开关样式
.custom-switch-small {
  position: relative;
  width: 30px;
  height: 16px;
  background-color: #e5e5ea;
  border-radius: 8px;
  transition: all 0.3s;
}

.switch-active {
  background-color: #2563eb;
}

.switch-thumb {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 16px;
  height: 16px;
  background-color: white;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  transition: all 0.3s;
}

.switch-active .switch-thumb {
  left: 22px;
}

.switch-thumb-small {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 12px;
  height: 12px;
  background-color: white;
  border-radius: 50%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  transition: all 0.3s;
}

.switch-active .switch-thumb-small {
  left: 16px;
}

// 新增样式
.sticky {
  position: sticky;
  top: 76rpx;
  z-index: 10;
}

.shadow-sm {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.stat-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 120rpx;
  padding: 12rpx;
  border-radius: 8rpx;
}

.stat-card-small {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 90rpx;
  padding: 8rpx;
  border-radius: 8rpx;
}

.stat-label {
  margin-bottom: 8rpx;
  font-size: 24rpx;
  color: #666;
}

.stat-value {
  font-size: 36rpx;
  font-weight: 600;
}

.bg-orange-50 {
  background-color: rgba(255, 237, 213, 0.5);
}

.text-orange-700 {
  color: #c2410c;
}

.bg-green-50 {
  background-color: rgba(240, 253, 244, 0.5);
}

.text-green-600 {
  color: #059669;
}

.bg-purple-50 {
  background-color: rgba(245, 243, 255, 0.5);
}

.text-purple-600 {
  color: #7c3aed;
}

.border-blue-100 {
  border-color: #dbeafe;
}

.border-green-100 {
  border-color: #d1fae5;
}

.border-purple-100 {
  border-color: #ede9fe;
}

.text-blue-600 {
  color: #2563eb;
}

.bg-blue-50 {
  background-color: rgba(239, 246, 255, 0.5);
}

.border {
  border-width: 1px;
}

.border-solid {
  border-style: solid;
}

.rounded-lg {
  border-radius: 8rpx;
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.gap-2 {
  gap: 8rpx;
}

.rounded-full {
  border-radius: 9999px;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

// 倒计时样式
.countdown-container {
  // text-align: center; // 已使用UnoCSS的flex布局，不再需要这个样式
}

.countdown-box {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  margin: 0 10rpx;
}

.countdown-number {
  display: inline-block;
  min-width: 56rpx;
  height: 56rpx;
  padding: 0 8rpx;
  font-size: 28rpx;
  font-weight: bold;
  line-height: 56rpx;
  color: white;
  text-align: center;
  background-color: #2563eb;
  border-radius: 8rpx;
}

.countdown-label {
  margin-top: 6rpx;
  font-size: 24rpx;
  color: #2563eb;
}

// 图表样式
.chart-container {
  width: 100%;
  padding: 12rpx 16rpx 16rpx;
  cursor: pointer;
  background-color: #fff;
  border: 1rpx solid #dbeafe;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.chart-content {
  width: 100%;
  height: 180rpx;
}

// 弹性布局
.flex {
  display: flex;
}

.flex-row {
  flex-direction: row;
}

.flex-1 {
  flex: 1;
}

// 自定义选课确认对话框样式
.course-confirm-dialog {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.dialog-container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  background-color: #fff;
  border-radius: 12rpx;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1px solid #eee;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  color: #999;
}

.dialog-content {
  flex: 1;
  padding: 24rpx;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx 24rpx;
  border-top: 1px solid #eee;
}

.cancel-btn,
.confirm-btn {
  padding: 16rpx 32rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
}

.cancel-btn {
  color: #666;
  background-color: #f5f5f5;
}

.confirm-btn {
  color: #fff;
  background-color: #2563eb;
}

.confirm-btn-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: not-allowed;
  background-color: #4d85ed;
  opacity: 0.9;
}

.loading-icon {
  margin-right: 8rpx;
  animation: loading-rotate 1s linear infinite;
}

@keyframes loading-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.info-section {
  margin-bottom: 24rpx;
}

.section-title {
  padding-left: 16rpx;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  border-left: 4rpx solid #2563eb;
}

.section-content {
  padding: 0 16rpx;
  font-size: 26rpx;
  line-height: 1.5;
  color: #666;
}

.course-main-info {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 0 16rpx;
  margin-bottom: 12rpx;
}

.teacher-name {
  margin-right: 12rpx;
  font-size: 28rpx;
  color: #333;
}

.course-name {
  margin-right: 8rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #2563eb;
}

.class-name {
  font-size: 26rpx;
  color: #666;
}

.info-table {
  width: 100%;
  border-collapse: collapse;
}

.table-row {
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px solid #f5f5f5;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 12rpx 16rpx;
  font-size: 26rpx;
}

.label {
  width: 120rpx;
  font-weight: 500;
  color: #666;
}

.value {
  flex: 1;
  color: #333;
}

.course-remark {
  max-height: 200rpx;
  overflow-y: auto;
}

// 添加弹窗中的颜色样式
.category-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #2563eb;
  background-color: #dbeafe;
  border-radius: 6rpx;
}

.campus-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #059669;
  background-color: #d1fae5;
  border-radius: 6rpx;
}

.credit-value {
  display: inline-block;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #7c3aed;
  background-color: #ede9fe;
  border-radius: 6rpx;
}

.teaching-method {
  display: inline-block;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  border-radius: 6rpx;
}

// 图表详情弹窗样式
.chart-detail-summary {
  padding: 16rpx 0;
  text-align: center;
}

.chart-detail-list {
  max-height: 60vh;
  overflow-y: auto;
}

.chart-detail-item {
  display: flex;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.chart-detail-item:last-child {
  border-bottom: none;
}

.chart-detail-color {
  width: 24rpx;
  height: 24rpx;
  margin-right: 16rpx;
  border-radius: 4rpx;
}

.chart-detail-name {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.chart-detail-value {
  font-size: 28rpx;
  font-weight: 600;
  color: #2563eb;
}

// 添加点击提示
.chart-click-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 6rpx;
  opacity: 0.9;
  transition: opacity 0.3s;
  transform: translate(-50%, -50%);
}

.semester-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #2563eb;
  background-color: #dbeafe;
  border-radius: 6rpx;
}
/* 添加成绩列表样式 */
.course-scores-list {
  max-height: 60vh;
  overflow-y: auto;
}

.course-score-item {
  transition: all 0.2s;

  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}
</style>
