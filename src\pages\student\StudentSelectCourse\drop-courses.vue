<route lang="json5">
{
  style: {
    navigationBarTitleText: '我要退选',
  },
}
</route>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import {
  getSchoolSelectCourseList,
  unselectCourse,
  getSelectCourseSetting,
} from '@/service/selectCourse'
import { loadDictData, getDictLabel } from '@/utils/dict'
import { useMessage } from 'wot-design-uni'
import type { DictData } from '@/types/system'
import type {
  StudentCourseItem,
  SchoolSelectCourseQuery,
  SchoolSelectCourseResponse,
  UnselectCourseParams,
  SelectCourseSettingResponse,
} from '@/types/selectCourse'

// MessageBox实例
const message = useMessage()

// 用户信息
const userStore = useUserStore()
const userInfo = computed(() => ({
  name: userStore.userInfo.realname,
  major: userStore.userInfo.department,
  grade: userStore.userInfo.className,
}))

// 课程分类字典
const courseCategoryDict = ref<DictData[]>([])
// 当前选中的课程分类
const selectedCourseCategory = ref('')

// 退选确认对话框状态
const showDropConfirmDialog = ref(false)
// 当前要退选的课程
const currentDropCourse = ref<any>(null)
// 退选确认按钮加载状态
const confirmLoading = ref(false)
// 退选原因
const dropReason = ref('')
// 添加新变量：防止列表中的退选按钮重复点击
const processingCourseId = ref<string | number | null>(null)

// 学年学期数据
const semesterInfo = ref<{
  xn: string
  xq: number
}>({
  xn: '',
  xq: 1,
})

// 查询参数
const queryParams = ref<SchoolSelectCourseQuery>({
  optype: 'selectList',
  page: 1,
  pageSize: 10,
  semesters: '',
  courseCategoryName: '',
  courseName: '',
  leaderTeacherName: '',
  className: '',
  courseTotalHours: '',
  weekHours: '',
  creditHour: '',
  startWeek: '',
  teachingInfo: '',
  siteName: '',
  limitCount: '',
  selectedCount: '',
  campusName: '',
})

// 已选课程列表
const selectedCourses = ref<StudentCourseItem[]>([])

// API 响应数据
const apiResponse = ref<SchoolSelectCourseResponse | null>(null)

// 加载状态
const loading = ref(false)

// 总课程数量
const totalCourses = ref<number>(3)
// 已退选数量
const droppedCount = ref<number>(0)
// 退选截止时间
const dropDeadline = ref<string>('')

// 退选相关信息
const unselectInfo = ref({
  startTime: '', // 退选开始时间
  endTime: '', // 退选截止时间
  usedTimes: 0, // 已使用退选次数
  totalTimes: 0, // 总退选次数
})

// 格式化课程数据
const formattedCourses = computed(() => {
  if (!selectedCourses.value || selectedCourses.value.length === 0) {
    return []
  }

  return selectedCourses.value.map((course) => {
    // 判断课程分类以设置背景色和文字颜色
    let typeBg = 'bg-blue-100'
    let typeTextColor = 'text-blue-700'

    if (
      course.courseCategoryName?.includes('人工智能') ||
      course.courseCategoryName?.includes('智能教育')
    ) {
      typeBg = 'bg-red-100'
      typeTextColor = 'text-red-700'
    } else if (
      course.courseCategoryName?.includes('艺术') ||
      course.courseCategoryName?.includes('美育')
    ) {
      typeBg = 'bg-green-100'
      typeTextColor = 'text-green-700'
    } else if (course.courseCategoryName?.includes('其他')) {
      typeBg = 'bg-yellow-100'
      typeTextColor = 'text-yellow-700'
    } else if (
      course.courseCategoryName?.includes('人文') ||
      course.courseCategoryName?.includes('社科')
    ) {
      typeBg = 'bg-red-100'
      typeTextColor = 'text-red-700'
    } else if (
      course.courseCategoryName?.includes('自然') ||
      course.courseCategoryName?.includes('科学')
    ) {
      typeBg = 'bg-yellow-100'
      typeTextColor = 'text-yellow-700'
    } else if (
      course.courseCategoryName?.includes('计算机') ||
      course.courseCategoryName?.includes('信息')
    ) {
      typeBg = 'bg-blue-100'
      typeTextColor = 'text-blue-700'
    }

    // 使用可选链和空值合并避免类型错误
    const semester = apiResponse.value?.xnxqszxx
      ? `${apiResponse.value.xnxqszxx.xn || '2024-2025'} 第${apiResponse.value.xnxqszxx.xq || '1'}学期`
      : `${course.studyYear || '2024-2025'} 第${course.studyTerm || '1'}学期`

    return {
      id: course.id,
      type: course.courseCategoryName || '未知',
      typeBg,
      typeTextColor,
      name: course.courseName,
      semester,
      credit: course.creditHour,
      teacher: course.leaderTeacherName,
      // StudentCourseItem没有这些属性，使用默认值
      limitCount: course.maxCount,
      selectedCount: course.selectedCount,
      campus: course.siteName,
      teachingMode: `${course.courseTotalHours}学时`,
      time: course.teachingInfo || '待安排',
      isDropped: false,
      isDropping: false,
      isSubmitting: false,
    }
  })
})

// 过滤后的课程列表
const filteredCourses = computed(() => {
  if (!selectedCourseCategory.value) {
    return formattedCourses.value
  }

  // 按课程类别筛选已选课程
  return formattedCourses.value.filter(
    (course) =>
      course.type &&
      course.type.includes(getDictLabel(courseCategoryDict.value, selectedCourseCategory.value)),
  )
})

/**
 * 打开退选确认对话框
 * @param course 要退选的课程
 */
const openDropConfirmDialog = (course: any) => {
  // 如果当前有正在处理的课程，或者当前课程已经退选/正在提交，则不处理
  if (processingCourseId.value !== null || course.isDropped || course.isSubmitting) return

  currentDropCourse.value = course
  // 记录正在处理的课程ID
  processingCourseId.value = course.id
  dropReason.value = ''
  showDropConfirmDialog.value = true
}

/**
 * 确认退选课程
 */
const confirmDropCourse = () => {
  // 如果已经在加载中，不允许再次点击
  if (confirmLoading.value) return

  if (!currentDropCourse.value || !dropReason.value.trim()) {
    uni.showToast({
      title: '请输入退选原因',
      icon: 'none',
    })
    return
  }

  // 设置加载状态
  confirmLoading.value = true

  // 设置课程的提交状态
  if (currentDropCourse.value) {
    currentDropCourse.value.isSubmitting = true
  }

  const params: UnselectCourseParams = {
    id: currentDropCourse.value.id,
    remark: dropReason.value.trim(),
    optype: 'unSelect',
  }

  unselectCourse(params)
    .then(() => {
      // 添加过渡动画标记
      if (currentDropCourse.value) {
        currentDropCourse.value.isDropping = true
      }

      // 退选成功后设置状态
      setTimeout(() => {
        if (currentDropCourse.value) {
          currentDropCourse.value.isDropped = true
        }
        droppedCount.value++

        // 移除过渡动画标记
        setTimeout(() => {
          if (currentDropCourse.value) {
            currentDropCourse.value.isDropping = false
          }
        }, 600)

        uni.showToast({
          title: '退选成功',
          icon: 'success',
        })

        // 退选成功后刷新列表
        fetchSelectedCourses()
      }, 300)
    })
    .catch((error) => {
      console.error('退选失败:', error)
      if (currentDropCourse.value) {
        currentDropCourse.value.isDropping = false
      }

      // 错误处理
      let errorMsg = '退选失败'
      if (error.msg) {
        try {
          errorMsg = JSON.parse(error.msg)
        } catch (e) {
          errorMsg = error.msg
        }
      }
      uni.showToast({
        title: errorMsg,
        icon: 'none',
      })
    })
    .finally(() => {
      // 无论成功失败，都重置提交状态
      confirmLoading.value = false
      if (currentDropCourse.value) {
        currentDropCourse.value.isSubmitting = false
      }
      // 关闭对话框
      showDropConfirmDialog.value = false
      // 清除正在处理的课程ID
      processingCourseId.value = null
    })
}

/**
 * 取消退选操作
 */
const cancelDropCourse = () => {
  showDropConfirmDialog.value = false
  currentDropCourse.value = null
  dropReason.value = ''
  // 清除正在处理的课程ID
  processingCourseId.value = null
}

/**
 * 退选课程 (原实现，使用 MessageBox)
 * @param course 要退选的课程
 */
const dropCourse = (course: any): void => {
  // 改用自定义对话框
  openDropConfirmDialog(course)
}

// 切换课程分类
const changeCourseCategory = (category: string) => {
  selectedCourseCategory.value = category
  // 切换分类后重新请求数据
  fetchSelectedCourses()
}

/**
 * 获取当前学年学期信息
 */
const fetchCurrentSemesterInfo = async () => {
  try {
    const res = await getSelectCourseSetting()

    if (res.xnxqszxx) {
      // 更新学年学期信息
      semesterInfo.value = {
        xn: res.xnxqszxx.xn || '',
        xq: res.xnxqszxx.xq || 1,
      }

      // 更新查询参数中的学期信息
      queryParams.value.semesters = `${semesterInfo.value.xn}|${semesterInfo.value.xq}`

      console.log('获取到当前学年学期:', queryParams.value.semesters)
    }

    return res
  } catch (error) {
    console.error('获取当前学年学期信息失败:', error)
    uni.showToast({
      title: '获取学期信息失败',
      icon: 'none',
    })
    throw error
  }
}

// 获取已选课程数据
const fetchSelectedCourses = () => {
  loading.value = true
  // 更新查询参数，添加课程类别筛选
  if (selectedCourseCategory.value) {
    const categoryName = getDictLabel(courseCategoryDict.value, selectedCourseCategory.value)
    queryParams.value.courseCategoryName = categoryName
  } else {
    queryParams.value.courseCategoryName = ''
  }

  return getSchoolSelectCourseList(queryParams.value)
    .then((res) => {
      apiResponse.value = res
      selectedCourses.value = res.items || []

      // 更新退选信息
      if (res.xnxqszxx) {
        // 更新退选信息
        unselectInfo.value.endTime = res.xnxqszxx.rxktxjssj || '' // 退选截止时间
        unselectInfo.value.startTime = res.xnxqszxx.rxktxkssj || '' // 退选开始时间
        unselectInfo.value.totalTimes = res.xnxqszxx.xszxtxczcs || 0 // 退选总次数
        unselectInfo.value.usedTimes = res.xszxtxyczcs || 0 // 已使用退选次数
        totalCourses.value = res.xnxqszxx.xszxtxczcs || 0 // 已使用退选次数
        // 更新退选截止时间显示
        if (res.xnxqszxx.rxktxjssj) {
          dropDeadline.value = res.xnxqszxx.rxktxjssj
        }
      }
      return res
    })
    .catch((error) => {
      console.error('获取选课数据失败:', error)
      uni.showToast({
        title: '获取数据失败',
        icon: 'none',
      })
      throw error
    })
    .finally(() => {
      loading.value = false
    })
}

// 添加一个计算属性，判断是否还有退选次数
const hasRemainingUnselectTimes = computed(() => {
  const remainingTimes = unselectInfo.value.totalTimes - unselectInfo.value.usedTimes
  return remainingTimes > 0
})

// 页面加载时获取数据
onMounted(() => {
  // 先获取当前学年学期信息
  fetchCurrentSemesterInfo()
    .then(() => {
      // 加载课程分类字典
      return loadDictData(['DM_XKKCFLDM'])
    })
    .then((dicts) => {
      courseCategoryDict.value = dicts.DM_XKKCFLDM || []
      // 获取已选课程数据
      return fetchSelectedCourses()
    })
    .catch((error) => {
      console.error('初始化数据失败:', error)
      // 即使字典加载失败，也尝试获取课程数据
      fetchSelectedCourses()
    })
})
</script>

<template>
  <!-- 自定义退选确认对话框 -->
  <view v-if="showDropConfirmDialog && currentDropCourse" class="drop-confirm-dialog">
    <view class="dialog-mask" @click="cancelDropCourse"></view>
    <view class="dialog-container">
      <view class="dialog-header">
        <text class="dialog-title">确认退选</text>
        <wd-icon name="close" size="20px" class="close-icon" @click="cancelDropCourse" />
      </view>
      <view class="dialog-content">
        <!-- 退选课程信息 -->
        <view class="info-section">
          <view class="section-title">退选课程</view>
          <view class="course-main-info">
            <text class="teacher-name">{{ currentDropCourse.teacher }}</text>
            <text class="course-name">{{ currentDropCourse.name }}</text>
            <text class="class-name">({{ currentDropCourse.semester }})</text>
          </view>
        </view>

        <!-- 课程信息 -->
        <view class="info-section">
          <view class="section-title">课程信息</view>
          <view class="info-table">
            <view class="table-row">
              <view class="table-cell label">类别:</view>
              <view class="table-cell value">
                <text class="category-tag">{{ currentDropCourse.type }}</text>
              </view>
            </view>
            <view class="table-row">
              <view class="table-cell label">总学时:</view>
              <view class="table-cell value">{{ currentDropCourse.teachingMode }}</view>
              <view class="table-cell label">学分:</view>
              <view class="table-cell value">
                <text class="credit-value">{{ currentDropCourse.credit }}</text>
              </view>
            </view>
            <view class="table-row">
              <view class="table-cell label">学年学期:</view>
              <view class="table-cell value">
                <text class="semester-tag">{{ currentDropCourse.semester }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 授课时间 -->
        <view class="info-section">
          <view class="section-title">授课时间</view>
          <view class="section-content">{{ currentDropCourse.time || '暂无' }}</view>
        </view>

        <!-- 开放校区 -->
        <view class="info-section">
          <view class="section-title">开放校区</view>
          <view class="info-table">
            <view class="table-row">
              <view class="table-cell label">校区:</view>
              <view class="table-cell value">
                <text class="campus-tag">{{ currentDropCourse.campus || '暂无' }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 退选理由 -->
        <view class="info-section">
          <view class="section-title">退选理由</view>
          <view class="text-area-container">
            <textarea
              v-model="dropReason"
              class="drop-reason-textarea"
              placeholder="请输入退选理由（必填）"
              :maxlength="100"
            ></textarea>
            <text class="text-count">{{ dropReason.length }}/100</text>
          </view>
        </view>

        <!-- 退选提醒 -->
        <view class="info-section warning-section">
          <view class="warning-content">
            <wd-icon name="warn-bold" class="warning-icon" />
            <text class="warning-text">
              退选后需重新选课才能再次选择该课程，且退选次数有限，请慎重操作。 剩余退选次数：{{
                unselectInfo.totalTimes - unselectInfo.usedTimes
              }}次
            </text>
          </view>
        </view>
      </view>
      <view class="dialog-footer">
        <view class="cancel-btn" @click="cancelDropCourse">取消</view>
        <view
          class="confirm-btn"
          :class="{ 'confirm-btn-loading': confirmLoading, 'confirm-btn-disabled': confirmLoading }"
          @click="confirmDropCourse"
        >
          <wd-icon v-if="confirmLoading" name="refresh" size="16px" class="loading-icon" />
          <text>{{ confirmLoading ? '退选中...' : '确认退选' }}</text>
        </view>
      </view>
    </view>
  </view>

  <view class="drop-courses-container bg-gray-100">
    <!-- 退选信息 -->
    <view class="bg-white p-4">
      <view class="flex items-center text-sm mb-2">
        <wd-icon name="info-circle-filled" class="text-blue-500 mr-2 text-lg"></wd-icon>
        <text>
          {{ semesterInfo.xn }}学年 第{{ semesterInfo.xq }}学期，你自行退选最多操作
          <text class="text-blue-500 font-bold">{{ totalCourses }}</text>
          门，已退
          <text class="text-blue-500 font-bold">{{ unselectInfo.usedTimes }}</text>
          门
        </text>
      </view>
      <view class="text-xs text-gray-500 ml-6">
        退选时间: {{ unselectInfo.startTime }} ~ {{ unselectInfo.endTime }}
      </view>
    </view>

    <!-- 课程分类选择器 -->
    <view v-if="courseCategoryDict.length > 0" class="bg-white mt-2 py-2 px-3 mt-1">
      <view class="flex overflow-x-auto py-1 no-scrollbar">
        <view
          class="flex-none px-3 py-1 mr-2 rounded-full text-center transition-all duration-300 text-sm"
          :class="!selectedCourseCategory ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'"
          @click="changeCourseCategory('')"
        >
          全部
        </view>
        <view
          v-for="item in courseCategoryDict"
          :key="item.dictValue"
          class="flex-none px-3 py-1 mr-2 rounded-full text-center transition-all duration-300 text-sm"
          :class="
            selectedCourseCategory === item.dictValue
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 text-gray-600'
          "
          @click="changeCourseCategory(item.dictValue)"
        >
          {{ item.dictLabel }}
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="flex justify-center items-center py-10">
      <wd-loading color="#007aff" />
    </view>

    <!-- 退选课程列表 -->
    <view v-else class="mt-2 bg-white">
      <view v-if="filteredCourses.length === 0" class="py-10 text-center text-gray-500">
        <wd-icon name="empty" size="60" color="#d1d1d6" />
        <view class="mt-2">
          {{ selectedCourseCategory ? '没有符合条件的已选课程' : '暂无已选课程可退选' }}
        </view>
      </view>

      <!-- 课程项 -->
      <view
        v-for="course in filteredCourses"
        :key="course.id"
        class="course-item p-4"
        :class="{
          'bg-gray-50': course.isDropped,
          dropping: course.isDropping,
        }"
      >
        <view class="flex justify-between items-start mb-2">
          <view class="course-info-container">
            <view class="course-heading">
              <text
                :class="[
                  course.typeBg,
                  course.typeTextColor,
                  'course-type-tag px-2 py-1 rounded text-xs font-medium whitespace-nowrap',
                ]"
              >
                【{{ course.type }}】
              </text>
              <text
                class="font-medium text-lg course-name"
                :class="{ 'text-gray-500': course.isDropped }"
              >
                {{ course.name }}
              </text>
            </view>
          </view>

          <view
            v-if="!course.isDropped && hasRemainingUnselectTimes"
            class="ml-2 px-4 py-2 bg-red-500 text-white rounded-lg text-sm transition-all duration-300 whitespace-nowrap flex-shrink-0"
            hover-class="bg-red-600"
            :class="{ 'opacity-70': course.isSubmitting || processingCourseId !== null }"
            @click="dropCourse(course)"
          >
            <text>退选</text>
          </view>
          <view
            v-else-if="!hasRemainingUnselectTimes"
            class="ml-2 px-4 py-2 bg-gray-300 text-white rounded-lg text-sm whitespace-nowrap flex-shrink-0"
          >
            次数已用完
          </view>
          <view
            v-else
            class="ml-2 px-4 py-2 bg-gray-300 text-white rounded-lg text-sm whitespace-nowrap flex-shrink-0"
          >
            <view class="flex items-center">
              <wd-icon name="check-bold" class="mr-1"></wd-icon>
              已退选
            </view>
          </view>
        </view>

        <view class="text-sm text-gray-500 mb-3">{{ course.semester }}</view>

        <view class="grid grid-cols-2 gap-2 text-sm mb-2">
          <view class="flex items-center">
            <wd-icon name="location" class="text-gray-400 mr-1" />
            <text>{{ course.campus }}</text>
          </view>
          <view class="flex items-center">
            <wd-icon name="books" class="text-gray-400 mr-1" />
            <text>学分: {{ course.credit }}</text>
          </view>
        </view>

        <view class="grid grid-cols-2 gap-2 text-sm">
          <view class="flex items-center">
            <wd-icon name="user" class="text-gray-400 mr-1" />
            <text>教师: {{ course.teacher }}</text>
          </view>
          <view class="flex items-center">
            <wd-icon name="usergroup" class="text-gray-400 mr-1" />
            <text>已选: {{ course.selectedCount }}人/限: {{ course.limitCount }}人</text>
          </view>
        </view>

        <view class="grid grid-cols-2 gap-2 text-sm mt-2">
          <view class="flex items-center">
            <wd-icon name="laptop" class="text-gray-400 mr-1" />
            <text>总学时: {{ course.teachingMode }}</text>
          </view>
          <view class="flex items-center">
            <wd-icon name="time" class="text-gray-400 mr-1" />
            <text>授课时间: {{ course.time }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 提示信息 -->
    <view class="p-4">
      <view
        class="bg-yellow-50 border-solid border border-yellow-200 rounded-lg p-3 text-sm text-yellow-800"
      >
        <view class="flex items-start">
          <wd-icon name="error-fill" class="text-yellow-500 mt-0.5 mr-2"></wd-icon>
          <view>
            <text class="font-medium">退选须知：</text>
            <view class="mt-1">1. 退选后需重新选课才能再次选择该课程</view>
            <view>2. 退选截止时间后将无法再进行退选操作</view>
            <view>
              3. 你共有 {{ unselectInfo.totalTimes }} 次退选机会，已使用
              {{ unselectInfo.usedTimes }} 次
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.drop-courses-container {
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    sans-serif;
}

.course-item {
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.3s ease;

  &:last-child {
    border-bottom: none;
  }

  &.dropping {
    background-color: rgba(254, 226, 226, 0.5);
  }
}

.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.course-info-container {
  flex: 1;
  min-width: 0;
  padding-right: 8rpx;
}

.course-heading {
  display: flex;
  flex-wrap: wrap;
  align-items: baseline;
}

.course-type-tag {
  float: left;
  margin-right: 8rpx;
  margin-bottom: 4rpx;
}

.course-name {
  word-break: break-word;
}

.no-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
}

// 添加自定义退选确认对话框样式
.drop-confirm-dialog {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dialog-mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.dialog-container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  background-color: #fff;
  border-radius: 12rpx;
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1px solid #eee;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-icon {
  color: #999;
}

.dialog-content {
  box-sizing: border-box;
  flex: 1;
  padding: 24rpx;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx 24rpx;
  border-top: 1px solid #eee;
}

.cancel-btn,
.confirm-btn {
  padding: 16rpx 32rpx;
  margin-left: 20rpx;
  font-size: 28rpx;
  border-radius: 8rpx;
}

.cancel-btn {
  color: #666;
  background-color: #f5f5f5;
}

.confirm-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: #e34d59;
}

.confirm-btn-loading {
  cursor: not-allowed;
  background-color: #e57981;
  opacity: 0.9;
}

.confirm-btn-disabled {
  pointer-events: none;
}

.loading-icon {
  margin-right: 8rpx;
  animation: loading-rotate 1s linear infinite;
}

@keyframes loading-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.info-section {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 24rpx;
}

.section-title {
  padding-left: 16rpx;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  border-left: 4rpx solid #e34d59;
}

.section-content {
  box-sizing: border-box;
  padding: 0 16rpx;
  font-size: 26rpx;
  line-height: 1.5;
  color: #666;
}

.course-main-info {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 0 16rpx;
  margin-bottom: 12rpx;
}

.course-name {
  margin-right: 8rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #e34d59;
}

.info-table {
  width: 100%;
  border-collapse: collapse;
}

.table-row {
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px solid #f5f5f5;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 12rpx 16rpx;
  font-size: 26rpx;
}

.label {
  width: 120rpx;
  font-weight: 500;
  color: #666;
}

.value {
  flex: 1;
  color: #333;
}

.text-area-container {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  padding: 0 16rpx;
}

.drop-reason-textarea {
  box-sizing: border-box;
  width: 100%;
  height: 160rpx;
  padding: 16rpx;
  font-size: 26rpx;
  background-color: #f9f9f9;
  border: 1px solid #eee;
  border-radius: 8rpx;
}

.text-count {
  position: absolute;
  right: 32rpx;
  bottom: 16rpx;
  z-index: 1;
  font-size: 24rpx;
  color: #999;
}

.warning-section {
  padding: 16rpx;
  background-color: #fff9f9;
  border-radius: 8rpx;
}

.warning-content {
  display: flex;
  align-items: flex-start;
}

.warning-icon {
  margin-right: 8rpx;
  color: #e34d59;
}

.warning-text {
  font-size: 24rpx;
  line-height: 1.5;
  color: #e34d59;
}

// 添加弹窗中的颜色样式
.category-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #e34d59;
  background-color: #fff1f0;
  border-radius: 6rpx;
}

.campus-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #059669;
  background-color: #d1fae5;
  border-radius: 6rpx;
}

.credit-value {
  display: inline-block;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #7c3aed;
  background-color: #ede9fe;
  border-radius: 6rpx;
}

.semester-tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #2563eb;
  background-color: #dbeafe;
  border-radius: 6rpx;
}

.teacher-name {
  margin-right: 12rpx;
  font-size: 28rpx;
  color: #333;
}

.class-name {
  font-size: 26rpx;
  color: #666;
}

.course-name {
  margin-right: 8rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #e34d59;
}
</style>
