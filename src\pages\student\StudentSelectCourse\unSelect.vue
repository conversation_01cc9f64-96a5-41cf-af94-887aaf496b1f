<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的选课列表',
  },
}
</route>
<script setup lang="ts">
import { ref, computed } from 'vue'

// 模拟数据
const userInfo = ref({
  name: '张同学',
  major: '计算机科学与技术',
  grade: '2020级',
})

const notice = ref({
  title: '选课通知',
  content: '2023-2024第二学期选课将于下周一(3月4日)开始，请同学们提前了解课程信息。',
  time: '3小时前',
})

const selectionStatus = ref({
  selectedCourses: 5,
  earnedCredits: 18,
})

// 课程列表数据
const courses = ref([
  {
    id: 1,
    courseCode: 'CS301',
    courseName: '数据结构与算法',
    teacherName: '李教授',
    department: '计算机科学',
    icon: 'book',
    iconBgColor: '#007AFF',
    credits: 4.0,
    courseType: '专业必修',
    hours: 64,
    schedule: '周三 10:00-11:40, 周五 14:00-15:40',
    location: '教学楼B-305',
    status: 1,
    semester: '2023-2024学年(2)',
  },
  {
    id: 2,
    courseCode: 'CS101',
    courseName: 'C语言程序设计',
    teacherName: '张教授',
    department: '计算机科学',
    icon: 'laptop-code',
    iconBgColor: '#5AC8FA',
    credits: 3.0,
    courseType: '专业必修',
    hours: 48,
    schedule: '周一 08:00-09:40, 周三 14:00-15:40',
    location: '教学楼A-201',
    status: 1,
    semester: '2023-2024学年(2)',
  },
])

// 选课进度
const semesterInfo = ref({
  title: '2023-2024学年第二学期选课',
  time: '选课时间: 2023年3月4日 08:00 - 2023年3月10日 20:00',
  currentCredits: 8.5,
  maxCredits: 26,
})

// 当前选中的标签
const activeTab = ref('available')

// 可选课程列表
const availableCourses = ref([
  {
    id: 1,
    courseCode: 'CS301',
    courseName: '计算机网络',
    teacherName: '陈教授',
    department: '计算机科学',
    icon: 'network',
    iconBgColor: '#5AC8FA',
    credits: 3.5,
    courseType: '专业必修',
    hours: 56,
    schedule: '周一 10:00-11:40, 周三 14:00-15:40',
    location: '教学楼A-203',
    remaining: '25/60',
    status: 1,
  },
  {
    id: 2,
    courseCode: 'CS302',
    courseName: '软件工程',
    teacherName: '林教授',
    department: '软件工程',
    icon: 'project',
    iconBgColor: '#4CD964',
    credits: 4.0,
    courseType: '专业必修',
    hours: 64,
    schedule: '周二 08:00-09:40, 周四 10:00-11:40',
    location: '教学楼B-105',
    remaining: '18/50',
    status: 1,
  },
  {
    id: 3,
    courseCode: 'CS303',
    courseName: '人工智能导论',
    teacherName: '王教授',
    department: '人工智能',
    icon: 'robot',
    iconBgColor: '#FF9500',
    credits: 3.0,
    courseType: '专业选修',
    hours: 48,
    schedule: '周三 08:00-09:40, 周五 10:00-11:40',
    location: '教学楼C-302',
    remaining: '2/45',
    status: 0,
  },
])

// 已选课程列表
const selectedCourses = ref([
  {
    id: 4,
    courseCode: 'CS304',
    courseName: '数据结构与算法',
    teacherName: '李教授',
    department: '计算机科学',
    icon: 'book',
    iconBgColor: '#007AFF',
    credits: 4.0,
    courseType: '专业必修',
    hours: 64,
    schedule: '周三 10:00-11:40, 周五 14:00-15:40',
    location: '教学楼B-305',
    status: 1,
  },
  {
    id: 5,
    courseCode: 'MA202',
    courseName: '高等数学(II)',
    teacherName: '赵教授',
    department: '数学',
    icon: 'calculator',
    iconBgColor: '#FF9500',
    credits: 4.5,
    courseType: '公共必修',
    hours: 72,
    schedule: '周二 14:00-15:40, 周四 08:00-09:40',
    location: '教学楼C-102',
    status: 1,
  },
])

// 获取课程状态文本
const getCourseStatusText = (status: number): string => {
  const statusMap = {
    0: '未开始',
    1: '正常授课中',
    2: '已完成',
  }
  return statusMap[status as keyof typeof statusMap] || '未知状态'
}

// 获取课程状态颜色
const getCourseStatusColor = (status: number): string => {
  const colorMap = {
    0: '#faad14', // 未开始-黄色
    1: '#52c41a', // 进行中-绿色
    2: '#8E8E93', // 已完成-灰色
  }
  return colorMap[status as keyof typeof colorMap] || '#8E8E93'
}

// 查看课程详情
const viewCourseDetail = (courseId: number) => {
  uni.navigateTo({
    url: `/pages/student/course-detail?id=${courseId}`,
  })
}

// 切换标签
const handleTabChange = (tab: string) => {
  activeTab.value = tab
}

// 选课
const selectCourse = (courseId: number, courseName: string) => {
  uni.navigateTo({
    url: `/pages/student/course-detail?id=${courseId}`,
  })
}

// 退选
const unselectCourse = (courseId: number, courseName: string) => {
  uni.showModal({
    title: '确认退选',
    content: `确定要退选${courseName}吗？`,
    success: (res) => {
      if (res.confirm) {
        // 模拟退选成功
        uni.showToast({
          title: '退选成功',
          icon: 'success',
        })
      }
    },
  })
}

// 计算选课进度百分比
const progressPercentage = computed(() => {
  return (semesterInfo.value.currentCredits / semesterInfo.value.maxCredits) * 100
})
</script>

<template>
  <view class="min-h-screen bg-gray-100 p-4">
    <!-- 用户信息 -->
    <view class="flex items-center mb-5">
      <view class="w-15 h-15 rounded-full bg-blue-50 flex items-center justify-center mr-4">
        <wd-icon name="user" size="24px" color="#1890ff" />
      </view>
      <view>
        <view class="text-lg font-semibold">{{ userInfo.name }}</view>
        <view class="text-sm text-gray-500">{{ userInfo.major }} • {{ userInfo.grade }}</view>
      </view>
    </view>

    <!-- 通知卡片 -->
    <view
      class="mb-5 bg-[#fff5eb] rounded-lg p-3 shadow border-l-4 border-[#faad14] border-l-solid"
    >
      <view class="flex items-center justify-between">
        <view class="flex items-center">
          <wd-icon name="bell" color="#faad14" class="mr-2" />
          <text class="font-semibold text-[#faad14]">{{ notice.title }}</text>
        </view>
        <text class="text-xs text-gray-500">{{ notice.time }}</text>
      </view>
      <view class="mt-1.5 text-sm text-gray-600">{{ notice.content }}</view>
    </view>

    <!-- 选课状态 -->
    <view class="flex justify-between mb-5 gap-3">
      <view class="w-48% bg-white rounded-lg p-3 text-center shadow">
        <view class="text-xl font-semibold text-blue-500">
          {{ selectionStatus.selectedCourses }}
        </view>
        <view class="text-xs text-gray-500 mt-0.5">已选课程</view>
      </view>
      <view class="w-48% bg-white rounded-lg p-3 text-center shadow">
        <view class="text-xl font-semibold text-blue-500">
          {{ selectionStatus.earnedCredits }}
        </view>
        <view class="text-xs text-gray-500 mt-0.5">已修学分</view>
      </view>
    </view>

    <!-- 选课进度 -->
    <view class="bg-white p-4 mb-4">
      <view class="text-lg font-semibold">{{ semesterInfo.title }}</view>
      <view class="text-xs text-gray-500 mt-1">{{ semesterInfo.time }}</view>

      <!-- 进度条 -->
      <view class="mt-4 h-1.5 bg-gray-100 rounded-full overflow-hidden">
        <view
          class="h-full bg-blue-500 rounded-full transition-all duration-300"
          :style="{ width: `${progressPercentage}%` }"
        ></view>
      </view>

      <!-- 学分信息 -->
      <view class="flex justify-between mt-2 text-sm">
        <view>
          <text class="text-gray-500">已选学分:</text>
          <text class="font-medium ml-1">{{ semesterInfo.currentCredits }}</text>
        </view>
        <view>
          <text class="text-gray-500">学期限制:</text>
          <text class="font-medium ml-1">{{ semesterInfo.maxCredits }}</text>
        </view>
      </view>
    </view>

    <!-- 分段控制器 -->
    <view class="bg-gray-100 mb-4 rounded-lg">
      <view class="flex bg-white rounded-lg p-1">
        <view
          class="flex-1 text-center py-2 rounded-md transition-all duration-300"
          :class="activeTab === 'available' ? 'bg-[#007aff] text-white' : 'text-gray-600'"
          @click="handleTabChange('available')"
        >
          可选课程
        </view>
        <view
          class="flex-1 text-center py-2 rounded-md transition-all duration-300"
          :class="activeTab === 'selected' ? 'bg-[#007aff] text-white' : 'text-gray-600'"
          @click="handleTabChange('selected')"
        >
          已选课程
        </view>
      </view>
    </view>

    <!-- 筛选按钮 -->
    <view class="flex justify-between mb-4">
      <view class="flex items-center bg-white px-3 py-2 rounded-lg border border-gray-200">
        <wd-icon name="category" class="text-gray-500 mr-1" />
        <text class="text-sm">全部分类</text>
      </view>
      <view class="flex items-center bg-white px-3 py-2 rounded-lg border border-gray-200">
        <wd-icon name="filter" class="text-gray-500 mr-1" />
        <text class="text-sm">筛选</text>
      </view>
      <view class="flex items-center bg-white px-3 py-2 rounded-lg border border-gray-200">
        <wd-icon name="sort" class="text-gray-500 mr-1" />
        <text class="text-sm">排序</text>
      </view>
    </view>

    <!-- 课程列表 -->
    <view class="">
      <template v-if="activeTab === 'available'">
        <view v-if="availableCourses.length > 0">
          <view v-for="course in availableCourses" :key="course.id" class="course-card">
            <view class="course-header">
              <view class="course-icon" :style="{ 'background-color': course.iconBgColor }">
                <wd-icon :name="course.icon" size="20" />
              </view>
              <view class="course-info">
                <view class="course-name">{{ course.courseName }}</view>
                <view class="course-teacher">
                  {{ course.teacherName }} | {{ course.department }}
                </view>
              </view>
            </view>
            <view class="course-body">
              <view class="course-meta">
                <view class="meta-item">{{ course.credits }}学分</view>
                <view class="meta-item">{{ course.courseType }}</view>
                <view class="meta-item">{{ course.hours }}学时</view>
              </view>
              <view class="course-schedule">
                <wd-icon name="clock" size="14" class="schedule-icon" />
                {{ course.schedule }}
              </view>
              <view class="course-schedule">
                <wd-icon name="location" size="14" class="schedule-icon" />
                {{ course.location }}
              </view>
              <view class="course-actions">
                <view class="course-status">
                  <view
                    class="status-indicator"
                    :style="{ 'background-color': course.status === 1 ? '#52c41a' : '#faad14' }"
                  ></view>
                  <text>剩余名额: {{ course.remaining }}</text>
                </view>
                <wd-button
                  size="small"
                  type="primary"
                  custom-style="background-color: #007aff; border-color: #007aff; border-radius: 6px;"
                  @click="selectCourse(course.id, course.courseName)"
                >
                  选课
                </wd-button>
              </view>
            </view>
          </view>
        </view>
        <view v-else class="empty-state">
          <wd-icon name="empty" size="60" color="#d1d1d6" />
          <view class="empty-text">暂无可选课程</view>
        </view>
      </template>

      <template v-else>
        <view v-if="selectedCourses.length > 0">
          <view v-for="course in selectedCourses" :key="course.id" class="course-card">
            <view class="course-header">
              <view class="course-icon" :style="{ 'background-color': course.iconBgColor }">
                <wd-icon :name="course.icon" size="20" />
              </view>
              <view class="course-info">
                <view class="course-name">{{ course.courseName }}</view>
                <view class="course-teacher">
                  {{ course.teacherName }} | {{ course.department }}
                </view>
              </view>
            </view>
            <view class="course-body">
              <view class="course-meta">
                <view class="meta-item">{{ course.credits }}学分</view>
                <view class="meta-item">{{ course.courseType }}</view>
                <view class="meta-item">{{ course.hours }}学时</view>
              </view>
              <view class="course-schedule">
                <wd-icon name="clock" size="14" class="schedule-icon" />
                {{ course.schedule }}
              </view>
              <view class="course-schedule">
                <wd-icon name="location" size="14" class="schedule-icon" />
                {{ course.location }}
              </view>
              <view class="course-actions">
                <view class="course-status">
                  <view class="status-indicator" style="background-color: #52c41a"></view>
                  <text>已选课程</text>
                </view>
                <view class="flex gap-2">
                  <wd-button
                    size="small"
                    type="default"
                    custom-style="background-color: #e5e5ea; border-color: #e5e5ea; color: #8e8e93; border-radius: 6px;"
                    @click="unselectCourse(course.id, course.courseName)"
                  >
                    退选
                  </wd-button>
                  <wd-button
                    size="small"
                    type="primary"
                    custom-style="background-color: #007aff; border-color: #007aff; border-radius: 6px;"
                    @click="viewCourseDetail(course.id)"
                  >
                    查看
                  </wd-button>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view v-else class="empty-state">
          <wd-icon name="empty" size="60" color="#d1d1d6" />
          <view class="empty-text">你还没有选择任何课程</view>
          <wd-button type="primary" class="mt-4" @click="handleTabChange('available')">
            浏览可选课程
          </wd-button>
        </view>
      </template>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.section-title {
  margin: 24rpx 0 16rpx;
  font-size: 16px;
  font-weight: 600;
}

.course-card {
  margin-bottom: 16px;
  overflow: hidden;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
}

.course-header {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f2f2f7;
}

.course-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  margin-right: 12px;
  color: white;
  border-radius: 12px;
}

.course-info {
  flex: 1;
}

.course-name {
  margin-bottom: 2px;
  font-size: 17px;
  font-weight: 600;
}

.course-teacher {
  font-size: 13px;
  color: #8e8e93;
}

.course-body {
  padding: 16px;
}

.course-meta {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.meta-item {
  padding: 4px 10px;
  margin-right: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #606266;
  background-color: #f2f2f7;
  border-radius: 14px;
}

.course-schedule {
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.schedule-icon {
  margin-right: 4px;
  color: #8e8e93;
}

.course-actions {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 16px;
  align-items: center;
  padding-top: 16px;
  margin-top: 16px;
  border-top: 1px solid #f2f2f7;
}

.course-status {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.status-indicator {
  width: 8px;
  height: 8px;
  margin-right: 6px;
  border-radius: 50%;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
  background-color: white;
  border-radius: 12px;
}

.empty-text {
  margin-top: 16px;
  margin-bottom: 24px;
  font-size: 16px;
  color: #8e8e93;
}
</style>
