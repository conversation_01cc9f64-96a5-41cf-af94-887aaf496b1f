<route lang="json5">
{
  style: {
    navigationBarTitleText: '录取查询',
  },
}
</route>
<template>
  <view class="px-5 pt-4 pb-16 bg-gray-100">
    <!-- 查询表单 -->
    <view class="bg-white rounded-2xl p-4 mb-4">
      <view class="text-base font-bold mb-4">录取信息查询</view>
      <view class="space-y-4">
        <view>
          <view class="text-sm text-gray-700 mb-1">考生号</view>
          <wd-input v-model="form.studentId" placeholder="请输入考生号" class="rounded-xl" />
        </view>
        <view>
          <view class="text-sm text-gray-700 mb-1">身份证号</view>
          <wd-input v-model="form.idCard" placeholder="请输入身份证号" class="rounded-xl" />
        </view>
        <wd-button type="primary" block class="rounded-xl h-12 text-md" @click="handleSearch">
          查询
        </wd-button>
        <view class="text-xs text-gray-500 text-center">
          如遇查询问题，请联系招生办：0755-12345678
        </view>
      </view>
    </view>

    <!-- 模拟数据提示 -->
    <view v-if="!searchResult && !showNoResult" class="bg-blue-50 rounded-2xl p-4 mb-4">
      <view class="flex items-start">
        <view class="text-blue-500 mr-2 mt-1">
          <wd-icon name="info-circle" />
        </view>
        <view class="text-sm text-blue-800">
          <view class="font-medium mb-1">测试账号</view>
          <view class="text-blue-700 mb-2">可使用以下测试账号进行查询：</view>
          <view class="grid grid-cols-2 gap-2 text-xs mb-2">
            <view>
              <view class="text-blue-600 font-medium">已录取示例：</view>
              <view>考生号：202405120001</view>
              <view>身份证号：******************</view>
            </view>
            <view>
              <view class="text-blue-600 font-medium">审核中示例：</view>
              <view>考生号：202405120002</view>
              <view>身份证号：******************</view>
            </view>
          </view>
          <view class="grid grid-cols-2 gap-2 text-xs">
            <view>
              <view class="text-blue-600 font-medium">未录取示例：</view>
              <view>考生号：202405120003</view>
              <view>身份证号：******************</view>
            </view>
            <view></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 查询结果展示 -->
    <view v-if="searchResult" class="bg-white rounded-2xl p-4 mb-4">
      <view class="text-base font-bold mb-3">录取查询结果</view>
      <view class="flex justify-between items-center mb-3">
        <view>
          <view class="font-medium">{{ searchResult.name }}</view>
          <view class="text-sm text-gray-500">考生号：{{ searchResult.studentId }}</view>
        </view>
        <view
          class="inline-block px-2.5 py-1 rounded-3xl text-xs font-medium"
          :class="getStatusClass(searchResult.status)"
        >
          {{ getStatusText(searchResult.status) }}
        </view>
      </view>
      <view v-if="searchResult.status === 'admitted'" class="bg-gray-50 p-3 rounded-xl mb-4">
        <view class="grid grid-cols-2 gap-2 text-sm">
          <view>
            <view class="text-gray-500">录取院校</view>
            <view class="font-medium">{{ searchResult.college }}</view>
          </view>
          <view>
            <view class="text-gray-500">录取专业</view>
            <view class="font-medium">{{ searchResult.major }}</view>
          </view>
          <view>
            <view class="text-gray-500">录取批次</view>
            <view class="font-medium">{{ searchResult.batch }}</view>
          </view>
          <view>
            <view class="text-gray-500">录取日期</view>
            <view class="font-medium">{{ searchResult.admitDate }}</view>
          </view>
        </view>
      </view>

      <!-- 录取进度 -->
      <view v-if="searchResult.status === 'admitted'" class="mb-3 text-sm font-medium">
        录取进度
      </view>
      <view v-if="searchResult.status === 'admitted'" class="flex items-center mb-4">
        <template v-for="(step, index) in steps" :key="index">
          <view
            class="w-5 h-5 rounded-full flex items-center justify-center text-white text-xs"
            :class="getStepClass(index, searchResult.progressStep)"
          >
            <wd-icon v-if="index < searchResult.progressStep" name="check" size="12px" />
            <text v-else>{{ index + 1 }}</text>
          </view>

          <view v-if="index < steps.length - 1" class="h-px bg-gray-300 flex-1 mx-2.5"></view>
        </template>
      </view>
      <view
        v-if="searchResult.status === 'admitted'"
        class="grid grid-cols-4 text-xs text-center gap-2"
      >
        <view
          v-for="(step, index) in steps"
          :key="index"
          :class="getStepTextClass(index, searchResult.progressStep)"
        >
          {{ step }}
        </view>
      </view>
    </view>

    <!-- 入学须知 -->
    <view
      v-if="searchResult && searchResult.status === 'admitted'"
      class="bg-white rounded-2xl p-4"
    >
      <view class="text-base font-bold mb-3">入学须知</view>
      <view class="space-y-3">
        <view class="flex items-start">
          <view class="text-blue-500 mr-2 mt-1">
            <wd-icon name="info-circle" />
          </view>
          <view class="text-sm">
            <view class="font-medium">报到时间</view>
            <view class="text-gray-500">2024年9月1日-3日，请按照录取通知书上的时间准时报到。</view>
          </view>
        </view>
        <view class="flex items-start">
          <view class="text-blue-500 mr-2 mt-1">
            <wd-icon name="info-circle" />
          </view>
          <view class="text-sm">
            <view class="font-medium">报到地点</view>
            <view class="text-gray-500">
              广东省高新技术职业学院南校区体育馆，详细地址请查看录取通知书。
            </view>
          </view>
        </view>
        <view class="flex items-start">
          <view class="text-blue-500 mr-2 mt-1">
            <wd-icon name="info-circle" />
          </view>
          <view class="text-sm">
            <view class="font-medium">所需材料</view>
            <view class="text-gray-500">
              录取通知书、身份证原件及复印件、户口本、高考准考证、1寸蓝底照片4张。
            </view>
          </view>
        </view>
        <view class="flex items-start">
          <view class="text-blue-500 mr-2 mt-1">
            <wd-icon name="info-circle" />
          </view>
          <view class="text-sm">
            <view class="font-medium">缴费信息</view>
            <view class="text-gray-500">
              学费：8800元/年，住宿费：1200元/年，请在报到前通过银行汇款或网上支付。
            </view>
          </view>
        </view>
      </view>
      <wd-button type="primary" block class="rounded-xl h-12 text-md mt-4">
        下载录取通知书
      </wd-button>
    </view>

    <!-- 无结果提示 -->
    <view
      v-if="showNoResult"
      class="bg-white rounded-2xl p-4 flex flex-col items-center justify-center py-8"
    >
      <wd-icon name="error-circle" size="48px" class="text-gray-400 mb-4" />
      <view class="text-gray-500 mb-2">未查询到录取信息</view>
      <view class="text-xs text-gray-400 text-center">
        请检查您输入的考生号和身份证号是否正确，
        <br />
        或联系招生办公室咨询
      </view>
    </view>

    <!-- 底部重置按钮 -->
    <view v-if="searchResult || showNoResult" class="mt-4">
      <wd-button type="info" block class="rounded-xl" @click="resetSearch">重新查询</wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'

// 表单数据
const form = reactive({
  studentId: '',
  idCard: '',
})

// 进度步骤
const steps = ['资格审核', '专业录取', '通知发放', '报到注册']

// 模拟数据
const mockData = [
  {
    studentId: '202405120001',
    idCard: '******************',
    name: '李华',
    status: 'admitted', // admitted, pending, rejected
    college: '广东省高新技术职业学院',
    major: '软件工程',
    batch: '本科提前批',
    admitDate: '2024-07-15',
    progressStep: 3, // 当前进度步骤，从0开始
  },
  {
    studentId: '202405120002',
    idCard: '******************',
    name: '张三',
    status: 'pending',
    college: '',
    major: '',
    batch: '',
    admitDate: '',
    progressStep: 0,
  },
  {
    studentId: '202405120003',
    idCard: '******************',
    name: '王五',
    status: 'rejected',
    college: '',
    major: '',
    batch: '',
    admitDate: '',
    progressStep: 0,
  },
]

// 搜索结果
const searchResult = ref(null)
const showNoResult = ref(false)

// 查询处理
const handleSearch = () => {
  // 重置状态
  searchResult.value = null
  showNoResult.value = false

  if (!form.studentId || !form.idCard) {
    uni.showToast({
      title: '请输入考生号和身份证号',
      icon: 'none',
    })
    return
  }

  // 模拟API请求延迟
  uni.showLoading({ title: '查询中...' })

  setTimeout(() => {
    const result = mockData.find(
      (item) => item.studentId === form.studentId && item.idCard === form.idCard,
    )

    uni.hideLoading()

    if (result) {
      searchResult.value = result
    } else {
      showNoResult.value = true
    }
  }, 800)
}

// 重置查询
const resetSearch = () => {
  form.studentId = ''
  form.idCard = ''
  searchResult.value = null
  showNoResult.value = false
}

// 获取状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case 'admitted':
      return 'bg-green-100 text-green-800'
    case 'pending':
      return 'bg-blue-100 text-blue-800'
    case 'rejected':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 获取状态文本
const getStatusText = (status) => {
  switch (status) {
    case 'admitted':
      return '已录取'
    case 'pending':
      return '审核中'
    case 'rejected':
      return '未录取'
    default:
      return '未知状态'
  }
}

// 获取步骤样式类
const getStepClass = (index, currentStep) => {
  if (index < currentStep) {
    return 'bg-green-500' // 已完成
  } else if (index === currentStep) {
    return 'bg-blue-500' // 当前步骤
  } else {
    return 'bg-gray-400' // 未开始
  }
}

// 获取步骤文本样式类
const getStepTextClass = (index, currentStep) => {
  if (index < currentStep) {
    return 'text-green-600 font-medium'
  } else if (index === currentStep) {
    return 'text-blue-600 font-medium'
  } else {
    return 'text-gray-500'
  }
}
</script>

<style scoped>
/* 已全部使用UnoCSS的类名代替 */
</style>
