<route lang="json5">
{
  style: {
    navigationBarTitleText: '选课详情',
  },
}
</route>
<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 当前激活的标签
const activeTab = ref('details')

// 课程信息
const courseInfo = ref({
  id: 1,
  courseCode: 'CS301',
  courseName: '数据结构与算法',
  department: '计算机科学',
  credits: 4.0,
  teacherName: '李教授',
  teacherTitle: '计算机科学与技术学院 副教授',
  courseType: '专业必修课',
  hours: 64,
  semester: '2023-2024学年第二学期',
  capacity: 60,
  enrolled: 42,
  description:
    '本课程主要介绍计算机程序设计中常用的数据结构及算法的设计与分析。主要内容包括线性表、栈和队列、树与二叉树、图、查找和排序等经典数据结构与算法，以及对复杂问题求解的基本方法与策略。通过本课程的学习，学生将掌握基本数据结构与算法的设计与实现方法，培养分析问题和解决问题的能力。',
  rating: 4.5,
  ratingCount: 125,
  schedule: [
    { day: '周三', time: '10:00 - 11:40', location: '教学楼B-305' },
    { day: '周五', time: '14:00 - 15:40', location: '教学楼B-305' },
  ],
  assessment: [
    { title: '期末考试', subtitle: '闭卷笔试', percentage: 50 },
    { title: '平时作业', subtitle: '5次编程作业', percentage: 30 },
    { title: '课堂表现', subtitle: '出勤率、课堂互动', percentage: 10 },
    { title: '小组项目', subtitle: '算法实现与展示', percentage: 10 },
  ],
  materials: [
    { title: '《数据结构》', author: '严蔚敏 编著，清华大学出版社', isPrimary: true },
    { title: '《算法导论》', author: 'Thomas H.Cormen 等，机械工业出版社', isPrimary: false },
    {
      title: '《数据结构与算法分析》',
      author: 'Mark Allen Weiss，机械工业出版社',
      isPrimary: false,
    },
  ],
  syllabus: [
    '第1-2周: 绪论与算法分析',
    '第3-4周: 线性表',
    '第5-6周: 栈与队列',
    '第7-9周: 树与二叉树',
    '第10-11周: 图',
    '第12-13周: 查找',
    '第14-16周: 排序',
  ],
  reviews: [
    {
      name: '王同学',
      date: '2023年12月',
      rating: 5,
      content:
        '李教授讲课非常清晰，内容安排合理，难度适中。通过这门课我对数据结构有了深入的理解，作业虽然有点多，但对提高编程能力很有帮助。',
    },
    {
      name: '张同学',
      date: '2023年12月',
      rating: 4,
      content:
        '课程内容充实，老师讲解很详细，但有些算法的实现较复杂，希望能有更多的实例和练习。总体来说是一门很有收获的课程。',
    },
    {
      name: '李同学',
      date: '2023年11月',
      rating: 4.5,
      content:
        '这门课对计算机专业的学生非常重要，老师讲解透彻，案例丰富。期末考试有一定难度，但只要按时完成作业，认真听讲，成绩不会太差。',
    },
  ],
})

// 是否已收藏
const isBookmarked = ref(false)

// 处理收藏按钮点击
const toggleBookmark = () => {
  isBookmarked.value = !isBookmarked.value
  uni.showToast({
    title: isBookmarked.value ? '已添加到收藏' : '已取消收藏',
    icon: 'none',
  })
}

// 处理标签切换
const handleTabChange = (tab: string) => {
  activeTab.value = tab
}

// 选课
const selectCourse = () => {
  uni.showModal({
    title: '确认选课',
    content: `确定要选择${courseInfo.value.courseName}吗？`,
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '选课成功',
          icon: 'success',
        })
        // 返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    },
  })
}

onMounted(() => {
  // 这里可以根据路由参数获取课程ID，然后调用API获取课程详情
  // const courseId = Number(uni.getStorageSync('courseId'))
  // 目前使用的是模拟数据
})
</script>

<template>
  <view class="course-detail">
    <!-- 课程头部 -->
    <view class="course-header">
      <view class="bookmark-button" @click="toggleBookmark">
        <wd-icon :name="isBookmarked ? 'bookmark' : 'bookmark-o'" size="20" color="#ffffff" />
      </view>
      <view class="course-title">{{ courseInfo.courseName }}</view>
      <view class="course-subtitle">
        {{ courseInfo.department }} • {{ courseInfo.credits }}学分
      </view>
      <view class="rating-stars">
        <wd-icon
          v-for="i in 5"
          :key="i"
          :name="
            i <= courseInfo.rating ? 'star' : i - 0.5 <= courseInfo.rating ? 'star-half' : 'star-o'
          "
          color="#FF9500"
          size="14"
        />
        <text class="ml-1 text-white">
          {{ courseInfo.rating }} ({{ courseInfo.ratingCount }}人评价)
        </text>
      </view>
      <view class="course-wave"></view>
    </view>

    <!-- 标签导航 -->
    <view class="tab-view">
      <view
        class="tab"
        :class="{ active: activeTab === 'details' }"
        @click="handleTabChange('details')"
      >
        课程详情
      </view>
      <view
        class="tab"
        :class="{ active: activeTab === 'schedule' }"
        @click="handleTabChange('schedule')"
      >
        课程安排
      </view>
      <view
        class="tab"
        :class="{ active: activeTab === 'reviews' }"
        @click="handleTabChange('reviews')"
      >
        学生评价
      </view>
    </view>

    <!-- 标签内容 -->
    <view class="tab-content">
      <!-- 课程详情标签 -->
      <view v-if="activeTab === 'details'" class="tab-pane">
        <!-- 基本信息 -->
        <view class="course-info-grid">
          <view class="info-item">
            <view class="info-label">课程类型</view>
            <view class="info-value">{{ courseInfo.courseType }}</view>
          </view>
          <view class="info-item">
            <view class="info-label">学时</view>
            <view class="info-value">{{ courseInfo.hours }}学时</view>
          </view>
          <view class="info-item">
            <view class="info-label">开课学期</view>
            <view class="info-value">{{ courseInfo.semester }}</view>
          </view>
          <view class="info-item">
            <view class="info-label">限选人数</view>
            <view class="info-value">{{ courseInfo.capacity }}人</view>
          </view>
        </view>

        <!-- 教师信息 -->
        <view class="section-title">授课教师</view>
        <view class="teacher-card">
          <view class="teacher-avatar">
            <wd-icon name="user-tie" size="24" color="#007aff" />
          </view>
          <view class="teacher-info">
            <view class="teacher-name">{{ courseInfo.teacherName }}</view>
            <view class="teacher-title">{{ courseInfo.teacherTitle }}</view>
          </view>
          <wd-icon name="arrow-right" size="16" color="#8e8e93" />
        </view>

        <!-- 课程简介 -->
        <view class="section-title">课程简介</view>
        <view class="course-description">
          {{ courseInfo.description }}
        </view>

        <!-- 课程图片 -->
        <view class="section-image">
          <image
            src="https://images.unsplash.com/photo-1516116216624-53e697fedbea?ixlib=rb-1.2.1&auto=format&fit=crop&w=1000&q=80"
            mode="aspectFill"
          />
        </view>

        <!-- 考核方式 -->
        <view class="section-title">考核方式</view>
        <view class="ios-list">
          <view v-for="(item, index) in courseInfo.assessment" :key="index" class="ios-list-item">
            <view>
              <view class="ios-list-item-title">{{ item.title }}</view>
              <view class="ios-list-item-subtitle">{{ item.subtitle }}</view>
            </view>
            <view class="text-primary">{{ item.percentage }}%</view>
          </view>
        </view>
      </view>

      <!-- 课程安排标签 -->
      <view v-if="activeTab === 'schedule'" class="tab-pane">
        <view class="section-title">每周课程安排</view>
        <view v-for="(schedule, index) in courseInfo.schedule" :key="index" class="schedule-item">
          <view class="schedule-day">{{ schedule.day }}</view>
          <view class="schedule-info">
            <view class="schedule-time">{{ schedule.time }}</view>
            <view class="schedule-location">{{ schedule.location }}</view>
          </view>
        </view>

        <view class="section-title">教材与参考资料</view>
        <view class="ios-list">
          <view
            v-for="(material, index) in courseInfo.materials"
            :key="index"
            class="ios-list-item"
          >
            <view>
              <view class="ios-list-item-title">{{ material.title }}</view>
              <view class="ios-list-item-subtitle">{{ material.author }}</view>
            </view>
            <view :class="material.isPrimary ? 'text-primary' : 'text-gray-500'">
              {{ material.isPrimary ? '主教材' : '参考' }}
            </view>
          </view>
        </view>

        <view class="section-title">课程大纲</view>
        <view class="ios-list">
          <view v-for="(item, index) in courseInfo.syllabus" :key="index" class="ios-list-item">
            <view class="ios-list-item-title">{{ item }}</view>
          </view>
        </view>
      </view>

      <!-- 学生评价标签 -->
      <view v-if="activeTab === 'reviews'" class="tab-pane">
        <view class="flex items-center justify-between">
          <view class="section-title mb-0">学生评价</view>
          <button class="text-primary text-sm bg-transparent border-0">发表评价</button>
        </view>

        <view class="reviews-card">
          <view v-for="(review, index) in courseInfo.reviews" :key="index" class="review-item">
            <view class="review-header">
              <view class="reviewer-name">{{ review.name }}</view>
              <view class="review-date">{{ review.date }}</view>
            </view>
            <view class="rating-stars">
              <wd-icon
                v-for="i in 5"
                :key="i"
                :name="
                  i <= review.rating ? 'star' : i - 0.5 <= review.rating ? 'star-half' : 'star-o'
                "
                color="#FF9500"
                size="14"
              />
            </view>
            <view class="review-content">
              {{ review.content }}
            </view>
          </view>
        </view>

        <button class="ios-button-outline">查看更多评价</button>
      </view>
    </view>

    <!-- 选课按钮底部固定栏 -->
    <view class="enroll-footer">
      <view class="enrollment-status">
        <view class="text-gray-500">已选/限选</view>
        <view class="status-number">{{ courseInfo.enrolled }}/{{ courseInfo.capacity }}</view>
      </view>
      <wd-button
        type="primary"
        class="enroll-button bg-[#007aff]"
        custom-style="border-radius: 6px;background-color: #007aff;"
        @click="selectCourse"
      >
        选择此课程
      </wd-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.course-detail {
  min-height: 100vh;
  padding-bottom: 150rpx;
  background-color: #f2f2f7;
}

.course-header {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 400rpx;
  padding: 40rpx;
  color: white;
  background: linear-gradient(135deg, #007aff, #5ac8fa);
}

.course-wave {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 80rpx;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' fill-opacity='1' d='M0,192L48,176C96,160,192,128,288,122.7C384,117,480,139,576,154.7C672,171,768,181,864,186.7C960,192,1056,192,1152,170.7C1248,149,1344,107,1392,85.3L1440,64L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
  background-position: center;
  background-size: cover;
}

.bookmark-button {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72rpx;
  height: 72rpx;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.course-title {
  margin-bottom: 12rpx;
  font-size: 48rpx;
  font-weight: 600;
  line-height: 1.3;
}

.course-subtitle {
  font-size: 32rpx;
  opacity: 0.9;
}

.rating-stars {
  display: flex;
  align-items: center;
  margin-top: 16rpx;
}

.tab-view {
  display: flex;
  background-color: white;
  border-bottom: 1px solid #e5e5ea;
}

.tab {
  position: relative;
  flex: 1;
  padding: 24rpx 0;
  font-size: 30rpx;
  color: #8e8e93;
  text-align: center;
}

.tab.active {
  font-weight: 600;
  color: #007aff;
}

.tab.active::after {
  position: absolute;
  bottom: 0;
  left: 25%;
  width: 50%;
  height: 6rpx;
  content: '';
  background-color: #007aff;
  border-radius: 6rpx 6rpx 0 0;
}

.tab-content {
  padding: 32rpx;
}

.tab-pane {
  margin-bottom: 32rpx;
}

.section-title {
  margin-top: 48rpx;
  margin-bottom: 24rpx;
  font-size: 36rpx;
  font-weight: 600;
}

.course-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
}

.info-label {
  margin-bottom: 8rpx;
  font-size: 26rpx;
  color: #8e8e93;
}

.info-value {
  font-size: 32rpx;
  font-weight: 500;
}

.teacher-card {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  background-color: white;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

.teacher-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 100rpx;
  margin-right: 24rpx;
  background-color: #e6f2ff;
  border-radius: 50%;
}

.teacher-info {
  flex: 1;
}

.teacher-name {
  margin-bottom: 4rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.teacher-title {
  font-size: 26rpx;
  color: #8e8e93;
}

.course-description {
  font-size: 30rpx;
  line-height: 1.6;
  color: #333;
}

.section-image {
  height: 280rpx;
  margin-top: 32rpx;
  overflow: hidden;
  border-radius: 24rpx;
}

.section-image image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ios-list {
  margin-bottom: 32rpx;
  overflow: hidden;
  background-color: white;
  border-radius: 24rpx;
}

.ios-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx;
  border-bottom: 1px solid #e5e5ea;
}

.ios-list-item:last-child {
  border-bottom: none;
}

.ios-list-item-title {
  margin-bottom: 4rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.ios-list-item-subtitle {
  font-size: 26rpx;
  color: #8e8e93;
}

.schedule-item {
  display: flex;
  align-items: center;
  padding: 24rpx 32rpx;
  margin-bottom: 24rpx;
  background-color: white;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.04);
}

.schedule-day {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72rpx;
  height: 72rpx;
  margin-right: 24rpx;
  font-size: 26rpx;
  font-weight: 600;
  color: #007aff;
  background-color: #e6f2ff;
  border-radius: 50%;
}

.schedule-info {
  flex: 1;
}

.schedule-time {
  margin-bottom: 4rpx;
  font-size: 30rpx;
  font-weight: 500;
}

.schedule-location {
  font-size: 26rpx;
  color: #8e8e93;
}

.reviews-card {
  padding: 32rpx;
  margin-top: 32rpx;
  margin-bottom: 32rpx;
  background-color: white;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);
}

.review-item {
  padding-bottom: 24rpx;
  margin-bottom: 24rpx;
  border-bottom: 1px solid #e5e5ea;
}

.review-item:last-child {
  padding-bottom: 0;
  margin-bottom: 0;
  border-bottom: none;
}

.review-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.reviewer-name {
  font-size: 28rpx;
  font-weight: 600;
}

.review-date {
  font-size: 24rpx;
  color: #8e8e93;
}

.review-content {
  font-size: 28rpx;
  line-height: 1.5;
  color: #333;
}

.ios-button-outline {
  display: block;
  width: 100%;
  padding: 24rpx 0;
  font-size: 32rpx;
  font-weight: 500;
  color: #007aff;
  text-align: center;
  background-color: transparent;
  border: 1px solid #007aff;
  border-radius: 12rpx;
}

.enroll-footer {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.enrollment-status {
  font-size: 28rpx;
}

.status-number {
  font-size: 32rpx;
  font-weight: 600;
}

.enroll-button {
  flex: 1;
  margin-left: 32rpx;
}

.text-primary {
  color: #007aff;
}

.text-gray-500 {
  color: #8e8e93;
}

.ml-1 {
  margin-left: 8rpx;
}

.mb-0 {
  margin-bottom: 0;
}
</style>
