<route lang="json5">
{
  style: {
    navigationBarTitleText: '宿舍管理',
  },
}
</route>
<template>
  <view class="page bg-#f7f8fa py-4 px-4">
    <!-- 宿舍基本信息 -->
    <view class="bg-white rounded-[16px] overflow-hidden mb-6 shadow-sm">
      <view class="bg-gradient-to-r from-#4e54c8 to-#8f94fb p-5 text-white relative">
        <view class="flex items-center text-[18px] font-semibold mb-1">
          <wd-icon name="home" class="mr-2" />
          <text>16号楼 508室</text>
        </view>
        <view class="text-[14px] opacity-90">宿舍类型：六人间 | 状态：正常</view>
      </view>

      <view class="p-5">
        <view class="flex mb-4">
          <view class="w-10 h-10 rounded-full bg-rgba(78,84,200,0.1) flex-center mr-3">
            <wd-icon name="usergroup" class="text-#4e54c8 text-[18px]" />
          </view>
          <view class="flex-1">
            <view class="text-[14px] text-#666 mb-1">入住人数</view>
            <view class="text-[16px] font-medium text-#333">6/6人</view>
          </view>
        </view>

        <view class="flex mb-4">
          <view class="w-10 h-10 rounded-full bg-rgba(78,84,200,0.1) flex-center mr-3">
            <wd-icon name="location" class="text-#4e54c8 text-[18px]" />
          </view>
          <view class="flex-1">
            <view class="text-[14px] text-#666 mb-1">宿舍地址</view>
            <view class="text-[16px] font-medium text-#333">学生生活区东区16号楼5层508室</view>
          </view>
        </view>

        <view class="flex mb-4">
          <view class="w-10 h-10 rounded-full bg-rgba(78,84,200,0.1) flex-center mr-3">
            <wd-icon name="user-circle" class="text-#4e54c8 text-[18px]" />
          </view>
          <view class="flex-1">
            <view class="text-[14px] text-#666 mb-1">宿管信息</view>
            <view class="text-[16px] font-medium text-#333">李老师 (联系电话: 136****2580)</view>
          </view>
        </view>

        <view class="flex mb-4">
          <view class="w-10 h-10 rounded-full bg-rgba(78,84,200,0.1) flex-center mr-3">
            <wd-icon name="check" class="text-#4e54c8 text-[18px]" />
          </view>
          <view class="flex-1">
            <view class="text-[14px] text-#666 mb-1">电费余额</view>
            <view class="text-[16px] font-medium text-#333">87.65 度 （预计可用8天）</view>
          </view>
        </view>

        <view class="flex mb-4">
          <view class="w-10 h-10 rounded-full bg-rgba(78,84,200,0.1) flex-center mr-3">
            <wd-icon name="close" class="text-#4e54c8 text-[18px]" />
          </view>
          <view class="flex-1">
            <view class="text-[14px] text-#666 mb-1">水费余额</view>
            <view class="text-[16px] font-medium text-#333">25.12 吨 （预计可用12天）</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 通知公告 -->
    <view class="bg-white rounded-[16px] p-4 mb-4 shadow-sm">
      <view class="flex items-center text-[16px] font-semibold mb-3">
        <wd-icon name="notification" class="text-#f44336 mr-2" />
        <text>宿舍通知</text>
      </view>
      <view class="text-[14px] text-#666 leading-normal">
        <view class="mb-2">1. 5月20日至5月25日期间将进行宿舍安全检查，请保持宿舍整洁。</view>
        <view class="mb-2">2. 暑假离校时间为7月1日至7月5日，请提前安排行程并做好离校准备。</view>
        <view>3. 近期天气炎热，请注意用电安全，离开宿舍请关闭电源。</view>
      </view>
    </view>

    <!-- 室友信息 -->
    <view class="flex items-center text-[17px] font-semibold my-7">
      <wd-icon name="usergroup" class="text-#4e54c8 mr-2" />
      <text>室友信息</text>
    </view>

    <view class="bg-white rounded-[16px] overflow-hidden mb-6 shadow-sm">
      <view class="p-4">
        <view class="py-3 border-b border-#f0f0f0 flex items-center">
          <view class="w-[50px] h-[50px] rounded-full bg-#f0f0f0 flex-center mr-4">
            <wd-icon name="user" class="text-#aaa text-[20px]" />
          </view>
          <view class="flex-1">
            <view class="text-[16px] font-medium mb-1">张三（本人）</view>
            <view class="text-[13px] text-#666 flex items-center">
              <text class="mr-3">计应2201班</text>
              <text>床位: 上铺1号</text>
            </view>
          </view>
        </view>

        <view class="py-3 border-b border-#f0f0f0 flex items-center">
          <view class="w-[50px] h-[50px] rounded-full bg-#f0f0f0 flex-center mr-4">
            <wd-icon name="user" class="text-#aaa text-[20px]" />
          </view>
          <view class="flex-1">
            <view class="text-[16px] font-medium mb-1">李四</view>
            <view class="text-[13px] text-#666 flex items-center">
              <text class="mr-3">计应2201班</text>
              <text>床位: 上铺2号</text>
            </view>
          </view>
          <view class="w-10 h-10 rounded-full bg-rgba(78,84,200,0.1) flex-center text-#4e54c8">
            <wd-icon name="chat" class="text-[16px]" />
          </view>
        </view>

        <view class="py-3 border-b border-#f0f0f0 flex items-center">
          <view class="w-[50px] h-[50px] rounded-full bg-#f0f0f0 flex-center mr-4">
            <wd-icon name="user" class="text-#aaa text-[20px]" />
          </view>
          <view class="flex-1">
            <view class="text-[16px] font-medium mb-1">王五</view>
            <view class="text-[13px] text-#666 flex items-center">
              <text class="mr-3">计应2201班</text>
              <text>床位: 上铺3号</text>
            </view>
          </view>
          <view class="w-10 h-10 rounded-full bg-rgba(78,84,200,0.1) flex-center text-#4e54c8">
            <wd-icon name="chat" class="text-[16px]" />
          </view>
        </view>

        <view class="py-3 border-b border-#f0f0f0 flex items-center">
          <view class="w-[50px] h-[50px] rounded-full bg-#f0f0f0 flex-center mr-4">
            <wd-icon name="user" class="text-#aaa text-[20px]" />
          </view>
          <view class="flex-1">
            <view class="text-[16px] font-medium mb-1">赵六</view>
            <view class="text-[13px] text-#666 flex items-center">
              <text class="mr-3">计应2201班</text>
              <text>床位: 下铺1号</text>
            </view>
          </view>
          <view class="w-10 h-10 rounded-full bg-rgba(78,84,200,0.1) flex-center text-#4e54c8">
            <wd-icon name="chat" class="text-[16px]" />
          </view>
        </view>

        <view class="py-3 border-b border-#f0f0f0 flex items-center">
          <view class="w-[50px] h-[50px] rounded-full bg-#f0f0f0 flex-center mr-4">
            <wd-icon name="user" class="text-#aaa text-[20px]" />
          </view>
          <view class="flex-1">
            <view class="text-[16px] font-medium mb-1">孙七</view>
            <view class="text-[13px] text-#666 flex items-center">
              <text class="mr-3">计应2201班</text>
              <text>床位: 下铺2号</text>
            </view>
          </view>
          <view class="w-10 h-10 rounded-full bg-rgba(78,84,200,0.1) flex-center text-#4e54c8">
            <wd-icon name="chat" class="text-[16px]" />
          </view>
        </view>

        <view class="py-3 flex items-center">
          <view class="w-[50px] h-[50px] rounded-full bg-#f0f0f0 flex-center mr-4">
            <wd-icon name="user" class="text-#aaa text-[20px]" />
          </view>
          <view class="flex-1">
            <view class="text-[16px] font-medium mb-1">钱八</view>
            <view class="text-[13px] text-#666 flex items-center">
              <text class="mr-3">计应2201班</text>
              <text>床位: 下铺3号</text>
            </view>
          </view>
          <view class="w-10 h-10 rounded-full bg-rgba(78,84,200,0.1) flex-center text-#4e54c8">
            <wd-icon name="chat" class="text-[16px]" />
          </view>
        </view>
      </view>
    </view>

    <!-- 宿舍设施 -->
    <view class="flex items-center text-[17px] font-semibold my-7">
      <wd-icon name="computer" class="text-#4e54c8 mr-2" />
      <text>宿舍设施</text>
    </view>

    <view class="bg-white rounded-[16px] overflow-hidden mb-6 shadow-sm">
      <view class="grid grid-cols-3 gap-3 p-4">
        <view class="bg-rgba(78,84,200,0.05) rounded-[12px] p-4 flex-col flex-center text-center">
          <wd-icon name="refresh" class="text-#4e54c8 text-[24px] mb-2" />
          <view class="text-[13px] font-medium mb-1">空调</view>
          <view class="text-[12px] text-#4caf50">正常</view>
        </view>

        <view class="bg-rgba(78,84,200,0.05) rounded-[12px] p-4 flex-col flex-center text-center">
          <wd-icon name="time" class="text-#4e54c8 text-[24px] mb-2" />
          <view class="text-[13px] font-medium mb-1">热水器</view>
          <view class="text-[12px] text-#4caf50">正常</view>
        </view>

        <view class="bg-rgba(78,84,200,0.05) rounded-[12px] p-4 flex-col flex-center text-center">
          <wd-icon name="wifi-error" class="text-#4e54c8 text-[24px] mb-2" />
          <view class="text-[13px] font-medium mb-1">网络</view>
          <view class="text-[12px] text-#4caf50">正常</view>
        </view>

        <view class="bg-rgba(78,84,200,0.05) rounded-[12px] p-4 flex-col flex-center text-center">
          <wd-icon name="view" class="text-#4e54c8 text-[24px] mb-2" />
          <view class="text-[13px] font-medium mb-1">照明</view>
          <view class="text-[12px] text-#ffa000">灯管老化</view>
        </view>

        <view class="bg-rgba(78,84,200,0.05) rounded-[12px] p-4 flex-col flex-center text-center">
          <wd-icon name="lock-on" class="text-#4e54c8 text-[24px] mb-2" />
          <view class="text-[13px] font-medium mb-1">门锁</view>
          <view class="text-[12px] text-#4caf50">正常</view>
        </view>

        <view class="bg-rgba(78,84,200,0.05) rounded-[12px] p-4 flex-col flex-center text-center">
          <wd-icon name="refresh" class="text-#4e54c8 text-[24px] mb-2" />
          <view class="text-[13px] font-medium mb-1">风扇</view>
          <view class="text-[12px] text-#4caf50">正常</view>
        </view>
      </view>
    </view>

    <!-- 报修记录 -->
    <view class="flex items-center text-[17px] font-semibold my-7">
      <wd-icon name="tools" class="text-#4e54c8 mr-2" />
      <text>报修记录</text>
    </view>

    <view class="bg-white rounded-[16px] overflow-hidden mb-6 shadow-sm">
      <view class="p-4">
        <view class="flex justify-between py-3 border-b border-#f0f0f0">
          <view>
            <view class="text-[15px] font-medium mb-1">卫生间水龙头漏水</view>
            <view class="text-[13px] text-#666">报修时间：2024-05-10</view>
          </view>
          <view
            class="py-1 px-[10px] rounded-[12px] text-[12px] font-medium bg-#fff8e1 text-#ffa000 self-start"
          >
            处理中
          </view>
        </view>

        <view class="flex justify-between py-3 border-b border-#f0f0f0">
          <view>
            <view class="text-[15px] font-medium mb-1">寝室门把手松动</view>
            <view class="text-[13px] text-#666">报修时间：2024-04-25</view>
          </view>
          <view
            class="py-1 px-[10px] rounded-[12px] text-[12px] font-medium bg-#e8f5e9 text-#4caf50 self-start"
          >
            已完成
          </view>
        </view>

        <view class="flex justify-between py-3">
          <view>
            <view class="text-[15px] font-medium mb-1">宿舍窗户无法锁紧</view>
            <view class="text-[13px] text-#666">报修时间：2024-03-18</view>
          </view>
          <view
            class="py-1 px-[10px] rounded-[12px] text-[12px] font-medium bg-#e8f5e9 text-#4caf50 self-start"
          >
            已完成
          </view>
        </view>
      </view>

      <!-- 报修按钮 -->
      <view class="px-4 pb-4">
        <button
          class="w-full h-12 bg-gradient-to-r from-#4e54c8 to-#8f94fb text-white rounded-[12px] text-[16px] font-semibold flex-center"
        >
          <wd-icon name="tools" class="mr-2" />
          <text>申请维修</text>
        </button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
// 暂时不需要实现接口和数据逻辑
</script>

<style>
/* 在UnoCSS中添加一些自定义辅助类 */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
