<route lang="json5">
{
  style: {
    navigationBarTitleText: '毕业审核',
  },
}
</route>
<template>
  <view class="graduation-container px-5 pt-4 pb-16">
    <!-- 总体完成情况 -->
    <view class="bg-white rounded-3xl p-5 mb-4">
      <view class="flex items-center justify-between mb-3">
        <view class="text-base font-bold">毕业要求完成进度</view>
        <view class="text-sm text-blue-500">{{ graduationData.graduationYear }}届毕业生</view>
      </view>
      <view class="flex">
        <view class="progress-chart-container">
          <l-echart ref="chartRef" class="progress-chart"></l-echart>
        </view>
        <view class="ml-5 flex flex-col justify-center">
          <view class="text-lg font-bold">{{ graduationData.status }}</view>
          <view class="text-sm text-gray-500 mb-2">
            还有
            <text class="text-red-500 font-medium">{{ graduationData.remainingItems }} 项</text>
            未达标
          </view>
          <view class="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full inline-block">
            距离毕业审核还有 {{ graduationData.daysUntilGraduation }} 天
          </view>
        </view>
      </view>
    </view>

    <!-- 必修课程完成情况 -->
    <view class="bg-white rounded-3xl p-4 mb-4">
      <view class="text-base font-bold mb-3">必修课程完成情况</view>
      <view class="space-y-2">
        <view class="text-sm text-gray-500 mb-2">
          已完成
          <text class="text-green-500 font-medium">{{ courseData.completed }}</text>
          / {{ courseData.total }} 门必修课
        </view>
        <view class="bg-gray-200 h-2 rounded-full overflow-hidden">
          <view
            class="bg-green-500 h-full rounded-full"
            :style="{ width: `${courseData.completionRate}%` }"
          ></view>
        </view>
      </view>

      <view class="mt-4">
        <view class="text-sm font-medium mb-2">未通过课程</view>
        <view class="space-y-2">
          <view
            v-for="(course, index) in courseData.failedCourses"
            :key="index"
            class="course-item"
            :class="course.status === 'failed' ? 'bg-red-50' : 'bg-yellow-50'"
          >
            <view class="flex items-center">
              <view
                class="status-indicator"
                :class="course.status === 'failed' ? 'bg-red-500' : 'bg-yellow-500'"
              ></view>
              <view>
                <view class="font-medium">{{ course.name }}</view>
                <view class="text-xs text-gray-500">
                  学分：{{ course.credit }} | {{ course.statusText }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="mt-4">
        <view class="text-sm font-medium mb-2">未修课程</view>
        <view class="space-y-2">
          <view
            v-for="(course, index) in courseData.notTakenCourses"
            :key="index"
            class="course-item bg-gray-50"
          >
            <view class="flex items-center">
              <view class="status-indicator bg-gray-400"></view>
              <view>
                <view class="font-medium">{{ course.name }}</view>
                <view class="text-xs text-gray-500">
                  学分：{{ course.credit }} | 状态：{{ course.statusText }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 其他毕业要求 -->
    <view class="bg-white rounded-3xl p-4 mb-4">
      <view class="text-base font-bold mb-3">其他毕业要求</view>
      <view class="divide-y">
        <view
          v-for="(requirement, index) in otherRequirements"
          :key="index"
          class="py-3 flex items-center"
        >
          <view class="flex-1">
            <view class="font-medium">{{ requirement.name }}</view>
            <view class="text-xs text-gray-500">要求：{{ requirement.requirement }}</view>
          </view>
          <view class="flex items-center">
            <view class="text-sm mr-2" :class="getStatusClass(requirement.status)">
              {{ getStatusText(requirement.status) }}
            </view>
            <view
              class="w-5 h-5 rounded-full flex items-center justify-center text-white"
              :class="getStatusBgClass(requirement.status)"
            >
              <wd-icon :name="getStatusIcon(requirement.status)" size="12px" />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 建议与指导 -->
    <view class="bg-white rounded-3xl p-4">
      <view class="text-base font-bold mb-3">建议与指导</view>
      <view class="space-y-3">
        <view v-for="(advice, index) in adviceList" :key="index" class="flex items-start">
          <view :class="`text-${advice.color}-500 mr-2 mt-1`">
            <wd-icon :name="advice.icon" size="16px" />
          </view>
          <view class="text-sm">
            <view class="font-medium">{{ advice.title }}</view>
            <view class="text-gray-500">{{ advice.content }}</view>
          </view>
        </view>
      </view>
      <wd-button type="primary" block size="large" class="mt-4 rounded-xl" @click="contactAdvisor">
        联系学业导师咨询
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useToast } from 'wot-design-uni'
import * as echarts from 'echarts'

const toast = useToast()
const chartRef = ref()

// 毕业审核基本数据
const graduationData = ref({
  graduationYear: 2024,
  progressPercentage: 85,
  status: '预计可以毕业',
  remainingItems: 2,
  daysUntilGraduation: 145,
})

// 课程完成情况
const courseData = ref({
  completed: 32,
  total: 35,
  completionRate: 91,
  failedCourses: [
    {
      name: '高等数学(下)',
      credit: '4.0',
      score: 55,
      status: 'failed',
      statusText: '成绩：55分(未通过)',
    },
    {
      name: '大学物理',
      credit: '3.0',
      status: 'absent',
      statusText: '成绩：缺考(未通过)',
    },
  ],
  notTakenCourses: [
    {
      name: '就业指导',
      credit: '1.0',
      status: 'notSelected',
      statusText: '未选课',
    },
  ],
})

// 初始化进度图表
onMounted(() => {
  setTimeout(async () => {
    if (!chartRef.value) return
    const myChart = await chartRef.value.init(echarts)
    const option = {
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}%',
        confine: true,
      },
      grid: {
        left: 0,
        right: 0,
        bottom: 0,
        top: 0,
        containLabel: true,
      },
      series: [
        {
          name: '毕业进度',
          type: 'pie',
          radius: ['70%', '90%'],
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: false, // 不显示强调文本，保持中心的百分比显示
            },
          },
          labelLine: {
            show: false,
          },
          data: [
            {
              value: graduationData.value.progressPercentage,
              name: '已完成',
              itemStyle: { color: '#4CAF50' },
            },
            {
              value: 100 - graduationData.value.progressPercentage,
              name: '未完成',
              itemStyle: { color: '#f3f4f6' },
            },
          ],
          animation: false,
        },
        {
          name: '进度',
          type: 'pie',
          radius: ['0', '50%'],
          avoidLabelOverlap: false,
          label: {
            show: true,
            position: 'center',
            fontSize: '24',
            fontWeight: 'bold',
            formatter: graduationData.value.progressPercentage + '%',
            color: '#4CAF50',
          },
          emphasis: {
            label: {
              show: true,
            },
          },
          labelLine: {
            show: false,
          },
          data: [{ value: 1, name: '进度', itemStyle: { color: '#ffffff' } }],
          animation: false,
        },
      ],
    }
    myChart.setOption(option)
  }, 300)
})

// 其他毕业要求
const otherRequirements = ref([
  {
    name: '英语四级',
    requirement: '425分及以上',
    status: 'completed',
  },
  {
    name: '专业实习',
    requirement: '至少完成8周专业实习',
    status: 'completed',
  },
  {
    name: '创新学分',
    requirement: '至少获得2个创新学分',
    status: 'failed',
  },
  {
    name: '毕业论文',
    requirement: '完成并通过毕业论文答辩',
    status: 'inProgress',
  },
])

// 建议与指导
const adviceList = ref([
  {
    title: '关于未通过课程',
    content: '建议在2024年春季学期重修「高等数学(下)」和「大学物理」，请联系教务处安排。',
    icon: 'error-circle',
    color: 'red',
  },
  {
    title: '关于创新学分',
    content:
      '可以通过参加学科竞赛、发表论文或参与创新项目获取。学校将于3月举办创新创业大赛，建议报名参加。',
    icon: 'info-circle',
    color: 'yellow',
  },
  {
    title: '毕业时间节点',
    content: '请在2024年5月30日前完成所有毕业要求，6月进行毕业资格审核，7月10日毕业典礼。',
    icon: 'help-circle',
    color: 'blue',
  },
])

// 获取状态对应的文本
const getStatusText = (status: string): string => {
  switch (status) {
    case 'completed':
      return '已达标'
    case 'failed':
      return '未达标'
    case 'inProgress':
      return '进行中'
    default:
      return ''
  }
}

// 获取状态对应的CSS类
const getStatusClass = (status: string): string => {
  switch (status) {
    case 'completed':
      return 'text-green-500'
    case 'failed':
      return 'text-red-500'
    case 'inProgress':
      return 'text-gray-500'
    default:
      return ''
  }
}

// 获取状态对应的背景CSS类
const getStatusBgClass = (status: string): string => {
  switch (status) {
    case 'completed':
      return 'bg-green-500'
    case 'failed':
      return 'bg-red-500'
    case 'inProgress':
      return 'bg-yellow-500'
    default:
      return ''
  }
}

// 获取状态对应的图标
const getStatusIcon = (status: string): string => {
  switch (status) {
    case 'completed':
      return 'check'
    case 'failed':
      return 'close'
    case 'inProgress':
      return 'time'
    default:
      return ''
  }
}

// 联系学业导师
const contactAdvisor = (): void => {
  toast.show('联系功能即将上线，请稍后再试')
}
</script>

<style lang="scss" scoped>
.graduation-container {
  min-height: 100vh;
  background-color: #f7f7f7;
}

.progress-chart-container {
  width: 120px;
  height: 120px;
}

.progress-chart {
  width: 100%;
  height: 100%;
}

.status-indicator {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  margin-right: 12px;
  border-radius: 50%;
}

.course-item {
  padding: 12px;
  margin-bottom: 8px;
  border-radius: 10px;
}
</style>
