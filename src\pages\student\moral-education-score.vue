<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的德育分',
  },
}
</route>
<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import * as echarts from 'echarts'
import { getMoralEdu, getMoralEduRecord } from '@/service/student'
import type { MoralEduItem, MoralEduRecordQuery, MoralEduRecordResponse } from '@/types/student'
import SemesterWeekPicker from '@/components/SemesterWeekPicker/index.vue'
import type { SemesterOption } from '@/types/semester'
import { useUserStore } from '@/store/user'

// 获取用户store
const userStore = useUserStore()

interface SemesterItem {
  id: string
  name: string
  isActive: boolean
}

interface ScoreDetail {
  id: number
  type: 'add' | 'sub'
  icon: string
  title: string
  detail: string
  time: string
  score: number
}

interface RankItem {
  id: number
  rank: number
  name: string
  className: string
  score: number
  avatar: string
  isCurrentUser: boolean
}

// 当前选中的学期
const selectedSemester = ref<string>('')
const currentSemesterLabel = ref<string>('')

// 加载状态
const loading = ref<boolean>(false)
const loadingRecords = ref<boolean>(false)

// 德育分明细 - 将使用API数据替换
const scoreDetailList = ref<ScoreDetail[]>([])

// 实际德育记录数据
const moralEduRecords = ref<any[]>([])

// 班级排行榜
const rankList = ref<RankItem[]>([
  {
    id: 1,
    rank: 1,
    name: '张三',
    className: '计算机科学2101',
    score: 98,
    avatar: 'https://via.placeholder.com/80?text=张三',
    isCurrentUser: false,
  },
  {
    id: 2,
    rank: 2,
    name: '李四',
    className: '计算机科学2101',
    score: 97,
    avatar: 'https://via.placeholder.com/80?text=李四',
    isCurrentUser: false,
  },
  {
    id: 3,
    rank: 3,
    name: '王五',
    className: '计算机科学2101',
    score: 96,
    avatar: 'https://via.placeholder.com/80?text=王五',
    isCurrentUser: false,
  },
  {
    id: 4,
    rank: 8,
    name: '王小明（我）',
    className: '计算机科学2101',
    score: 95,
    avatar: 'https://via.placeholder.com/80?text=我',
    isCurrentUser: true,
  },
])

// 分段控制器选项
const tabs = ref([
  { name: '德育分明细', value: 0 },
  { name: '班级排行', value: 1 },
  { name: '评分规则', value: 2 },
])
const activeTab = ref(0)

// 德育数据
const moralEduData = ref<MoralEduItem | null>(null)

// 学生信息 - 使用计算属性从userStore获取基本信息，同时添加德育相关信息
const studentInfo = computed(() => {
  // 计算加分和减分总数
  let addScore = 0
  let subScore = 0

  moralEduRecords.value.forEach((record) => {
    const score = parseFloat(record.dyfz || '0')
    if (score >= 0) {
      addScore += score
    } else {
      subScore += Math.abs(score)
    }
  })

  return {
    name: userStore.userInfo.realname || '',
    class: userStore.userInfo.className || '计算机科学与技术 2101班',
    studentId: userStore.userInfo.username || '',
    avatar: userStore.userInfo.avatar || 'https://via.placeholder.com/80',
    totalScore: moralEduData.value?.moralEducationScore || 0,
    addScore,
    subScore,
    classRank: moralEduData.value?.moralEducationRank || 0,
    majorRank: moralEduData.value?.comprehensiveEvaluationRank || 0,
  }
})

// 圆环进度图表
const chartRef = ref()

// 处理学期变更
const handleSemesterChange = (semesterInfo: { label: string; value: string }) => {
  currentSemesterLabel.value = semesterInfo.label
  selectedSemester.value = semesterInfo.value

  // 切换学期时先清空现有数据
  clearMoralEduData()

  // 然后重新获取数据
  fetchMoralEduData()
}

// 清空德育数据
const clearMoralEduData = () => {
  moralEduData.value = null
  moralEduRecords.value = []
  scoreDetailList.value = []

  // 清空图表
  if (chartRef.value) {
    chartRef.value.clear && chartRef.value.clear()
  }
}

// 获取德育分数据
const fetchMoralEduData = async () => {
  loading.value = true
  try {
    const params = {
      page: 1,
      pageSize: 10,
      sortBy: 'id',
      sortOrder: 'desc' as 'asc' | 'desc',
      semesters: selectedSemester.value
        ? [selectedSemester.value.replace(/^(\d{4}-\d{4})-(\d)$/, '$1|$2')]
        : [],
      selfRatingScore: '',
      mutualScore: '',
      activityScore: '',
      detailScore: '',
      moralEducationRank: '',
      gradeScore: '',
      gradeRank: '',
      comprehensiveEvaluationScore: '',
      comprehensiveEvaluationRank: '',
      moralEducationScore: '',
    }

    const res = await getMoralEdu(params)

    if (res.items && res.items.length > 0) {
      moralEduData.value = res.items[0]
      updateChart()
      // 获取德育分详情
      fetchMoralEduRecords()
    } else {
      // 没有数据，确保清空所有相关数据
      clearMoralEduData()
      uni.showToast({
        title: '暂无德育分数据',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('获取德育分数据失败:', error)
    // 出错时也清空数据
    clearMoralEduData()
    uni.showToast({
      title: '获取德育分数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 获取德育分记录详情
const fetchMoralEduRecords = async () => {
  loadingRecords.value = true
  try {
    const params: MoralEduRecordQuery = {
      page: 1,
      pageSize: 50, // 获取更多记录
      sortBy: 'djsj', // 按登记时间排序
      sortOrder: 'desc',
      semesters: selectedSemester.value
        ? selectedSemester.value.replace(/^(\d{4}-\d{4})-(\d)$/, '$1|$2')
        : '',
    }

    const res: any = await getMoralEduRecord(params)

    // 先清空现有记录，避免显示旧数据
    moralEduRecords.value = []
    scoreDetailList.value = []

    // 检查返回的数据结构
    if (res.items && res.items.length > 0) {
      moralEduRecords.value = res.items

      // 将API返回的记录数据转换为页面所需的格式
      scoreDetailList.value = res.items.map((item: any, index: number) => {
        // 判断是加分还是减分（假设dyfz字段是带符号的数字）
        const score = parseFloat(item.dyfz)
        const type = score >= 0 ? 'add' : 'sub'

        // 根据项目名称选择合适的图标
        const icon = getIconByProjectName(item.dyxmmc, type)

        return {
          id: item.id || index + 1,
          type,
          icon,
          title: item.dyxmmc || '德育分记录',
          detail: item.remark || '',
          time: item.djsj || '',
          score: Math.abs(score) || 0,
        }
      })
    } else {
      // 如果没有记录或返回格式不符，确保列表为空
      moralEduRecords.value = []
      scoreDetailList.value = []

      // 如果返回成功但没有数据，显示正常提示
      if (!res.items) {
        uni.showToast({
          title: res.msg || '德育分记录为空',
          icon: 'none',
        })
      }
    }
  } catch (error) {
    console.error('获取德育分记录失败:', error)
    // 出错时清空数据
    moralEduRecords.value = []
    scoreDetailList.value = []
    uni.showToast({
      title: '获取德育分记录失败',
      icon: 'none',
    })
  } finally {
    loadingRecords.value = false
  }
}

// 根据德育项目名称和加减分类型获取合适的图标
const getIconByProjectName = (projectName: string, scoreType: 'add' | 'sub') => {
  // 通过项目名称匹配合适的图标
  if (!projectName) return scoreType === 'add' ? 'check-circle' : 'close-circle'

  // 项目名称关键词匹配表
  const keywordMap: Record<string, string> = {
    // 德育加分类型
    获奖: 'award',
    比赛: 'award',
    荣誉: 'award',
    志愿: 'hand-holding-heart',
    服务: 'hand-holding-heart',
    公益: 'hand-holding-heart',
    班干: 'users',
    干部: 'users',
    学术: 'book-reader',
    报告: 'book-reader',
    论文: 'book-reader',
    阅读: 'book-reader',
    早晚自习: 'check',
    满勤: 'check',
    考勤: 'check',
    运动: 'running',
    体育: 'running',
    竞赛: 'running',

    // 德育减分类型
    迟到: 'clock',
    旷课: 'clock',
    缺勤: 'clock',
    违纪: 'mobile-alt',
    违反: 'mobile-alt',
    处分: 'mobile-alt',
    宿舍: 'bed',
    卫生: 'bed',
  }

  // 遍历关键词匹配表，查找项目名称中包含的关键词
  for (const [keyword, icon] of Object.entries(keywordMap)) {
    if (projectName.includes(keyword)) {
      return icon
    }
  }

  // 默认图标
  return scoreType === 'add' ? 'check-circle' : 'close-circle'
}

// 更新图表
const updateChart = () => {
  setTimeout(async () => {
    if (!chartRef.value || !moralEduData.value) return

    // 先销毁现有图表实例
    if (chartRef.value.clear) {
      chartRef.value.clear()
    }

    // 然后创建新图表
    const myChart = await chartRef.value.init(echarts)
    const option = {
      series: [
        {
          type: 'gauge',
          radius: '100%',
          startAngle: 90,
          endAngle: -270,
          pointer: {
            show: false,
          },
          progress: {
            show: true,
            overlap: false,
            roundCap: true,
            clip: false,
            itemStyle: {
              color: '#1989fa',
            },
          },
          axisLine: {
            lineStyle: {
              width: 10,
              color: [[1, '#f0f0f0']],
            },
          },
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            show: false,
          },
          data: [
            {
              value: moralEduData.value.moralEducationScore || 0,
              name: '',
              title: {
                offsetCenter: ['0%', '0%'],
              },
              detail: {
                offsetCenter: ['0%', '0%'],
              },
            },
          ],
          title: {
            fontSize: 12,
            color: '#999',
            offsetCenter: [0, '30%'],
          },
          detail: {
            width: 50,
            height: 14,
            fontSize: 28,
            fontWeight: 'bold',
            color: '#1989fa',
            formatter: '{value}',
          },
        },
      ],
    }
    myChart.setOption(option)
  }, 300)
}

onMounted(() => {
  // 组件挂载时不再主动请求数据
  // SemesterWeekPicker组件会在挂载时自动初始化学期数据
  // 当选择学期时，会触发handleSemesterChange函数
})

// 切换标签页
const handleTabChange = (value: number) => {
  activeTab.value = value
}
</script>

<template>
  <view class="moral-education-container">
    <!-- 学期选择 -->
    <view class="semester-picker-container mb-4">
      <SemesterWeekPicker
        v-model:semesterValue="selectedSemester"
        :show-week-label="false"
        :show-week="false"
        :show-all-week="false"
        :show-all-semester="false"
        size="large"
        @semesterChange="handleSemesterChange"
      />
    </view>

    <!-- 加载中 -->
    <view v-if="loading" class="loading-container">
      <wd-loading color="#1989fa" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 无数据提示 -->
    <view v-else-if="!moralEduData" class="empty-container">
      <wd-icon name="close-bold" size="60px" color="#999" />
      <text class="empty-text">暂无德育数据</text>
    </view>

    <!-- 有数据展示 -->
    <template v-else>
      <!-- 德育分概览卡片 -->
      <view class="overview-card">
        <view class="card-header">
          <text class="card-title">德育分概览</text>
          <view class="badge">当前学期</view>
        </view>
        <view class="card-content">
          <!-- 学生基本信息 -->
          <view class="student-info">
            <image :src="studentInfo.avatar" class="student-avatar" />
            <view class="student-detail">
              <text class="student-name">{{ studentInfo.name }}</text>
              <text class="student-class">{{ studentInfo.class }}</text>
              <text class="student-id">学号: {{ studentInfo.studentId }}</text>
            </view>
          </view>

          <!-- 分数统计 -->
          <view class="score-stats">
            <!-- 圆环进度图 -->
            <view class="circle-progress">
              <l-echart ref="chartRef" class="chart"></l-echart>
            </view>

            <!-- 统计数据 -->
            <view class="stats-grid">
              <view class="stats-item">
                <text class="stats-value add-score">+{{ studentInfo.addScore }}</text>
                <text class="stats-label">加分</text>
              </view>
              <view class="stats-item">
                <text class="stats-value sub-score">-{{ studentInfo.subScore }}</text>
                <text class="stats-label">减分</text>
              </view>
              <view class="stats-item">
                <text class="stats-value">{{ studentInfo.classRank }}</text>
                <text class="stats-label">班级排名</text>
              </view>
              <view class="stats-item">
                <text class="stats-value">{{ studentInfo.majorRank }}</text>
                <text class="stats-label">专业排名</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 分段控制器 -->
      <view class="custom-tabs">
        <view class="tabs-header">
          <view
            v-for="tab in tabs"
            :key="tab.value"
            class="tab-item"
            :class="{ active: activeTab === tab.value }"
            @click="handleTabChange(tab.value)"
          >
            {{ tab.name }}
          </view>
        </view>
      </view>

      <!-- 标签页内容 -->
      <view v-if="activeTab === 0" class="tab-content">
        <!-- 德育分数据展示 -->
        <view class="moral-education-data">
          <view class="data-row">
            <text class="data-label">自评分</text>
            <text class="data-value">{{ moralEduData.selfRatingScore }}</text>
          </view>
          <view class="data-row">
            <text class="data-label">互评分</text>
            <text class="data-value">{{ moralEduData.mutualScore }}</text>
          </view>
          <view class="data-row">
            <text class="data-label">活动分</text>
            <text class="data-value">{{ moralEduData.activityScore }}</text>
          </view>
          <view class="data-row">
            <text class="data-label">明细分</text>
            <text class="data-value">{{ moralEduData.detailScore }}</text>
          </view>
          <view class="data-row">
            <text class="data-label">德育补偿分</text>
            <text class="data-value">{{ moralEduData.moralEducationCompensateScore }}</text>
          </view>
          <view class="data-row">
            <text class="data-label">学业分</text>
            <text class="data-value">{{ moralEduData.gradeScore }}</text>
          </view>
          <view class="data-row">
            <text class="data-label">综测分</text>
            <text class="data-value">{{ moralEduData.comprehensiveEvaluationScore }}</text>
          </view>
        </view>

        <!-- 德育分明细 -->
        <view class="section-title mt-30">德育分明细</view>

        <!-- 加载中 -->
        <view v-if="loadingRecords" class="loading-container py-4">
          <wd-loading color="#1989fa" />
          <text class="loading-text">加载明细中...</text>
        </view>

        <!-- 无明细数据提示 -->
        <view v-else-if="scoreDetailList.length === 0" class="empty-container py-4">
          <wd-icon name="info-circle" size="60px" color="#999" />
          <text class="empty-text">暂无德育分明细记录</text>
        </view>

        <!-- 明细列表 -->
        <view v-else class="score-detail-list">
          <view v-for="item in scoreDetailList" :key="item.id" class="score-detail-item">
            <view :class="['icon-container', item.type]">
              <wd-icon :name="item.icon" />
            </view>
            <view class="detail-content">
              <text class="detail-title">{{ item.title }}</text>
              <text class="detail-desc">{{ item.detail }}</text>
              <text class="detail-time">{{ item.time }}</text>
            </view>
            <text :class="['detail-score', item.type]">
              {{ item.type === 'add' ? '+' : '-' }}{{ item.score }}
            </text>
          </view>
        </view>
      </view>

      <view v-if="activeTab === 1" class="tab-content">
        <view class="section-title">班级德育分排行</view>

        <!-- 班级排行榜 -->
        <view class="rank-list">
          <view
            v-for="item in rankList"
            :key="item.id"
            class="rank-item"
            :class="{ 'current-user': item.isCurrentUser }"
          >
            <view :class="['rank-number', { [`top${item.rank}`]: item.rank <= 3 }]">
              {{ item.rank }}
            </view>
            <image :src="item.avatar" class="rank-avatar" />
            <view class="rank-info">
              <text class="rank-name">{{ item.name }}</text>
              <text class="rank-class">{{ item.className }}</text>
            </view>
            <text class="rank-score">{{ item.score }}分</text>
          </view>
        </view>
      </view>

      <view v-if="activeTab === 2" class="tab-content">
        <view class="section-title">德育分评分规则</view>
        <view class="rules-content">
          <text class="rules-desc">
            德育分是衡量学生综合素质的重要指标，根据学校德育评分标准，每学期初始分为85分，通过参与各类活动可获得加分，违反校规校纪将被扣分。
          </text>

          <view class="rules-section">
            <text class="rules-section-title">一、加分项目</text>
            <view class="rules-list">
              <view class="rules-item">
                <text class="rules-item-title">1. 参加校级及以上比赛获奖：</text>
                <text class="rules-item-desc">
                  一等奖加8分，二等奖加5分，三等奖加3分，优秀奖加1分
                </text>
              </view>
              <view class="rules-item">
                <text class="rules-item-title">2. 参加志愿服务活动：</text>
                <text class="rules-item-desc">每累计4小时加1分，单学期最高加5分</text>
              </view>
              <view class="rules-item">
                <text class="rules-item-title">3. 担任学生干部：</text>
                <text class="rules-item-desc">学期考核优秀加3分，良好加2分，合格加1分</text>
              </view>
            </view>
          </view>

          <view class="rules-section">
            <text class="rules-section-title">二、减分项目</text>
            <view class="rules-list">
              <view class="rules-item">
                <text class="rules-item-title">1. 违反课堂纪律：</text>
                <text class="rules-item-desc">
                  迟到每次减1分，旷课每次减2分，课堂使用手机每次减2分
                </text>
              </view>
              <view class="rules-item">
                <text class="rules-item-title">2. 宿舍管理：</text>
                <text class="rules-item-desc">
                  宿舍卫生检查不合格每次减2分，违反作息制度每次减3分
                </text>
              </view>
              <view class="rules-item">
                <text class="rules-item-title">3. 违反校规校纪：</text>
                <text class="rules-item-desc">视情节轻重减5-10分，情节严重者可直接记为0分</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </template>
  </view>
</template>

<style scoped lang="scss">
.moral-education-container {
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
  padding: 30rpx;
  overflow-x: hidden;
  background-color: #f5f5f5;
}

.semester-picker-container {
  margin-bottom: 20rpx;
}

:deep(.picker-container) {
  width: 440rpx;
}

:deep(.semester-select) {
  background-color: #fff !important;
}

// 保留原有样式但不使用
.semester-tabs {
  display: none; // 隐藏原有的学期选项卡
  flex-wrap: wrap;
  margin-bottom: 30rpx;
}

.semester-item {
  display: inline-block;
  padding: 16rpx 32rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #666;
  border: 1px solid #e0e0e0;
  border-radius: 40rpx;
  transition: all 0.2s;

  &.active {
    color: #fff;
    background-color: #1989fa;
    border-color: #1989fa;
  }
}

.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  margin: 30rpx 0;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.loading-text,
.empty-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

.overview-card {
  margin-bottom: 30rpx;
  overflow: hidden;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1px solid #eee;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
}

.badge {
  display: inline-block;
  padding: 4rpx 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #fff;
  background-color: #1989fa;
  border-radius: 24rpx;
}

.card-content {
  padding: 30rpx;
}

.student-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.student-avatar {
  width: 120rpx;
  height: 120rpx;
  margin-right: 30rpx;
  border-radius: 60rpx;
}

.student-detail {
  display: flex;
  flex-direction: column;
}

.student-name {
  margin-bottom: 8rpx;
  font-size: 36rpx;
  font-weight: 600;
}

.student-class,
.student-id {
  margin-bottom: 8rpx;
  font-size: 26rpx;
  color: #666;
}

.score-stats {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.circle-progress {
  position: relative;
  width: 200rpx;
  height: 200rpx;
}

.chart {
  width: 100%;
  height: 100%;
}

.circle-text {
  position: absolute;
  top: 50%;
  left: 50%;
  text-align: center;
  transform: translate(-50%, -50%);
}

.circle-value {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  line-height: 1;
  color: #1989fa;
}

.circle-label {
  display: block;
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #999;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 30rpx;
}

.stats-item {
  text-align: center;
}

.stats-value {
  display: block;
  margin-bottom: 8rpx;
  font-size: 36rpx;
  font-weight: 600;

  &.add-score {
    color: #4caf50;
  }

  &.sub-score {
    color: #f44336;
  }
}

.stats-label {
  font-size: 24rpx;
  color: #999;
}

.section-title {
  margin: 30rpx 0 20rpx;
  font-size: 32rpx;
  font-weight: 600;

  &.mt-30 {
    margin-top: 60rpx;
  }
}

.tab-content {
  box-sizing: border-box;
  width: 100%;
  padding-bottom: 50rpx;
  overflow-x: hidden;
}

.moral-education-data {
  box-sizing: border-box;
  width: 100%;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.data-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }
}

.data-label {
  font-size: 28rpx;
  color: #666;
}

.data-value {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.score-detail-list {
  overflow: hidden;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.score-detail-item {
  display: flex;
  align-items: flex-start;
  padding: 30rpx;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }
}

.icon-container {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  border-radius: 40rpx;

  &.add {
    color: #4caf50;
    background-color: #e8f5e9;
  }

  &.sub {
    color: #f44336;
    background-color: #ffebee;
  }
}

.detail-content {
  flex: 1;
}

.detail-title {
  display: block;
  margin-bottom: 8rpx;
  font-size: 30rpx;
  font-weight: 500;
}

.detail-desc {
  display: block;
  font-size: 26rpx;
  line-height: 1.4;
  color: #666;
}

.detail-time {
  display: block;
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #999;
}

.detail-score {
  margin-left: 24rpx;
  font-size: 32rpx;
  font-weight: 600;

  &.add {
    color: #4caf50;
  }

  &.sub {
    color: #f44336;
  }
}

.rank-list {
  overflow: hidden;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.rank-item {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }

  &.current-user {
    background-color: #f0f7ff;
  }
}

.rank-number {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  margin-right: 24rpx;
  font-weight: 600;

  &.top1,
  &.top2,
  &.top3 {
    color: transparent;

    &::before {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      font-size: 28rpx;
      color: white;
      content: attr(class);
      border-radius: 50%;
    }
  }

  &.top1::before {
    content: '1';
    background: linear-gradient(45deg, #ffd700, #ffa500);
  }

  &.top2::before {
    content: '2';
    background: linear-gradient(45deg, #c0c0c0, #a9a9a9);
  }

  &.top3::before {
    content: '3';
    background: linear-gradient(45deg, #cd7f32, #b87333);
  }
}

.rank-avatar {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  object-fit: cover;
  border-radius: 40rpx;
}

.rank-info {
  flex: 1;
}

.rank-name {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
}

.rank-class {
  display: block;
  font-size: 24rpx;
  color: #666;
}

.rank-score {
  margin-left: auto;
  font-weight: 600;
  color: #1989fa;
}

.rules-content {
  padding: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.rules-desc {
  display: block;
  margin-bottom: 30rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #666;
}

.rules-section {
  margin-bottom: 30rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.rules-section-title {
  display: block;
  margin-bottom: 20rpx;
  font-size: 30rpx;
  font-weight: 600;
}

.rules-list {
  margin-left: 20rpx;
}

.rules-item {
  margin-bottom: 20rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.rules-item-title {
  display: block;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.rules-item-desc {
  display: block;
  font-size: 26rpx;
  line-height: 1.5;
  color: #666;
}

.custom-tabs {
  position: sticky;
  top: 0;
  z-index: 1;
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tabs-header {
  box-sizing: border-box;
  display: flex;
  width: 100%;
}

.tab-item {
  position: relative;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  height: 90rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s;

  &.active {
    font-weight: 500;
    color: #1989fa;

    &::after {
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 60rpx;
      height: 6rpx;
      content: '';
      background-color: #1989fa;
      border-radius: 3rpx;
      transform: translateX(-50%);
    }
  }
}

.py-4 {
  padding-top: 40rpx;
  padding-bottom: 40rpx;
}
</style>
