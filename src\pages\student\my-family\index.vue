<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的家庭成员',
  },
}
</route>
<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { getStudentFamily, getStudentInfo } from '@/service/student'
import type { StudentFamilyQuery, StudentFamilyMember, StudentInfo } from '@/types/student'

// 加载状态
const loading = ref(true)

// 学生信息
const studentInfo = ref<StudentInfo>({} as StudentInfo)

// 家庭成员信息
const familyMembers = ref<(StudentFamilyMember & { expanded: boolean })[]>([])

// 查询参数
const queryParams = ref<StudentFamilyQuery>({
  page: 1,
  pageSize: 10,
  sortBy: 'id',
  sortOrder: 'desc',
})

// 总记录数
const total = ref(0)

// 处理头像URL
const formatAvatarUrl = (url?: string) => {
  if (!url) return ''

  // 如果是绝对URL，直接返回
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }

  // 如果是以www开头或其他形式的相对路径，添加http://前缀
  if (url.startsWith('www.') || url.startsWith('/')) {
    return `http://${url.startsWith('/') ? url.substring(1) : url}`
  }

  // 其他情况，假设是相对路径，添加基础URL
  return `http://www.fjpit.com/${url.startsWith('/') ? url.substring(1) : url}`
}

// 格式化后的头像URL
const avatarUrl = computed(() => {
  return formatAvatarUrl(studentInfo.value?.avatarUrl)
})

// 获取学生信息
const fetchStudentInfo = async () => {
  try {
    studentInfo.value = await getStudentInfo()
    console.log(studentInfo.value)
  } catch (error) {
    console.error('获取学生信息失败:', error)
  }
}

// 获取家庭成员信息
const fetchFamilyMembers = async () => {
  loading.value = true
  try {
    const res = await getStudentFamily(queryParams.value)
    familyMembers.value = res.items.map((item) => ({
      ...item,
      expanded: true,
    }))
    total.value = res.total
  } catch (error) {
    console.error('获取家庭成员失败:', error)
  } finally {
    loading.value = false
  }
}

// 切换展开/折叠
const toggleExpand = (index: number) => {
  familyMembers.value[index].expanded = !familyMembers.value[index].expanded
}

// 获取性别文本
const getGenderText = (gender: number) => {
  return gender === 1 ? '男' : '女'
}

// 显示监护人标识
const getGuardianText = (isGuardian: string) => {
  return isGuardian === '1' ? ' | 监护人' : ''
}

// 页面加载时获取数据
onMounted(() => {
  fetchStudentInfo()
  fetchFamilyMembers()
})
</script>

<template>
  <view class="page-container">
    <!-- 学生基本信息卡片 -->
    <view class="student-info">
      <view class="info-header">
        <view class="student-avatar">
          <image v-if="avatarUrl" :src="avatarUrl" class="avatar-image" mode="aspectFill" />
          <wd-icon v-else name="user-avatar" size="60rpx" />
        </view>
        <view class="student-info-text">
          <view class="student-name">{{ studentInfo.studentName || '加载中...' }}</view>
          <view class="student-id">学号：{{ studentInfo.studentCode || '加载中...' }}</view>
        </view>
      </view>
      <view class="info-body">
        <view class="info-item">
          <view class="info-label">院系班级:</view>
          <view class="info-value">
            {{ studentInfo.deptName || '--' }} {{ studentInfo.className || '--' }}
          </view>
        </view>
        <view class="info-item">
          <view class="info-label">入学年份:</view>
          <view class="info-value">
            {{
              studentInfo.startStudyDate ? studentInfo.startStudyDate.substring(0, 4) + '年' : '--'
            }}
          </view>
        </view>
        <view class="info-item">
          <view class="info-label">联系电话:</view>
          <view class="info-value">{{ studentInfo.mobile || '--' }}</view>
        </view>
        <view class="info-item">
          <view class="info-label">家庭住址:</view>
          <view class="info-value">{{ studentInfo.homeAddress || '--' }}</view>
        </view>
      </view>
    </view>

    <!-- 家庭成员标题 -->
    <view class="section-title">家庭成员信息</view>

    <!-- 加载中状态 -->
    <view v-if="loading" class="loading-state family-list">
      <wd-icon name="time" size="40px" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 家庭成员列表 -->
    <view v-else-if="familyMembers.length > 0" class="family-list">
      <view v-for="(member, index) in familyMembers" :key="member.id" class="family-member">
        <view class="member-header" @click="toggleExpand(index)">
          <view class="member-avatar">
            <wd-icon name="user" size="24px" />
          </view>
          <view class="member-title">
            <view class="member-name">{{ member.familyMemberName }}</view>
            <view class="member-relation">
              {{ member.familyMemberRelationship }}{{ getGuardianText(member.isGuardian) }}
            </view>
          </view>
          <view class="flex items-center justify-center w-8 h-8">
            <wd-icon :name="member.expanded ? 'chevron-up' : 'chevron-down'" />
          </view>
        </view>
        <view v-if="member.expanded" class="member-content">
          <view class="detail-item">
            <view class="detail-label">性别:</view>
            <view class="detail-value">{{ getGenderText(member.familyMemberGender) }}</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">出生日期:</view>
            <view class="detail-value">{{ member.familyMemberBirthdate || '--' }}</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">联系电话:</view>
            <view class="detail-value">{{ member.phoneNumber || '--' }}</view>
          </view>
          <view class="detail-item" v-if="member.workPosition">
            <view class="detail-label">工作职位:</view>
            <view class="detail-value">{{ member.workPosition }}</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">证件号码:</view>
            <view class="detail-value">{{ member.IDNumber || '--' }}</view>
          </view>
          <view class="detail-item">
            <view class="detail-label">政治面貌:</view>
            <view class="detail-value">{{ member.familyMemberPoliticalStatus || '--' }}</view>
          </view>
        </view>
        <view class="member-actions">
          <view class="action-btn flex items-center">
            <wd-icon name="edit-outline" class="mr-1" size="14px" />
            <text>编辑</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空数据状态 -->
    <view v-else class="family-list">
      <view class="empty-family">
        <view class="empty-icon">
          <wd-icon name="error-circle" size="40px" />
        </view>
        <view>暂无家庭成员信息</view>
      </view>
    </view>

    <!-- 特殊情况部分 -->
    <!-- <view class="section-title">特殊情况</view>
    <view class="family-list">
      <view class="empty-family">
        <view class="empty-icon">
          <wd-icon name="error-circle" size="40px" />
        </view>
        <view>暂无特殊情况记录</view>
      </view>
    </view> -->

    <!-- 添加按钮 -->
    <!-- <view class="fab-button flex items-center justify-center">
      <wd-icon name="add" size="24px" />
    </view> -->
  </view>
</template>

<style scoped lang="scss">
.page-container {
  min-height: 100vh;
  padding-top: 30rpx;
  padding-bottom: 30px;
  background-color: #f5f5f7;
}

.student-info {
  margin: 30rpx;
  margin-top: 0;
  overflow: hidden;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.info-header {
  position: relative;
  display: flex;
  align-items: center;
  padding: 20px;
  color: white;
  background-color: #3b82f6;
}

.student-avatar {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 120rpx;
  height: 120rpx;
  margin-right: 15px;
  overflow: hidden;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.student-info-text {
  flex: 1;
}

.student-name {
  margin-bottom: 5px;
  font-size: 20px;
  font-weight: 600;
}

.student-id {
  font-size: 14px;
  opacity: 0.9;
}

.info-body {
  padding: 15px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  flex-shrink: 0;
  width: 80px;
  font-size: 14px;
  color: #6b7280;
}

.info-value {
  flex: 1;
  font-size: 14px;
  color: #374151;
}

.family-list {
  margin: 15px;
}

.family-member {
  margin-bottom: 15px;
  overflow: hidden;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.member-header {
  display: flex;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f3f4f6;
}

.member-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  margin-right: 15px;
  color: #6b7280;
  background-color: #e5e7eb;
  border-radius: 50%;
}

.member-title {
  flex: 1;
}

.member-name {
  margin-bottom: 3px;
  font-size: 16px;
  font-weight: 600;
  color: #111827;
}

.member-relation {
  font-size: 14px;
  color: #6b7280;
}

.member-content {
  padding: 15px;
}

.detail-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  flex-shrink: 0;
  width: 70px;
  font-size: 14px;
  color: #6b7280;
}

.detail-value {
  flex: 1;
  font-size: 14px;
  color: #374151;
}

.member-actions {
  display: flex;
  justify-content: flex-end;
  padding: 10px 15px;
  border-top: 1px solid #f3f4f6;
}

.action-btn {
  font-size: 14px;
  color: #3b82f6;
  cursor: pointer;
}

.section-title {
  padding: 15px 15px 5px;
  font-size: 16px;
  font-weight: 600;
  color: #4b5563;
}

.empty-family {
  padding: 40px 20px;
  color: #9ca3af;
  text-align: center;
}

.empty-icon {
  margin-bottom: 15px;
  font-size: 40px;
  color: #d1d5db;
}

.loading-state {
  padding: 40px 20px;
  color: #9ca3af;
  text-align: center;
}

.loading-text {
  display: block;
  margin-top: 15px;
}

.fab-button {
  position: fixed;
  right: 20px;
  bottom: 30px;
  z-index: 100;
  width: 56px;
  height: 56px;
  color: white;
  background-color: #3b82f6;
  border-radius: 28px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
</style>
