<route lang="json5">
{
  style: {
    navigationBarTitleText: '个人信息',
  },
}
</route>
<script setup lang="ts">
import { onMounted, ref, watch, computed } from 'vue'
import {
  getStudentInfo,
  saveStudentInfo,
  applyLearningProve,
  applyGraduateProve,
} from '@/service/student'
import { getDict } from '@/service/system'
import type { StudentInfo, SaveStudentInfoParams } from '@/types/student'
import type { DictItem } from '@/types/system'
import { DictTypeEnum } from '@/types/system'
import useUpload from '@/hooks/useUpload'
import RegionPicker from '@/components/region-picker.vue'
import { loadDictData, getDictOptions } from '@/utils/dict'
import ActionButton from '@/components/common/ActionButton.vue'
import ConfirmDialog from '@/components/common/ConfirmDialog.vue'
import FileUploader from '@/components/common/FileUploader.vue'

const studentInfo = ref<StudentInfo>({} as StudentInfo)
const idTypeDict = ref<DictItem[]>([])
const politicalStatusDict = ref<DictItem[]>([])
const healthStatusDict = ref<DictItem[]>([])
const accountsCategoryDict = ref<DictItem[]>([]) // 户口类型字典
const nationalityDict = ref<DictItem[]>([]) // 国籍地区字典
const gatqwDict = ref<DictItem[]>([]) // 港澳台侨外字典
const educationalDict = ref<DictItem[]>([]) // 学历字典
const isAloneDict = ref<DictItem[]>([]) // 是否独生子女字典
const lodgingDict = ref<DictItem[]>([]) // 住宿情况字典
const residenceTypeDict = ref<DictItem[]>([]) // 居住地类型字典
const activeTab = ref('basic') // 当前激活的选项卡
const isEditing = ref(false) // 是否处于编辑状态
const editForm = ref<Partial<StudentInfo>>({} as Partial<StudentInfo>) // 编辑表单
const tempAvatarUrl = ref<string>('') // 临时头像URL

// 头像上传相关
const {
  loading: uploadLoading,
  error: uploadError,
  data: uploadData,
  run: uploadAvatar,
} = useUpload<string>({
  type: 'avatar',
  module: 'student',
})

// 监听头像上传结果
watch(uploadData, (newValue) => {
  if (newValue) {
    try {
      // 解析上传返回的结果
      const result = typeof newValue === 'string' ? JSON.parse(newValue) : newValue
      if (result.code === 1) {
        // 上传成功
        tempAvatarUrl.value = result.data.file.url
        uni.showToast({
          title: '头像上传成功',
          icon: 'success',
        })
      } else {
        // 上传失败
        uni.showToast({
          title: result.msg || '头像上传失败',
          icon: 'error',
        })
      }
    } catch (error) {
      console.error('解析上传结果失败:', error)
      uni.showToast({
        title: '头像上传失败',
        icon: 'error',
      })
    }
  }
})

// 监听头像上传错误
watch(uploadError, (hasError) => {
  if (hasError) {
    uni.showToast({
      title: '头像上传失败',
      icon: 'error',
    })
  }
})

// 监听选项卡切换，如果处于编辑状态则自动取消编辑
watch(activeTab, (newTab, oldTab) => {
  if (isEditing.value && newTab !== oldTab) {
    // 显示确认对话框
    uni.showModal({
      title: '提示',
      content: '切换选项卡将取消当前编辑，是否继续？',
      success: (res) => {
        if (res.confirm) {
          // 用户确认切换，取消编辑
          isEditing.value = false
          editForm.value = {} as Partial<StudentInfo>
          tempAvatarUrl.value = ''
          console.log('切换选项卡，已取消编辑状态')
        } else {
          // 用户取消切换，恢复原选项卡
          activeTab.value = oldTab
        }
      },
    })
  }
})

// 日期选择器值
const leagueDateTimestamp = ref<number>(0)
const partyDateTimestamp = ref<number>(0)

// 日期格式相关函数
// 将日期字符串转为时间戳，优化性能
const dateToTimestamp = (dateStr: string): number => {
  if (!dateStr || dateStr === '0000-00-00') return 0
  // 分割日期字符串，提高性能
  const parts = dateStr.split('-')
  if (parts.length !== 3) return 0

  try {
    // 手动构建日期对象，避免不同浏览器对日期格式的兼容性问题
    const year = parseInt(parts[0])
    const month = parseInt(parts[1]) - 1 // 月份从0开始
    const day = parseInt(parts[2])
    return new Date(year, month, day).getTime()
  } catch (e) {
    console.error('日期转换出错:', e)
    return 0
  }
}

// 将时间戳转为日期字符串，格式YYYY-MM-DD
const timestampToDate = (timestamp: number): string => {
  if (!timestamp) return ''
  try {
    const date = new Date(timestamp)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  } catch (e) {
    console.error('时间戳转日期出错:', e)
    return ''
  }
}

// 使用计算属性代替部分watch，减少不必要的监听
const todayTimestamp = computed(() => new Date().getTime())
const sixMonthsAgo = computed(() => {
  const date = new Date()
  date.setMonth(date.getMonth() - 6)
  return date.getTime()
})
const sixMonthsLater = computed(() => {
  const date = new Date()
  date.setMonth(date.getMonth() + 6)
  return date.getTime()
})

// 处理入团时间选择确认
const handleLeagueDateConfirm = (event: { value: number }) => {
  if (!event.value) return
  editForm.value.leagueDate = timestampToDate(event.value)
}

// 处理入党时间选择确认
const handlePartyDateConfirm = (event: { value: number }) => {
  if (!event.value) return
  editForm.value.partyDate = timestampToDate(event.value)
}

// 获取学生信息
const getInfo = async () => {
  try {
    const data = await getStudentInfo()

    // 确保birthLocation和originLocation是数组
    const birthLocation = Array.isArray(data.birthLocation)
      ? data.birthLocation.map((item) => String(item))
      : []

    const originLocation = Array.isArray(data.originLocation)
      ? data.originLocation.map((item) => String(item))
      : []

    // 确保户口所在地是数组
    const accountsLocation = Array.isArray(data.accountsLocation)
      ? data.accountsLocation.map((item) => String(item))
      : []

    // 确保现家庭住址是数组
    const currentHomeLocation =
      data.hkszsf && data.hkszcs && data.hkszdq
        ? [String(data.hkszsf), String(data.hkszcs), String(data.hkszdq)]
        : []

    // 确保生源地信息是数组
    const stuOriginLocation =
      data.sydqsf && data.sydqcs && data.sydq
        ? [String(data.sydqsf), String(data.sydqcs), String(data.sydq)]
        : []

    // 构造出生地名称字段，将省市区名称用"|"连接
    let birthLocationNameValue = ''
    if (data.cssfmc && data.cscsmc && data.csdqmc) {
      birthLocationNameValue = `${data.cssfmc}|${data.cscsmc}|${data.csdqmc}`
    }

    // 构造籍贯地名称字段，将省市区名称用"|"连接
    let originLocationNameValue = ''
    if (data.hkdqsfmc && data.hkdqcsmc && data.originCountyName) {
      originLocationNameValue = `${data.hkdqsfmc}|${data.hkdqcsmc}|${data.originCountyName}`
    }

    // 构造现家庭住址名称字段，将省市区名称用"|"连接
    let currentHomeLocationNameValue = ''
    if (data.kkszsfmc && data.kkszcsmc && data.stuOriginCountyName) {
      currentHomeLocationNameValue = `${data.kkszsfmc}|${data.kkszcsmc}|${data.stuOriginCountyName}`
    }

    // 构造生源地名称字段，将省市区名称用"|"连接
    let stuOriginLocationNameValue = ''
    if (data.sydqsfmc && data.sydqcsmc && data.sydqmc) {
      stuOriginLocationNameValue = `${data.sydqsfmc}|${data.sydqcsmc}|${data.sydqmc}`
    }

    studentInfo.value = {
      ...data,
      // 如果没有港澳台侨外和国籍信息，设置默认值
      hmtCode: data.hmtCode || '0',
      hmtName: data.hmtName || '非港澳台侨',
      nationalityCode: data.nationalityCode || '156', // 默认中国
      nationalityName: data.nationalityName || '中国',
      // 确保出生地和籍贯地字段格式正确
      birthLocation,
      birthLocationName: birthLocationNameValue,
      originLocation,
      originLocationName: originLocationNameValue,
      accountsLocation,
      // 添加现家庭住址相关字段
      currentHomeLocation,
      currentHomeLocationName: currentHomeLocationNameValue,
      // 添加生源地信息相关字段
      stuOriginLocation,
      stuOriginLocationName: stuOriginLocationNameValue,
    }
    console.log('学生信息:', studentInfo.value)
  } catch (error) {
    console.error('获取学生信息失败:', error)
  }
}

// 获取字典数据
const getDictData = async () => {
  try {
    // 收集所有需要的字典类型
    const dictTypes = [
      DictTypeEnum.ID_TYPE, // 证件类型
      DictTypeEnum.POLITICAL_STATUS, // 政治面貌
      DictTypeEnum.STUDENT_HEALTH_STATUS, // 健康状况
      DictTypeEnum.ACCOUNTS_CATEGORY, // 户口类型
      DictTypeEnum.NATIONALITY, // 国籍地区
      DictTypeEnum.HMT_CODE, // 港澳台侨外
      DictTypeEnum.STUDENT_EDUCATION, // 学历
      DictTypeEnum.IS_ALONE, // 是否独生子女
      DictTypeEnum.LODGING, // 住宿情况
      DictTypeEnum.RESIDENCE_TYPE, // 居住地类型
    ]

    // 使用优化后的loadDictData一次性加载所有字典
    const dicts = await loadDictData(dictTypes)

    // 从返回结果中获取对应字典数据并转换为DictItem格式
    idTypeDict.value = getDictOptions(dicts[DictTypeEnum.ID_TYPE])
    politicalStatusDict.value = getDictOptions(dicts[DictTypeEnum.POLITICAL_STATUS])
    healthStatusDict.value = getDictOptions(dicts[DictTypeEnum.STUDENT_HEALTH_STATUS])
    accountsCategoryDict.value = getDictOptions(dicts[DictTypeEnum.ACCOUNTS_CATEGORY])
    nationalityDict.value = getDictOptions(dicts[DictTypeEnum.NATIONALITY])
    gatqwDict.value = getDictOptions(dicts[DictTypeEnum.HMT_CODE])
    educationalDict.value = getDictOptions(dicts[DictTypeEnum.STUDENT_EDUCATION])
    isAloneDict.value = getDictOptions(dicts[DictTypeEnum.IS_ALONE])
    lodgingDict.value = getDictOptions(dicts[DictTypeEnum.LODGING])
    residenceTypeDict.value = getDictOptions(dicts[DictTypeEnum.RESIDENCE_TYPE])

    console.log('字典数据加载成功', dicts)
  } catch (error) {
    console.error('获取字典数据失败:', error)
  }
}

// 处理出生地选择结果
const handleBirthLocationSelected = (data: any) => {
  console.log('出生地选择结果:', data)
  // 确保存储的是字符串数组
  editForm.value.birthLocation = data.value.map((item: any) => String(item))
  ;(editForm.value as any).birthLocationName = data.locationName

  console.log('更新后的出生地数据:', {
    编码: editForm.value.birthLocation,
    名称: (editForm.value as any).birthLocationName,
  })
}

// 处理籍贯地选择结果
const handleOriginLocationSelected = (data: any) => {
  console.log('籍贯地选择结果:', data)
  // 确保存储的是字符串数组
  editForm.value.originLocation = data.value.map((item: any) => String(item))
  ;(editForm.value as any).originLocationName = data.locationName

  console.log('更新后的籍贯地数据:', {
    编码: editForm.value.originLocation,
    名称: (editForm.value as any).originLocationName,
  })
}

// 处理户口所在地选择结果
const handleAccountsLocationSelected = (data: any) => {
  console.log('户口所在地选择结果:', data)
  // 确保存储的是字符串数组
  editForm.value.accountsLocation = data.value.map((item: any) => String(item))
  ;(editForm.value as any).accountsLocationName = data.locationName

  console.log('更新后的户口所在地数据:', {
    编码: editForm.value.accountsLocation,
    名称: (editForm.value as any).accountsLocationName,
  })
}

// 处理现家庭住址选择结果
const handleCurrentHomeLocationSelected = (data: any) => {
  console.log('现家庭住址选择结果:', data)
  // 确保存储的是字符串数组
  editForm.value.currentHomeLocation = data.value.map((item: any) => String(item))
  ;(editForm.value as any).currentHomeLocationName = data.locationName

  console.log('更新后的现家庭住址数据:', {
    编码: editForm.value.currentHomeLocation,
    名称: (editForm.value as any).currentHomeLocationName,
  })
}

// 处理生源地选择结果
const handleStuOriginLocationSelected = (data: any) => {
  console.log('生源地选择结果:', data)
  // 确保存储的是字符串数组
  editForm.value.stuOriginLocation = data.value.map((item: any) => String(item))
  ;(editForm.value as any).stuOriginLocationName = data.locationName

  console.log('更新后的生源地数据:', {
    编码: editForm.value.stuOriginLocation,
    名称: (editForm.value as any).stuOriginLocationName,
  })
}

// 处理家庭位置选择结果
const handleHomeLocationSelected = (data: any) => {
  console.log('家庭位置选择结果:', data)
  // 确保存储的是字符串数组
  editForm.value.homeLocation = data.value.map((item: any) => String(item))
  ;(editForm.value as any).homeLocationName = data.locationName

  console.log('更新后的家庭位置数据:', {
    编码: editForm.value.homeLocation,
    名称: (editForm.value as any).homeLocationName,
  })
}

// 获取证件类型名称
const getIdTypeName = (value: string) => {
  return idTypeDict.value.find((item) => item.value === value)?.label || value
}

// 获取政治面貌名称
const getPoliticalStatusName = (value: string) => {
  return politicalStatusDict.value.find((item) => item.value === value)?.label || value
}

// 获取健康状况名称
const getHealthStatusName = (value: string) => {
  return healthStatusDict.value.find((item) => item.value === value)?.label || value
}

// 获取户口类型名称
const getAccountsCategoryName = (value: string) => {
  return accountsCategoryDict.value.find((item) => item.value === value)?.label || value
}

// 获取国籍地区名称
const getNationalityName = (value: string) => {
  return nationalityDict.value.find((item) => item.value === value)?.label || value || '中国'
}

// 获取港澳台侨外名称
const getGatqwName = (value: string) => {
  // 如果值为空，返回默认值
  if (!value || value === '') return '--'

  // 尝试从字典中获取名称
  const dictLabel = gatqwDict.value.find((item) => item.value === value)?.label

  // 如果能找到对应的标签，返回标签；否则返回原值或默认值
  return dictLabel || value || '--'
}

// 获取学历名称
const getEducationalName = (value: string) => {
  return educationalDict.value.find((item) => item.value === value)?.label || value
}

// 获取是否独生子女名称
const getIsAloneName = (value: string) => {
  return isAloneDict.value.find((item) => item.value === value)?.label || '--'
}

// 格式化性别
const formatGender = (gender: string) => {
  return gender === '1' ? '男' : '女'
}

// 获取住宿情况名称
const getLodgingName = (value: string) => {
  return lodgingDict.value.find((item) => item.value === value)?.label || '--'
}

// 获取居住地类型名称
const getResidenceTypeName = (value: string) => {
  return residenceTypeDict.value.find((item) => item.value === value)?.label || '--'
}

// 切换到编辑模式
const startEditing = () => {
  if (activeTab.value === 'basic') {
    // 初始化基本信息编辑表单
    editForm.value = {
      IDTypeCode: studentInfo.value.IDTypeCode,
      politicalOutlookCode: studentInfo.value.politicalOutlookCode,
      accountsCategoryCode: studentInfo.value.accountsCategoryCode,
      healthStatusCode: studentInfo.value.healthStatusCode,
      nationalityCode: studentInfo.value.nationalityCode, // 国籍编码
      hmtCode: studentInfo.value.hmtCode || '0', // 港澳台侨外编码，确保有默认值
      educationalCode: studentInfo.value.educationalCode, // 学历代码
      // 确保birthLocation和originLocation是字符串数组类型
      birthLocation: Array.isArray(studentInfo.value.birthLocation)
        ? studentInfo.value.birthLocation.map((item) => String(item))
        : [],
      originLocation: Array.isArray(studentInfo.value.originLocation)
        ? studentInfo.value.originLocation.map((item) => String(item))
        : [],
      // 初始化地区名称
      birthLocationName: studentInfo.value.birthLocationName || '',
      originLocationName: studentInfo.value.originLocationName || '',
    }
  } else if (activeTab.value === 'additional') {
    // 初始化辅助信息编辑表单
    editForm.value = {
      height: studentInfo.value.height,
      weight: studentInfo.value.weight,
      seatNumber: studentInfo.value.seatNumber,
      namePY: (studentInfo.value as any).namePY,
      formerName: (studentInfo.value as any).formerName,
      educationalCode: studentInfo.value.educationalCode,
      isAlone: (studentInfo.value as any).isAlone,
      accountsCategoryCode: studentInfo.value.accountsCategoryCode,
      accountAddress: studentInfo.value.accountAddress,
      accountPoliceStation: (studentInfo.value as any).accountPoliceStation,
      // 确保accountsLocation是字符串数组类型
      accountsLocation: Array.isArray(studentInfo.value.accountsLocation)
        ? studentInfo.value.accountsLocation.map((item) => String(item))
        : [],
      accountsLocationName: (studentInfo.value as any).accountsLocationName || '',
    }
  } else if (activeTab.value === 'contact') {
    // 初始化联系信息编辑表单
    editForm.value = {
      locationCategoryCode: studentInfo.value.locationCategoryCode,
      lodgingCode: studentInfo.value.lodgingCode,
      homePhone: studentInfo.value.homePhone,
      homeAddress: studentInfo.value.homeAddress,
      Zip: studentInfo.value.Zip,
      email: studentInfo.value.email,
      mobile: studentInfo.value.mobile,
      parentsName: studentInfo.value.parentsName,
      parentsPhone: studentInfo.value.parentsPhone,
      leagueDate: studentInfo.value.leagueDate, // 添加入团时间
      partyDate: studentInfo.value.partyDate, // 添加入党时间
      resume: studentInfo.value.resume, // 添加个人简历
      // 不再包含bankInfo，因为银行卡信息不支持修改
      // 添加家庭位置
      homeLocation: Array.isArray(studentInfo.value.homeLocation)
        ? studentInfo.value.homeLocation.map((item) => String(item))
        : [],
      homeLocationName: studentInfo.value.homeLocationName || '',
      // 添加现家庭住址
      currentHomeLocation: Array.isArray(studentInfo.value.currentHomeLocation)
        ? studentInfo.value.currentHomeLocation.map((item) => String(item))
        : [],
      currentHomeLocationName: studentInfo.value.currentHomeLocationName || '',
      // 添加生源地信息
      stuOriginLocation: Array.isArray(studentInfo.value.stuOriginLocation)
        ? studentInfo.value.stuOriginLocation.map((item) => String(item))
        : [],
      stuOriginLocationName: studentInfo.value.stuOriginLocationName || '',
    }

    // 初始化日期时间戳
    leagueDateTimestamp.value = dateToTimestamp(studentInfo.value.leagueDate || '')
    partyDateTimestamp.value = dateToTimestamp(studentInfo.value.partyDate || '')
  }

  console.log('编辑前表单初始值:', editForm.value, '字典值:', {
    gatqwDict: gatqwDict.value,
    nationalityDict: nationalityDict.value,
    educationalDict: educationalDict.value,
    accountsCategoryDict: accountsCategoryDict.value,
    isAloneDict: isAloneDict.value,
    地区数据: {
      出生地编码: editForm.value.birthLocation,
      出生地名称: (editForm.value as any).birthLocationName,
      籍贯地编码: editForm.value.originLocation,
      籍贯地名称: (editForm.value as any).originLocationName,
      户口所在地编码: editForm.value.accountsLocation,
      户口所在地名称: (editForm.value as any).accountsLocationName,
      现家庭住址编码: editForm.value.currentHomeLocation,
      现家庭住址名称: (editForm.value as any).currentHomeLocationName,
      生源地编码: editForm.value.stuOriginLocation,
      生源地名称: (editForm.value as any).stuOriginLocationName,
    },
  })

  tempAvatarUrl.value = studentInfo.value.avatarUrl || ''

  isEditing.value = true
}

// 处理证件类型确认
const handleIDTypeConfirm = (event: { value: string; selectedItems: any }) => {
  editForm.value.IDTypeCode = event.value
}

// 处理政治面貌确认
const handlePoliticalStatusConfirm = (event: { value: string; selectedItems: any }) => {
  editForm.value.politicalOutlookCode = event.value
}

// 处理户口类型确认
const handleAccountsCategoryConfirm = (event: { value: string; selectedItems: any }) => {
  editForm.value.accountsCategoryCode = event.value
}

// 处理健康状况确认
const handleHealthStatusConfirm = (event: { value: string; selectedItems: any }) => {
  editForm.value.healthStatusCode = event.value
}

// 处理国籍确认
const handleNationalityConfirm = (event: { value: string; selectedItems: any }) => {
  editForm.value.nationalityCode = event.value
}

// 处理港澳台侨外确认
const handleGatqwConfirm = (event: { value: string; selectedItems: any }) => {
  console.log('港澳台侨外选择:', event)
  editForm.value.hmtCode = event.value
}

// 处理学历确认
const handleEducationalConfirm = (event: { value: string; selectedItems: any }) => {
  editForm.value.educationalCode = event.value
}

// 处理是否独生子女确认
const handleIsAloneConfirm = (event: { value: string; selectedItems: any }) => {
  editForm.value.isAlone = event.value
}

// 处理住宿情况确认
const handleLodgingConfirm = (event: { value: string; selectedItems: any }) => {
  editForm.value.lodgingCode = event.value
}

// 处理居住地类型确认
const handleResidenceTypeConfirm = (event: { value: string; selectedItems: any }) => {
  editForm.value.locationCategoryCode = event.value
}

// 取消编辑
const cancelEditing = () => {
  // 显示确认对话框
  uni.showModal({
    title: '提示',
    content: '确定要取消编辑吗？未保存的修改将丢失',
    success: (res) => {
      if (res.confirm) {
        // 用户点击确定，执行取消编辑操作
        console.log('取消编辑，恢复原始状态:', studentInfo.value)
        isEditing.value = false
        editForm.value = {} as Partial<StudentInfo>
        tempAvatarUrl.value = ''
      }
    },
  })
}

// 保存编辑信息
const saveInfo = async () => {
  try {
    // 根据当前激活的选项卡确定要保存的类型
    const saveType =
      activeTab.value === 'basic'
        ? 'base_info'
        : activeTab.value === 'additional'
          ? 'additional_info'
          : 'contact_info'

    // 构建基础保存参数
    const baseParams: Partial<SaveStudentInfoParams> = {
      studentCode: studentInfo.value.studentCode,
      studentName: studentInfo.value.studentName,
      ticketCode: studentInfo.value.ticketCode,
      deptName: studentInfo.value.deptName,
      majorName: studentInfo.value.majorName,
      className: studentInfo.value.className,
      startStudyDate: studentInfo.value.startStudyDate,
      stuStatus: studentInfo.value.stuStatus,
      stuSchoolStatus: studentInfo.value.stuSchoolStatus,
      avatarUrl: tempAvatarUrl.value || studentInfo.value.avatarUrl,
      birthDate: studentInfo.value.birthDate,
      type: saveType as 'base_info' | 'additional_info' | 'contact_info',
    }

    // 根据当前选项卡添加不同的参数
    let params: SaveStudentInfoParams

    if (saveType === 'base_info') {
      // 基本信息页面参数
      params = {
        ...baseParams,
        IDTypeCode: editForm.value.IDTypeCode || studentInfo.value.IDTypeCode,
        IDTypeName: getIdTypeName(editForm.value.IDTypeCode || studentInfo.value.IDTypeCode),
        IDNumber: studentInfo.value.IDNumber,
        birthLocation:
          Array.isArray(editForm.value.birthLocation) && editForm.value.birthLocation.length > 0
            ? editForm.value.birthLocation.map((item) => String(item))
            : studentInfo.value.birthLocation,
        originLocation:
          Array.isArray(editForm.value.originLocation) && editForm.value.originLocation.length > 0
            ? editForm.value.originLocation.map((item) => String(item))
            : studentInfo.value.originLocation,
        politicalOutlookCode:
          editForm.value.politicalOutlookCode || studentInfo.value.politicalOutlookCode,
        politicalOutlookName: getPoliticalStatusName(
          editForm.value.politicalOutlookCode || studentInfo.value.politicalOutlookCode,
        ),
        nationalityCode: editForm.value.nationalityCode || studentInfo.value.nationalityCode,
        nationalityName: getNationalityName(
          editForm.value.nationalityCode || studentInfo.value.nationalityCode,
        ),
        hmtCode:
          editForm.value.hmtCode !== undefined
            ? editForm.value.hmtCode
            : studentInfo.value.hmtCode || '0',
        hmtName:
          editForm.value.hmtCode !== undefined
            ? getGatqwName(editForm.value.hmtCode)
            : studentInfo.value.hmtName || '非港澳台侨',
        healthStatusCode: editForm.value.healthStatusCode || studentInfo.value.healthStatusCode,
        healthStatusName: getHealthStatusName(
          editForm.value.healthStatusCode || studentInfo.value.healthStatusCode,
        ),
        birthLocationName:
          (editForm.value as any).birthLocationName || studentInfo.value.birthLocationName,
        originLocationName:
          (editForm.value as any).originLocationName || studentInfo.value.originLocationName,
        educationalCode: editForm.value.educationalCode || studentInfo.value.educationalCode,
        educationalName: getEducationalName(
          editForm.value.educationalCode || studentInfo.value.educationalCode || '',
        ),
      } as SaveStudentInfoParams
    } else if (saveType === 'additional_info') {
      // 辅助信息页面参数
      const isAloneValue = editForm.value.isAlone || (studentInfo.value as any).isAlone || '0'
      const isAloneLabel = getIsAloneName(isAloneValue)

      params = {
        ...baseParams,
        height: editForm.value.height,
        weight: editForm.value.weight,
        seatNumber: editForm.value.seatNumber,
        namePY: editForm.value.namePY,
        formerName: editForm.value.formerName,
        educationalCode: editForm.value.educationalCode || studentInfo.value.educationalCode,
        educationalName: getEducationalName(
          editForm.value.educationalCode || studentInfo.value.educationalCode || '',
        ),
        accountsCategoryCode:
          editForm.value.accountsCategoryCode || studentInfo.value.accountsCategoryCode,
        accountsCategoryName: getAccountsCategoryName(
          editForm.value.accountsCategoryCode || studentInfo.value.accountsCategoryCode,
        ),
        isAlone: isAloneLabel,
        accountAddress: editForm.value.accountAddress || studentInfo.value.accountAddress,
        accountPoliceStation:
          editForm.value.accountPoliceStation || (studentInfo.value as any).accountPoliceStation,
        accountsLocation:
          Array.isArray(editForm.value.accountsLocation) &&
          editForm.value.accountsLocation.length > 0
            ? editForm.value.accountsLocation.map((item) => String(item))
            : studentInfo.value.accountsLocation || [],
        accountsLocationName:
          (editForm.value as any).accountsLocationName ||
          (studentInfo.value as any).accountsLocationName,
      } as SaveStudentInfoParams
    } else {
      // 联系信息页面参数
      params = {
        ...baseParams,
        locationCategoryCode:
          editForm.value.locationCategoryCode || studentInfo.value.locationCategoryCode,
        locationCategoryName: getResidenceTypeName(
          editForm.value.locationCategoryCode || studentInfo.value.locationCategoryCode || '',
        ),
        lodgingCode: editForm.value.lodgingCode || studentInfo.value.lodgingCode,
        lodgingName: getLodgingName(
          editForm.value.lodgingCode || studentInfo.value.lodgingCode || '',
        ),
        homePhone: editForm.value.homePhone || studentInfo.value.homePhone,
        homeAddress: editForm.value.homeAddress || studentInfo.value.homeAddress,
        Zip: editForm.value.Zip || studentInfo.value.Zip,
        email: editForm.value.email || studentInfo.value.email,
        mobile: editForm.value.mobile || studentInfo.value.mobile,
        parentsName: editForm.value.parentsName || studentInfo.value.parentsName,
        parentsPhone: editForm.value.parentsPhone || studentInfo.value.parentsPhone,
        leagueDate: editForm.value.leagueDate || studentInfo.value.leagueDate, // 添加入团时间
        partyDate: editForm.value.partyDate || studentInfo.value.partyDate, // 添加入党时间
        resume: editForm.value.resume || studentInfo.value.resume, // 添加个人简历
        bankInfo: studentInfo.value.bankInfo, // 银行卡信息保持不变
        // 添加家庭位置数据
        homeLocation: editForm.value.homeLocation || studentInfo.value.homeLocation || [],
        homeLocationName:
          (editForm.value as any).homeLocationName || studentInfo.value.homeLocationName || '',
        // 添加现家庭住址数据
        hkszsf:
          editForm.value.currentHomeLocation && editForm.value.currentHomeLocation[0]
            ? editForm.value.currentHomeLocation[0]
            : studentInfo.value.hkszsf,
        hkszcs:
          editForm.value.currentHomeLocation && editForm.value.currentHomeLocation[1]
            ? editForm.value.currentHomeLocation[1]
            : studentInfo.value.hkszcs,
        hkszdq:
          editForm.value.currentHomeLocation && editForm.value.currentHomeLocation[2]
            ? editForm.value.currentHomeLocation[2]
            : studentInfo.value.hkszdq,
        kkszsfmc:
          (editForm.value.currentHomeLocationName &&
            editForm.value.currentHomeLocationName.split('|')[0]) ||
          studentInfo.value.kkszsfmc,
        kkszcsmc:
          (editForm.value.currentHomeLocationName &&
            editForm.value.currentHomeLocationName.split('|')[1]) ||
          studentInfo.value.kkszcsmc,
        stuOriginCountyName:
          (editForm.value.currentHomeLocationName &&
            editForm.value.currentHomeLocationName.split('|')[2]) ||
          studentInfo.value.stuOriginCountyName,
        // 添加生源地信息数据
        stuOriginLocation:
          editForm.value.stuOriginLocation || studentInfo.value.stuOriginLocation || [],
        stuOriginLocationName:
          (editForm.value as any).stuOriginLocationName ||
          studentInfo.value.stuOriginLocationName ||
          '',
        sydqsf:
          editForm.value.stuOriginLocation && editForm.value.stuOriginLocation[0]
            ? editForm.value.stuOriginLocation[0]
            : studentInfo.value.sydqsf,
        sydqcs:
          editForm.value.stuOriginLocation && editForm.value.stuOriginLocation[1]
            ? editForm.value.stuOriginLocation[1]
            : studentInfo.value.sydqcs,
        sydq:
          editForm.value.stuOriginLocation && editForm.value.stuOriginLocation[2]
            ? editForm.value.stuOriginLocation[2]
            : studentInfo.value.sydq,
        sydqsfmc:
          (editForm.value.stuOriginLocationName &&
            editForm.value.stuOriginLocationName.split('|')[0]) ||
          studentInfo.value.sydqsfmc,
        sydqcsmc:
          (editForm.value.stuOriginLocationName &&
            editForm.value.stuOriginLocationName.split('|')[1]) ||
          studentInfo.value.sydqcsmc,
        sydqmc:
          (editForm.value.stuOriginLocationName &&
            editForm.value.stuOriginLocationName.split('|')[2]) ||
          studentInfo.value.sydqmc,
      } as SaveStudentInfoParams
    }

    console.log('准备提交的数据:', params)

    // 调用API保存数据
    const result = await saveStudentInfo(params)

    // 显示保存成功的提示
    uni.showToast({
      title: '保存成功',
      icon: 'success',
    })

    // 重新获取最新数据刷新页面
    await getInfo()

    // 重置编辑状态
    isEditing.value = false
    editForm.value = {} as Partial<StudentInfo>
    tempAvatarUrl.value = ''
  } catch (error) {
    console.error('保存学生信息失败:', error)
    uni.showToast({
      title: '保存失败，请稍后重试',
      icon: 'error',
    })
  }
}

// 上传头像时的点击处理
const handleAvatarClick = () => {
  if (!isEditing.value) return
  uploadAvatar() // 调用useUpload hook的run方法
}

// 添加返回函数
const handleBack = () => {
  uni.navigateBack()
}

// 申请打印相关状态
const confirmDialogRef = ref()
const uploadDialogRef = ref()
const currentApplicationType = ref('')
const uploadedFiles = ref<Array<{ url: string; name: string }>>([])
const applicationTypes = {
  enrollment: {
    title: '申请打印在学证明',
    message: '确认要申请打印在学证明吗？(申请提交后辅导员审核通过方可打印)',
  },
  graduation: {
    title: '申请打印预计可毕业证明',
    message: '确认要申请打印预计可毕业证明吗？(申请提交后辅导员审核通过方可打印)',
  },
  graduationMilitary: {
    title: '申请打印预计可毕业证明(入伍)',
    message:
      '确认要申请打印预计可毕业证明吗？(申请提交后需辅导员、二级学院及领导审核通过后即可打印)',
  },
}

// 处理申请按钮点击
const handleApplicationClick = (type: string) => {
  currentApplicationType.value = type

  if (type === 'graduationMilitary') {
    // 入伍申请需要先上传文件
    uploadedFiles.value = []
    uploadDialogRef.value?.show()
  } else {
    // 其他申请直接显示确认对话框
    confirmDialogRef.value?.show()
  }
}

// 确认申请
const confirmApplication = async () => {
  const appType = applicationTypes[currentApplicationType.value as keyof typeof applicationTypes]
  if (!appType) return

  try {
    let result
    if (currentApplicationType.value === 'enrollment') {
      // 申请在学证明
      result = await applyLearningProve()
    } else if (currentApplicationType.value === 'graduation') {
      // 申请预计可毕业证明
      result = await applyGraduateProve({ optype: 'apply_normal' })
    } else if (currentApplicationType.value === 'graduationMilitary') {
      // 申请预计可毕业证明(入伍)
      // 构建附件列表字符串
      const fjlb = uploadedFiles.value.map((file) => `${file.url}|${file.name}`).join(',')
      result = await applyGraduateProve({
        optype: 'apply',
        fjlb,
      })
    }

    uni.showToast({
      title: `${appType.title}申请已提交`,
      icon: 'success',
    })
  } catch (error: any) {
    console.error('申请失败:', error)
  }

  // 清空状态
  currentApplicationType.value = ''
  uploadedFiles.value = []
}

// 取消申请
const cancelApplication = () => {
  // 如果是入伍申请，重新显示上传对话框
  if (currentApplicationType.value === 'graduationMilitary') {
    setTimeout(() => {
      uploadDialogRef.value?.show()
    }, 100)
  } else {
    // 其他申请直接清空状态
    currentApplicationType.value = ''
  }
}

// 处理文件上传确认
const handleUploadConfirm = () => {
  // 先隐藏上传对话框，然后显示确认对话框
  uploadDialogRef.value?.hide()
  // 延迟一下显示确认对话框，确保上传对话框完全关闭
  setTimeout(() => {
    confirmDialogRef.value?.show()
  }, 100)
}

// 取消文件上传
const cancelUpload = () => {
  currentApplicationType.value = ''
  uploadedFiles.value = []
}

onMounted(() => {
  Promise.all([getInfo(), getDictData()])
    .then(() => {
      console.log('所有数据加载完成')
      // 初始化日期时间戳，提高日期选择器性能
      if (studentInfo.value.leagueDate) {
        leagueDateTimestamp.value = dateToTimestamp(studentInfo.value.leagueDate)
      }
      if (studentInfo.value.partyDate) {
        partyDateTimestamp.value = dateToTimestamp(studentInfo.value.partyDate)
      }
    })
    .catch((error) => {
      console.error('数据加载出错:', error)
    })
})
</script>

<template>
  <view class="container" :class="{ 'has-bottom-bar': isEditing }">
    <!-- 个人头像和基础信息 -->
    <view class="card">
      <view class="profile-header">
        <view class="avatar-container" @click="handleAvatarClick">
          <image :src="tempAvatarUrl || studentInfo.avatarUrl" class="avatar" mode="aspectFill" />
          <view v-if="isEditing && activeTab === 'basic'" class="avatar-upload-icon">
            <wd-icon v-if="!uploadLoading" name="camera" size="36rpx" color="#ffffff" />
            <wd-icon v-else name="refresh" size="36rpx" color="#ffffff" class="rotate" />
          </view>
          <view v-if="uploadLoading" class="avatar-loading-mask">
            <text class="upload-text">上传中...</text>
          </view>
        </view>
        <view class="basic-info">
          <text class="name">{{ studentInfo.studentName }}</text>
          <text class="job-number">学号：{{ studentInfo.studentCode }}</text>
        </view>
        <view class="edit-button-container">
          <wd-button
            v-if="!isEditing"
            class="edit-button"
            size="small"
            type="primary"
            @click="startEditing"
          >
            编辑资料
          </wd-button>
          <view v-else class="edit-actions">
            <wd-button class="cancel-button" size="small" @click="cancelEditing">取消</wd-button>
            <wd-button class="save-button" size="small" type="primary" @click="saveInfo">
              保存
            </wd-button>
          </view>
        </view>
      </view>

      <view class="status-summary">
        <view class="status-item">
          <text class="status-value">{{ studentInfo.stuStatus || '--' }}</text>
          <text class="status-label">学生状态</text>
        </view>
        <view class="status-item">
          <text class="status-value">{{ studentInfo.majorName || '--' }}</text>
          <text class="status-label">专业</text>
        </view>
        <view class="status-item">
          <text class="status-value">{{ studentInfo.deptName || '--' }}</text>
          <text class="status-label">所属院系</text>
        </view>
      </view>
    </view>

    <!-- 申请打印按钮区域 -->
    <view class="card application-buttons">
      <view class="info-title mb-2">申请打印</view>
      <view class="button-group">
        <ActionButton
          type="primary"
          text="申请打印在学证明"
          @click="handleApplicationClick('enrollment')"
        />
        <ActionButton
          type="primary"
          text="申请打印预计可毕业证明"
          @click="handleApplicationClick('graduation')"
        />
        <ActionButton
          type="primary"
          text="申请打印预计可毕业证明(入伍)"
          @click="handleApplicationClick('graduationMilitary')"
        />
      </view>
    </view>

    <!-- 选项卡 -->
    <view class="tab-container">
      <view
        class="tab-item"
        :class="{ active: activeTab === 'basic' }"
        @click="activeTab = 'basic'"
      >
        <text>个人基本信息</text>
      </view>
      <view
        class="tab-item"
        :class="{ active: activeTab === 'additional' }"
        @click="activeTab = 'additional'"
      >
        <text>个人辅助信息</text>
      </view>
      <view
        class="tab-item"
        :class="{ active: activeTab === 'contact' }"
        @click="activeTab = 'contact'"
      >
        <text>个人联系信息</text>
      </view>
    </view>

    <!-- 个人基本信息内容 -->
    <view class="card" v-if="activeTab === 'basic'">
      <view class="info-group">
        <view class="info-title">个人基本信息</view>
        <view>
          <view class="info-item">
            <text class="info-label">学号</text>
            <text class="info-value">{{ studentInfo.studentCode || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">姓名</text>
            <text class="info-value">{{ studentInfo.studentName || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">考生号</text>
            <text class="info-value">{{ studentInfo.ticketCode || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">系部</text>
            <text class="info-value">{{ studentInfo.deptName || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">专业</text>
            <text class="info-value">{{ studentInfo.majorName || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">班级</text>
            <text class="info-value">{{ studentInfo.className || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">入学时间</text>
            <text class="info-value">{{ studentInfo.startStudyDate || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">学生状态</text>
            <text class="info-value">{{ studentInfo.stuStatus || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">学籍状态</text>
            <text class="info-value">{{ studentInfo.stuSchoolStatus || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">性别</text>
            <text class="info-value">{{ formatGender(studentInfo.gender) }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">出生年月</text>
            <text class="info-value">{{ studentInfo.birthDate || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">民族</text>
            <text class="info-value">{{ studentInfo.ethnicGroupName || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">证件类型</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ getIdTypeName(studentInfo.IDTypeCode) || '--' }}</text>
            </template>
            <template v-else>
              <wd-picker
                v-model="editForm.IDTypeCode"
                :columns="idTypeDict"
                placeholder="请选择证件类型"
                align-right
                @confirm="handleIDTypeConfirm"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">证件号码</text>
            <text class="info-value">{{ studentInfo.IDNumber || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">出生地</text>
            <RegionPicker
              v-model="editForm.birthLocation"
              :location-name="(editForm as any).birthLocationName || studentInfo.birthLocationName"
              :is-editing="isEditing"
              placeholder="请选择出生地"
              title="请选择出生地"
              @region-selected="handleBirthLocationSelected"
            />
          </view>
          <view class="info-item">
            <text class="info-label">籍贯地</text>
            <RegionPicker
              v-model="editForm.originLocation"
              :location-name="
                (editForm as any).originLocationName || studentInfo.originLocationName
              "
              :is-editing="isEditing"
              placeholder="请选择籍贯地"
              title="请选择籍贯地"
              @region-selected="handleOriginLocationSelected"
            />
          </view>
          <view class="info-item">
            <text class="info-label">政治面貌</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.politicalOutlookName || '--' }}</text>
            </template>
            <template v-else>
              <wd-picker
                v-model="editForm.politicalOutlookCode"
                :columns="politicalStatusDict"
                placeholder="请选择政治面貌"
                align-right
                @confirm="handlePoliticalStatusConfirm"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">国籍</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.nationalityName || '中国' }}</text>
            </template>
            <template v-else>
              <wd-picker
                v-model="editForm.nationalityCode"
                :columns="nationalityDict"
                placeholder="请选择国籍"
                align-right
                @confirm="handleNationalityConfirm"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">港澳台</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.hmtName || '--' }}</text>
            </template>
            <template v-else>
              <wd-picker
                v-model="editForm.hmtCode"
                :columns="gatqwDict"
                placeholder="请选择港澳台侨外"
                align-right
                @confirm="handleGatqwConfirm"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">健康状况</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.healthStatusName || '--' }}</text>
            </template>
            <template v-else>
              <wd-picker
                v-model="editForm.healthStatusCode"
                :columns="healthStatusDict"
                placeholder="请选择健康状况"
                align-right
                @confirm="handleHealthStatusConfirm"
              />
            </template>
          </view>
        </view>
      </view>
    </view>

    <!-- 个人辅助信息内容 -->
    <view class="card" v-if="activeTab === 'additional'">
      <view class="info-group">
        <view class="info-title">个人辅助信息</view>
        <view>
          <view class="info-item">
            <text class="info-label">身高</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.height || '--' }} cm</text>
            </template>
            <template v-else>
              <input
                v-model="editForm.height"
                class="info-input"
                type="number"
                placeholder="请输入身高(cm)"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">体重</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.weight || '--' }} kg</text>
            </template>
            <template v-else>
              <input
                v-model="editForm.weight"
                class="info-input"
                type="number"
                placeholder="请输入体重(kg)"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">座位号</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.seatNumber || '--' }}</text>
            </template>
            <template v-else>
              <input
                v-model="editForm.seatNumber"
                class="info-input"
                type="number"
                placeholder="请输入座位号"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">姓名拼音</text>
            <template v-if="!isEditing">
              <text class="info-value">
                {{ (studentInfo as any).namePY || '--' }}
              </text>
            </template>
            <template v-else>
              <input v-model="editForm.namePY" class="info-input" placeholder="请输入姓名拼音" />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">曾用名</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ (studentInfo as any).formerName || '--' }}</text>
            </template>
            <template v-else>
              <input v-model="editForm.formerName" class="info-input" placeholder="请输入曾用名" />
            </template>
          </view>

          <view class="info-item">
            <text class="info-label">当前学历</text>
            <template v-if="!isEditing">
              <text class="info-value">
                {{ getEducationalName(studentInfo.educationalCode || '') || '--' }}
              </text>
            </template>
            <template v-else>
              <wd-picker
                v-model="editForm.educationalCode"
                :columns="educationalDict"
                placeholder="请选择学历"
                align-right
                @confirm="handleEducationalConfirm"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">户口所在地</text>
            <RegionPicker
              v-model="editForm.accountsLocation"
              :location-name="
                (editForm as any).accountsLocationName || (studentInfo as any).accountsLocationName
              "
              :is-editing="isEditing"
              placeholder="请选择户口所在地"
              title="请选择户口所在地"
              @region-selected="handleAccountsLocationSelected"
            />
          </view>
          <view class="info-item">
            <text class="info-label">户口类型</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.accountsCategoryName || '--' }}</text>
            </template>
            <template v-else>
              <wd-picker
                v-model="editForm.accountsCategoryCode"
                :columns="accountsCategoryDict"
                placeholder="请选择户口类型"
                align-right
                @confirm="handleAccountsCategoryConfirm"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">是否独生子女</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.isAlone }}</text>
            </template>
            <template v-else>
              <wd-picker
                v-model="editForm.isAlone"
                :columns="isAloneDict"
                placeholder="请选择是否独生子女"
                align-right
                @confirm="handleIsAloneConfirm"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">户口详细地址</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.accountAddress || '--' }}</text>
            </template>
            <template v-else>
              <input
                v-model="editForm.accountAddress"
                class="info-input"
                placeholder="请输入户口详细地址"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">户口派出所</text>
            <template v-if="!isEditing">
              <text class="info-value">
                {{ (studentInfo as any).accountPoliceStation || '--' }}
              </text>
            </template>
            <template v-else>
              <input
                v-model="editForm.accountPoliceStation"
                class="info-input"
                placeholder="请输入户口派出所"
              />
            </template>
          </view>
        </view>
      </view>

      <!-- 编辑状态下的操作按钮 -->
    </view>

    <!-- 个人联系信息内容 -->
    <view class="card" v-if="activeTab === 'contact'">
      <view class="info-group">
        <view class="info-title">个人联系信息</view>
        <view>
          <view class="info-item">
            <text class="info-label">现家庭住址地区</text>
            <RegionPicker
              v-model="editForm.currentHomeLocation"
              :location-name="
                (editForm as any).currentHomeLocationName || studentInfo.currentHomeLocationName
              "
              :is-editing="isEditing"
              placeholder="请选择现家庭住址地区"
              title="请选择现家庭住址地区"
              @region-selected="handleCurrentHomeLocationSelected"
            />
          </view>
          <view class="info-item">
            <text class="info-label">居住地类型</text>
            <template v-if="!isEditing">
              <text class="info-value">
                {{ getResidenceTypeName(studentInfo.locationCategoryCode || '') }}
              </text>
            </template>
            <template v-else>
              <wd-picker
                v-model="editForm.locationCategoryCode"
                :columns="residenceTypeDict"
                placeholder="请选择居住地类型"
                align-right
                @confirm="handleResidenceTypeConfirm"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">家庭联系电话</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.homePhone || '--' }}</text>
            </template>
            <template v-else>
              <input
                v-model="editForm.homePhone"
                class="info-input"
                placeholder="请输入家庭联系电话"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">家庭地址</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.homeAddress || '--' }}</text>
            </template>
            <template v-else>
              <input
                v-model="editForm.homeAddress"
                class="info-input"
                placeholder="请输入家庭地址"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">邮编</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.Zip || '--' }}</text>
            </template>
            <template v-else>
              <input v-model="editForm.Zip" class="info-input" placeholder="请输入邮编" />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">电子邮箱</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.email || '--' }}</text>
            </template>
            <template v-else>
              <input v-model="editForm.email" class="info-input" placeholder="请输入电子邮箱" />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">移动电话</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.mobile || '--' }}</text>
            </template>
            <template v-else>
              <input v-model="editForm.mobile" class="info-input" placeholder="请输入移动电话" />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">紧急联系人(家长)</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.parentsName || '--' }}</text>
            </template>
            <template v-else>
              <input
                v-model="editForm.parentsName"
                class="info-input"
                placeholder="请输入紧急联系人"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">紧急联系人电话</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.parentsPhone || '--' }}</text>
            </template>
            <template v-else>
              <input
                v-model="editForm.parentsPhone"
                class="info-input"
                placeholder="请输入紧急联系人电话"
              />
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">住宿情况</text>
            <text class="info-value">{{ studentInfo.lodgingName || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">宿舍楼</text>
            <text class="info-value">{{ studentInfo.locationBuildName || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">房号</text>
            <text class="info-value">{{ studentInfo.roomNum || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">入学时间</text>
            <text class="info-value">{{ studentInfo.startStudyDate || '--' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">入团时间</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.leagueDate || '--' }}</text>
            </template>
            <template v-else>
              <wd-calendar
                v-model="leagueDateTimestamp"
                type="date"
                align-right
                :min-date="sixMonthsAgo"
                :max-date="todayTimestamp"
                with-cell="false"
                immediate-change
                @confirm="handleLeagueDateConfirm"
              >
                <view class="date-select-trigger">
                  {{ editForm.leagueDate || '请选择日期' }}
                  <wd-icon name="arrow-right" size="28rpx" color="#999" />
                </view>
              </wd-calendar>
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">入党时间</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.partyDate || '--' }}</text>
            </template>
            <template v-else>
              <wd-calendar
                v-model="partyDateTimestamp"
                type="date"
                align-right
                :min-date="sixMonthsAgo"
                :max-date="todayTimestamp"
                with-cell="false"
                immediate-change
                @confirm="handlePartyDateConfirm"
              >
                <view class="date-select-trigger">
                  {{ editForm.partyDate || '请选择日期' }}
                  <wd-icon name="arrow-right" size="28rpx" color="#999" />
                </view>
              </wd-calendar>
            </template>
          </view>
          <view class="info-item">
            <text class="info-label">生源地信息</text>
            <RegionPicker
              v-model="editForm.stuOriginLocation"
              :location-name="
                (editForm as any).stuOriginLocationName || studentInfo.stuOriginLocationName
              "
              :is-editing="isEditing"
              placeholder="请选择生源地"
              title="请选择生源地"
              @region-selected="handleStuOriginLocationSelected"
            />
          </view>
          <view class="info-item">
            <text class="info-label"></text>
            <text class="info-value info-note">
              注：生源地是指参加高考时户籍所在地，如生源地信息错误将影响毕业后就业派遣等事宜。
            </text>
          </view>
          <view class="info-item">
            <text class="info-label">银行卡信息</text>
            <rich-text class="info-value" :nodes="studentInfo.bankInfo || '--'"></rich-text>
          </view>
          <view class="info-item">
            <text class="info-label">个人简历</text>
            <template v-if="!isEditing">
              <text class="info-value">{{ studentInfo.resume || '--' }}</text>
            </template>
            <template v-else>
              <textarea
                v-model="editForm.resume"
                class="info-textarea"
                placeholder="请输入个人简历"
              ></textarea>
            </template>
          </view>
        </view>
      </view>
    </view>

    <!-- 文件上传对话框 -->
    <ConfirmDialog
      ref="uploadDialogRef"
      title="请拍照上传相关材料"
      confirm-text="确认"
      cancel-text="取消"
      @confirm="handleUploadConfirm"
      @cancel="cancelUpload"
    >
      <template #content>
        <view class="upload-content">
          <text class="upload-message">
            1、本人承诺书。
            <br />
            2、入伍当地武装部出具的双合格（体检合格、政审合格）证明。
          </text>
          <view class="mt-4">
            <FileUploader
              v-model="uploadedFiles"
              title="上传材料"
              tip-text="仅支持jpg、png等图片格式"
              :extensions="['jpg', 'jpeg', 'png']"
              :count="5"
              upload-type="military_application"
            />
          </view>
        </view>
      </template>
    </ConfirmDialog>

    <!-- 申请确认对话框 -->
    <ConfirmDialog
      ref="confirmDialogRef"
      :title="applicationTypes[currentApplicationType as keyof typeof applicationTypes]?.title"
      :message="applicationTypes[currentApplicationType as keyof typeof applicationTypes]?.message"
      confirm-text="确认申请"
      cancel-text="取消"
      @confirm="confirmApplication"
      @cancel="cancelApplication"
    />

    <!-- 底部固定操作区 -->
    <view v-if="isEditing" class="bottom-action-bar">
      <view class="action-buttons">
        <wd-button class="bottom-cancel-btn" size="large" @click="cancelEditing">取消</wd-button>
        <wd-button class="bottom-save-btn" size="large" type="primary" @click="saveInfo">
          保存
        </wd-button>
      </view>
    </view>
  </view>
</template>
<style scoped lang="scss">
.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 32rpx;
  background-color: #f7f8fc;

  &.has-bottom-bar {
    padding-bottom: calc(32rpx + 128rpx + env(safe-area-inset-bottom, 0));
  }
}

.top-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;

  .back-btn {
    display: flex;
    align-items: center;

    .back-text {
      margin-left: 4rpx;
      font-size: 28rpx;
      color: #333333;
    }
  }

  .page-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #1f2329;
  }

  .placeholder {
    width: 64rpx; /* 与返回按钮宽度一致，保持对称 */
  }
}

.card {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  padding: 32rpx;
  margin-bottom: 32rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(31, 35, 41, 0.05);
}

.profile-header {
  display: flex;
  align-items: center;
  padding-bottom: 32rpx;
  border-bottom: 1rpx solid rgba(31, 35, 41, 0.1);

  .avatar-container {
    position: relative;
    width: 120rpx;
    height: 120rpx;
    margin-right: 32rpx;

    .avatar {
      width: 100%;
      height: 100%;
      border-radius: 60rpx;
    }

    .avatar-upload-icon {
      position: absolute;
      right: 0;
      bottom: 0;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 48rpx;
      height: 48rpx;
      background-color: rgba(0, 0, 0, 0.6);
      border-radius: 24rpx;
    }

    .avatar-loading {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 3;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.3);
      border-radius: 60rpx;
    }

    .avatar-loading-mask {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 60rpx;

      .upload-text {
        font-size: 24rpx;
        color: #ffffff;
      }
    }
  }

  .basic-info {
    flex: 1;

    .name {
      display: block;
      margin-bottom: 8rpx;
      font-size: 36rpx;
      font-weight: 600;
      color: #1f2329;
    }

    .job-number {
      font-size: 28rpx;
      color: #86909c;
    }
  }

  .edit-button-container {
    margin-left: auto;

    .edit-button {
      height: 64rpx;
      font-size: 26rpx;
      font-weight: 500;
      color: #ffffff;
      background: linear-gradient(135deg, #3a8eff 0%, #6fa6ff 100%);
      border: none;
      border-radius: 8rpx;
      box-shadow: 0 6rpx 12rpx rgba(58, 142, 255, 0.2);
    }

    .edit-actions {
      display: flex;
      gap: 16rpx;

      .cancel-button {
        height: 64rpx;
        font-size: 26rpx;
        font-weight: 500;
        color: #4e5969;
        background: #f2f3f5;
        border: none;
        border-radius: 8rpx;
      }

      .save-button {
        height: 64rpx;
        font-size: 26rpx;
        font-weight: 500;
        color: #ffffff;
        background: linear-gradient(135deg, #3a8eff 0%, #6fa6ff 100%);
        border: none;
        border-radius: 8rpx;
        box-shadow: 0 6rpx 12rpx rgba(58, 142, 255, 0.2);
      }
    }
  }
}

.status-summary {
  display: flex;
  justify-content: space-around;
  padding-top: 32rpx;
  text-align: center;

  .status-item {
    .status-value {
      display: block;
      margin-bottom: 8rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: var(--primary-color, #1989fa);
    }

    .status-label {
      font-size: 24rpx;
      color: #86909c;
    }
  }
}

.tab-container {
  display: flex;
  margin-bottom: 32rpx;
  background: #ffffff;
  border-bottom: 1rpx solid rgba(31, 35, 41, 0.1);
  border-radius: 24rpx 24rpx 0 0;

  .tab-item {
    position: relative;
    flex: 1;
    padding: 24rpx 0;
    font-size: 28rpx;
    color: #86909c;
    text-align: center;

    &.active {
      font-weight: 500;
      color: var(--primary-color, #1989fa);

      &::after {
        position: absolute;
        bottom: -1rpx;
        left: 50%;
        width: 40rpx;
        height: 6rpx;
        content: '';
        background-color: var(--primary-color, #1989fa);
        border-radius: 6rpx;
        transform: translateX(-50%);
      }
    }
  }
}

.info-group {
  position: relative;
  margin-bottom: 40rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .info-title {
    position: relative;
    padding-left: 20rpx;
    margin-bottom: 24rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #1f2329;

    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      width: 6rpx;
      height: 32rpx;
      content: '';
      background-color: var(--primary-color, #1989fa);
      border-radius: 6rpx;
      transform: translateY(-50%);
    }
  }
}

.info-item {
  display: flex;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(31, 35, 41, 0.1);

  &:last-child {
    border-bottom: none;
  }

  .info-label {
    flex-shrink: 0;
    width: 180rpx;
    font-size: 28rpx;
    color: #86909c;
  }

  .info-value {
    flex: 1;
    font-size: 28rpx;
    color: #1f2329;
  }
}

.photo-preview {
  position: relative;
  width: 120rpx;
  height: 160rpx;
  overflow: hidden;
  border-radius: 8rpx;

  .photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .photo-upload-icon {
    position: absolute;
    right: 8rpx;
    bottom: 8rpx;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48rpx;
    height: 48rpx;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 24rpx;
  }
}

.empty-content {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  font-size: 28rpx;
  color: #86909c;
}

.info-note {
  font-size: 24rpx;
  line-height: 1.4;
  color: #86909c;
}

.info-value :deep(br) {
  display: block;
  margin: 4rpx 0;
}

.info-input {
  flex: 1;
  height: 60rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: #1f2329;
  background-color: #f7f8fa;
  border: 1rpx solid #e5e6eb;
  border-radius: 8rpx;
}

.info-textarea {
  flex: 1;
  height: 200rpx;
  padding: 16rpx;
  font-size: 28rpx;
  color: #1f2329;
  background-color: #f7f8fa;
  border: 1rpx solid #e5e6eb;
  border-radius: 8rpx;
}

.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 1;
  box-sizing: border-box;
  width: 100%;
  padding: 24rpx 32rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #e5e6eb;
  box-shadow: 0 -4rpx 16rpx rgba(31, 35, 41, 0.1);

  .action-buttons {
    display: flex;
    gap: 24rpx;

    .bottom-cancel-btn,
    .bottom-save-btn {
      flex: 1;
      height: 80rpx;
      font-size: 30rpx;
      font-weight: 500;
      border-radius: 12rpx;
    }

    .bottom-cancel-btn {
      color: #4e5969;
      background: #f2f3f5;
      border: none;
    }

    .bottom-save-btn {
      color: #ffffff;
      background: linear-gradient(135deg, #3a8eff 0%, #6fa6ff 100%);
      border: none;
      box-shadow: 0 6rpx 12rpx rgba(58, 142, 255, 0.2);
    }
  }
}

// 为底部操作栏添加安全区域内边距（适配全面屏）
@supports (padding-bottom: env(safe-area-inset-bottom)) {
  .bottom-action-bar {
    padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  }
}

.edit-actions-row {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
  margin-top: 32rpx;

  .cancel-button-inline,
  .save-button-inline {
    height: 64rpx;
    font-size: 26rpx;
    font-weight: 500;
  }

  .cancel-button-inline {
    color: #4e5969;
    background: #f2f3f5;
    border: none;
    border-radius: 8rpx;
  }

  .save-button-inline {
    color: #ffffff;
    background: linear-gradient(135deg, #3a8eff 0%, #6fa6ff 100%);
    border: none;
    border-radius: 8rpx;
    box-shadow: 0 6rpx 12rpx rgba(58, 142, 255, 0.2);
  }
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.rotate {
  animation: rotating 2s linear infinite;
}

.date-select-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 60rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  color: #1f2329;
  background-color: #f7f8fa;
  border: 1rpx solid #e5e6eb;
  border-radius: 8rpx;
}

// 申请按钮区域样式
.application-buttons {
  .button-group {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
  }
}

// 文件上传对话框内容样式
.upload-content {
  text-align: left;
}

.upload-message {
  margin-bottom: 24rpx;
  font-size: 28rpx;
  line-height: 1.6;
  color: #666;
}
</style>
