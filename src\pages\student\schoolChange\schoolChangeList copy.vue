<script setup lang="ts">
import { ref } from 'vue'

interface StudentInfo {
  name: string
  studentId: string
  department: string
  major: string
  className: string
  educationSystem: string
  enrollmentDate: string
  expectedGraduation: string
  status: string
}

interface StatusBadge {
  text: string
  active: boolean
}

interface TimelineItem {
  title: string
  date: string
  content: string
  status: string
  active?: boolean
}

// 学生信息数据
const studentInfo = ref<StudentInfo>({
  name: '张小明',
  studentId: '2021010203',
  department: '计算机科学与技术学院',
  major: '软件工程',
  className: '软件工程2班',
  educationSystem: '四年制本科',
  enrollmentDate: '2021年9月',
  expectedGraduation: '2025年7月',
  status: '在读',
})

// 状态标签数据
const statusBadges = ref<StatusBadge[]>([
  { text: '在籍', active: true },
  { text: '在校', active: true },
  { text: '普通全日制', active: true },
  { text: '休学', active: false },
  { text: '退学', active: false },
  { text: '转专业', active: false },
  { text: '保留学籍', active: false },
])

// 学籍变更记录数据
const changeRecords = ref<TimelineItem[]>([
  {
    title: '复学',
    date: '2023-09-01',
    content: '因健康原因申请休学一学期后，已完全康复，申请复学继续学习。',
    status: '已批准',
    active: true,
  },
  {
    title: '休学',
    date: '2023-02-15',
    content: '因健康原因申请休学一学期，预计2023年9月返校继续学习。',
    status: '已批准',
  },
  {
    title: '学籍注册',
    date: '2021-09-10',
    content: '2021级新生入学学籍注册，专业：软件工程，学制：四年，学习形式：普通全日制本科。',
    status: '已完成',
  },
])

// 异动申请数据
const applicationRecords = ref<TimelineItem[]>([
  {
    title: '转专业申请',
    date: '2024-01-15',
    content: '申请从软件工程专业转入人工智能专业，理由：对人工智能领域有更强的兴趣和发展规划。',
    status: '审核中',
  },
])
</script>

<template>
  <view class="min-h-screen bg-light-2">
    <!-- 学生基本信息卡片 -->
    <view class="m-4 bg-white rounded-3 overflow-hidden shadow-sm">
      <view class="flex items-center p-4 border-b border-gray-100">
        <view class="w-15 h-15 rounded-full bg-blue flex items-center justify-center mr-4">
          <wd-icon name="user" size="30px" color="#ffffff"></wd-icon>
        </view>
        <view class="flex-1">
          <view class="text-lg font-semibold text-gray-900 mb-1">{{ studentInfo.name }}</view>
          <view class="text-sm text-gray-500">学号：{{ studentInfo.studentId }}</view>
        </view>
        <view class="bg-green-100 text-green-500 px-2 py-1 rounded text-xs font-medium">
          {{ studentInfo.status }}
        </view>
      </view>

      <view
        class="flex py-3 px-4 border-b border-gray-100"
        v-for="(label, field, index) in {
          department: '院系',
          major: '专业',
          className: '班级',
          educationSystem: '学制',
          enrollmentDate: '入学时间',
          expectedGraduation: '预计毕业',
        }"
        :key="index"
        :class="{ 'border-b-0': index === 5 }"
      >
        <view class="w-20 text-gray-500 text-sm">{{ label }}:</view>
        <view class="flex-1 text-gray-900 text-sm">
          {{ studentInfo[field as keyof StudentInfo] }}
        </view>
      </view>
    </view>

    <!-- 状态标签区域 -->
    <view class="m-4 bg-white rounded-3 p-4 shadow-sm flex flex-wrap gap-2">
      <view
        v-for="(badge, index) in statusBadges"
        :key="index"
        class="px-3 py-1.5 rounded-full text-sm"
        :class="badge.active ? 'bg-green-100 text-green-500' : 'bg-gray-200 text-gray-500'"
      >
        {{ badge.text }}
      </view>
    </view>

    <!-- 学籍变更记录 -->
    <view class="px-4 pt-5 pb-2.5 flex justify-between items-center">
      <view class="text-gray-700 font-semibold text-base">学籍变更记录</view>
      <view class="text-sm text-blue flex items-center">
        查看全部
        <wd-icon name="arrow-right" size="13px"></wd-icon>
      </view>
    </view>

    <view class="relative mx-4 mb-4 pl-5">
      <!-- 时间线竖线 -->
      <view class="absolute left-0 top-1.5 bottom-5 w-0.5 bg-gray-200"></view>

      <view
        class="relative pb-6"
        v-for="(item, index) in changeRecords"
        :key="index"
        :class="{ 'pb-0': index === changeRecords.length - 1 }"
      >
        <!-- 时间线圆点 -->
        <view
          class="absolute left-[-29px] top-0 w-4 h-4 rounded-full border-2 border-blue z-1"
          :class="item.active ? 'bg-blue' : 'bg-white'"
        ></view>

        <!-- 内容卡片 -->
        <view class="bg-white rounded-3 p-4 shadow-sm">
          <view class="flex justify-between items-center mb-2.5">
            <view class="font-semibold text-gray-900">{{ item.title }}</view>
            <view class="text-xs text-gray-500">{{ item.date }}</view>
          </view>
          <view class="text-gray-700 text-sm leading-normal">
            {{ item.content }}
          </view>
          <view class="flex justify-between mt-2.5 text-sm">
            <view class="text-gray-500">状态: {{ item.status }}</view>
            <view class="text-blue">查看详情</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 异动申请 -->
    <view class="px-4 pt-5 pb-2.5 font-semibold text-gray-700 text-base">异动申请</view>

    <view class="relative mx-4 mb-4 pl-5">
      <!-- 时间线竖线 -->
      <view class="absolute left-0 top-1.5 bottom-5 w-0.5 bg-gray-200"></view>

      <view
        class="relative pb-6"
        v-for="(item, index) in applicationRecords"
        :key="index"
        :class="{ 'pb-0': index === applicationRecords.length - 1 }"
      >
        <!-- 时间线圆点 -->
        <view
          class="absolute left-[-29px] top-0 w-4 h-4 rounded-full bg-white border-2 border-blue z-1"
        ></view>

        <!-- 内容卡片 -->
        <view class="bg-white rounded-3 p-4 shadow-sm">
          <view class="flex justify-between items-center mb-2.5">
            <view class="font-semibold text-gray-900">{{ item.title }}</view>
            <view class="text-xs text-gray-500">{{ item.date }}</view>
          </view>
          <view class="text-gray-700 text-sm leading-normal">
            {{ item.content }}
          </view>
          <view class="flex justify-between mt-2.5 text-sm">
            <view class="text-gray-500">状态: {{ item.status }}</view>
            <view class="text-blue">查看详情</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 悬浮按钮 -->
    <view
      class="fixed bottom-20 right-5 w-14 h-14 rounded-full bg-blue flex items-center justify-center shadow-md z-10"
    >
      <wd-icon name="edit-outline" size="24px" color="#ffffff"></wd-icon>
    </view>
  </view>
</template>

<style scoped lang="scss">
.bg-light-2 {
  background-color: #f5f5f7;
}

.bg-blue {
  background-color: #3b82f6;
}

.text-blue {
  color: #3b82f6;
}

.bg-green-100 {
  background-color: #dcfce7;
}

.text-green-500 {
  color: #22c55e;
}

.shadow-sm {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.shadow-md {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
</style>
