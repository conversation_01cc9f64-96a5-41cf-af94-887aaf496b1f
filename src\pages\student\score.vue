<route lang="json5">
{
  style: {
    navigationBarTitleText: '成绩信息',
  },
}
</route>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import * as echarts from 'echarts'
import { getStudentScore } from '@/service/student'
import { getStudentTotalScore } from '@/service/score'
// import { getSemesterSelect } from '@/service/system'
import { getSemesterConfig } from '@/service/semester'
import { loadDictData, getDictLabel, getDictOptions } from '@/utils/dict'
import type { ScoreItem, ScoreStatistics } from '@/types/student'
import type { TotalScoreResponse, TotalScoreQuery } from '@/types/score'
// import type { SemesterOption } from '@/types/semester'
import type { DictData } from '@/types/system'
// 导入SchoolYearPicker组件
import SchoolYearPicker from '@/components/SchoolYearPicker/index.vue'

// 学年选择
const yearValue = ref('')

// 课程筛选
const showCourseFilter = ref(false)
const selectedCourse = ref<CourseScore | null>(null)
const courseFilterOptions = computed(() => {
  const options = [{ label: '全部课程', value: '' }]

  // 从原始成绩明细中提取不重复的课程
  const uniqueCourses = new Map<string, { label: string; value: string }>()

  gradeDetails.value.forEach((item) => {
    if (!uniqueCourses.has(item.kcdm)) {
      uniqueCourses.set(item.kcdm, {
        label: item.kcmc,
        value: item.kcdm,
      })
    }
  })

  return [...options, ...Array.from(uniqueCourses.values())]
})

// 课程性质字典
const courseTypeDict = ref<DictData[]>([])
const courseTypeOptions = ref<{ label: string; value: string }[]>([])

// 获取课程性质字典
const getCourseTypeDict = async () => {
  try {
    const dicts = await loadDictData(['DM_KCXZ'])
    courseTypeDict.value = dicts.DM_KCXZ
    console.log(getDictOptions(dicts.DM_KCXZ))
    courseTypeOptions.value = getDictOptions(dicts.DM_KCXZ)
  } catch (error) {
    console.error('获取课程性质字典失败:', error)
    uni.showToast({
      title: '获取课程性质字典失败',
      icon: 'error',
    })
  }
}

// 获取课程性质名称
const getCourseTypeName = (type: string) => {
  return getDictLabel(courseTypeDict.value, type)
}

// 获取学期列表
/*
const getSemesterOptions = async () => {
  try {
    const res = await getSemesterSelect()
    // 添加全部学年学期选项
    semesterOptions.value = res.data
    // 默认选择全部学年学期
    currentSemester.value = res.data[0]
  } catch (error) {
    console.error('获取学期列表失败:', error)
  }
}
*/

// 初始化图表
const initCharts = async () => {
  // 初始化成绩分布图表
  /*
  if (chartRef.value) {
    const myChart = await chartRef.value.init(echarts)
    const option = {
      tooltip: {
        trigger: 'axis',
        confine: true,
      },
      grid: {
        left: 20,
        right: 20,
        bottom: 15,
        top: 40,
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: gradeDistribution.value.map((item) => item.range),
          axisLine: {
            lineStyle: {
              color: '#999999',
            },
          },
          axisLabel: {
            color: '#666666',
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: '#999999',
            },
          },
          axisLabel: {
            color: '#666666',
          },
        },
      ],
      series: [
        {
          type: 'bar',
          data: gradeDistribution.value.map((item) => item.count),
          itemStyle: {
            color: '#1890ff',
          },
          label: {
            show: true,
            position: 'top',
          },
        },
      ],
    }
    myChart.setOption(option)
  }
  */

  // 初始化学习趋势图表
  if (trendChartRef.value) {
    const trendChart = await trendChartRef.value.init(echarts)
    const primaryColor = '#3b7cff'

    const trendOption = {
      tooltip: {
        trigger: 'axis',
        confine: true,
      },
      grid: {
        left: 20,
        right: 20,
        bottom: 15,
        top: 40,
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          data: studyTrend.value.map((item) => item.semester),
          axisLine: {
            lineStyle: {
              color: '#999999',
            },
          },
          axisLabel: {
            color: '#666666',
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          min: 0,
          max: 4.5,
          axisLine: {
            lineStyle: {
              color: '#999999',
            },
          },
          axisLabel: {
            color: '#666666',
            formatter: '{value}',
          },
        },
      ],
      series: [
        {
          type: 'line',
          data: studyTrend.value.map((item) => item.gpa),
          itemStyle: {
            color: primaryColor,
          },
          label: {
            show: true,
            position: 'top',
            formatter: '{c}',
          },
          lineStyle: {
            width: 3,
            shadowColor: 'rgba(0,0,0,0.2)',
            shadowBlur: 6,
          },
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
        },
      ],
    }
    trendChart.setOption(trendOption)
  }
}

// 处理学年变化
const handleYearChange = (year: { label: string; value: string }) => {
  console.log('学年变化:', year)
  selectedCourse.value = null // 重置课程选择
  fetchScoreData() // 获取新学年的成绩数据
  fetchTotalScoreData() // 获取新学年的总评成绩数据
}

// 总体概览数据
const overviewData = ref({
  taskCount: '0', // 任务数
  dailyScoreCount: '0', // 日常成绩数
  averageScore: '0', // 平均分
  failingCount: '0', // 不及格数
})

// 课程成绩数据结构（合并后）
interface CourseScore {
  id: string // 课程ID：courseCode + semesters 作为唯一标识
  courseCode: string
  courseName: string
  courseType: string
  courseCredit: number
  leaderTeacherName: string
  leaderTeacherCode: string
  totalScore: number // 总分
  passMark: number // 是否通过
  courseGpa: string // 课程绩点
  className: string
  semesters: string
  scoreItems: ScoreItem[] // 该课程的所有成绩组成项
}

// 成绩明细数据（原始数据）
const gradeDetails = ref<ScoreItem[]>([])

// 合并后的课程成绩数据
const courseScores = computed<CourseScore[]>(() => {
  const courseMap = new Map<string, CourseScore>()

  gradeDetails.value.forEach((item) => {
    const courseKey = `${item.kcdm}-${item.semesters}`

    if (!courseMap.has(courseKey)) {
      // 新课程，创建记录
      courseMap.set(courseKey, {
        id: courseKey,
        courseCode: item.kcdm,
        courseName: item.kcmc,
        courseType: item.kcxz,
        courseCredit: item.kcxf,
        leaderTeacherName: item.zdjsxm,
        leaderTeacherCode: item.zdjs,
        totalScore: item.comprehensiveScore, // 使用综合得分作为总分
        passMark: item.tgbz,
        courseGpa: item.cjjd,
        className: item.ssbjmc,
        semesters: item.semesters,
        scoreItems: [item],
      })
    } else {
      // 已有课程，添加成绩项
      const course = courseMap.get(courseKey)!
      course.scoreItems.push(item)

      // 如果是总成绩/综合成绩类型，更新课程总分和绩点
      if (item.rccjlxmc.includes('总') || item.rccjlxmc.includes('综合')) {
        course.totalScore = item.comprehensiveScore
        course.courseGpa = item.cjjd
      }
    }
  })

  return Array.from(courseMap.values())
})

// 课程详情展开状态 - 改为只控制显示评语部分
const expandedComments = ref<Record<string, boolean>>({})

// 切换评语展开状态
const toggleCourseComments = (courseId: string) => {
  expandedComments.value[courseId] = !expandedComments.value[courseId]
}

// 学习趋势数据
const studyTrend = ref([
  { semester: '大一上', gpa: 0, height: 0 },
  { semester: '大一下', gpa: 0, height: 0 },
  { semester: '大二上', gpa: 0, height: 0 },
  { semester: '大二下', gpa: 0, height: 0 },
  { semester: '大三上', gpa: 0, height: 0 },
])

// 学习建议
const studySuggestions = ref<string[]>([])

// 成绩分布图表
// const chartRef = ref()
// 学习趋势图表
const trendChartRef = ref()

// 计算平均绩点
const calculateAverageGPA = (items: CourseScore[]): string => {
  if (items.length === 0) return '0.0'
  const totalGPA = items.reduce((sum, item) => sum + parseFloat(item.courseGpa), 0)
  return (totalGPA / items.length).toFixed(1)
}

// 计算平均分
const calculateAverageScore = (items: CourseScore[]): string => {
  if (items.length === 0) return '0'
  const totalScore = items.reduce((sum, item) => sum + item.totalScore, 0)
  return (totalScore / items.length).toFixed(1)
}

// 更新学习建议
const updateStudySuggestions = (items: CourseScore[], stats: ScoreStatistics) => {
  const suggestions: string[] = []

  // 分析不及格课程
  const failingCourses = items.filter((item) => item.passMark === 0)
  if (failingCourses.length > 0) {
    suggestions.push(
      `你有${failingCourses.length}门课程不及格，包括${failingCourses
        .map((c) => c.courseName)
        .join('、')}，需要重点关注这些课程的学习。`,
    )
  }

  // 分析优秀课程
  const excellentCourses = items.filter((item) => item.totalScore >= 90)
  if (excellentCourses.length > 0) {
    suggestions.push(
      `你在${excellentCourses
        .map((c) => c.courseName)
        .join('、')}等课程表现优秀，继续保持良好的学习状态。`,
    )
  }

  // 分析平均分
  const avgScore = parseFloat(calculateAverageScore(items))
  if (avgScore >= 85) {
    suggestions.push('你的平均分较高，学习状态良好，继续保持。')
  } else if (avgScore >= 75) {
    suggestions.push('你的平均分处于中等水平，建议加强学习，争取更好的成绩。')
  } else {
    suggestions.push('你的平均分较低，需要调整学习方法，提高学习效率。')
  }

  studySuggestions.value = suggestions
}

// 更新统计信息
const updateStatistics = (items: CourseScore[] | ScoreItem[], stats: ScoreStatistics) => {
  // 从stats中获取统计数据
  const taskCount = stats.rws.toString() // 任务数
  const dailyScoreCount = stats.cjs.toString() // 日常成绩数
  const avgScore = stats.pjf.toString() // 平均分
  const failingCount = stats.bjgs.toString() // 不及格数

  // 更新数据
  overviewData.value = {
    taskCount,
    dailyScoreCount,
    averageScore: avgScore,
    failingCount,
  }
}

// 添加一个变量来保存统计信息
const scoreStatistics = ref<ScoreStatistics>({
  rws: 0,
  cjs: 0,
  pjf: 0,
  bjgs: 0,
  pageData: [],
  typeData: [],
})

// 总评成绩数据
const totalScoreData = ref<TotalScoreResponse | null>(null)
const hasTotalScore = computed(
  () => totalScoreData.value && totalScoreData.value.items && totalScoreData.value.items.length > 0,
)

// 获取成绩数据
const fetchScoreData = async () => {
  try {
    // 如果没有选择学年，给出提示并返回

    if (!yearValue.value) {
      console.warn('未选择学期，无法获取成绩数据')
      return
    }

    const params: any = {
      page: 1,
      pageSize: 100,
    }

    // 如果不是全部学年，则添加学期参数
    if (yearValue.value) {
      params.semesters = [yearValue.value]
    }

    const res = await getStudentScore(params)

    // 保存统计信息
    scoreStatistics.value = res.statistics || {
      rws: 0,
      cjs: 0,
      pjf: 0,
      bjgs: 0,
      pageData: [],
      typeData: [],
    }

    // 重置课程选择
    selectedCourse.value = null

    // 更新成绩明细原始数据
    gradeDetails.value = res.items

    // 更新统计信息和成绩分布
    updateDisplayData()

    // 更新学习建议
    // updateStudySuggestions(courseScores.value, res.statistics)

    // 默认展开所有课程
    setAllCoursesExpanded()
  } catch (error) {
    console.error('获取成绩数据失败:', error)
    uni.showToast({
      title: '获取成绩数据失败',
      icon: 'error',
    })
  }
}

// 根据当前视图选择更新数据的数据源
const updateDisplayData = () => {
  // 使用保存的统计信息
  const stats = scoreStatistics.value

  if (selectedCourse.value) {
    // 选择了特定课程，使用该课程的数据
    const courseItems = [selectedCourse.value]
    updateStatistics(courseItems, stats)
  } else {
    // 全部课程视图，使用合并后的课程数据
    updateStatistics(courseScores.value, stats)
  }

  // 更新图表
  setTimeout(() => {
    initCharts()
  }, 300)
}

// 获取学生总评成绩
const fetchTotalScoreData = async () => {
  try {
    if (!yearValue.value) {
      console.warn('未选择学期，无法获取总评成绩数据')
      return
    }

    const params: TotalScoreQuery = {
      page: 1,
      pageSize: 100,
    }

    // 如果不是全部学年，则添加学期参数
    if (yearValue.value) {
      params.semesters = [yearValue.value]
    }

    const res = await getStudentTotalScore(params)
    totalScoreData.value = res
    console.log('总评成绩数据:', res)
  } catch (error) {
    console.error('获取总评成绩数据失败:', error)
  }
}

onMounted(() => {
  initializeData()
})

// 初始化数据函数
const initializeData = () => {
  // 获取课程性质字典
  getCourseTypeDict()
  // 获取成绩数据
  fetchScoreData()
  // 获取总评成绩数据
  fetchTotalScoreData()
}

// 刷新数据
const refreshData = () => {
  fetchScoreData()
  fetchTotalScoreData() // 获取总评成绩数据
  selectedCourse.value = null // 重置课程选择
  uni.showToast({
    title: '刷新成功',
    icon: 'success',
  })
  // 刷新后也默认展开所有课程
  setTimeout(() => {
    setAllCoursesExpanded()
  }, 500)
}

// 选择课程
const selectCourse = (courseCode: string) => {
  if (!courseCode) {
    selectedCourse.value = null
  } else {
    // 找到选择的课程
    const course = courseScores.value.find((c) => c.courseCode === courseCode)

    if (course) {
      selectedCourse.value = course

      // 展开当前选择的课程，确保评语展示正常
      // 如果课程有评语，默认展开评语
      if (course.scoreItems.some((item) => item.rccjsm)) {
        expandedComments.value[course.id] = true
      }
    } else {
      uni.showToast({
        title: '未找到对应课程',
        icon: 'none',
      })
      selectedCourse.value = null
    }
  }
  showCourseFilter.value = false

  // 更新统计和图表数据
  updateDisplayData()
}

// 新增：课程展开状态
const expandedCourses = ref<Record<string, boolean>>({})

// 切换课程详情展开状态
const toggleCourseDetails = (courseId: string) => {
  expandedCourses.value[courseId] = !expandedCourses.value[courseId]
}

// 设置所有课程为展开状态
const setAllCoursesExpanded = () => {
  const newExpandedState: Record<string, boolean> = {}
  courseScores.value.forEach((course) => {
    newExpandedState[course.id] = true
  })
  expandedCourses.value = newExpandedState
}

// 获取课程的总评成绩
const getTotalScoreForCourse = (courseCode: string) => {
  if (!totalScoreData.value || !totalScoreData.value.items) return null
  return totalScoreData.value.items.find((item) => item.kcdm === courseCode)
}
</script>

<template>
  <view class="score-page p-4 bg-gray-100 min-h-screen">
    <!-- 学期选择 -->
    <view class="flex justify-between items-center mb-4">
      <view class="flex items-center flex-1">
        <!-- 使用SchoolYearPicker组件替换原来的学期选择器 -->
        <SchoolYearPicker
          v-model:yearValue="yearValue"
          @yearChange="handleYearChange"
          size="large"
          allYearLabel="全部学年"
        />
      </view>
      <view class="flex items-center">
        <!-- <view
          class="text-blue-500 font-semibold flex items-center mr-3"
          @click="showCourseFilter = true"
        >
          {{ selectedCourse ? selectedCourse.courseName : '全部课程' }}
          <wd-icon name="filter" size="32rpx" class="ml-1"></wd-icon>
        </view> -->

        <!-- <view
          class="flex items-center justify-center w-8 h-8 rounded-full bg-blue-500 text-white"
          @tap="refreshData"
        >
          <wd-icon name="refresh" size=""></wd-icon>
        </view> -->
      </view>
    </view>

    <!-- 总体概览卡片 -->
    <view class="bg-white rounded-lg shadow mb-4 px-4 py-2">
      <view class="font-bold text-lg mb-2">学期总览</view>
      <view class="flex justify-between mb-4">
        <view class="text-center">
          <view class="text-2xl font-bold text-blue-600">{{ overviewData.taskCount }}</view>
          <view class="text-xs text-gray-500">门任务</view>
        </view>
        <view class="text-center">
          <view class="text-2xl font-bold text-green-500">{{ overviewData.dailyScoreCount }}</view>
          <view class="text-xs text-gray-500">次日常成绩</view>
        </view>
        <view class="text-center">
          <view class="text-2xl font-bold text-blue-600">{{ overviewData.averageScore }}</view>
          <view class="text-xs text-gray-500">平均分</view>
        </view>
        <view class="text-center">
          <view class="text-2xl font-bold text-red-500">{{ overviewData.failingCount }}</view>
          <view class="text-xs text-gray-500">不及格数</view>
        </view>
      </view>
    </view>

    <!-- 课程筛选器弹窗 -->
    <wd-popup v-model="showCourseFilter" position="bottom" round>
      <view class="semester-picker">
        <view class="picker-header">
          <text class="picker-title">选择课程</text>
          <wd-icon name="close" size="32rpx" @click="showCourseFilter = false"></wd-icon>
        </view>
        <scroll-view class="picker-content" scroll-y>
          <view
            v-for="item in courseFilterOptions"
            :key="item.value"
            :class="[
              'picker-item',
              {
                active:
                  selectedCourse?.courseCode === item.value || (!selectedCourse && !item.value),
              },
            ]"
            @click="selectCourse(item.value)"
          >
            <text class="item-label">{{ item.label }}</text>
            <wd-icon
              v-if="selectedCourse?.courseCode === item.value || (!selectedCourse && !item.value)"
              name="check"
              size="32rpx"
              color="#3a8eff"
            ></wd-icon>
          </view>
        </scroll-view>
      </view>
    </wd-popup>

    <!-- 未选择课程时：显示课程列表 -->
    <view v-if="!selectedCourse" class="section-card mb-4">
      <view class="p-4 border-b border-gray-100 pb-0">
        <view class="text-base font-bold">课程成绩</view>
      </view>

      <view class="p-3">
        <!-- 空数据状态 -->
        <view
          v-if="courseScores.length === 0"
          class="empty-state p-10 flex flex-col items-center justify-center"
        >
          <wd-icon name="note" size="80rpx" color="#c8c9cc"></wd-icon>
          <view class="text-gray-400 mt-3 text-center">暂无成绩数据</view>
        </view>

        <view
          v-else
          v-for="(item, index) in courseScores"
          :key="index"
          class="course-item mb-3 bg-white rounded-lg overflow-hidden shadow-sm"
        >
          <!-- 课程基本信息 - 可点击展开 -->
          <view
            class="p-3 flex justify-between items-center border-b border-gray-100"
            @click="toggleCourseDetails(item.id)"
          >
            <view>
              <view class="text-base font-medium">{{ item.courseName }}</view>
              <view class="text-xs text-gray-500 mt-1">
                {{ item.leaderTeacherName || '未知教师' }} |
                {{ getCourseTypeName(item.courseType) }} |
                <template v-if="item.courseCredit">{{ item.courseCredit }}学分</template>
                <template v-else>学分统计中</template>
              </view>
            </view>
            <view class="flex items-center">
              <view class="text-right mr-2">
                <!-- 如果有总评成绩数据，则显示总评成绩 -->
                <view class="text-lg font-bold text-blue-600">
                  <template
                    v-if="
                      getTotalScoreForCourse(item.courseCode) &&
                      getTotalScoreForCourse(item.courseCode).cj
                    "
                  >
                    {{ getTotalScoreForCourse(item.courseCode).cj }}
                    <span class="text-sm">分</span>
                  </template>
                  <template v-else-if="item.totalScore">
                    {{ item.totalScore }}
                    <span class="text-sm">分</span>
                  </template>
                  <template v-else>
                    <span class="text-sm">成绩统计中</span>
                  </template>
                </view>
                <view class="text-xs text-gray-500">
                  <template
                    v-if="
                      getTotalScoreForCourse(item.courseCode) &&
                      getTotalScoreForCourse(item.courseCode).cjjd
                    "
                  >
                    绩点: {{ getTotalScoreForCourse(item.courseCode).cjjd }}
                  </template>
                  <template v-else-if="item.courseGpa">绩点: {{ item.courseGpa }}</template>
                  <template v-else>绩点统计中</template>
                </view>
              </view>
              <wd-icon
                :name="expandedCourses[item.id] ? 'chevron-up' : 'chevron-down'"
                size="32rpx"
                class="text-gray-400"
              ></wd-icon>
            </view>
          </view>

          <!-- 课程详情 - 展开后显示 -->
          <view v-if="expandedCourses[item.id]" class="p-3 bg-gray-50">
            <!-- 成绩组成项列表 -->
            <view
              v-for="(scoreItem, itemIndex) in item.scoreItems"
              :key="itemIndex"
              class="score-item p-2 flex justify-between items-center border-b border-gray-100 last:border-0"
            >
              <view>
                <view class="text-sm font-medium">{{ scoreItem.rccjlxmc }}</view>
                <view class="text-xs text-gray-500">
                  {{ scoreItem.rccjkssj ? scoreItem.rccjkssj : '累计' }}
                </view>
              </view>
              <view class="text-right">
                <view class="text-base font-bold text-blue-600">
                  {{ scoreItem.bfzcj }}
                  <span class="text-xs">分</span>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 选择课程后：显示整合的课程成绩 -->
    <view v-if="selectedCourse" class="section-card mb-4">
      <view class="p-4 border-b border-gray-100 flex items-center justify-between pb-0">
        <view>
          <view class="text-base font-bold">{{ selectedCourse.courseName }}</view>
          <view class="text-xs text-gray-500 mt-1">
            {{ selectedCourse.leaderTeacherName || '未知教师' }} |
            {{ getCourseTypeName(selectedCourse.courseType) }} |
            <template v-if="selectedCourse.courseCredit">
              {{ selectedCourse.courseCredit }}学分
            </template>
            <template v-else>学分统计中</template>
          </view>
        </view>
        <view class="text-right">
          <view class="text-sm font-medium">
            总分:
            <text class="text-blue-600">
              <template
                v-if="
                  getTotalScoreForCourse(selectedCourse.courseCode) &&
                  getTotalScoreForCourse(selectedCourse.courseCode).cj
                "
              >
                {{ getTotalScoreForCourse(selectedCourse.courseCode).cj }}
              </template>
              <template v-else-if="selectedCourse.totalScore">
                {{ selectedCourse.totalScore }}
              </template>
              <template v-else>统计中</template>
            </text>
            /100
          </view>
          <view class="text-xs text-gray-500 mt-1">
            <template
              v-if="
                getTotalScoreForCourse(selectedCourse.courseCode) &&
                getTotalScoreForCourse(selectedCourse.courseCode).cjjd
              "
            >
              绩点: {{ getTotalScoreForCourse(selectedCourse.courseCode).cjjd }}
            </template>
            <template v-else-if="selectedCourse.courseGpa">
              绩点: {{ selectedCourse.courseGpa }}
            </template>
            <template v-else>绩点统计中</template>
          </view>
        </view>
      </view>

      <view class="p-3">
        <!-- 空数据状态 -->
        <view
          v-if="selectedCourse.scoreItems.length === 0"
          class="empty-state p-10 flex flex-col items-center justify-center"
        >
          <wd-icon name="note" size="80rpx" color="#c8c9cc"></wd-icon>
          <view class="text-gray-400 mt-3 text-center">暂无成绩明细数据</view>
        </view>

        <!-- 成绩组成项列表 - 始终可见 -->
        <view
          v-else
          v-for="(item, itemIndex) in selectedCourse.scoreItems"
          :key="itemIndex"
          class="score-item p-3 flex justify-between items-center"
        >
          <view>
            <view class="text-base font-medium">{{ item.rccjlxmc }}</view>
            <view class="text-xs text-gray-500 mt-1">
              {{ item.rccjkssj ? item.rccjkssj : '累计' }} | 权重
              {{ Math.round(100 / selectedCourse.scoreItems.length) }}%
            </view>
          </view>
          <view class="text-right">
            <view class="text-lg font-bold text-blue-600">
              {{ item.bfzcj }}
              <span class="text-sm">分</span>
            </view>
            <view class="text-xs text-gray-500">
              班级平均: {{ Math.round(item.bfzcj * 0.95) }}分
            </view>
          </view>
        </view>
      </view>

      <!-- 查看详细评语 - 只显示/隐藏评语 -->
      <view
        v-if="selectedCourse.scoreItems.some((item) => item.rccjsm)"
        class="p-3 border-t border-gray-100 text-center"
      >
        <view
          class="text-blue-600 text-sm font-medium flex items-center justify-center mx-auto"
          @click="toggleCourseComments(selectedCourse.id)"
        >
          {{ expandedComments[selectedCourse.id] ? '收起详细评语' : '查看详细评语' }}
          <wd-icon
            :name="expandedComments[selectedCourse.id] ? 'chevron-up' : 'chevron-down'"
            size="24rpx"
            class="ml-1"
          ></wd-icon>
        </view>

        <!-- 评语内容 -->
        <view v-if="expandedComments[selectedCourse.id]" class="mt-3 text-left">
          <view
            v-for="(item, dIndex) in selectedCourse.scoreItems.filter((s) => s.rccjsm)"
            :key="dIndex"
            class="mb-3 p-3 bg-gray-50 rounded-lg"
          >
            <view class="text-sm font-medium text-gray-700">{{ item.rccjlxmc }}评语:</view>
            <view class="text-sm text-gray-600 mt-1">{{ item.rccjsm }}</view>
          </view>
        </view>
      </view>

      <!-- 返回全部课程按钮 -->
      <view class="p-3 border-t border-gray-100 text-center">
        <view
          class="flex items-center justify-center bg-blue-50 text-blue-500 py-2 px-4 rounded-full mx-auto"
          style="width: fit-content"
          @click="
            () => {
              selectedCourse = null
              updateDisplayData()
            }
          "
        >
          <wd-icon name="arrow-left" size="24rpx" class="mr-1"></wd-icon>
          <text class="text-sm">返回全部课程</text>
        </view>
      </view>
    </view>

    <!-- 成绩分布图 -->
    <!--
    <view class="bg-white rounded-lg shadow mb-4 p-4">
      <view class="flex justify-between items-center mb-4">
        <text class="font-bold text-lg">成绩分布</text>
        <text class="text-xs text-gray-500">单位: 科目数</text>
      </view>
      <view class="chart-container">
        <l-echart ref="chartRef"></l-echart>
      </view>
    </view>
    -->

    <!-- 学习建议 -->
    <!--
    <view v-if="studySuggestions.length > 0" class="bg-white rounded-lg shadow mb-4 p-4">
      <view class="font-bold text-lg mb-2">学习建议</view>
      <view
        v-for="(suggestion, index) in studySuggestions"
        :key="index"
        class="mb-2 text-sm text-gray-700"
      >
        <view class="flex items-start">
          <wd-icon name="info-circle" size="32rpx" color="#3a8eff" class="mr-2 mt-1"></wd-icon>
          <text>{{ suggestion }}</text>
        </view>
      </view>
    </view>
    -->
  </view>
</template>

<style scoped lang="scss">
.chart-container {
  width: 100%;
  height: 600rpx;
  background-color: #fff;
  border-radius: 12rpx;
}

// 课程筛选器样式
.semester-picker {
  max-height: 60vh;
  padding: 32rpx;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;

  .picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32rpx;
  }

  .picker-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  .picker-content {
    max-height: calc(60vh - 100rpx);
  }

  .picker-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx;
    margin-bottom: 16rpx;
    background: #f7f8fa;
    border-radius: 12rpx;
    transition: all 0.3s;

    &:last-child {
      margin-bottom: 0;
    }

    &.active {
      color: #3a8eff;
      background: rgba(58, 142, 255, 0.1);
    }

    .item-label {
      font-size: 28rpx;
    }
  }
}

// 成绩卡片样式
.section-card {
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 15rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.score-item {
  transition: all 0.2s;

  &:active {
    background-color: #f5f5f5;
  }

  &:last-child {
    border-bottom: none;
  }
}

.course-item {
  transition: all 0.2s;

  &:last-child {
    margin-bottom: 0;
  }
}

// 空数据状态样式
.empty-state {
  min-height: 300rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
}
</style>
