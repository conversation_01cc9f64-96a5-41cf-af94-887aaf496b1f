<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的选课列表',
  },
}
</route>
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/store/user'
import {
  getSchoolSelectCourseList,
  unselectCourse,
  getAvailableCourseList,
  selectCourse as selectCourseApi,
} from '@/service/selectCourse'
import { loadDictData, getDictLabel, getDictOptions } from '@/utils/dict'
import type { DictData } from '@/types/system'
import type {
  StudentCourseItem,
  SchoolSelectCourseQuery,
  SchoolSelectCourseResponse,
  UnselectCourseParams,
  AvailableCourseItem,
  AvailableCourseResponse,
  SelectCourseParams,
} from '@/types/selectCourse'

// 用户信息
const userStore = useUserStore()
const userInfo = computed(() => ({
  name: userStore.userInfo.realname,
  major: userStore.userInfo.department,
  grade: userStore.userInfo.className,
}))

// 存储已选课程的选课信息（已选人数和限选人数）
const selectedCoursesInfo = ref<Record<number, { selectedCount: number; maxCount: number }>>({})

// 课程分类字典
const courseCategoryDict = ref<DictData[]>([])
// 当前选中的课程分类
const selectedCourseCategory = ref('')

// 通知内容
const notice = ref({
  title: '选课通知',
  content: '',
  time: '',
})

// 选课详细通知信息
const courseNoticeDetail = ref({
  title: '选课通知',
  currentSemester: '2024-2025(2)',
  selectTimeStart: '2024-12-06 17:00:00',
  selectTimeEnd: '2025-01-18 18:00:00',
  description:
    '根据2023级人才培养方案，2023级选修选修课程三年制不少于学分，五年制不少于6学分。其中，男育类2学分，美育类2学分，人文社科类2学分。及人工智能教育选修课程中"人工智能导论"课程由各二级学院负责组织与实施，技术类，环境类等。生活类选修课程由通识学院负责组织与实施。',
  allowedAreas: '龙城校区,社会校区,平湖校区 软件园校区',
  allowedDepartments: '全部',
  allowedGrades: '全部',
  campusSelectTimeStart: '2024-12-06 17:00:00',
  campusSelectTimeEnd: '2025-01-18 17:00:00',
})

// 选课状态
const selectionStatus = ref({
  selectedCourses: 0,
  earnedCredits: 0,
  availableCourses: 0,
  xsxktjszcheck: 0,
})

// 选课条件
const xsxktjsz = ref({
  remark: '',
})

// 查询参数
const queryParams = ref<SchoolSelectCourseQuery>({
  optype: 'selectList',
  page: 1,
  pageSize: 10,
  semesters: '2024-2025|2',
  courseCategoryName: '',
  courseName: '',
  leaderTeacherName: '',
  className: '',
  courseTotalHours: '',
  weekHours: '',
  creditHour: '',
  startWeek: '',
  teachingInfo: '',
  siteName: '',
  limitCount: '',
  selectedCount: '',
  campusName: '',
})

// 退选相关信息
const unselectInfo = ref({
  startTime: '', // 退选开始时间
  endTime: '', // 退选截止时间
  usedTimes: 0, // 已使用退选次数
  totalTimes: 0, // 总退选次数
})

// API 响应数据
const apiResponse = ref<SchoolSelectCourseResponse | null>(null)

// 已选课程列表
const selectedCourses = ref<StudentCourseItem[]>([])

// 加载状态
const loading = ref(false)

// 当前选中的标签
const activeTab = ref('available')

// 选课进度
const semesterInfo = ref({
  title: '2024-2025学年第二学期选课',
  time: '选课时间: 2025年2月19日 - 2025年7月2日',
  currentCredits: 0,
  maxCredits: 26,
})

// 可选课程列表
const availableCourses = ref<AvailableCourseItem[]>([])

// 数据加载计数器
const dataLoadCounter = ref(0)

// 是否已经显示过弹窗
const popupShown = ref(false)

// 弹窗显示状态
const showPopup = ref(false)

// 仅查看可选课程开关
const onlyShowAvailable = ref(false)

// 切换仅查看可选课程
const toggleOnlyShowAvailable = () => {
  onlyShowAvailable.value = !onlyShowAvailable.value
}

// 过滤后的可选课程列表
const filteredAvailableCourses = computed(() => {
  if (!onlyShowAvailable.value) {
    return availableCourses.value
  }

  // 只返回还有剩余名额的课程（已选人数小于最大人数的课程）
  return availableCourses.value.filter((course) => {
    // 如果maxCount为0或不限，则认为可以选
    if (!course.maxCount) return true
    // 已选人数小于最大人数，说明有名额
    return course.selectedCount < course.maxCount
  })
})

// 过滤后的已选课程列表
const filteredSelectedCourses = computed(() => {
  if (!selectedCourseCategory.value) {
    return selectedCourses.value
  }

  // 按课程类别筛选已选课程
  return selectedCourses.value.filter(
    (course) =>
      course.courseCategoryName &&
      course.courseCategoryName.includes(
        getDictLabel(courseCategoryDict.value, selectedCourseCategory.value),
      ),
  )
})

// 计算动态通知内容
const dynamicNotice = computed(() => {
  if (activeTab.value === 'available') {
    return {
      title: '选课通知',
      content: notice.value.content,
      time: notice.value.time,
    }
  } else {
    // 获取当前学期信息
    const currentSemester = semesterInfo.value.title.replace('选课', '').trim()
    console.log(unselectInfo.value)

    // 计算剩余次数
    const remainingTimes = unselectInfo.value.totalTimes - unselectInfo.value.usedTimes

    // 已选课程页面通知
    return {
      title: '退选通知',
      content: `以下为${currentSemester}你的选课列表，如需要退选，请在[ ${unselectInfo.value.startTime || '2024-12-13 00:00:00'} 至 ${unselectInfo.value.endTime || '2025-04-30 11:20:06'} ]时间范围内操作，你自行退选最多操作 ${unselectInfo.value.totalTimes || 3} 次，你已使用 ${unselectInfo.value.usedTimes} 次，剩余 ${remainingTimes} 次。逾期不再提供退选服务，请慎重操作！`,
      time: notice.value.time,
    }
  }
})

// 获取课程状态文本
const getCourseStatusText = (status: number): string => {
  const statusMap = {
    0: '未开始',
    1: '正常授课中',
    2: '已完成',
  }
  return statusMap[status as keyof typeof statusMap] || '未知状态'
}

// 获取课程状态颜色
const getCourseStatusColor = (status: number): string => {
  const colorMap = {
    0: '#faad14', // 未开始-黄色
    1: '#52c41a', // 进行中-绿色
    2: '#8E8E93', // 已完成-灰色
  }
  return colorMap[status as keyof typeof colorMap] || '#8E8E93'
}

// 查看课程详情
const viewCourseDetail = (courseId: number) => {
  uni.navigateTo({
    url: `/pages/student/course-detail?id=${courseId}`,
  })
}

// 切换标签
const handleTabChange = (tab: string) => {
  activeTab.value = tab
  // 切换标签时清空分类选择
  selectedCourseCategory.value = ''
  if (tab === 'available') {
    fetchAvailableCourses()
  } else {
    fetchSelectedCourses()
  }
}

// 选课
const selectCourse = async (courseId: number, courseName: string) => {
  uni.showModal({
    title: '确认选课',
    content: `是否确认选择课程《${courseName}》？`,
    success: async (res) => {
      if (res.confirm) {
        try {
          const params: SelectCourseParams = {
            xkxxid: courseId.toString(),
            optype: 'select',
            xktjid: 0,
          }
          await selectCourseApi(params)

          // 选课成功后刷新列表
          uni.showToast({
            title: '选课成功',
            icon: 'success',
          })
          fetchSelectedCourses()
          fetchAvailableCourses()
        } catch (error) {
          console.error('选课失败:', error)
          uni.showToast({
            title: error.msg || '选课失败',
            icon: 'none',
          })
        }
      }
    },
  })
}

// 退选
const unselectCourseHandler = async (courseId: number, courseName: string) => {
  uni.showModal({
    title: '确认退选',
    content: `请输入退选原因`,
    editable: true,
    placeholderText: '请输入退选原因（必填）',
    success: async (res) => {
      if (res.confirm) {
        // 验证退选原因是否填写
        if (!res.content || res.content.trim() === '') {
          uni.showToast({
            title: '退选原因不能为空',
            icon: 'none',
          })
          return
        }

        try {
          const params: UnselectCourseParams = {
            id: courseId,
            remark: res.content,
            optype: 'unSelect',
          }
          await unselectCourse(params)

          // 退选成功后刷新列表
          uni.showToast({
            title: '退选成功',
            icon: 'success',
          })
          fetchSelectedCourses()
        } catch (error) {
          console.error('退选失败:', error)
          let errorMsg = '退选失败'
          if (error.msg) {
            try {
              errorMsg = JSON.parse(error.msg)
            } catch (e) {
              errorMsg = error.msg
            }
          }
          uni.showToast({
            title: errorMsg,
            icon: 'none',
          })
        }
      }
    },
  })
}

// 获取已选课程数据
const fetchSelectedCourses = async () => {
  loading.value = true
  try {
    // 更新查询参数，添加课程类别筛选
    if (selectedCourseCategory.value) {
      const categoryName = getDictLabel(courseCategoryDict.value, selectedCourseCategory.value)
      queryParams.value.courseCategoryName = categoryName
    } else {
      queryParams.value.courseCategoryName = ''
    }

    const res = await getSchoolSelectCourseList(queryParams.value)
    apiResponse.value = res
    selectedCourses.value = res.items || []

    // 更新选课统计信息
    selectionStatus.value.selectedCourses = res.total || 0
    selectionStatus.value.xsxktjszcheck = res.xsxktjszcheck || 0

    // 更新选课条件
    if (res.xsxktjsz) {
      xsxktjsz.value = res.xsxktjsz
    }

    // 计算总学分
    const totalCredits = selectedCourses.value.reduce((sum, course) => sum + course.creditHour, 0)
    selectionStatus.value.earnedCredits = totalCredits
    semesterInfo.value.currentCredits = totalCredits

    // 更新选课时间信息
    if (res.xnxqszxx) {
      const { xn, xq, rxkxkkssj, rxkxkjssj, tkjlzcts } = res.xnxqszxx
      semesterInfo.value.title = `${xn}学年第${xq}学期选课`
      semesterInfo.value.time = `选课时间: ${rxkxkkssj} - ${rxkxkjssj}`

      // 更新退选信息
      unselectInfo.value.endTime = res.xnxqszxx.cxxkjssj || '' // 退选截止时间
      unselectInfo.value.startTime = res.xnxqszxx.cxxkkssj || '' // 退选开始时间
      unselectInfo.value.totalTimes = res.xnxqszxx.xszxtxczcs || 0 // 退选总次数
      unselectInfo.value.usedTimes = res.xszxtxyczcs || 0 // 已使用退选次数
    }

    // 获取已选课程的选课信息
    await fetchSelectedCoursesInfo()
  } catch (error) {
    console.error('获取选课数据失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
    // 更新数据加载计数器并检查是否应该显示弹窗
    dataLoadCounter.value++
    checkShowPopup()
  }
}

// 获取已选课程的选课信息（已选人数和限选人数）
const fetchSelectedCoursesInfo = async () => {
  try {
    // 通过获取可选课程列表来获取所有课程的选课信息
    const res = await getAvailableCourseList({
      optype: 'list',
      page: 1,
      pageSize: 100, // 大一点的数量来确保获取全部
      semesters: queryParams.value.semesters,
      courseCategory: '',
      courseCategoryName: '',
      courseName: '',
      leaderTeacherName: '',
      className: '',
      courseTotalHours: '',
      weekHours: '',
      creditHour: '',
      startWeek: '',
      teachingInfo: '',
      siteName: '',
      limitCount: '',
      selectedCount: '',
      campusName: '',
    })

    // 创建一个映射，通过课程ID关联信息
    const courseInfoMap: Record<number, { selectedCount: number; maxCount: number }> = {}

    if (res.items && res.items.length > 0) {
      res.items.forEach((course) => {
        courseInfoMap[course.id] = {
          selectedCount: course.selectedCount,
          maxCount: course.maxCount,
        }
      })
    }

    // 更新已选课程信息
    selectedCoursesInfo.value = courseInfoMap
  } catch (error) {
    console.error('获取课程选课信息失败:', error)
  }
}

// 获取可选课程数据
const fetchAvailableCourses = async () => {
  loading.value = true
  try {
    const res = await getAvailableCourseList({
      optype: 'list',
      page: 1,
      pageSize: 10,
      semesters: '2024-2025|2',
      courseCategory: selectedCourseCategory.value || '',
      courseCategoryName: '',
      courseName: '',
      leaderTeacherName: '',
      className: '',
      courseTotalHours: '',
      weekHours: '',
      creditHour: '',
      startWeek: '',
      teachingInfo: '',
      siteName: '',
      limitCount: '',
      selectedCount: '',
      campusName: '',
    })
    availableCourses.value = res.items || []
    selectionStatus.value.availableCourses = res.total || 0

    // 更新通知内容
    if (res.xnxqszxx) {
      const { xn, xq, rxkxkkssj, rxkxkjssj, rxkxsxxsl } = res.xnxqszxx
      const rxkxsyxs = res.rxkxsyxs

      if (selectionStatus.value.xsxktjszcheck === 0) {
        notice.value.content = `以下为${xn}学年第${xq}学期你可选择的院级选课列表[选课时间: ${rxkxkkssj} 至 ${rxkxkjssj}]。你共可选 ${rxkxsxxsl} 门，你已选 ${rxkxsyxs} 门。`
      } else {
        notice.value.content = `以下为${xn}学年第${xq}学期你可选择的院级选课列表[选课时间: ${rxkxkkssj} 至 ${rxkxkjssj}]。你选课条件: ${xsxktjsz.value.remark}，你已选 ${rxkxsyxs} 门。`
      }
    }
  } catch (error) {
    console.error('获取可选课程数据失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
    // 更新数据加载计数器并检查是否应该显示弹窗
    dataLoadCounter.value++
    checkShowPopup()
  }
}

// 获取已选课程的选课信息
const getSelectedCourseInfo = (courseId: number) => {
  return selectedCoursesInfo.value[courseId] || { selectedCount: 0, maxCount: 0 }
}

// 显示选课信息弹窗
const showSelectCoursePopup = () => {
  // 显示弹窗
  showPopup.value = true
  // 标记弹窗已显示
  popupShown.value = true
}

// 关闭弹窗
const handleClosePopup = () => {
  showPopup.value = false
}

// 检查是否应该显示弹窗（当两个数据都加载完成且尚未显示过弹窗时）
const checkShowPopup = () => {
  if (dataLoadCounter.value >= 2 && !popupShown.value) {
    showSelectCoursePopup()
  }
}

// 计算选课进度百分比
const progressPercentage = computed(() => {
  return (semesterInfo.value.currentCredits / semesterInfo.value.maxCredits) * 100
})

// 计算进度颜色
const progressColor = computed(() => {
  const percent = progressPercentage.value
  if (percent < 30) return '#52c41a' // 绿色
  if (percent < 70) return '#1890ff' // 蓝色
  if (percent < 90) return '#faad14' // 黄色
  return '#f5222d' // 红色
})

// 格式化课程图标和颜色
const getCourseIcon = (courseName: string) => {
  const iconMap: Record<string, { icon: string; color: string }> = {
    英语: { icon: 'speak', color: '#5AC8FA' },
    数学: { icon: 'calculator', color: '#FF9500' },
    计算机: { icon: 'laptop-code', color: '#007AFF' },
    物理: { icon: 'science', color: '#4CD964' },
    艺术: { icon: 'art', color: '#AF52DE' },
    测试: { icon: 'settings', color: '#FF2D55' },
  }

  // 默认图标
  let result = { icon: 'book', color: '#007AFF' }

  // 尝试根据课程名称匹配图标
  for (const key in iconMap) {
    if (courseName.includes(key)) {
      result = iconMap[key]
      break
    }
  }

  return result
}

// 切换课程分类
const changeCourseCategory = (category: string) => {
  selectedCourseCategory.value = category
  // 选中分类后刷新当前标签页的列表
  if (activeTab.value === 'available') {
    fetchAvailableCourses()
  } else {
    fetchSelectedCourses()
  }
}

// 添加一个计算属性，判断是否还有退选次数
const hasRemainingUnselectTimes = computed(() => {
  const remainingTimes = unselectInfo.value.totalTimes - unselectInfo.value.usedTimes
  return remainingTimes > 0
})

// 页面加载时获取数据
onMounted(async () => {
  // 加载课程分类字典
  try {
    const dicts = await loadDictData(['DM_XKKCFLDM'])
    courseCategoryDict.value = dicts.DM_XKKCFLDM || []
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }

  fetchSelectedCourses()
  fetchAvailableCourses()
})
</script>

<template>
  <view class="min-h-screen bg-gray-100 p-4">
    <!-- 用户信息 -->
    <view class="flex items-center mb-4">
      <view class="w-15 h-15 rounded-full bg-blue-50 flex items-center justify-center mr-4">
        <wd-icon name="user" size="24px" color="#1890ff" />
      </view>
      <view>
        <view class="text-lg font-semibold">{{ userInfo.name }}</view>
        <view class="text-sm text-gray-500">{{ userInfo.major }} • {{ userInfo.grade }}</view>
      </view>
    </view>

    <!-- 分段控制器 -->
    <view class="bg-gray-100 mb-3 rounded-lg">
      <view class="flex bg-white rounded-lg p-1 shadow">
        <view
          class="flex-1 text-center py-2 rounded-md transition-all duration-300"
          :class="activeTab === 'available' ? 'bg-[#007aff] text-white' : 'text-gray-600'"
          @click="handleTabChange('available')"
        >
          可选课程
        </view>
        <view
          class="flex-1 text-center py-2 rounded-md transition-all duration-300"
          :class="activeTab === 'selected' ? 'bg-[#007aff] text-white' : 'text-gray-600'"
          @click="handleTabChange('selected')"
        >
          已选课程
        </view>
      </view>
    </view>

    <!-- 课程分类选择器（同时用于可选课程和已选课程页面） -->
    <view v-if="courseCategoryDict.length > 0" class="mb-3">
      <view class="flex overflow-x-auto py-2 no-scrollbar">
        <view
          class="flex-none px-4 py-2 mr-2 rounded-full text-center transition-all duration-300"
          :class="
            !selectedCourseCategory
              ? 'bg-[#007aff] text-white'
              : 'bg-white text-gray-600 border border-gray-200'
          "
          @click="changeCourseCategory('')"
        >
          全部
        </view>
        <view
          v-for="item in courseCategoryDict"
          :key="item.dictValue"
          class="flex-none px-4 py-2 mr-2 rounded-full text-center transition-all duration-300"
          :class="
            selectedCourseCategory === item.dictValue
              ? 'bg-[#007aff] text-white'
              : 'bg-white text-gray-600 border border-gray-200'
          "
          @click="changeCourseCategory(item.dictValue)"
        >
          {{ item.dictLabel }}
        </view>
      </view>
    </view>

    <!-- 通知卡片 -->
    <view
      class="mb-3 bg-[#fff5eb] rounded-lg p-3 shadow border-l-4 border-[#faad14] border-l-solid"
    >
      <view class="flex items-center justify-between">
        <view class="flex items-center">
          <wd-icon name="bell" color="#faad14" class="mr-2" />
          <text class="font-semibold text-[#faad14]">{{ dynamicNotice.title }}</text>
        </view>
        <text class="text-xs text-gray-500">{{ dynamicNotice.time }}</text>
      </view>
      <view class="mt-1.5 text-sm text-gray-600">{{ dynamicNotice.content }}</view>
    </view>

    <!-- 选课状态（更紧凑的设计） -->
    <view class="flex justify-between mb-3 gap-2">
      <view class="w-48% bg-white rounded-lg py-2 px-3 flex items-center shadow">
        <view class="bg-blue-50 w-9 h-9 rounded-full flex items-center justify-center mr-2">
          <wd-icon name="list" size="18px" color="#1890ff" />
        </view>
        <view>
          <view class="text-sm text-gray-500">已选课程</view>
          <view class="text-lg font-semibold text-blue-500">
            {{ selectionStatus.selectedCourses }}
          </view>
        </view>
      </view>
      <view class="w-48% bg-white rounded-lg py-2 px-3 flex items-center shadow">
        <view class="bg-blue-50 w-9 h-9 rounded-full flex items-center justify-center mr-2">
          <wd-icon name="star" size="18px" color="#1890ff" />
        </view>
        <view>
          <view class="text-sm text-gray-500">已修学分</view>
          <view class="text-lg font-semibold text-blue-500">
            {{ selectionStatus.earnedCredits }}
          </view>
        </view>
      </view>
    </view>

    <!-- 选课进度 -->
    <view class="bg-white p-4 mb-3 rounded-lg shadow">
      <view class="text-lg font-semibold">{{ semesterInfo.title }}</view>
      <view class="text-xs text-gray-500 mt-1">{{ semesterInfo.time }}</view>

      <!-- 进度条 -->
      <view class="mt-4 h-1.5 bg-gray-100 rounded-full overflow-hidden">
        <view
          class="h-full rounded-full transition-all duration-300"
          :style="{ width: `${progressPercentage}%`, backgroundColor: progressColor }"
        ></view>
      </view>

      <!-- 学分信息 -->
      <view class="flex justify-between mt-2 text-sm">
        <view>
          <text class="text-gray-500">已选学分:</text>
          <text class="font-medium ml-1">{{ semesterInfo.currentCredits }}</text>
        </view>
        <view>
          <text class="text-gray-500">学期限制:</text>
          <text class="font-medium ml-1">{{ semesterInfo.maxCredits }}</text>
        </view>
      </view>
    </view>

    <!-- 仅看可选开关 -->
    <view v-if="activeTab === 'available'" class="flex justify-end items-center mb-3">
      <text class="text-sm text-gray-600 mr-2">仅看可选</text>
      <view
        class="w-12 h-6 rounded-full relative cursor-pointer transition-all duration-300"
        :class="onlyShowAvailable ? 'bg-[#007aff]' : 'bg-gray-300'"
        @click="toggleOnlyShowAvailable"
      >
        <view
          class="w-5 h-5 bg-white rounded-full absolute top-0.5 transition-all duration-300 shadow"
          :style="{ left: onlyShowAvailable ? '26px' : '2px' }"
        ></view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="flex justify-center items-center py-10">
      <wd-loading color="#007aff" />
    </view>

    <!-- 课程列表 -->
    <view v-else>
      <template v-if="activeTab === 'available'">
        <view v-if="filteredAvailableCourses.length > 0">
          <view v-for="course in filteredAvailableCourses" :key="course.id" class="course-card">
            <view class="course-header">
              <view
                class="course-icon"
                :style="{ 'background-color': getCourseIcon(course.courseName).color }"
              >
                <wd-icon :name="getCourseIcon(course.courseName).icon" size="20" />
              </view>
              <view class="course-info">
                <view class="course-name">{{ course.courseName }}</view>
                <view class="course-teacher">
                  {{ course.leaderTeacherName }} | {{ course.courseCategoryName }}
                </view>
              </view>
            </view>
            <view class="course-body">
              <view class="course-meta">
                <view class="meta-item">{{ course.creditHour }}学分</view>
                <view class="meta-item">{{ course.courseCategoryName }}</view>
                <view class="meta-item">{{ course.courseTotalHours }}学时</view>
              </view>
              <view class="course-schedule">
                <wd-icon name="clock" size="14" class="schedule-icon" />
                {{ course.teachingInfo }}
              </view>
              <view class="course-schedule">
                <wd-icon name="location" size="14" class="schedule-icon" />
                {{ course.siteName || '暂无教室信息' }}
              </view>
              <view class="course-schedule">
                <wd-icon name="calendar" size="14" class="schedule-icon" />
                {{ course.startWeek }}
              </view>
              <view class="course-actions">
                <view class="course-status">
                  <view class="status-indicator" style="background-color: #faad14"></view>
                  <text>已选 {{ course.selectedCount || 0 }}/{{ course.maxCount || '不限' }}</text>
                </view>
                <view class="flex gap-2">
                  <wd-button
                    size="small"
                    type="primary"
                    :disabled="course.maxCount > 0 && course.selectedCount >= course.maxCount"
                    :custom-style="
                      course.maxCount > 0 && course.selectedCount >= course.maxCount
                        ? 'background-color: #e5e5ea; border-color: #e5e5ea; color: #8e8e93; border-radius: 6px;'
                        : 'background-color: #007aff; border-color: #007aff; border-radius: 6px;'
                    "
                    @click="selectCourse(course.id, course.courseName)"
                  >
                    {{
                      course.maxCount > 0 && course.selectedCount >= course.maxCount
                        ? '已满'
                        : '选课'
                    }}
                  </wd-button>
                  <wd-button
                    size="small"
                    type="default"
                    custom-style="background-color: #e5e5ea; border-color: #e5e5ea; color: #8e8e93; border-radius: 6px;"
                    @click="viewCourseDetail(course.id)"
                  >
                    查看
                  </wd-button>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view v-else class="empty-state">
          <wd-icon name="empty" size="60" color="#d1d1d6" />
          <view class="empty-text">
            {{ onlyShowAvailable ? '暂无可选课程名额' : '暂无可选课程' }}
          </view>
          <wd-button
            v-if="onlyShowAvailable && availableCourses.length > 0"
            type="primary"
            class="mt-4"
            custom-style="background-color: #007aff; border-color: #007aff; border-radius: 6px;"
            @click="toggleOnlyShowAvailable"
          >
            查看全部课程
          </wd-button>
          <wd-button
            v-else
            type="primary"
            class="mt-4"
            custom-style="background-color: #007aff; border-color: #007aff; border-radius: 6px;"
            @click="handleTabChange('selected')"
          >
            查看已选课程
          </wd-button>
        </view>
      </template>

      <template v-else>
        <view v-if="filteredSelectedCourses.length > 0">
          <view v-for="course in filteredSelectedCourses" :key="course.id" class="course-card">
            <view class="course-header">
              <view
                class="course-icon"
                :style="{ 'background-color': getCourseIcon(course.courseName).color }"
              >
                <wd-icon :name="getCourseIcon(course.courseName).icon" size="20" />
              </view>
              <view class="course-info">
                <view class="course-name">{{ course.courseName }}</view>
                <view class="course-teacher">
                  {{ course.leaderTeacherName }} | {{ course.courseCategoryName }}
                </view>
              </view>
            </view>
            <view class="course-body">
              <view class="course-meta">
                <view class="meta-item">{{ course.creditHour }}学分</view>
                <view class="meta-item">{{ course.courseCategoryName }}</view>
                <view class="meta-item">{{ course.courseTotalHours }}学时</view>
              </view>
              <view class="course-schedule">
                <wd-icon name="clock" size="14" class="schedule-icon" />
                {{ course.teachingInfo }}
              </view>
              <view class="course-schedule">
                <wd-icon name="location" size="14" class="schedule-icon" />
                {{ course.siteName || '暂无教室信息' }}
              </view>
              <view class="course-schedule">
                <wd-icon name="calendar" size="14" class="schedule-icon" />
                {{ course.startWeek }}
              </view>
              <view class="course-actions">
                <view class="course-status">
                  <view class="status-indicator" style="background-color: #52c41a"></view>
                  <text>
                    已选 {{ getSelectedCourseInfo(course.id).selectedCount || 0 }}/
                    {{ getSelectedCourseInfo(course.id).maxCount || '不限' }}
                  </text>
                </view>
                <view class="flex gap-2">
                  <wd-button
                    size="small"
                    type="default"
                    :disabled="!hasRemainingUnselectTimes"
                    :custom-style="
                      hasRemainingUnselectTimes
                        ? 'background-color: #e5e5ea; border-color: #e5e5ea; color: #8e8e93; border-radius: 6px;'
                        : 'background-color: #f2f2f7; border-color: #f2f2f7; color: #c7c7cc; border-radius: 6px;'
                    "
                    @click="
                      hasRemainingUnselectTimes
                        ? unselectCourseHandler(course.id, course.courseName)
                        : null
                    "
                  >
                    {{ hasRemainingUnselectTimes ? '退选' : '次数已用完' }}
                  </wd-button>
                  <wd-button
                    size="small"
                    type="primary"
                    custom-style="background-color: #007aff; border-color: #007aff; border-radius: 6px;"
                    @click="viewCourseDetail(course.id)"
                  >
                    查看
                  </wd-button>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view v-else class="empty-state">
          <wd-icon name="empty" size="60" color="#d1d1d6" />
          <view class="empty-text">
            {{ selectedCourseCategory ? '没有符合条件的已选课程' : '你还没有选择任何课程' }}
          </view>
          <wd-button
            type="primary"
            class="mt-4"
            custom-style="background-color: #007aff; border-color: #007aff; border-radius: 6px;"
            @click="handleTabChange('available')"
          >
            浏览可选课程
          </wd-button>
        </view>
      </template>
    </view>

    <!-- 选课信息弹窗 -->
    <wd-popup
      v-model="showPopup"
      closable
      transition="zoom-in"
      :close-on-click-modal="true"
      custom-style="width: 90%; max-width: 700rpx; border-radius: 16rpx;"
      @close="handleClosePopup"
    >
      <view class="popup-container">
        <!-- 弹窗标题 -->
        <view class="popup-header">
          <view class="popup-title">{{ courseNoticeDetail.title }}</view>
        </view>

        <!-- 弹窗内容 -->
        <view class="popup-content">
          <view class="notice-table">
            <!-- 表格内容 -->
            <view class="table-row">
              <view class="table-cell label">当前学期：</view>
              <view class="table-cell value">{{ courseNoticeDetail.currentSemester }}</view>
            </view>

            <view class="table-row">
              <view class="table-cell label">选课时间：</view>
              <view class="table-cell value">
                {{ courseNoticeDetail.selectTimeStart }} 开始
                <br />
                {{ courseNoticeDetail.selectTimeEnd }} 结束
              </view>
            </view>

            <view class="table-row">
              <view class="table-cell label">选课说明：</view>
              <view class="table-cell value description">{{ courseNoticeDetail.description }}</view>
            </view>

            <view class="table-row">
              <view class="table-cell label">选课开放区域：</view>
              <view class="table-cell value">{{ courseNoticeDetail.allowedAreas }}</view>
            </view>

            <view class="table-row">
              <view class="table-cell label">选课开放部门：</view>
              <view class="table-cell value">{{ courseNoticeDetail.allowedDepartments }}</view>
            </view>

            <view class="table-row">
              <view class="table-cell label">选课开放年级：</view>
              <view class="table-cell value">{{ courseNoticeDetail.allowedGrades }}</view>
            </view>

            <view class="table-row">
              <view class="table-cell label">校区选课时间：</view>
              <view class="table-cell value">
                {{ courseNoticeDetail.campusSelectTimeStart }} 开始
                <br />
                {{ courseNoticeDetail.campusSelectTimeEnd }} 结束
              </view>
            </view>
          </view>
        </view>

        <!-- 按钮 -->
        <view class="popup-footer">
          <wd-button
            type="primary"
            size="large"
            block
            custom-style="border-radius: 8rpx; height: 88rpx;"
            @click="handleClosePopup"
          >
            我知道了
          </wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<style lang="scss" scoped>
.section-title {
  margin: 24rpx 0 16rpx;
  font-size: 16px;
  font-weight: 600;
}

.course-card {
  margin-bottom: 16px;
  overflow: hidden;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
}

.course-header {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f2f2f7;
}

.course-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  margin-right: 12px;
  color: white;
  border-radius: 12px;
}

.course-info {
  flex: 1;
}

.course-name {
  margin-bottom: 2px;
  font-size: 17px;
  font-weight: 600;
}

.course-teacher {
  font-size: 13px;
  color: #8e8e93;
}

.course-body {
  padding: 16px;
}

.course-meta {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.meta-item {
  padding: 4px 10px;
  margin-right: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #606266;
  background-color: #f2f2f7;
  border-radius: 14px;
}

.course-schedule {
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.schedule-icon {
  margin-right: 4px;
  color: #8e8e93;
}

.course-actions {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 16px;
  align-items: center;
  padding-top: 16px;
  margin-top: 16px;
  border-top: 1px solid #f2f2f7;
}

.course-status {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.status-indicator {
  width: 8px;
  height: 8px;
  margin-right: 6px;
  border-radius: 50%;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
}

.empty-text {
  margin-top: 16px;
  margin-bottom: 24px;
  font-size: 16px;
  color: #8e8e93;
}
/* 弹窗样式 */
.popup-container {
  padding: 24rpx;
}

.popup-header {
  margin-bottom: 24rpx;
  text-align: center;
}

.popup-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.popup-content {
  padding: 0 16rpx;
}
/* 通知表格样式 */
.notice-table {
  margin-bottom: 24rpx;
  overflow: hidden;
  background-color: #fff;
  border-radius: 8rpx;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  &:last-child {
    border-bottom: none;
  }
}

.table-cell {
  padding: 16rpx 12rpx;
  font-size: 26rpx;
  line-height: 1.5;
}

.label {
  flex: 0 0 200rpx;
  font-weight: 500;
  color: #333;
  text-align: right;
  background-color: #f9f9f9;
}

.value {
  flex: 1;
  color: #666;
}

.description {
  text-align: justify;
}

.popup-footer {
  padding-top: 16rpx;
}
</style>
