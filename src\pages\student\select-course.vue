<route lang="json5">
{
  style: {
    navigationBarTitleText: '学生选课系统',
  },
}
</route>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getSelectCourseSetting } from '@/service/selectCourse'
import type { SelectCourseSettingResponse } from '@/types/selectCourse'

// 当前学期信息
const currentSemester = ref('')

// 选课时间
const courseSelectionTime = ref({
  start: '',
  end: '',
})

// 校区选课时间
const campusSelectionTime = ref({
  start: '',
  end: '',
})

// 选课说明
const courseDescription = ref('')

// 开放校区
const openCampuses = ref('')

// 开放系部
const openDepartments = ref('')

// 开放年级
const openGrades = ref('')

// 加载状态
const loading = ref(false)

// 选课状态
const selectCourseStatus = ref({
  label: '', // 选课状态标签
  isOpen: true, // 是否开放选课，默认为true
})

// 选课按钮导航
const navigationButtons = ref([
  {
    id: 1,
    name: '院级选课',
    icon: 'goods',
    color: '#1989fa',
    bgColor: 'bg-blue-100',
    path: '/pages/student/StudentSelectCourse/college-courses',
    disabled: false,
  },
  /* {
    id: 2,
    name: '系级选课',
    icon: 'computer',
    color: '#07c160',
    bgColor: 'bg-green-100',
    path: '/pages/student/StudentSelectCourse/department-courses',
  }, */
  {
    id: 3,
    name: '我要退选',
    icon: 'close-circle',
    color: '#fa5151',
    bgColor: 'bg-red-100',
    path: '/pages/student/StudentSelectCourse/drop-courses',
    disabled: false,
  },
])

// 页面跳转
const navigateTo = (button: (typeof navigationButtons.value)[0]) => {
  console.log('导航到路径:', button.path)
  // 如果按钮被禁用，则显示提示并返回
  if (button.disabled) {
    uni.showToast({
      title: selectCourseStatus.value.label || '选课未开放',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  try {
    uni.navigateTo({
      url: button.path,
      fail: (err) => {
        console.error('导航失败:', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none',
        })
      },
    })
  } catch (error) {
    console.error('导航错误:', error)
  }
}

// 获取选课系统信息
const fetchSelectCourseInfo = async () => {
  loading.value = true
  try {
    // 直接从 selectCourseSet 接口获取数据
    const res = await getSelectCourseSetting()

    // 更新选课状态
    selectCourseStatus.value.label = res.label || ''
    selectCourseStatus.value.isOpen = res.label !== '选课未开放'

    // 更新院级选课按钮状态
    /*   navigationButtons.value[0].disabled = !selectCourseStatus.value.isOpen
    // 如果选课未开放，更改图标和颜色
    if (!selectCourseStatus.value.isOpen) {
      navigationButtons.value[0].icon = 'close'
      navigationButtons.value[0].color = '#999999'
      navigationButtons.value[0].bgColor = 'bg-gray-100'
    } */

    // 更新学期和选课时间信息
    if (res.xnxqszxx) {
      const { xn, xq, rxkxkkssj, rxkxkjssj } = res.xnxqszxx

      // 更新当前学期
      currentSemester.value = `${xn}(${xq})`

      // 更新选课时间
      if (rxkxkkssj && rxkxkjssj) {
        courseSelectionTime.value = {
          start: rxkxkkssj,
          end: rxkxkjssj,
        }
      }

      // 更新选课说明 - rxkxksm在类型定义中不存在，但可能在实际数据中存在
      // 如果有需要，可以使用类型断言或者添加类型定义
      const xnxqszxxAny = res.xnxqszxx as any
      if (xnxqszxxAny.rxkxksm) {
        courseDescription.value = xnxqszxxAny.rxkxksm
      }
    }

    // 处理学生开放时间信息 - 只从xskfsjxx获取数据
    if (res.xskfsjxx && Array.isArray(res.xskfsjxx) && res.xskfsjxx.length > 0) {
      // 尝试获取第一个元素的信息
      const firstItem = res.xskfsjxx[0] as any

      if (firstItem) {
        // 更新开放校区
        if (firstItem.kfxqmc) openCampuses.value = firstItem.kfxqmc

        // 更新开放系部
        if (firstItem.kfxbmc) openDepartments.value = firstItem.kfxbmc

        // 更新开放年级
        if (firstItem.kfnj) openGrades.value = firstItem.kfnj

        // 更新校区选课时间
        if (firstItem.kfkssj && firstItem.kfjssj) {
          campusSelectionTime.value = {
            start: firstItem.kfkssj,
            end: firstItem.kfjssj,
          }
        }
      }
    } else if (res.xskfsjxx && !Array.isArray(res.xskfsjxx)) {
      // 如果是对象，直接使用
      const item = res.xskfsjxx as any

      // 更新开放校区
      if (item.kfxqmc) openCampuses.value = item.kfxqmc

      // 更新开放系部
      if (item.kfxbmc) openDepartments.value = item.kfxbmc

      // 更新开放年级
      if (item.kfnj) openGrades.value = item.kfnj

      // 更新校区选课时间
      if (item.kfkssj && item.kfjssj) {
        campusSelectionTime.value = {
          start: item.kfkssj,
          end: item.kfjssj,
        }
      }
    }
    // 不再从jxrwxkxxkfsjxx获取数据，如果xskfsjxx没有数据，则相关字段保持为空
  } finally {
    loading.value = false
  }
}

// 页面加载时获取选课系统信息
onMounted(() => {
  fetchSelectCourseInfo()
})
</script>

<template>
  <view class="select-course-container">
    <!-- 加载状态 -->
    <view v-if="loading" class="flex justify-center items-center py-10">
      <wd-loading color="#007aff" />
    </view>

    <!-- 选课通知卡片 -->
    <view v-else class="px-4 py-5">
      <view class="bg-white rounded-lg shadow-sm p-5">
        <view class="text-center text-xl font-bold text-primary mb-4">选课通知</view>

        <view class="space-y-4">
          <view class="flex justify-between border-b border-b-solid border-gray-200 pb-3">
            <view class="text-gray-600">当前学期:</view>
            <view class="font-medium">{{ currentSemester }}</view>
          </view>

          <view class="flex justify-between border-b border-b-solid border-gray-200 pb-3">
            <view class="text-gray-600">总选课时间:</view>
            <view class="flex flex-col">
              <text class="font-medium">
                {{ courseSelectionTime.start ? courseSelectionTime.start + '开始' : '' }}
              </text>
              <text class="font-medium">
                {{ courseSelectionTime.end ? courseSelectionTime.end + '结束' : '' }}
              </text>
            </view>
          </view>

          <view class="flex justify-between border-b border-b-solid border-gray-200 pb-3">
            <view class="text-gray-600">选课说明:</view>
            <view class="text-right flex-1 ml-4">
              <text class="text-sm">{{ courseDescription }}</text>
            </view>
          </view>

          <view class="flex justify-between border-b border-b-solid border-gray-200 pb-3">
            <view class="text-gray-600">选课开放校区:</view>
            <view class="text-right">{{ openCampuses }}</view>
          </view>

          <view class="flex justify-between border-b border-b-solid border-gray-200 pb-3">
            <view class="text-gray-600">选课开放系部:</view>
            <view class="font-medium">{{ openDepartments }}</view>
          </view>

          <view class="flex justify-between border-b border-b-solid border-gray-200 pb-3">
            <view class="text-gray-600">选课开放年级:</view>
            <view class="font-medium">{{ openGrades }}</view>
          </view>

          <view class="flex justify-between border-b border-b-solid border-gray-200 pb-3">
            <view class="text-gray-600">校区选课时间:</view>
            <view class="flex flex-col">
              <text class="font-medium">
                {{ campusSelectionTime.start ? campusSelectionTime.start + '开始' : '' }}
              </text>
              <text class="font-medium">
                {{ campusSelectionTime.end ? campusSelectionTime.end + '结束' : '' }}
              </text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 选课按钮区域 -->
    <view class="px-4 py-3">
      <view class="button-grid">
        <view
          v-for="button in navigationButtons"
          :key="button.id"
          class="bg-white p-4 rounded-lg shadow-sm text-center flex flex-col items-center button-item"
          :class="{ 'disabled-button': button.disabled }"
          @click="navigateTo(button)"
        >
          <view
            class="w-12 h-12 rounded-full flex items-center justify-center mb-2"
            :class="button.bgColor"
          >
            <wd-icon :name="button.icon" size="24px" :color="button.color"></wd-icon>
          </view>
          <text class="text-sm font-medium" :class="{ 'text-gray-400': button.disabled }">
            {{ button.name }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.select-course-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.bg-primary {
  background-color: #1989fa;
}

.text-primary {
  color: #1989fa;
}

.bg-blue-100 {
  background-color: rgba(25, 137, 250, 0.1);
}

.bg-green-100 {
  background-color: rgba(7, 193, 96, 0.1);
}

.text-success {
  color: #07c160;
}

.bg-red-100 {
  background-color: rgba(250, 81, 81, 0.1);
}

.text-danger {
  color: #fa5151;
}

.text-gray-600 {
  color: #6b7280;
}

.text-gray-400 {
  color: #9ca3af;
}

.bg-gray-100 {
  background-color: rgba(243, 244, 246, 0.7);
}

.text-white {
  color: #ffffff;
}

.shadow-sm {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.border-gray-200 {
  border-color: #e5e7eb;
}

.border-b {
  border-bottom-style: solid;
  border-bottom-width: 1px;
}

.border-solid {
  border-style: solid;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.grid-cols-3 {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-full {
  border-radius: 9999px;
}

.font-bold {
  font-weight: 700;
}

.font-medium {
  font-weight: 500;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.px-4 {
  padding-right: 1rem;
  padding-left: 1rem;
}

.p-5 {
  padding: 1.25rem;
}

.p-4 {
  padding: 1rem;
}

.pb-3 {
  padding-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.ml-4 {
  margin-left: 1rem;
}

.text-xl {
  font-size: 1.25rem;
}

.text-sm {
  font-size: 0.875rem;
}

.w-12 {
  width: 3rem;
}

.h-12 {
  height: 3rem;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.button-grid {
  display: flex;
  flex-wrap: nowrap;
  align-items: stretch;
  justify-content: space-around;
  width: 100%;
}

.button-item {
  flex: 1;
  margin: 0 8rpx;
}

.disabled-button {
  cursor: not-allowed;
  opacity: 0.8;
}
</style>
