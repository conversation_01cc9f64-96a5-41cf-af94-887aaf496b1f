<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的学习计划',
  },
}
</route>
<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { getStudyPlan } from '@/service/student'
import type { StudyPlanItem, StudyPlanResponse } from '@/types/student'
import SemesterWeekPicker from '@/components/SemesterWeekPicker/index.vue'
import Pagination from '@/components/Pagination/index.vue'

// 加载状态
const loading = ref(false)
const error = ref('')

// 分页参数
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 排序参数
const sortBy = ref('giveLessonsDate')
const sortOrder = ref<'asc' | 'desc'>('desc')

/**
 * 学期周数选择数据
 */
const semesterValue = ref('')
const weekValue = ref<number | null>(null)

// 学习计划数据
const studyPlanResponse = ref<StudyPlanResponse | null>(null)
const studyPlanItems = ref<StudyPlanItem[]>([])

// 追踪组件是否初始化完成
const isInitialized = ref(false)

// 选择器组件引用
const semesterWeekPickerRef = ref()

/**
 * 获取学习计划数据
 */
const fetchStudyPlan = async () => {
  // 检查选择器组件是否已初始化完成
  if (semesterWeekPickerRef.value && semesterWeekPickerRef.value.isInitialized === false) {
    console.log('选择器组件尚未初始化完成，跳过数据加载')
    return
  }

  // 检查本页面是否初始化完成且不是手动触发，则跳过
  if (!isInitialized.value) {
    console.log('页面尚未初始化完成，跳过数据加载')
    return
  }

  loading.value = true
  error.value = ''

  try {
    // 由于formatSemester属性为true，这里需要确保传入的参数是正确的格式
    // 如果semesterValue.value是2024-2025-2格式，则需要保持原样传递
    // 前端展示时会自动转换为2024-2025|2格式
    const res = await getStudyPlan({
      page: page.value,
      pageSize: pageSize.value,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value,
      semesters: semesterValue.value ? [semesterValue.value] : [],
      week: weekValue.value,
    })

    studyPlanResponse.value = res
    studyPlanItems.value = res.items
    total.value = res.total
  } catch (err) {
    console.error('获取学习计划失败', err)
    error.value = '获取数据失败，请稍后重试'
    studyPlanItems.value = []
  } finally {
    loading.value = false
  }
}

/**
 * 页码变化处理
 */
const handlePageChange = (newPage: number) => {
  page.value = newPage
  fetchStudyPlan()
}

// 学期和周数变化处理
const handleSemesterWeekChange = (data: any) => {
  console.log('学期周数变化', data)
  semesterValue.value = data.semester.value
  weekValue.value = data.week.value
  page.value = 1 // 重置页码

  // 仅当组件已初始化时才请求数据
  if (isInitialized.value) {
    fetchStudyPlan()
  }
}

// 根据课程类型获取对应的图标和颜色
const getCourseIcon = (courseName: string) => {
  // 根据课程名称返回对应的图标和颜色
  if (courseName.includes('数据') || courseName.includes('算法')) {
    return {
      icon: 'code',
      iconBg: '#fee2e2',
      iconColor: '#ef4444',
    }
  } else if (courseName.includes('操作系统')) {
    return {
      icon: 'laptop',
      iconBg: '#e0f2fe',
      iconColor: '#0ea5e9',
    }
  } else if (courseName.includes('数据库')) {
    return {
      icon: 'data-table',
      iconBg: '#dcfce7',
      iconColor: '#22c55e',
    }
  } else {
    return {
      icon: 'book',
      iconBg: '#f3e8ff',
      iconColor: '#a855f7',
    }
  }
}

/**
 * 获取执行状态样式
 * @param status 执行状态
 */
function getExecutionStatusStyle(status: string) {
  return {
    backgroundColor: status === '是' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(234, 179, 8, 0.1)',
    color: status === '是' ? '#22c55e' : '#eab308',
  }
}

/**
 * 格式化日期时间戳
 * @param timestamp 时间戳
 */
function formatDate(timestamp: number) {
  if (!timestamp) return '无'
  const date = new Date(timestamp * 1000) // 假设时间戳是秒级的，转为毫秒
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(
    date.getDate(),
  ).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(
    date.getMinutes(),
  ).padStart(2, '0')}`
}

// 组件挂载时不直接加载数据，等待选择器组件初始化完成
onMounted(() => {
  // 延迟0.5秒后检查SemesterWeekPicker组件状态
  setTimeout(() => {
    if (semesterWeekPickerRef.value && semesterWeekPickerRef.value.isInitialized) {
      isInitialized.value = true
      fetchStudyPlan()
    } else {
      console.log('选择器组件仍在初始化中，延长等待时间')
      // 选择器组件尚未初始化完成，延长等待时间
      setTimeout(() => {
        isInitialized.value = true
        fetchStudyPlan()
      }, 800)
    }
  }, 300)
})

// 监听semesterValue变化，重新加载数据
watch(
  () => semesterValue.value,
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      console.log('学期变化，重新加载数据', newVal)
      if (isInitialized.value) {
        // 已初始化时，直接加载数据
        fetchStudyPlan()
      }
      // 未初始化时，在onLoad中会自动加载数据
    }
  },
)

/**
 * 学习目标数据 (暂时保留静态数据，后续可通过API获取)
 */
const learningGoals = ref([])
</script>

<template>
  <view class="student-plan-container pt-2">
    <!-- 学期周数选择部分 -->
    <view class="mx-2">
      <SemesterWeekPicker
        ref="semesterWeekPickerRef"
        v-model:semesterValue="semesterValue"
        v-model:weekValue="weekValue"
        size="large"
        :show-all-semester="true"
        :showToast="true"
        :formatSemester="true"
        @change="handleSemesterWeekChange"
      />
    </view>

    <!-- 学期计划摘要 -->
    <view class="plan-summary">
      <view class="summary-stats">
        <view class="stat-item">
          <view class="stat-number">{{ total }}</view>
          <view class="stat-label">课程记录</view>
        </view>
        <view class="stat-item border-0">
          <view class="stat-number">{{ studyPlanItems.length }}</view>
          <view class="stat-label">当前显示</view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <wd-icon name="loading" size="36px" class="loading-icon" />
      <view class="loading-text">加载中...</view>
    </view>

    <!-- 错误提示 -->
    <view v-else-if="error" class="error-container">
      <wd-icon name="error-circle" size="36px" class="error-icon" />
      <view class="error-text">{{ error }}</view>
      <view class="error-action" @click="fetchStudyPlan">重新加载</view>
    </view>

    <!-- 无数据提示 -->
    <view v-else-if="studyPlanItems.length === 0" class="empty-container">
      <wd-icon name="info-circle" size="36px" class="empty-icon" />
      <view class="empty-text">暂无学习计划数据</view>
    </view>

    <!-- 课程安排 -->
    <view v-else>
      <view class="course-list">
        <view v-for="(item, index) in studyPlanItems" :key="index" class="course-card">
          <!-- 课程头部信息 -->
          <view class="course-header">
            <view
              class="course-icon"
              :style="{
                backgroundColor: getCourseIcon(item.courseName).iconBg,
                color: getCourseIcon(item.courseName).iconColor,
              }"
            >
              <view class="i-carbon-{{ getCourseIcon(item.courseName).icon }}"></view>
            </view>
            <view class="header-content">
              <view class="header-title">
                <view class="title">{{ item.courseName }}</view>
                <view class="status-tag" :style="getExecutionStatusStyle(item.teachingPlanStatus)">
                  {{ item.teachingPlanStatus === '是' ? '已执行' : '未执行' }}
                </view>
              </view>
              <view class="subtitle">
                <view class="i-carbon-user text-xs"></view>
                <text class="subtitle-text">{{ item.teacherName }}</text>
                <text v-if="item.siteName" class="subtitle-divider">|</text>
                <view v-if="item.siteName" class="i-carbon-location text-xs"></view>
                <text v-if="item.siteName" class="subtitle-text">{{ item.siteName }}</text>
              </view>
            </view>
          </view>

          <!-- 课程信息卡片 -->
          <view class="course-info-card">
            <!-- 学年学期信息 (单独一行) -->
            <view class="semester-row">
              <view class="i-carbon-book"></view>
              <text class="semester-label">学年学期:</text>
              <text class="semester-value">
                {{ item.studyYear }}学年 第{{ item.studyTerm }}学期
              </text>
            </view>

            <!-- 基本信息行 -->
            <view class="info-row">
              <view class="info-item">
                <view class="i-carbon-calendar"></view>
                <text class="info-label">授课日期:</text>
                <text class="info-value">{{ item.giveLessonsDate }}</text>
              </view>
              <view class="info-item">
                <view class="i-carbon-time"></view>
                <text class="info-label">节次:</text>
                <text class="info-value">{{ item.section }} 节</text>
              </view>
            </view>

            <view class="info-row">
              <view class="info-item">
                <view class="i-carbon-user-multiple"></view>
                <text class="info-label">班级:</text>
                <text class="info-value">{{ item.className }}</text>
              </view>
              <view class="info-item">
                <view class="i-carbon-list-checked"></view>
                <text class="info-label">周次:</text>
                <text class="info-value">{{ item.cycle ? item.cycle + '周' : '无' }}</text>
              </view>
            </view>

            <view class="info-row">
              <view class="info-item">
                <view class="i-carbon-chart-multitype"></view>
                <text class="info-label">授课模式:</text>
                <text class="info-value">{{ item.giveLessonsModeName }}</text>
              </view>
              <view class="info-item">
                <view class="i-carbon-text-link"></view>
                <text class="info-label">ID:</text>
                <text class="info-value">{{ item.id }}</text>
              </view>
            </view>
          </view>

          <!-- 授课内容 -->
          <view class="content-section">
            <view class="content-header">
              <view class="i-carbon-document"></view>
              <text class="content-title">授课内容</text>
            </view>
            <view class="content-body">
              {{ item.giveLessonsContent || '暂无内容' }}
            </view>
          </view>

          <!-- 作业内容 (如果有) -->
          <view
            v-if="item.homeworkContent || item.homeworkNum"
            class="content-section homework-section"
          >
            <view class="content-header">
              <view class="i-carbon-edit"></view>
              <text class="content-title">作业内容</text>
            </view>
            <view v-if="item.homeworkContent" class="content-body">
              {{ item.homeworkContent }}
            </view>
            <view v-if="item.homeworkNum" class="homework-id">
              <view class="i-carbon-text-link text-sm"></view>
              <text>作业编号: {{ item.homeworkNum }}</text>
            </view>
          </view>

          <!-- 课程底部状态栏 -->
          <view class="course-footer">
            <view class="status-badges">
              <view
                class="status-badge"
                :class="{ 'status-badge-active': item.studentConfirmStatus === '是' }"
              >
                <view
                  :class="
                    item.studentConfirmStatus === '是'
                      ? 'i-carbon-checkmark-filled'
                      : 'i-carbon-circle-dash'
                  "
                  :style="{ color: item.studentConfirmStatus === '是' ? '#22c55e' : '#9ca3af' }"
                ></view>
                <text>学生已确认</text>
              </view>
              <view
                class="status-badge"
                :class="{ 'status-badge-active': item.teacherConfirmStatus === '是' }"
              >
                <view
                  :class="
                    item.teacherConfirmStatus === '是'
                      ? 'i-carbon-checkmark-filled'
                      : 'i-carbon-circle-dash'
                  "
                  :style="{ color: item.teacherConfirmStatus === '是' ? '#22c55e' : '#9ca3af' }"
                ></view>
                <text>教师已确认</text>
              </view>
              <view v-if="item.homeworkContent" class="status-badge homework-badge">
                <view class="i-carbon-edit"></view>
                <text>有作业</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 分页组件 -->
      <view v-if="total > pageSize" class="pagination-wrapper">
        <Pagination
          :total="total"
          :page="page"
          :pageSize="pageSize"
          @update:page="handlePageChange"
        />
      </view>
    </view>

    <!-- 学习目标 -->
  </view>
</template>

<style lang="scss" scoped>
.student-plan-container {
  min-height: 100vh;
  padding-bottom: 30rpx;
  background-color: #f5f5f7;

  .plan-summary {
    margin: 20rpx;
    overflow: hidden;
    background-color: #fff;
    border-radius: 24rpx;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  }

  .summary-header {
    padding: 30rpx;
    color: #fff;
    background-color: #3b82f6;
  }

  .summary-title {
    margin-bottom: 10rpx;
    font-size: 36rpx;
    font-weight: 600;
  }

  .summary-subtitle {
    font-size: 28rpx;
    opacity: 0.9;
  }

  .summary-stats {
    display: flex;
    padding: 30rpx 0;
  }

  .stat-item {
    flex: 1;
    text-align: center;
    border-right: 1px solid #f3f4f6;
  }

  .stat-item.border-0 {
    border-right: none;
  }

  .stat-number {
    margin-bottom: 10rpx;
    font-size: 40rpx;
    font-weight: 600;
    color: #111827;
  }

  .stat-label {
    font-size: 24rpx;
    color: #6b7280;
  }

  .section-title {
    padding: 40rpx 20rpx 20rpx;
    font-size: 32rpx;
    font-weight: 600;
    color: #4b5563;
  }

  .course-list {
    margin: 0 20rpx;
  }

  .course-card {
    margin-bottom: 24rpx;
    overflow: hidden;
    background-color: #ffffff;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);

    .course-header {
      display: flex;
      align-items: center;
      padding: 24rpx;
      background-color: #f9fafb;

      .course-icon {
        display: flex;
        flex-shrink: 0;
        align-items: center;
        justify-content: center;
        width: 80rpx;
        height: 80rpx;
        margin-right: 20rpx;
        font-size: 36rpx;
        border-radius: 16rpx;

        [class^='i-carbon-'] {
          font-size: 24px;
        }
      }

      .header-content {
        flex: 1;
        overflow: hidden;
      }

      .header-title {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;

        .title {
          flex: 1;
          overflow: hidden;
          font-size: 32rpx;
          font-weight: 600;
          color: #1f2937;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .subtitle {
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: #6b7280;

        [class^='i-carbon-'] {
          font-size: 12px;
          color: #6b7280;
        }

        .subtitle-text {
          margin-left: 6rpx;
        }

        .subtitle-divider {
          margin: 0 8rpx;
        }
      }

      .status-tag {
        flex-shrink: 0;
        height: 48rpx;
        padding: 0 16rpx;
        margin-left: 16rpx;
        font-size: 24rpx;
        line-height: 48rpx;
        border-radius: 8rpx;
      }
    }

    .course-info-card {
      padding: 24rpx;
      background-color: #fff;
      border-bottom: 1px solid #f3f4f6;

      .semester-row {
        display: flex;
        align-items: center;
        padding: 10rpx 0 16rpx;
        margin-bottom: 16rpx;
        border-bottom: 1px dashed #e5e7eb;

        .i-carbon-book {
          font-size: 18px;
          color: #3b82f6;
        }

        .semester-label {
          margin: 0 8rpx 0 6rpx;
          font-size: 28rpx;
          font-weight: 500;
          color: #6b7280;
        }

        .semester-value {
          flex: 1;
          font-size: 28rpx;
          font-weight: 600;
          color: #3b82f6;
        }
      }
    }

    .info-row {
      display: flex;
      margin-bottom: 16rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .info-item {
        display: flex;
        flex: 1;
        align-items: center;
        min-width: 0;
        padding-right: 10rpx;

        [class^='i-carbon-'] {
          font-size: 16px;
          color: #3b82f6;
        }

        &.full-width {
          width: 100%;
        }

        .info-label {
          margin: 0 8rpx 0 6rpx;
          font-size: 26rpx;
          color: #6b7280;
        }

        .info-value {
          flex: 1;
          overflow: hidden;
          font-size: 26rpx;
          font-weight: 500;
          color: #374151;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .content-section {
      padding: 24rpx;
      background-color: #fff;
      border-bottom: 1px solid #f3f4f6;

      &.homework-section {
        background-color: #f9fafb;
      }
    }

    .content-header {
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;

      .content-title {
        margin-left: 10rpx;
        font-size: 28rpx;
        font-weight: 500;
        color: #3b82f6;
      }
    }

    .content-body {
      padding: 16rpx;
      font-size: 28rpx;
      line-height: 1.6;
      color: #4b5563;
      background-color: #f9fafb;
      border-radius: 8rpx;
    }

    .homework-id {
      display: flex;
      align-items: center;
      padding-left: 36rpx;
      margin-top: 10rpx;
      font-size: 24rpx;
      color: #6b7280;

      text {
        margin-left: 6rpx;
      }
    }

    .course-footer {
      padding: 20rpx 24rpx;
      background-color: #f9fafb;
    }

    .status-badges {
      display: flex;
      flex-wrap: wrap;
    }

    .status-badge {
      display: flex;
      align-items: center;
      padding: 6rpx 12rpx;
      margin-right: 24rpx;
      color: #9ca3af;
      border-radius: 16rpx;

      text {
        margin-left: 6rpx;
        font-size: 24rpx;
      }

      &.status-badge-active {
        color: #22c55e;
        background-color: rgba(34, 197, 94, 0.1);
      }

      &.homework-badge {
        color: #3b82f6;
        background-color: rgba(59, 130, 246, 0.1);

        [class^='i-carbon-'] {
          color: #3b82f6;
        }
      }
    }
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
  }

  .loading-icon {
    animation: rotating 2s linear infinite;
  }

  @keyframes rotating {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .loading-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #6b7280;
  }

  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
  }

  .error-icon {
    color: #ef4444;
  }

  .error-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #6b7280;
  }

  .error-action {
    padding: 10rpx 30rpx;
    margin-top: 20rpx;
    font-size: 26rpx;
    color: #fff;
    background-color: #3b82f6;
    border-radius: 40rpx;
  }

  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100rpx 0;
  }

  .empty-icon {
    color: #9ca3af;
  }

  .empty-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #6b7280;
  }

  .pagination-wrapper {
    margin: 0 20rpx;
  }

  .mt-2 {
    margin-top: 16rpx;
  }
}
</style>
