<route lang="json5">
{
  style: {
    navigationBarTitleText: '学习计划详情',
  },
}
</route>
<script lang="ts" setup>
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { loadDictData, getDictLabel, getDictOptions } from '@/utils/dict'
import type { DictData } from '@/types/system'
import {
  getStudentScheduleInfo,
  getStudentAttendList,
  updateStudentAttend,
  updateTeacherSchedule,
} from '@/service/teacher'
import { submitStudentSchedule } from '@/service/student'
import type {
  TeacherScheduleInfo,
  TeacherAttendListResponse,
  UpdateTeacherAttendRequest,
  UpdateTeacherAttendRow,
  UpdateTeacherAttendResponse,
  UpdateTeacherScheduleRequest,
  UpdateTeacherScheduleResponse,
} from '@/types/teacher'
import type { StudentScheduleSubmitParams, StudentScheduleSubmitResponse } from '@/types/student'
import { onLoad } from '@dcloudio/uni-app'
// 导入分页组件
import Pagination from '@/components/Pagination/index.vue'
import { parseDateTime } from '@/utils'

// 页面参数
const pageParams = ref<{
  id?: number | string // 课程安排ID
}>({})

// 添加保存状态变量
const isSaving = ref(false)

// 添加防抖函数
const debounce = <T extends (...args: any[]) => any>(fn: T, delay: number) => {
  let timer: number | null = null
  return function (this: any, ...args: Parameters<T>) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay) as unknown as number
  }
}

// 分页相关状态
const pagination = reactive({
  page: 1,
  pageSize: 1000,
  total: 0,
})

// 搜索关键词
const searchKeyword = ref('')

// 过滤后的学生列表
const filteredStudents = computed(() => {
  if (!searchKeyword.value) {
    return students.value
  }
  const keyword = searchKeyword.value.toLowerCase()
  return students.value.filter(
    (student) =>
      student.name.toLowerCase().includes(keyword) || student.id.toLowerCase().includes(keyword),
  )
})

// 判断当前时间是否在可操作时间范围内
const isWithinOperationTime = computed(() => {
  if (!scheduleInfo.value || !scheduleInfo.value.skqrkssj || !scheduleInfo.value.skqrjssj) {
    return false
  }
  if (scheduleInfo.value.qrbtn === 0) {
    return false
  }

  const now = new Date().getTime()
  const startTime = parseDateTime(scheduleInfo.value.skqrkssj).getTime()
  const endTime = parseDateTime(scheduleInfo.value.skqrjssj).getTime()

  // return now >= startTime && now <= endTime
  return true
})

// 格式化日期时间显示
const formatDateTime = (dateTimeStr: string): string => {
  if (!dateTimeStr) return ''
  // 直接返回原始字符串，无需格式化，因为接口返回的已经是格式化后的字符串
  return dateTimeStr
}

// 字典数据
const dictData = reactive<{
  DM_SKFSDM: DictData[]
  DM_SYPGXSDM: DictData[]
  DM_XSKQZTDM: DictData[]
}>({
  DM_SKFSDM: [],
  DM_SYPGXSDM: [],
  DM_XSKQZTDM: [],
})

// 课程安排详情数据
const scheduleInfo = ref<TeacherScheduleInfo>()

// 学生考勤列表数据
const studentAttendList = ref<TeacherAttendListResponse>()

// 多选相关状态
const multiSelectMode = ref(true) // 是否开启多选模式，默认开启
const selectedStudents = ref<Set<string>>(new Set()) // 已选中的学生ID集合
const isAllSelected = computed(() => {
  return (
    filteredStudents.value.length > 0 &&
    selectedStudents.value.size === filteredStudents.value.length
  )
})

// 获取已选中的学生数量
const selectedCount = computed(() => {
  return selectedStudents.value.size
})

// 判断是否处于半选状态（部分学生被选中，但不是全部）
const isPartialSelected = computed(() => {
  return selectedCount.value > 0 && !isAllSelected.value
})

// 检查学生是否有备注
const hasRemarks = (student: Student): boolean => {
  if (!student || !student.remarks) return false
  return Object.values(student.remarks).some(
    (remark) => remark && typeof remark === 'string' && remark.trim() !== '',
  )
}

// 辅助函数：检查是否在可操作时间范围内，如果不在则显示提示
const checkOperationTime = (): boolean => {
  if (!isWithinOperationTime.value) {
    uni.showToast({
      title: '当前日志不可编辑',
      icon: 'none',
    })
    return false
  }
  return true
}

// 切换多选模式
const toggleMultiSelectMode = () => {
  // 检查是否在可操作时间范围内
  if (!checkOperationTime()) return

  multiSelectMode.value = !multiSelectMode.value
  // 退出多选模式时清空选择
  if (!multiSelectMode.value) {
    selectedStudents.value.clear()
  }
}

// 选择/取消选择单个学生
const toggleSelectStudent = (studentId: string) => {
  // 检查是否在可操作时间范围内
  if (!checkOperationTime()) return

  if (selectedStudents.value.has(studentId)) {
    selectedStudents.value.delete(studentId)
  } else {
    selectedStudents.value.add(studentId)
  }
}

// 全选/取消全选
const toggleSelectAll = () => {
  // 检查是否在可操作时间范围内
  if (!checkOperationTime()) return

  if (isAllSelected.value) {
    selectedStudents.value.clear()
  } else {
    selectedStudents.value = new Set(filteredStudents.value.map((student) => student.id))
  }
}

// 场地设备状态选项
const facilityStatusOptions = [
  { label: '正常', value: '0' },
  { label: '异常', value: '1' },
]

// 加载课程安排详情
const loadScheduleInfo = async (id: number) => {
  try {
    const res = await getStudentScheduleInfo(id)
    scheduleInfo.value = res
    console.log('课程安排详情:', res)

    if (res.jxrw) {
      courseInfo.value = {
        name: res.jxrw.kcmc || '', // 课程名称
        code: res.jxrw.id || '', // 教学任务ID作为课程代码
        type: res.skbjmc || '', // 班级名称作为课程类型
        total: res.jxrw.xqzxs || 0, // 实用人数
        current: res.syrs || 0, // 当前人数（默认与总人数相同）
        weekHours: res.jxrw.zxs || 0, // 周学时
        weeks: res.jxrw.zs || 0, // 周数
      }
    }

    // 填充日志表单数据
    journalForm.date = res.skrq || '' // 上课日期
    journalForm.week = `第${res.zc}周` // 周次
    journalForm.weekday = `星期${res.xqs}` // 星期
    journalForm.sessionTime = `第${res.jcshow}节（${res.jckssj}-${res.jcjssj}）` // 节次时间
    journalForm.teacher = res.skjsxm || '' // 授课教师
    journalForm.location = res.skcdmc || '未使用场地' // 授课场地
    journalForm.teachingMethod = res.skfs || '' // 授课方式
    journalForm.teachContent = res.sknl || '' // 教学内容
    journalForm.facilityStatus = res.cdsbzt.toString() // 场地设备状态
    journalForm.facilityUsage = res.cdsbsyqk || '' // 场地设备使用情况

    // 作业相关信息
    journalForm.homeworkCorrection = res.zypgfs || '00' // 作业批改状态
    journalForm.homeworkCount = res.zyts || 0 // 作业题数
    journalForm.homeworkContent = res.zynr || '' // 作业内容

    // 处理实训相关信息 - 如果存在
    if (res.sxkxx_id) {
      journalForm.taskName = res.sxkxx_zrwmc || '' // 典型任务名称
      journalForm.skillRequirements = res.sxkxx_sxjnyq || '' // 技能要求
      journalForm.trainingResources = res.sxkxx_ptsxzymc || '' // 配套实训资源
      journalForm.assessmentMethod = res.sxkxx_sxkhfs || '' // 考核方式
      journalForm.moduleCount = res.sxkxx_mksl || 0 // 模块数量
      journalForm.virtualSimulation = res.sxkxx_sfxnfzsx === 1 // 虚拟仿真实训
      journalForm.externalService = res.sxkxx_sfdwfw === 1 // 对外服务
      journalForm.otherTrainingLocation = res.sxkxx_qtsxdd || '' // 其他实训地点
      journalForm.trainingRemarks = res.sxkxx_remark || '' // 实训情况备注
    }

    // 如果存在kqjcInfo字段，设置课程节次
    if (res.kqjcInfo && Array.isArray(res.kqjcInfo) && res.kqjcInfo.length > 0) {
      // 根据kqjcInfo生成课程节次数据
      sessions.value = res.kqjcInfo.map((jc, index) => {
        return {
          id: jc,
          name: `第${jc}节`,
        }
      })
    }

    // 如果获取到了课程安排详情，则加载学生考勤列表
    if (res.id && res.jxrwid) {
      loadStudentAttendList(res.id, res.jxrwid)
    }
  } catch (error) {
    console.error('获取课程安排详情失败', error)
  }
}

// 加载学生考勤列表
const loadStudentAttendList = async (skjhid: number, jxrwid: number) => {
  try {
    const params = {
      skjhid,
      jxrwid,
      page: pagination.page,
      pageSize: pagination.pageSize,
    }
    const res = await getStudentAttendList(params)
    studentAttendList.value = res
    // 更新总数
    pagination.total = res.total || 0
    console.log('学生考勤列表:', res)
  } catch (error) {
    console.error('获取学生考勤列表失败', error)
  }
}

// 课程信息
const courseInfo = ref({
  name: '',
  code: '',
  type: '',
  total: 0.0,
  current: 0,
  weekHours: 0.0,
  weeks: 0,
})

// 学生类型定义
interface StudentStatus {
  [key: string]: string
}

interface StudentRemarks {
  [key: string]: string
}

interface Student {
  name: string
  id: string
  status: StudentStatus
  remarks: StudentRemarks // 添加备注字段
  remark?: string // 添加单个备注字段
  zwh?: number | string // 添加座位号字段，支持number或string类型
  classname?: string // 添加班级字段
}

// 课程节次类型定义
interface Session {
  id: string
  name: string
}

// 考勤统计数据类型定义
interface StatisticsItem {
  total: number
  [key: string]: number // 允许动态添加不同类型的统计
}

interface Statistics {
  [key: string]: StatisticsItem
}

// 日志表单数据
const journalForm = reactive({
  date: '', // 上课日期
  week: '', // 周次
  weekday: '', // 星期
  sessionTime: '', // 节次时间
  teacher: '', // 授课教师
  location: '', // 授课场地
  teachingMethod: '', // 授课方式
  teachContent: '', // 教学内容
  homeworkCorrection: '', // 作业批改状态
  homeworkCount: 0, // 作业题数
  homeworkContent: '', // 作业内容
  facilityStatus: '', // 场地设备状态
  facilityUsage: '', // 场地设备使用情况
  taskName: '', // 典型任务名称
  skillRequirements: '', // 技能要求
  trainingResources: '', // 配套实训资源
  assessmentMethod: '', // 考核方式
  moduleCount: 0, // 模块数量
  virtualSimulation: false, // 虚拟仿真实训，改为布尔值
  externalService: false, // 对外服务，改为布尔值
  otherTrainingLocation: '', // 其他实训地点
  trainingRemarks: '', // 实训情况备注
})

// 选择的功能标签
const activeTab = ref<'attendance' | 'journal'>('attendance')

// 课程节次
const sessions = ref<Session[]>([])

// 考勤统计数据
const totalStatistics = reactive<StatisticsItem>({
  total: 0,
})

// 考勤状态选项类型定义
interface AttendanceStatusOption {
  type: string
  icon: string
  label: string
  color: string
  value: string
  listClass?: string // 添加listClass属性用于字典中的样式类
}

// 根据考勤状态代码获取考勤状态类型
const getAttendanceTypeByCode = (code: string): string => {
  // 直接返回状态码作为类型，简化映射逻辑
  return code || '0'
}

// 获取考勤状态中文标签
const getAttendanceLabel = (code: string): string => {
  return getDictLabel(dictData.DM_XSKQZTDM, code) || '出勤'
}

// 学生列表数据 - 从studentAttendList中获取
const students = ref<Student[]>([])

// 从考勤数据中生成学生列表
const generateStudentsList = () => {
  if (
    !studentAttendList.value ||
    !studentAttendList.value.items ||
    !studentAttendList.value.items.length
  ) {
    return
  }

  const attendItems = studentAttendList.value.items

  students.value = attendItems.map((item) => {
    // 构建学生状态对象
    const status: StudentStatus = {}
    // 构建学生备注对象
    const remarks: StudentRemarks = {}

    // 遍历课程节次，获取对应的考勤状态
    sessions.value.forEach((session, index) => {
      const statusCode = item[`kqzt${index}`] || '0'
      status[session.id] = getAttendanceTypeByCode(statusCode)
      // 初始化备注为空字符串或从API获取的备注
      remarks[session.id] = item.remark || ''
    })

    return {
      name: item.xsxm, // 学生姓名
      id: item.xsxh, // 学生学号
      status, // 考勤状态
      remarks, // 备注信息,
      remark: item.remark,
      zwh: item.zwh, // 座位号
      classname: item.ssbjmc || '', // 班级名称，使用API返回的ssbjmc字段
    }
  })

  // 更新统计数据
  updateStatistics()
}

// 监听studentAttendList变化，生成学生列表
watch(
  () => studentAttendList.value,
  (newVal) => {
    if (newVal) {
      generateStudentsList()
    }
  },
  { immediate: true },
)

// 考勤状态选项 - 使用字典数据生成
const attendanceStatus = computed<AttendanceStatusOption[]>(() => {
  // 状态对应的图标配置（使用carbon图标）
  const iconMap: Record<string, string> = {
    '0': 'i-carbon-checkmark', // 出勤
    '1': 'i-carbon-logout', // 事假
    '2': 'i-carbon-reminder-medical', // 病假
    '3': 'i-carbon-time', // 迟到
    '4': 'i-carbon-close', // 旷课
    '5': 'i-carbon-arrow-up-right', // 早退
    '6': 'i-carbon-calendar', // 公假
    '7': 'i-carbon-laptop', // 实习
    '8': 'i-carbon-group', // 集训
    '90': 'i-carbon-checkbox-checked', // 免修
  }

  // 状态对应的颜色配置（基于listClass或自定义）
  const colorMap: Record<string, string> = {
    '0': '#10B981', // 出勤 - 绿色
    '1': '#F59E0B', // 事假 - 橙色
    '2': '#F59E0B', // 病假 - 橙色
    '3': '#1890ff', // 迟到 - 蓝色
    '4': '#EF4444', // 旷课 - 红色
    '5': '#EF4444', // 早退 - 红色
    '6': '#10B981', // 公假 - 绿色
    '7': '#06B6D4', // 实习 - 青色
    '8': '#06B6D4', // 集训 - 青色
    '90': '#10B981', // 免修 - 绿色
  }

  // 如果字典数据已加载，则使用字典数据生成选项
  if (dictData.DM_XSKQZTDM && dictData.DM_XSKQZTDM.length > 0) {
    return dictData.DM_XSKQZTDM.map((dict) => {
      const type = dict.dictValue
      const icon = iconMap[type] || 'i-carbon-checkmark'
      const color = colorMap[type] || '#10B981'

      return {
        type,
        icon,
        label: dict.dictLabel,
        color,
        value: dict.dictValue,
        listClass: dict.listClass,
      }
    })
  }

  // 如果字典数据未加载，则返回空数组
  return []
})

// 状态选择弹窗
const showStatusPopup = ref(false)
const currentStudent = ref<Student | null>(null)
const currentSessionId = ref<string>('')
const currentRemark = ref<string>('')

// 批量操作相关状态和方法
const showBatchActionPopup = ref(false)
const selectedSessionId = ref<Set<string>>(new Set()) // 批量操作时选择的节次ID集合，改为Set类型

// 判断是否所有节次都已选中
const isAllSessionsSelected = computed(() => {
  return sessions.value.length > 0 && selectedSessionId.value.size === sessions.value.length
})

// 全选/取消全选节次
const toggleSelectAllSessions = () => {
  // 检查是否在可操作时间范围内
  if (!checkOperationTime()) return

  if (isAllSessionsSelected.value) {
    // 如果全部选中，则清空选择
    selectedSessionId.value.clear()
  } else {
    // 如果未全部选中，则全选
    selectedSessionId.value = new Set(sessions.value.map((session) => session.id))
  }
}

// 切换单个节次的选中状态
const toggleSessionSelection = (sessionId: string) => {
  // 检查是否在可操作时间范围内
  if (!checkOperationTime()) return

  if (selectedSessionId.value.has(sessionId)) {
    selectedSessionId.value.delete(sessionId)
  } else {
    selectedSessionId.value.add(sessionId)
  }
}

// 打开批量操作弹窗
const openBatchActionPopup = () => {
  // 检查是否在可操作时间范围内
  if (!checkOperationTime()) return

  // 如果没有选中学生，则提示用户
  if (selectedStudents.value.size === 0) {
    uni.showToast({
      title: '请先选择学生',
      icon: 'none',
    })
    return
  }

  // 重置currentStudent为null，确保显示节次选择
  currentStudent.value = null

  // 默认全选所有节次
  selectedSessionId.value = new Set(sessions.value.map((session) => session.id))

  // 直接打开状态选择弹窗
  currentRemark.value = '' // 清空备注
  showStatusPopup.value = true
}

// 处理批量操作选择
const handleBatchAction = (action: 'attendance' | 'export') => {
  // 检查是否在可操作时间范围内
  if (!checkOperationTime()) return

  showBatchActionPopup.value = false

  // 获取选中的学生ID列表
  const selectedIds = Array.from(selectedStudents.value)

  // 根据不同操作执行不同逻辑
  switch (action) {
    case 'attendance':
      uni.showToast({
        title: `已选择${selectedIds.length}名学生进行批量考勤`,
        icon: 'none',
      })
      break
    case 'export':
      uni.showToast({
        title: `已选择${selectedIds.length}名学生进行数据导出`,
        icon: 'none',
      })
      break
  }

  // 实际业务逻辑暂不实现，仅展示提示
  console.log('批量操作', action, selectedIds)
}

// 处理考勤状态选择
const handleStatusSelect = (status: string) => {
  // 检查是否在可操作时间范围内
  if (!checkOperationTime()) return

  if (currentStudent.value && currentSessionId.value) {
    // 单个学生状态更新
    currentStudent.value.status[currentSessionId.value] = status
    // 保存备注到学生对象中
    currentStudent.value.remarks[currentSessionId.value] = currentRemark.value
    showStatusPopup.value = false
    updateStatistics()

    // 选择状态后立即保存当前行数据，并传入备注
    handleSaveRow(currentStudent.value, currentRemark.value)
  } else if (selectedStudents.value.size > 0 && selectedSessionId.value.size > 0) {
    // 批量更新选中学生的状态
    const selectedIds = Array.from(selectedStudents.value)
    const selectedSessions = Array.from(selectedSessionId.value)

    // 更新所有选中学生的所有选中节次的状态和备注
    students.value.forEach((student) => {
      if (selectedIds.includes(student.id)) {
        selectedSessions.forEach((sessionId) => {
          student.status[sessionId] = status
          student.remarks[sessionId] = currentRemark.value
        })
      }
    })

    showStatusPopup.value = false
    updateStatistics()

    // 批量保存选中学生的考勤记录
    handleBatchSave(selectedIds, status, currentRemark.value, selectedSessions)
  } else {
    uni.showToast({
      title: '请先选择节次',
      icon: 'none',
    })
  }
}

// 批量保存学生考勤记录
const handleBatchSave = async (
  studentIds: string[],
  status: string,
  remark: string = '',
  sessionIds: string[] = [],
) => {
  try {
    // 检查是否获取到了上课计划ID
    if (!scheduleInfo.value?.id) {
      uni.showToast({
        title: '未获取到课程安排ID',
        icon: 'none',
      })
      return
    }

    // 检查当前时间是否在可操作时间范围内
    if (!isWithinOperationTime.value) {
      uni.showToast({
        title: '不在允许操作的时间范围内',
        icon: 'none',
      })
      return
    }

    // 创建所有选中学生的考勤数据
    const rows: UpdateTeacherAttendRow[] = studentIds
      .map((studentId) => {
        // 找到对应的学生对象
        const student = students.value.find((s) => s.id === studentId)
        if (!student) return null

        // 创建考勤数据对象
        const rowData: UpdateTeacherAttendRow = {
          xsxh: studentId,
          remark,
          kqzt0: getDictValueByType(student.status[sessions.value[0]?.id || '0']),
          kqzt1:
            sessions.value.length > 1
              ? getDictValueByType(student.status[sessions.value[1]?.id || '0'])
              : '0',
        }

        // 更新选中节次的考勤状态
        sessionIds.forEach((sessionId) => {
          const sessionIndex = sessions.value.findIndex((s) => s.id === sessionId)
          if (sessionIndex >= 0) {
            rowData[`kqzt${sessionIndex}`] = getDictValueByType(status)
          }
        })

        return rowData
      })
      .filter(Boolean) as UpdateTeacherAttendRow[]

    // 构建请求参数
    const params: UpdateTeacherAttendRequest = {
      skjhid: scheduleInfo.value.id,
      rows,
    }

    console.log('批量保存考勤记录', params)

    // 调用更新考勤API
    await updateStudentAttend(params)

    uni.showToast({
      title: '批量保存成功',
      icon: 'success',
    })

    // 清空选择
    selectedStudents.value.clear()
  } catch (error) {
    console.error('批量保存考勤记录失败', error)
  }
}

// 打开状态选择弹窗
const openStatusPopup = (student: Student, sessionId: string) => {
  // 检查是否在可操作时间范围内
  if (!checkOperationTime()) return

  currentStudent.value = student
  currentSessionId.value = sessionId
  selectedSessionId.value = new Set() // 清空批量操作的节次ID
  // 重置备注字段
  currentRemark.value = student.remarks[sessionId] || ''
  showStatusPopup.value = true
}

// 更新统计数据 - 重构为统计所有节次的人次总和
const updateStatistics = () => {
  // 重置统计数据
  totalStatistics.total = students.value.length * sessions.value.length // 总人次 = 学生数 × 节次数

  // 为每种考勤状态创建计数，初始值为0
  attendanceStatus.value.forEach((status) => {
    totalStatistics[status.type] = 0
  })

  // 统计每种状态的学生人次数量
  students.value.forEach((student) => {
    // 遍历每个学生的所有节次状态
    sessions.value.forEach((session) => {
      const sessionId = session.id
      const studentStatus = student.status[sessionId]
      if (totalStatistics[studentStatus] !== undefined) {
        totalStatistics[studentStatus]++
      }
    })
  })
}

// 获取状态图标和颜色
const getStatusInfo = (status: string): AttendanceStatusOption => {
  const statusItem = attendanceStatus.value.find((item) => item.type === status)
  return (
    statusItem ||
    (attendanceStatus.value.length > 0
      ? attendanceStatus.value[0]
      : {
          type: '0',
          icon: 'i-carbon-checkmark',
          label: '出勤',
          color: '#10B981',
          value: '0',
        })
  )
}

// 根据前端状态类型获取字典值
const getDictValueByType = (type: string): string => {
  // 由于现在type就是dictValue，直接返回
  return type || '0'
}

// 保存单个学生的考勤记录
const handleSaveRow = async (student: Student, remark: string = '') => {
  try {
    // 检查是否获取到了上课计划ID
    if (!scheduleInfo.value?.id) {
      uni.showToast({
        title: '未获取到课程安排ID',
        icon: 'none',
      })
      return
    }

    // 检查当前时间是否在可操作时间范围内
    if (!isWithinOperationTime.value) {
      uni.showToast({
        title: '不在允许操作的时间范围内',
        icon: 'none',
      })
      return
    }

    // 如果提供了备注参数，则使用它，否则使用当前节次的备注
    const remarkToSave =
      remark || (currentSessionId.value ? student.remarks[currentSessionId.value] || '' : '')

    // 创建该学生的考勤数据
    const rowData: UpdateTeacherAttendRow = {
      xsxh: student.id, // 学生学号
      remark: remarkToSave, // 使用备注
      kqzt0: getDictValueByType(student.status[sessions.value[0]?.id || '0']),
      kqzt1:
        sessions.value.length > 1
          ? getDictValueByType(student.status[sessions.value[1]?.id || '0'])
          : '0',
    }

    // 构建请求参数
    const params: UpdateTeacherAttendRequest = {
      skjhid: scheduleInfo.value.id,
      rows: [rowData], // 只包含当前学生的数据
    }

    console.log('保存单行考勤记录', params)

    // 调用更新考勤API
    await updateStudentAttend(params)

    loadStudentAttendList(scheduleInfo.value.id, scheduleInfo.value.jxrwid)
    uni.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 1000,
    })
  } catch (error) {
    console.error('保存考勤记录失败', error)
  }
}

// 监听isWithinOperationTime变化，当不在可操作时间范围内时自动切换到journal视图
watch(
  () => isWithinOperationTime.value,
  (newVal) => {
    if (newVal === false) {
      activeTab.value = 'attendance'
    }
  },
  { immediate: true }, // 立即执行一次
)

// 全部出勤
const handleAllPresent = async () => {
  // 检查是否在可操作时间范围内
  if (!checkOperationTime()) return

  try {
    // 检查是否获取到了上课计划ID
    if (!scheduleInfo.value?.id) {
      uni.showToast({
        title: '未获取到课程安排ID',
        icon: 'none',
      })
      return
    }

    // 将所有学生的所有节次设置为出勤状态
    students.value.forEach((student) => {
      // 遍历所有节次
      sessions.value.forEach((session) => {
        student.status[session.id] = '0' // 0表示出勤
      })
    })
    updateStatistics()

    // 构建所有学生的考勤数据
    const rows: UpdateTeacherAttendRow[] = students.value.map((student) => ({
      xsxh: student.id,
      remark: '',
      kqzt0: getDictValueByType(student.status[sessions.value[0]?.id || '0']),
      kqzt1:
        sessions.value.length > 1
          ? getDictValueByType(student.status[sessions.value[1]?.id || '0'])
          : '0',
    }))

    // 构建请求参数并保存
    const params: UpdateTeacherAttendRequest = {
      skjhid: scheduleInfo.value.id,
      rows,
    }

    console.log('保存全部出勤记录', params)

    // 调用更新考勤API
    await updateStudentAttend(params)

    loadStudentAttendList(scheduleInfo.value.id, scheduleInfo.value.jxrwid)
    uni.showToast({
      title: '保存成功',
      icon: 'success',
    })
  } catch (error) {
    console.error('保存考勤记录失败', error)
  }
}

// 添加对话框相关状态变量
const successDialogVisible = ref(false)
const successMessage = ref('')

// 保存日志详情
const handleSaveJournal = debounce(async () => {
  // 如果已经在保存中，则不重复执行
  if (isSaving.value) return

  try {
    // 设置保存状态为true
    isSaving.value = true

    // 检查是否获取到了课程安排ID
    if (!scheduleInfo.value?.id || !scheduleInfo.value?.jxrwid) {
      uni.showToast({
        title: '未获取到课程安排ID',
        icon: 'none',
      })
      isSaving.value = false
      return
    }

    // 检查当前时间是否在可操作时间范围内
    if (!isWithinOperationTime.value) {
      uni.showToast({
        title: '不在允许操作的时间范围内',
        icon: 'none',
      })
      isSaving.value = false
      return
    }

    // 构建请求参数
    const params: UpdateTeacherScheduleRequest = {
      id: scheduleInfo.value.id,
      jxrwid: scheduleInfo.value.jxrwid,
      skfs: journalForm.teachingMethod,
      zypgfs: journalForm.homeworkCorrection || '00', // 默认无作业
      zyts: journalForm.homeworkCount || 0,
      cdsbzt: Number(journalForm.facilityStatus || 0),
      sxkxx_mksl: journalForm.moduleCount || 0,
      sxkxx_sfxnfzsx: journalForm.virtualSimulation ? 1 : 0,
      sxkxx_sfdwfw: journalForm.externalService ? 1 : 0,
      qrbtn: 1, // 确认按钮默认为1
      skkssj: scheduleInfo.value.skkssj || '',
      skjsxm: journalForm.teacher || '',
      skcdmc: journalForm.location || '',
      sknl: journalForm.teachContent || '',
      zynr: journalForm.homeworkContent || '',
      cdsbsyqk: journalForm.facilityUsage || '',
      sxkxx_zrwmc: journalForm.taskName || '',
      sxkxx_sxjnyq: journalForm.skillRequirements || '',
      sxkxx_ptsxzymc: journalForm.trainingResources || '',
      sxkxx_sxkhfs: journalForm.assessmentMethod || '',
      sxkxx_qtsxdd: journalForm.otherTrainingLocation || '',
      sxkxx_remark: journalForm.trainingRemarks || '',
    }

    console.log('保存日志详情', params)

    // 调用更新接口
    const res = await updateTeacherSchedule(params)

    // 更新成功消息并显示对话框
    if (res.frontMsg) {
      successMessage.value = res.frontMsg
      successDialogVisible.value = true
    } else {
      uni.showToast({
        title: '保存成功',
        icon: 'success',
      })
    }

    // 重新加载数据
    if (pageParams.value.id) {
      loadScheduleInfo(Number(pageParams.value.id))
    }
  } catch (error) {
    console.error('保存日志详情失败', error)
  } finally {
    // 无论成功失败，最终都将保存状态设为false
    setTimeout(() => {
      isSaving.value = false
    }, 500)
  }
}, 300) // 300ms的防抖延迟

// 获取页面参数
onLoad((options) => {
  if (options.id) {
    pageParams.value.id = options.id
  }
})

// 加载字典数据
onMounted(async () => {
  try {
    const dicts = await loadDictData(['DM_SKFSDM', 'DM_SYPGXSDM', 'DM_XSKQZTDM'])
    dictData.DM_SKFSDM = dicts.DM_SKFSDM
    dictData.DM_SYPGXSDM = dicts.DM_SYPGXSDM
    dictData.DM_XSKQZTDM = dicts.DM_XSKQZTDM

    // 加载课程安排详情
    if (pageParams.value.id) {
      loadScheduleInfo(Number(pageParams.value.id))
    } else {
      // 如果没有ID参数，可以使用默认ID或显示提示信息
      console.warn('未提供课程安排ID，使用默认ID')
      loadScheduleInfo(1) // 使用默认ID
    }
  } catch (error) {
    console.error('加载字典数据失败', error)
  }
})

// 监听批量操作弹窗关闭事件
watch(
  () => showBatchActionPopup.value,
  (newVal, oldVal) => {
    // 当弹窗从打开状态变为关闭状态时刷新列表
    if (
      oldVal === true &&
      newVal === false &&
      scheduleInfo.value?.id &&
      scheduleInfo.value?.jxrwid
    ) {
      loadStudentAttendList(scheduleInfo.value.id, scheduleInfo.value.jxrwid)
    }
  },
)

// 判断当前授课方式是否为实训
const isTrainingMode = computed(() => {
  // 根据授课方式的字典标签判断是否为实训
  const teachingMethodLabel = getDictLabel(dictData.DM_SKFSDM, journalForm.teachingMethod)
  return teachingMethodLabel?.includes('实训') || false
})

// 计算选中的学生姓名列表（用顿号分隔）
const selectedStudentNames = computed(() => {
  if (selectedStudents.value.size === 0) return ''

  const names = Array.from(selectedStudents.value)
    .map((id) => {
      const student = students.value.find((s) => s.id === id)
      return student ? student.name : ''
    })
    .filter((name) => name !== '')

  return names.join('、')
})

// 处理分页变化
const handlePageChange = (page: number) => {
  pagination.page = page
  // 重新加载数据
  if (scheduleInfo.value?.id && scheduleInfo.value?.jxrwid) {
    loadStudentAttendList(scheduleInfo.value.id, scheduleInfo.value.jxrwid)
  }
}

// 添加考勤确认对话框相关状态变量
const attendanceConfirmDialogVisible = ref(false)
const attendanceConfirmMessage = ref('')

// 处理考勤确认按钮点击
const handleAttendanceConfirm = debounce(async () => {
  // 如果已经在保存中，则不重复执行
  if (isSaving.value) return

  try {
    // 设置保存状态为true
    isSaving.value = true

    // 检查是否获取到了课程安排ID
    if (!scheduleInfo.value?.id || !scheduleInfo.value?.jxrwid) {
      uni.showToast({
        title: '未获取到课程安排ID',
        icon: 'none',
      })
      isSaving.value = false
      return
    }

    // 检查当前时间是否在可操作时间范围内
    if (!isWithinOperationTime.value) {
      uni.showToast({
        title: '不在允许操作的时间范围内',
        icon: 'none',
      })
      isSaving.value = false
      return
    }

    // 调用提交学生课程表接口
    const params: StudentScheduleSubmitParams = {
      jxrwid: scheduleInfo.value.jxrwid,
      id: scheduleInfo.value.id,
    }

    const res = await submitStudentSchedule(params)

    attendanceConfirmMessage.value = res.frontMsg
    attendanceConfirmDialogVisible.value = true

    // 重新加载数据
    if (pageParams.value.id) {
      loadScheduleInfo(Number(pageParams.value.id))
    }
  } catch (error) {
    console.error('提交考勤确认失败', error)
  } finally {
    // 无论成功失败，最终都将保存状态设为false
    setTimeout(() => {
      isSaving.value = false
    }, 500)
  }
}, 300) // 300ms的防抖延迟
</script>

<template>
  <view class="p-4 bg-[#f9fafb]">
    <!-- 时间范围横幅 -->
    <view
      class="rounded-lg p-3 mb-3"
      :class="isWithinOperationTime ? 'bg-green-100 time-banner' : 'bg-red-100 time-banner-red'"
    >
      <view class="flex items-center">
        <wd-icon
          :name="isWithinOperationTime ? 'time-filled' : 'error-circle'"
          class="mr-2"
          :class="isWithinOperationTime ? 'text-green-500' : 'text-red-500'"
        />
        <view
          class="text-sm font-medium"
          :class="isWithinOperationTime ? 'text-green-700' : 'text-red-700'"
        >
          当前教学日志{{ isWithinOperationTime ? '可进行确认操作' : '不在授课确认时间范围内' }}
        </view>
      </view>
      <view
        class="text-xs mt-1 ml-6"
        :class="isWithinOperationTime ? 'text-green-600' : 'text-red-600'"
      >
        可操作时间范围：{{ formatDateTime(scheduleInfo?.skqrkssj) }} 至
        {{ formatDateTime(scheduleInfo?.skqrjssj) }}
      </view>
    </view>

    <!-- 课程信息 -->
    <view class="bg-blue-500 text-white rounded-lg p-4 mb-3">
      <view class="flex items-center justify-between text-xs mb-2">
        <view class="text-gray-200">课程名称：</view>
        <view class="font-medium">{{ courseInfo.type }}《{{ courseInfo.name }}》</view>
      </view>

      <view class="flex items-center justify-between text-xs mb-2">
        <view class="text-gray-200">总学时/周学时/周数：</view>
        <view>{{ courseInfo.total }} / {{ courseInfo.weekHours }} / {{ courseInfo.weeks }}</view>
      </view>

      <view class="flex text-xs mb-2">
        <view class="text-gray-200 shrink-0">授课时间：</view>
        <view class="flex-1 text-right">
          <view>{{ journalForm.week }} {{ journalForm.date }} {{ journalForm.weekday }}</view>
          <view>{{ journalForm.sessionTime }}</view>
        </view>
      </view>

      <view class="flex items-start justify-between text-xs">
        <view class="text-gray-200 mt-1">确认信息：</view>
        <view class="flex flex-col gap-2">
          <view class="flex items-center">
            <view
              class="w-2 h-2 rounded-full mr-1"
              :class="scheduleInfo?.skjhjsqrzt ? 'bg-green-400' : 'bg-red-400'"
            ></view>
            <text>
              <text v-if="scheduleInfo?.skjhjsqrryxm" class="mr-1">
                {{ scheduleInfo.skjhjsqrryxm }}
              </text>
              教师{{ scheduleInfo?.skjhjsqrzt ? '已确认' : '未确认' }}
            </text>
            <text
              v-if="scheduleInfo?.skjhjsqrzt && scheduleInfo?.skjhjsqrsj"
              class="ml-1 text-gray-300"
            >
              ({{ formatDateTime(scheduleInfo.skjhjsqrsj).substring(5) }})
            </text>
          </view>
          <view class="flex items-center">
            <view
              class="w-2 h-2 rounded-full mr-1"
              :class="scheduleInfo?.skjhxsqrzt ? 'bg-green-400' : 'bg-red-400'"
            ></view>
            <text>
              <text v-if="scheduleInfo?.skjhxsqrxsxm" class="mr-1">
                {{ scheduleInfo.skjhxsqrxsxm }}
              </text>
              学生{{ scheduleInfo?.skjhxsqrzt ? '已确认' : '未确认' }}
            </text>
            <text
              v-if="scheduleInfo?.skjhxsqrzt && scheduleInfo?.skjhxsqrsj"
              class="ml-1 text-gray-300"
            >
              ({{ formatDateTime(scheduleInfo.skjhxsqrsj).substring(5) }})
            </text>
          </view>
          <view class="flex items-center">
            <view
              class="w-2 h-2 rounded-full mr-1"
              :class="scheduleInfo?.skjhgzlrdzt ? 'bg-green-400' : 'bg-red-400'"
            ></view>
            <text>工作量{{ scheduleInfo?.skjhgzlrdzt ? '已认定' : '未认定' }}</text>
            <text v-if="scheduleInfo?.gzlrdzt && scheduleInfo?.gzlrdsj" class="ml-1 text-gray-300">
              ({{ formatDateTime(scheduleInfo.gzlrdsj).substring(5) }})
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能按钮 -->
    <view class="bg-white rounded-lg flex gap-3 mb-3 p-2">
      <view
        v-for="action in [
          { key: 'journal', label: '授课确认' },
          { key: 'attendance', label: '考勤登记' },
        ]"
        :key="action.key"
        :class="{
          'bg-blue-500 text-white': activeTab === action.key,
          'bg-gray-100': activeTab !== action.key,
        }"
        class="flex-1 text-center py-3 text-sm rounded-lg font-medium transition-all"
        @click="activeTab = action.key as 'attendance' | 'journal'"
      >
        {{ action.label }}
      </view>
    </view>

    <!-- 考勤记录内容 -->
    <view v-if="activeTab === 'attendance'">
      <!-- 考勤统计 -->
      <view class="bg-white rounded-lg mb-3 px-4 py-2">
        <view class="flex justify-between items-center mb-3">
          <view class="text-base font-semibold">考勤记录统计（所有节次）</view>
        </view>
        <view v-if="totalStatistics.total > 0" class="flex flex-wrap justify-around mb-3">
          <!-- 总人次统计 -->
          <view class="text-center px-2 py-1">
            <view class="text-lg font-semibold mb-1">
              {{ totalStatistics.total }}
            </view>
            <view class="text-xs text-gray-500">总人次</view>
          </view>

          <!-- 各状态统计 -->
          <view
            v-for="attendStatus in attendanceStatus"
            :key="attendStatus.type"
            class="text-center px-2 py-1"
          >
            <view class="text-lg font-semibold mb-1" :style="{ color: attendStatus.color }">
              {{ totalStatistics[attendStatus.type] || 0 }}
            </view>
            <view class="text-xs text-gray-500">{{ attendStatus.label }}</view>
          </view>
        </view>
        <view v-if="sessions && sessions.length > 0" class="text-xs text-gray-500 text-center">
          统计范围：{{ sessions.length }}个课程节次，{{ students.length }}名学生
          <text v-if="pagination.total > pagination.pageSize">
            (当前第{{ pagination.page }}/{{ Math.ceil(pagination.total / pagination.pageSize) }}页)
          </text>
        </view>
      </view>

      <!-- 不可编辑状态提示 -->
      <!-- <view
        v-if="!isWithinOperationTime"
        class="bg-red-50 rounded-lg mb-3 p-4 border border-red-200"
      >
        <view class="flex items-center">
          <wd-icon name="error-circle" class="text-red-500 mr-2" size="20px" />
          <view class="text-sm font-medium text-red-700">当前日志不可编辑</view>
        </view>
        <view class="text-xs text-red-600 mt-1 ml-6">
          考勤记录仅在可操作时间范围内可编辑，当前仅可查看。
        </view>
      </view> -->

      <!-- 学生列表 -->
      <view class="bg-white rounded-lg mb-3 p-4 relative">
        <view class="flex justify-between pb-3 border-b border-gray-100 mb-2">
          <view class="text-base font-semibold">学生名单</view>
          <view class="flex items-center gap-3">
            <view
              class="text-sm"
              :class="isWithinOperationTime ? 'text-blue-500' : 'text-gray-400'"
              @click="handleAllPresent"
              v-if="false"
            >
              全部出勤
            </view>
            <view
              class="text-sm"
              :class="isWithinOperationTime ? 'text-blue-500' : 'text-gray-400'"
              @click="toggleMultiSelectMode"
              v-if="false"
            >
              {{ multiSelectMode ? '取消多选' : '多选' }}
            </view>
          </view>
        </view>

        <!-- 搜索框 -->
        <view class="search-box flex items-center bg-gray-100 rounded-lg px-3 py-2 mb-3">
          <wd-icon name="search" class="text-gray-400 mr-2" size="16px" />
          <input
            class="flex-1 bg-transparent text-sm"
            type="text"
            v-model="searchKeyword"
            placeholder="搜索学生姓名或学号"
          />
          <wd-icon
            v-if="searchKeyword"
            name="close"
            class="text-gray-400"
            size="16px"
            @click="searchKeyword = ''"
          />
        </view>

        <!-- 多选操作栏 -->
        <view
          v-if="multiSelectMode && selectedCount > 0"
          class="flex justify-between items-center py-2 mb-2 bg-blue-50 px-3 rounded-lg"
        >
          <view class="flex items-center">
            <text class="text-sm">已选择 {{ selectedCount }} 人</text>
          </view>
          <view class="flex gap-3">
            <view
              class="text-sm"
              :class="isWithinOperationTime ? 'text-blue-500' : 'text-gray-400 not-allowed'"
              @click="openBatchActionPopup"
            >
              批量操作
            </view>
          </view>
        </view>

        <!-- 课程节次标题 -->
        <view class="flex items-center py-3 border-b border-gray-100">
          <view
            class="w-8 h-8 flex items-center justify-center"
            :class="!isWithinOperationTime ? 'opacity-50 not-allowed' : 'cursor-pointer'"
            @click="toggleSelectAll"
          >
            <view
              class="w-4 h-4 mr-2 rounded border-2 flex items-center justify-center transition-colors border-solid"
              :class="{
                'bg-blue-500 border-blue-500': isAllSelected || isPartialSelected,
                'border-gray-400': !isAllSelected && !isPartialSelected,
              }"
            >
              <wd-icon v-if="isAllSelected" name="check" class="text-white" size="12px" />
              <view v-else-if="isPartialSelected" class="w-2.5 h-2.5 bg-white rounded-sm"></view>
            </view>
          </view>
          <view class="flex-1"></view>
          <view class="flex gap-2">
            <view
              v-for="session in sessions"
              :key="session.id"
              class="w-10 h-10 rounded-full bg-blue-50 flex items-center justify-center text-xs text-blue-500"
            >
              {{ session.name.replace('', '').replace('', '') }}
            </view>
          </view>
        </view>
        <!-- 搜索结果为空时的提示 -->
        <view v-if="filteredStudents.length === 0" class="py-10 text-center">
          <view class="text-gray-400 mb-2">
            <wd-icon name="search1" size="48px" />
          </view>
          <view class="text-gray-500 text-sm">未找到匹配的学生</view>
        </view>

        <template v-for="student in filteredStudents" :key="student.id">
          <!-- 学生行 -->
          <view class="flex items-center py-3 border-b border-gray-100">
            <!-- 多选复选框 -->
            <view
              class="w-8 h-8 flex items-center justify-center"
              :class="!isWithinOperationTime ? 'opacity-50 not-allowed' : ''"
              @click.stop="toggleSelectStudent(student.id)"
            >
              <view
                class="w-4 h-4 rounded border-2 flex items-center justify-center border-solid mr-2"
                :class="
                  selectedStudents.has(student.id)
                    ? 'bg-blue-500 border-blue-500'
                    : 'border-gray-400'
                "
              >
                <wd-icon
                  v-if="selectedStudents.has(student.id)"
                  name="check"
                  class="text-white"
                  size="12px"
                />
              </view>
            </view>

            <view
              class="w-10 h-10 rounded-full bg-blue-50 flex items-center justify-center text-base text-blue-500 mr-3"
              :class="!isWithinOperationTime ? 'opacity-50 not-allowed' : ''"
              @click="toggleSelectStudent(student.id)"
            >
              {{ student.zwh || student.name[0] }}
            </view>
            <view class="flex-1" @click="toggleSelectStudent(student.id)">
              <view class="text-sm font-medium mb-0.5">{{ student.name }}</view>
              <view class="text-xs text-gray-500">{{ student.id }}</view>
              <view v-if="student.classname" class="text-xs text-gray-500">
                {{ student.classname }}
              </view>
            </view>
            <view class="flex gap-2">
              <view
                v-for="session in sessions"
                :key="session.id"
                class="w-10 h-10 rounded-full flex flex-col items-center justify-center"
                :class="!isWithinOperationTime ? 'opacity-50 not-allowed' : ''"
                :style="{
                  backgroundColor: `${getStatusInfo(student.status[session.id]).color}20`,
                }"
                @click="openStatusPopup(student, session.id)"
              >
                <view
                  :class="[getStatusInfo(student.status[session.id]).icon]"
                  :style="{ color: getStatusInfo(student.status[session.id]).color }"
                  class="text-base"
                ></view>
                <text
                  class="status-label text-xxs"
                  :style="{ color: getStatusInfo(student.status[session.id]).color }"
                >
                  {{ getStatusInfo(student.status[session.id]).label }}
                </text>
              </view>
            </view>
          </view>

          <!-- 备注显示行 - 只在有备注时显示 -->
          <view
            v-if="hasRemarks(student)"
            class="flex py-2 px-8 bg-gray-50 border-b border-gray-100"
          >
            <view class="flex-1">
              <view class="text-xs text-gray-600">
                <wd-icon name="note" size="14px" class="mr-1" />
                备注：{{ student.remarks[Object.keys(student.remarks)[0]] || '' }}
              </view>
            </view>
          </view>
        </template>

        <!-- 不可编辑状态遮罩 -->
        <view
          v-if="!isWithinOperationTime"
          class="absolute inset-0 bg-white bg-opacity-40 flex items-center justify-center z-10 rounded-lg"
          style="pointer-events: none"
        ></view>
      </view>

      <!-- 分页组件 -->
      <view v-if="pagination.total > 0 && searchKeyword === ''" class="mt-3">
        <Pagination
          :total="pagination.total"
          :page="pagination.page"
          :pageSize="pagination.pageSize"
          @update:page="handlePageChange"
        />
      </view>
    </view>

    <!-- 日志详情内容 -->
    <view v-if="activeTab === 'journal'" class="bg-white rounded-lg px-4 py-2 relative">
      <!-- 日志表单 -->
      <view class="form-container">
        <!-- 教学内容部分 -->
        <view class="form-section">
          <view class="section-title">教学内容</view>

          <!-- 授课方式 -->
          <view class="form-item">
            <view class="form-row-flex">
              <text class="form-label">授课方式</text>
              <view class="form-content">
                <picker
                  :range="getDictOptions(dictData.DM_SKFSDM)"
                  range-key="label"
                  @change="
                    (e) =>
                      (journalForm.teachingMethod = getDictOptions(dictData.DM_SKFSDM)[
                        e.detail.value
                      ].value)
                  "
                  disabled
                >
                  <view class="picker-display" :class="!isWithinOperationTime ? 'disabled' : ''">
                    <text v-if="journalForm.teachingMethod">
                      {{ getDictLabel(dictData.DM_SKFSDM, journalForm.teachingMethod) }}
                    </text>
                    <text v-else class="placeholder">请选择授课方式</text>
                    <wd-icon name="chevron-down" class="text-gray-500" size="16px" />
                  </view>
                </picker>
              </view>
            </view>
          </view>

          <!-- 教学内容 -->
          <view class="form-item">
            <view class="form-row-column">
              <text class="form-label required">授课内容</text>
              <view class="form-content">
                <textarea
                  class="form-textarea"
                  v-model="journalForm.teachContent"
                  placeholder="请输入本次课程教学内容"
                  disabled
                />
              </view>
            </view>
          </view>
        </view>

        <!-- 作业情况部分 -->
        <view class="form-section">
          <view class="section-title">作业情况</view>

          <!-- 作业批改 -->
          <view class="form-item">
            <view class="form-row-flex">
              <text class="form-label">作业批改</text>
              <view class="form-content">
                <picker
                  :range="getDictOptions(dictData.DM_SYPGXSDM)"
                  range-key="label"
                  @change="
                    (e) =>
                      (journalForm.homeworkCorrection = getDictOptions(dictData.DM_SYPGXSDM)[
                        e.detail.value
                      ].value)
                  "
                  disabled
                >
                  <view class="picker-display" :class="!isWithinOperationTime ? 'disabled' : ''">
                    <text v-if="journalForm.homeworkCorrection">
                      {{ getDictLabel(dictData.DM_SYPGXSDM, journalForm.homeworkCorrection) }}
                    </text>
                    <text v-else class="placeholder">请选择作业批改状态</text>
                    <wd-icon name="chevron-down" class="text-gray-500" size="16px" />
                  </view>
                </picker>
              </view>
            </view>
          </view>

          <!-- 作业题数 - 仅在不是"无作业"时显示 -->
          <view class="form-item" v-if="journalForm.homeworkCorrection !== '00'">
            <view class="form-row-flex">
              <text class="form-label">作业题数</text>
              <view class="form-content">
                <input
                  class="form-input"
                  type="number"
                  v-model="journalForm.homeworkCount"
                  placeholder="请输入作业题数"
                  disabled
                />
              </view>
            </view>
          </view>

          <!-- 作业内容 - 仅在不是"无作业"时显示 -->
          <view class="form-item" v-if="journalForm.homeworkCorrection !== '00'">
            <view class="form-row-column">
              <text class="form-label">作业内容</text>
              <view class="form-content">
                <textarea
                  class="form-textarea"
                  v-model="journalForm.homeworkContent"
                  placeholder="请输入布置的作业内容"
                  disabled
                />
              </view>
            </view>
          </view>
        </view>

        <!-- 场地设备部分 -->
        <view class="form-section">
          <view class="section-title">场地设备</view>

          <!-- 场地设备状态 -->
          <view class="form-item">
            <view class="form-row-flex">
              <text class="form-label">场地设备状态</text>
              <view class="form-content">
                <picker
                  :range="facilityStatusOptions"
                  range-key="label"
                  @change="
                    (e) =>
                      (journalForm.facilityStatus = facilityStatusOptions[e.detail.value].value)
                  "
                  disabled
                >
                  <view class="picker-display" :class="!isWithinOperationTime ? 'disabled' : ''">
                    <text v-if="journalForm.facilityStatus !== undefined">
                      {{
                        facilityStatusOptions.find(
                          (item) => item.value === journalForm.facilityStatus,
                        )?.label || '请选择'
                      }}
                    </text>
                    <text v-else class="placeholder">请选择场地设备状态</text>
                    <wd-icon name="chevron-down" class="text-gray-500" size="16px" />
                  </view>
                </picker>
              </view>
            </view>
          </view>

          <!-- 场地设备使用情况 -->
          <view class="form-item">
            <view class="form-row-column">
              <text class="form-label">场地设备使用情况</text>
              <view class="form-content">
                <textarea
                  class="form-textarea"
                  v-model="journalForm.facilityUsage"
                  placeholder="请输入场地设备使用情况"
                  disabled
                />
              </view>
            </view>
          </view>
        </view>

        <!-- 典型任务部分 -->
        <view class="form-section" v-if="isTrainingMode">
          <view class="section-title">典型任务</view>

          <!-- 任务名称 -->
          <view class="form-item">
            <view class="form-row-flex">
              <text class="form-label">任务名称</text>
              <view class="form-content">
                <input
                  class="form-input"
                  v-model="journalForm.taskName"
                  placeholder="请输入典型任务名称"
                  disabled
                />
              </view>
            </view>
          </view>

          <!-- 技能要求 -->
          <view class="form-item">
            <view class="form-row-column">
              <text class="form-label">技能要求</text>
              <view class="form-content">
                <textarea
                  class="form-textarea"
                  v-model="journalForm.skillRequirements"
                  placeholder="请输入技能要求"
                  disabled
                />
              </view>
            </view>
          </view>

          <!-- 配套实训资源 -->
          <view class="form-item">
            <view class="form-row-column">
              <text class="form-label">配套实训资源</text>
              <view class="form-content">
                <textarea
                  class="form-textarea"
                  v-model="journalForm.trainingResources"
                  placeholder="请输入配套实训资源"
                  disabled
                />
              </view>
            </view>
          </view>
        </view>

        <!-- 考核方式部分 -->
        <view class="form-section" v-if="isTrainingMode">
          <view class="section-title">考核与实训</view>

          <!-- 考核方式 -->
          <view class="form-item">
            <view class="form-row-flex">
              <text class="form-label">考核方式</text>
              <view class="form-content">
                <input
                  class="form-input"
                  v-model="journalForm.assessmentMethod"
                  placeholder="请输入考核方式"
                  disabled
                />
              </view>
            </view>
          </view>

          <!-- 模块数量 -->
          <view class="form-item">
            <view class="form-row-flex">
              <text class="form-label">模块数量</text>
              <view class="form-content">
                <input
                  class="form-input"
                  type="number"
                  v-model="journalForm.moduleCount"
                  placeholder="请输入模块数量"
                  disabled
                />
              </view>
            </view>
          </view>

          <!-- 虚拟仿真实训 -->
          <view class="form-item">
            <view class="form-row-flex">
              <text class="form-label">虚拟仿真实训</text>
              <view class="form-content flex justify-between items-center">
                <text class="text-sm text-gray-600">
                  {{ journalForm.virtualSimulation ? '是' : '否' }}
                </text>
                <wd-switch v-model="journalForm.virtualSimulation" size="20px" disabled />
              </view>
            </view>
          </view>

          <!-- 对外服务 -->
          <view class="form-item">
            <view class="form-row-flex">
              <text class="form-label">对外服务</text>
              <view class="form-content flex justify-between items-center">
                <text class="text-sm text-gray-600">
                  {{ journalForm.externalService ? '是' : '否' }}
                </text>
                <wd-switch v-model="journalForm.externalService" size="20px" disabled />
              </view>
            </view>
          </view>

          <!-- 其他实训地点 -->
          <view class="form-item">
            <view class="form-row-flex">
              <text class="form-label">其他实训地点</text>
              <view class="form-content">
                <input
                  class="form-input"
                  v-model="journalForm.otherTrainingLocation"
                  placeholder="请输入其他实训地点"
                  disabled
                />
              </view>
            </view>
          </view>

          <!-- 实训情况备注 -->
          <view class="form-item">
            <view class="form-row-column">
              <text class="form-label">实训情况备注</text>
              <view class="form-content">
                <textarea
                  class="form-textarea"
                  v-model="journalForm.trainingRemarks"
                  placeholder="请输入实训情况备注"
                  disabled
                />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 不可编辑状态遮罩 -->
      <view
        v-if="!isWithinOperationTime"
        class="absolute inset-0 bg-white bg-opacity-40 flex items-center justify-center z-10 rounded-lg"
        style="pointer-events: none"
      ></view>
    </view>
    <template v-if="false && activeTab === 'journal' && isWithinOperationTime">
      <view class="h-20" />
      <!-- 添加固定在底部的按钮 -->
      <view class="fixed-bottom-buttons pb-4">
        <view class="button-container safe-area-inset-bottom">
          <button
            class="btn-primary"
            :class="{ 'btn-disabled': !isWithinOperationTime || isSaving }"
            :disabled="!isWithinOperationTime || isSaving"
            @click="handleSaveJournal"
          >
            <view v-if="isSaving" class="flex items-center justify-center">
              <view class="loading-spinner mr-2"></view>
              <text>保存中...</text>
            </view>
            <text v-else>授课登记</text>
          </button>
        </view>
      </view>
    </template>
    <!-- 状态选择弹窗 -->
    <wd-popup
      v-model="showStatusPopup"
      position="bottom"
      custom-style="max-height: 80vh; border-radius: 20rpx 20rpx 0 0;"
    >
      <scroll-view class="status-popup-scroll" scroll-y>
        <view class="p-4">
          <view class="text-center text-base font-semibold mb-4">
            {{ currentStudent ? '选择考勤状态' : `批量设置考勤状态 (${selectedCount}人)` }}
          </view>

          <!-- 显示已选择的学生名称 -->
          <view
            v-if="!currentStudent && selectedStudents.size > 0 && selectedStudentNames"
            class="mb-4 px-3 py-2 bg-gray-50 rounded-lg"
          >
            <view class="text-sm text-gray-600 mb-1">已选择学生：</view>
            <view class="text-sm text-gray-800">
              {{ selectedStudentNames }}
            </view>
          </view>

          <!-- 批量操作时显示节次选择 -->
          <view v-if="!currentStudent && selectedStudents.size > 0" class="mb-4">
            <view class="flex justify-between items-center mb-2">
              <view class="text-sm text-gray-600">选择节次</view>
              <view
                class="text-sm"
                :class="isWithinOperationTime ? 'text-blue-500' : 'text-gray-400'"
                @click="toggleSelectAllSessions"
              >
                {{ isAllSessionsSelected ? '取消全选' : '全选' }}
              </view>
            </view>
            <view class="flex flex-wrap gap-2">
              <view
                v-for="session in sessions"
                :key="session.id"
                class="px-3 py-2 rounded-full text-sm"
                :class="
                  selectedSessionId.has(session.id)
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-700'
                "
                @click="toggleSessionSelection(session.id)"
              >
                {{ session.name }}
              </view>
            </view>
          </view>

          <view class="flex flex-wrap justify-around mb-4">
            <view
              v-for="status in attendanceStatus"
              :key="status.value"
              class="flex flex-col items-center gap-2 p-2"
              :class="!isWithinOperationTime ? 'opacity-50' : ''"
              @click="handleStatusSelect(status.type)"
            >
              <view
                class="w-12 h-12 rounded-full flex items-center justify-center"
                :style="{
                  backgroundColor: `${status.color}20`,
                  color: status.color,
                }"
              >
                <view
                  :class="[status.icon]"
                  :style="{ color: status.color }"
                  class="text-xl"
                ></view>
              </view>
              <view class="text-sm" :style="{ color: status.color }">
                {{ status.label }}
              </view>
            </view>
          </view>

          <!-- 添加备注输入框 -->
          <view class="mb-4">
            <view class="text-sm text-gray-600 mb-2">备注信息</view>
            <textarea
              class="form-textarea"
              v-model="currentRemark"
              placeholder="请输入备注信息（限100字）"
              :maxlength="100"
              :disabled="!isWithinOperationTime"
            ></textarea>
          </view>

          <view
            class="bg-gray-100 text-center text-base font-medium py-3 rounded-lg"
            @click="showStatusPopup = false"
          >
            取消
          </view>
        </view>
      </scroll-view>
    </wd-popup>

    <!-- 批量操作弹窗 -->
    <wd-popup v-model="showBatchActionPopup" position="bottom">
      <view class="p-4">
        <view class="text-center text-base font-semibold mb-4">批量操作</view>
        <view class="flex flex-col gap-3 mb-4">
          <view
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
            :class="!isWithinOperationTime ? 'opacity-50 not-allowed' : ''"
            @click="handleBatchAction('attendance')"
          >
            <view class="flex items-center">
              <view
                class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3"
              >
                <wd-icon name="check" class="text-blue-500" size="20px" />
              </view>
              <view>
                <view class="text-base font-medium">批量考勤</view>
                <view class="text-xs text-gray-500">为选中学生批量设置考勤状态</view>
              </view>
            </view>
            <wd-icon name="arrow-right" class="text-gray-400" size="16px" />
          </view>

          <view
            class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
            :class="!isWithinOperationTime ? 'opacity-50' : ''"
            @click="handleBatchAction('export')"
          >
            <view class="flex items-center">
              <view
                class="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3"
              >
                <wd-icon name="download" class="text-green-500" size="20px" />
              </view>
              <view>
                <view class="text-base font-medium">导出数据</view>
                <view class="text-xs text-gray-500">导出选中学生的考勤数据</view>
              </view>
            </view>
            <wd-icon name="arrow-right" class="text-gray-400" size="16px" />
          </view>
        </view>
        <view
          class="bg-gray-100 text-center text-base font-medium py-3 rounded-lg"
          @click="showBatchActionPopup = false"
        >
          取消
        </view>
      </view>
    </wd-popup>

    <!-- 替换wot-design-ui的dialog，使用自定义对话框 -->
    <view
      v-if="successDialogVisible"
      class="custom-dialog-mask"
      @click.stop="successDialogVisible = false"
    >
      <view class="custom-dialog" @click.stop>
        <view class="custom-dialog-header">
          <view class="custom-dialog-title">保存成功</view>
          <view class="custom-dialog-close" @click="successDialogVisible = false">
            <wd-icon name="close" size="18px" class="text-gray-500"></wd-icon>
          </view>
        </view>
        <view class="custom-dialog-content p-4">
          <view class="mb-4 flex items-center justify-center">
            <view class="w-16 h-16 rounded-full bg-green-50 flex items-center justify-center">
              <wd-icon name="check-bold" size="32px" class="text-green-500"></wd-icon>
            </view>
          </view>
          <view class="text-base text-gray-700">
            <template v-for="(line, index) in successMessage.split('\n')" :key="index">
              <view :class="index === 0 ? 'font-semibold text-green-600 mb-2' : 'mb-2'">
                {{ line }}
              </view>
            </template>
          </view>
        </view>
        <view class="custom-dialog-footer">
          <button class="confirm-button" @click="successDialogVisible = false">确定</button>
        </view>
      </view>
    </view>

    <!-- 添加固定在底部的考勤确认按钮 -->
    <template v-if="activeTab === 'attendance' && isWithinOperationTime">
      <view class="h-20" />
      <view class="fixed-bottom-buttons pb-4">
        <view class="button-container safe-area-inset-bottom">
          <button
            class="btn-primary"
            :class="{ 'btn-disabled': !isWithinOperationTime || isSaving }"
            :disabled="!isWithinOperationTime || isSaving"
            @click="handleAttendanceConfirm"
          >
            <view v-if="isSaving" class="flex items-center justify-center">
              <view class="loading-spinner mr-2"></view>
              <text>提交中...</text>
            </view>
            <text v-else>提交考勤确认</text>
          </button>
        </view>
      </view>
    </template>

    <!-- 考勤确认结果对话框 -->
    <view
      v-if="attendanceConfirmDialogVisible"
      class="custom-dialog-mask"
      @click.stop="attendanceConfirmDialogVisible = false"
    >
      <view class="custom-dialog" @click.stop>
        <view class="custom-dialog-header">
          <view class="custom-dialog-title">考勤登记完成</view>
          <view class="custom-dialog-close" @click="attendanceConfirmDialogVisible = false">
            <wd-icon name="close" size="18px" class="text-gray-500"></wd-icon>
          </view>
        </view>
        <view class="custom-dialog-content p-4">
          <view class="mb-4 flex items-center justify-center">
            <view class="w-16 h-16 rounded-full bg-blue-50 flex items-center justify-center">
              <wd-icon name="check-bold" size="32px" class="text-blue-500"></wd-icon>
            </view>
          </view>
          <view class="text-base text-gray-700">
            <template v-for="(line, index) in attendanceConfirmMessage.split('\n')" :key="index">
              <view
                :class="{
                  'font-semibold text-blue-600 text-lg text-center': index === 0,
                  'text-center mb-2': index === 1 || index === 2,
                  'font-medium text-gray-800 mt-4': index === 3,
                  'ml-4': index > 3,
                }"
              >
                {{ line }}
              </view>
            </template>
          </view>
        </view>
        <view class="custom-dialog-footer">
          <button
            class="confirm-button bg-blue-500"
            @click="attendanceConfirmDialogVisible = false"
          >
            确定
          </button>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
.form-container {
  width: 100%;
}

.search-box {
  input {
    height: 60rpx;
    font-size: 28rpx;
    color: #333333;
    &::placeholder {
      color: #999999;
    }
  }
}

.time-banner {
  border-left: 8rpx solid #22c55e;
}
.time-banner-red {
  border-left: 8rpx solid #f43f5e;
}

.form-section {
  box-sizing: border-box;
}

.section-title {
  padding-bottom: 16rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  border-bottom: 1px solid #f0f0f0;
}

.form-item {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 20rpx;
}

.form-row-flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.form-row-column {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.form-label {
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333333;

  &.required::before {
    margin-right: 4rpx;
    color: #f5222d;
    content: '*';
  }
}

.form-content {
  box-sizing: border-box;
  width: 100%;
}

.form-textarea {
  box-sizing: border-box;
  width: 100%;
  height: 180rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;

  &:disabled {
    color: #999999;
    cursor: not-allowed;
    background-color: #f5f5f5;
  }
}

.picker-display {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;

  .placeholder {
    color: #999999;
  }
}

.button-group {
  display: flex;
  gap: 32rpx;
  justify-content: center;
  margin-top: 48rpx;
  margin-bottom: 48rpx;
}
/* 添加固定底部按钮样式 */
.fixed-bottom-buttons {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.button-container {
  display: flex;
  gap: 32rpx;
  justify-content: center;
  padding: 24rpx 32rpx 40rpx; /* 增加底部间隙 */
}

// 按钮基础样式
button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 0 12px;
  font-size: 13px;
  border-radius: 6px;
  transition: opacity 0.3s;

  &:active {
    opacity: 0.85;
  }
}

// 主要按钮样式 - 蓝色
.btn-primary {
  color: #fff;
  background-color: #007aff;
  border: 1px solid #007aff;
}

// 默认按钮样式 - 灰色
.btn-default {
  color: #666666;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
}

// 成功按钮样式 - 绿色
.btn-success {
  color: #fff;
  background-color: #4cd964;
  border: 1px solid #4cd964;
}

// 为底部固定按钮添加特殊样式
.fixed-bottom-buttons .btn-primary,
.fixed-bottom-buttons .btn-default,
.fixed-bottom-buttons .btn-success {
  flex: 1;
  height: 80rpx;
  font-size: 28rpx;
}

.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */
  padding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */
}

// 警告按钮样式 - 橙色
.btn-warning {
  color: #fff;
  background-color: #ff9500;
  border: 1px solid #ff9500;
}

// 危险按钮样式 - 红色
.btn-danger {
  color: #fff;
  background-color: #ff3b30;
  border: 1px solid #ff3b30;
}

// 信息按钮样式 - 青色
.btn-info {
  color: #fff;
  background-color: #5ac8fa;
  border: 1px solid #5ac8fa;
}

// 禁用按钮样式
.btn-disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.form-input {
  box-sizing: border-box;
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;

  &:disabled {
    color: #999999;
    cursor: not-allowed;
    background-color: #f5f5f5;
  }
}

.info-list {
  margin-bottom: 24rpx;
}

.info-item {
  display: flex;
  padding: 16rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  flex: 0 0 200rpx;
  font-size: 28rpx;
  color: #666666;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.status-label {
  margin-top: 2rpx;
  font-size: 16rpx;
  line-height: 1;
}

.text-xxs {
  font-size: 16rpx;
}

.success-dialog-content {
  max-height: 60vh;
  overflow-y: auto;
}

.dialog-footer {
  width: 100%;
}

.confirm-button {
  width: 240rpx;
  height: 80rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  color: #ffffff;
  text-align: center;
  background-color: #1890ff;
  border-radius: 40rpx;
}
/* 自定义对话框样式 */
.custom-dialog-mask {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
  transition: all 0.3s;
}

.custom-dialog {
  width: 85%;
  max-width: 600rpx;
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  animation: dialogFadeIn 0.3s;
}

.custom-dialog-header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 48rpx;
  border-bottom: 1px solid #f0f0f0;
}

.custom-dialog-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.custom-dialog-close {
  position: absolute;
  top: 50%;
  right: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  cursor: pointer;
  transform: translateY(-50%);
}

.custom-dialog-content {
  max-height: 60vh;
  overflow-y: auto;
}

.custom-dialog-footer {
  display: flex;
  justify-content: center;
  padding: 24rpx;
  border-top: 1px solid #f0f0f0;
}

@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 状态选择弹窗滚动视图样式
.status-popup-scroll {
  height: auto;
  max-height: 80vh;
}

// 确保名单滚动区域有明确的高度限制
.max-h-24 {
  max-height: 6rem;
  overflow-y: auto;
}

// 添加禁用状态的样式
.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

// 添加半透明遮罩样式
.bg-opacity-40 {
  opacity: 0.4;
}

// 添加不可点击样式
.not-allowed {
  cursor: not-allowed !important;
}

// 添加蓝色确认按钮样式
.bg-blue-500 {
  color: #ffffff !important;
  background-color: #3b82f6 !important;
  border-color: #3b82f6 !important;
}

.text-blue-600 {
  color: #2563eb !important;
}

.text-blue-500 {
  color: #3b82f6 !important;
}

.bg-blue-50 {
  background-color: #eff6ff !important;
}

.text-lg {
  font-size: 18px !important;
}
/* 添加加载动画样式 */
.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: #ffffff;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
