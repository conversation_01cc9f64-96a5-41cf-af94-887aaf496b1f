<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的领用教材',
  },
}
</route>
<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { getTeachingMaterialReceiveList } from '@/service/teachingMaterial'
import type { TeachingMaterialReceiveItem } from '@/types/teachingMaterial'
import SemesterWeekPicker from '@/components/SemesterWeekPicker/index.vue'
import { useUserStore } from '@/store/user'

// 用户状态
const userStore = useUserStore()
const { userInfo } = userStore

// 当前选中的学期
const selectedSemester = ref<string>('')
const currentSemesterLabel = ref<string>('')

// 学期选择数据 - 保留用于兼容原有逻辑
const semesters = [
  { id: 1, name: '2023-2024-2', value: '2023-2024|2' },
  { id: 2, name: '2023-2024-1', value: '2023-2024|1' },
  { id: 3, name: '2022-2023-2', value: '2022-2023|2' },
  { id: 4, name: '2022-2023-1', value: '2022-2023|1' },
]
const activeSemester = ref(semesters[0].id)

// 教材列表加载状态
const loading = ref(false)
// 教材列表数据
const receiveList = ref<TeachingMaterialReceiveItem[]>([])
// 教材列表总数
const total = ref(0)
// 分页参数
const page = ref(1)
const pageSize = ref(10)
// 排序参数
const sortBy = ref('id')
const sortOrder = ref<'asc' | 'desc'>('desc')
// 是否已初始化加载
const isInitialized = ref(false)

// 已领取的教材数量
const receivedBooksCount = computed(() => {
  return receiveList.value.filter((item) => item.receiveNum > 0).length
})

// 购买的教材总数量
const totalPurchasedBooks = computed(() => {
  return receiveList.value.reduce((total, item) => total + (item.receiveNum || 0), 0)
})

// 教材总花费金额
const totalSpentAmount = computed(() => {
  return receiveList.value
    .reduce((total, item) => {
      const money = parseFloat(item.money || '0')
      return total + money * (item.receiveNum || 0)
    }, 0)
    .toFixed(2)
})

// 当前选中学期的value
const currentSemesterValue = computed(() => {
  if (selectedSemester.value) {
    return selectedSemester.value.replace(/^(\d{4}-\d{4})-(\d)$/, '$1|$2')
  }
  const semester = semesters.find((item) => item.id === activeSemester.value)
  return semester ? semester.value : ''
})

// 获取教材领用列表
const fetchReceiveList = async () => {
  if (loading.value) return
  try {
    loading.value = true
    const params = {
      page: page.value,
      pageSize: pageSize.value,
      sortBy: sortBy.value,
      sortOrder: sortOrder.value,
      semesters: selectedSemester.value ? currentSemesterValue.value : '',
      name: '',
      isbn: '',
      mainEditor: '',
      publishingHouse: '',
      receiveNum: '',
      money: '',
      leaveData: '',
      settlementMethod: '',
    }
    const res = await getTeachingMaterialReceiveList(params)
    receiveList.value = res.items
    total.value = res.total
    isInitialized.value = true
  } catch (error) {
    console.error('获取教材领用列表失败', error)
    uni.showToast({
      title: '获取教材领用列表失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 转换教材项为页面显示格式
const formatBookList = computed(() => {
  return receiveList.value.map((item) => {
    // 根据实际情况判断教材状态
    let status = 'pending'
    let statusText = '待到货'

    // 假设：有领用数量的为已领取
    if (item.receiveNum > 0) {
      status = 'received'
      statusText = '已领取'
    }

    // 格式化创建时间和更新时间
    const createTime = item.create_time
      ? new Date(item.create_time * 1000).toLocaleString()
      : '未知'
    const updateTime = item.update_time
      ? new Date(item.update_time * 1000).toLocaleString()
      : '未知'

    return {
      id: item.id,
      receiveCode: item.receiveCode || '未分配',
      title: item.name,
      author: item.mainEditor,
      isbn: item.isbn || '未知',
      teachingMaterialId: item.teachingMaterialId,
      inventoryId: item.inventoryId,
      receiveNum: item.receiveNum || 0,
      money: item.money || '0',
      leaveId: item.leaveId,
      adjustMoney: item.adjustMoney || '0',
      settlementMethod: item.settlementMethod || '未知',
      remark: item.remark || '无',
      createTime,
      updateTime,
      deltag: item.deltag,
      oprybh: item.oprybh || '未知',
      publisher: `${item.publishingHouse || '未知出版社'}`,
      publicationTime: item.publicationTime || '未知',
      printingTime: item.printingTime || '未知',
      studyYear: item.studyYear || '未知',
      studyTerm: item.studyTerm || '未知',
      leaveData: item.leaveData || '未知',
      cover: '', // 默认为空，在页面中判断显示图标或图片
      status,
      statusText,
      receiveTime: new Date().toLocaleString(), // 实际应使用接口返回的时间
      expectedTime: '2024-03-10',
      arriveTime: '2024-03-01',
    }
  })
})

// 获取状态对应的样式类
const getStatusClass = (status: string): string => {
  switch (status) {
    case 'received':
      return 'bg-success'
    case 'arrived':
      return 'bg-info'
    case 'pending':
      return 'bg-warning'
    default:
      return 'bg-primary'
  }
}

// 切换学期
const changeSemester = (id: number) => {
  activeSemester.value = id
  page.value = 1 // 重置页码
  fetchReceiveList() // 重新获取数据
}

// 处理学期变更
const handleSemesterChange = (semesterInfo: { label: string; value: string }) => {
  currentSemesterLabel.value = semesterInfo.label
  page.value = 1 // 重置页码
  fetchReceiveList() // 重新获取数据
}

// 组件挂载时只初始化，不立即请求数据
// 依赖学期选择器的初始化来触发第一次数据加载
onMounted(() => {
  // 页面加载时不主动请求数据，等待学期选择器初始化并触发变更事件
  console.log('组件已挂载，等待学期选择器初始化...')
})
</script>

<template>
  <view class="page-container">
    <!-- 通知公告 -->
    <!--  <view class="notice-panel">
      <view class="notice-title">
        <wd-icon name="warning" />
        通知
      </view>
      <view class="notice-content">
        2023-2024学年第二学期教材领取时间：2024年2月25日-3月10日，请各位同学及时到指定地点领取。
      </view>
    </view> -->

    <!-- 学期选择器 -->
    <view class="semester-picker-container">
      <SemesterWeekPicker
        v-model:semesterValue="selectedSemester"
        :show-week-label="false"
        :show-week="false"
        :show-all-week="false"
        :show-all-semester="true"
        size="large"
        @semesterChange="handleSemesterChange"
      />
    </view>

    <!-- 学生信息卡片 -->
    <view class="student-card">
      <view class="student-profile">
        <view class="avatar-container">
          <image
            class="student-avatar"
            :src="userInfo.avatar || '/static/images/default-avatar.png'"
            mode="aspectFill"
          />
        </view>
        <view class="student-info">
          <view class="student-name">{{ userInfo.realname }}</view>
          <view class="student-class">{{ userInfo.className }}</view>
          <view class="student-id">学号: {{ userInfo.username }}</view>
        </view>
      </view>
      <view class="stats-container">
        <view class="stat-item">
          <view class="stat-value">{{ total }}</view>
          <view class="stat-label">应领教材</view>
        </view>
        <view class="stat-item">
          <view class="stat-value text-success">{{ receivedBooksCount }}/{{ total }}</view>
          <view class="stat-label">已领教材</view>
        </view>
        <view class="stat-item">
          <view class="stat-value text-primary">{{ totalPurchasedBooks }}</view>
          <view class="stat-label">共购买</view>
        </view>
        <view class="stat-item">
          <view class="stat-value text-warning">￥{{ totalSpentAmount }}</view>
          <view class="stat-label">总花费</view>
        </view>
      </view>
    </view>

    <!-- 加载中提示 -->
    <view v-if="loading" class="loading-container">
      <wd-icon name="loading" class="loading-icon" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 空数据提示 -->
    <view v-else-if="formatBookList.length === 0" class="empty-container">
      <wd-icon name="info-circle" class="empty-icon" />
      <text class="empty-text">暂无教材数据</text>
    </view>

    <!-- 教材卡片列表 -->
    <view v-else class="book-list">
      <view v-for="book in formatBookList" :key="book.id" class="book-card">
        <!-- 卡片顶部 -->
        <view class="book-header">
          <!-- 判断是否有封面图片，没有则显示图标 -->
          <view v-if="!book.cover" class="book-cover-placeholder">
            <wd-icon name="books" size="40" />
          </view>
          <image v-else :src="book.cover" class="book-cover" mode="aspectFill" />
          <view class="book-info">
            <view class="book-title">{{ book.title }}</view>
            <view class="book-author">作者：{{ book.author }}</view>
            <view class="book-other-info">
              <text>领用编号：{{ book.receiveCode }}</text>
            </view>
            <!-- 添加学年学期信息 -->
            <view class="book-term-info">
              <text>{{ book.studyYear }} 第{{ book.studyTerm }}学期</text>
            </view>
          </view>
        </view>

        <!-- 卡片内容区 - 整合为单一区域 -->
        <view class="book-content">
          <!-- 状态信息 -->
          <view class="book-item book-status-item">
            <view class="book-label">领用状态:</view>
            <view class="book-value">
              <view :class="['status-badge', getStatusClass(book.status)]">
                {{ book.statusText }}
              </view>
            </view>
          </view>

          <!-- 两列布局信息区 -->
          <view class="book-grid">
            <view class="book-item">
              <view class="book-label">单价:</view>
              <view class="book-value">￥{{ book.money }}</view>
            </view>
            <view class="book-item">
              <view class="book-label">领用数量:</view>
              <view class="book-value">{{ book.receiveNum }} 本</view>
            </view>
            <view class="book-item">
              <view class="book-label">调整金额:</view>
              <view class="book-value">￥{{ book.adjustMoney }}</view>
            </view>
            <view class="book-item">
              <view class="book-label">结算方式:</view>
              <view class="book-value">{{ book.settlementMethod }}</view>
            </view>
            <!-- 移除了学年学期信息，已移到header部分 -->
            <view class="book-item">
              <view class="book-label">领取时间:</view>
              <view class="book-value">{{ book.leaveData }}</view>
            </view>
          </view>
        </view>

        <!-- 卡片底部操作区 -->
        <view class="book-actions">
          <view v-if="book.status === 'received'" class="action-btn">
            <wd-icon name="view" />
            查看收据
          </view>
          <view v-if="book.status === 'arrived'" class="action-btn action-primary">
            <wd-icon name="check" />
            前往领取
          </view>
          <view v-if="book.status === 'pending'" class="action-btn">
            <wd-icon name="notification" />
            到货提醒
          </view>
          <view class="action-btn">
            <wd-icon name="info-circle" />
            详细信息
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  min-height: 100vh;
  padding: 20rpx;
  background-color: #f5f5f5;
}

// 学期选择器容器样式
.semester-picker-container {
  margin-bottom: 20rpx;
}

:deep(.picker-container) {
  width: 440rpx;
}

:deep(.semester-select) {
  background-color: #fff !important;
}

// 通知公告样式
.notice-panel {
  padding: 24rpx;
  margin-bottom: 30rpx;
  background-color: #e6f7ff;
  border-left: 8rpx solid #1677ff;
  border-radius: 8rpx;
}

.notice-title {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  font-weight: 600;

  .wd-icon {
    margin-right: 10rpx;
    color: #1677ff;
  }
}

.notice-content {
  font-size: 26rpx;
  line-height: 1.4;
  color: #666;
}

// 学期选择Tab样式 - 保留但不使用
.semester-tabs {
  display: none; // 隐藏原有的学期选项卡
  flex-wrap: wrap;
  margin-bottom: 30rpx;
}

.tab-button {
  padding: 16rpx 30rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #fff;
  border: 2rpx solid #ddd;
  border-radius: 40rpx;
  transition: all 0.2s;

  &.active {
    color: white;
    background-color: #1677ff;
    border-color: #1677ff;
  }
}

// 加载中和空数据样式
.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.loading-icon,
.empty-icon {
  font-size: 80rpx;
  color: #999;
}

.loading-text,
.empty-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

.loading-icon {
  animation: rotate 1.5s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 学生信息卡片样式
.student-card {
  padding: 24rpx;
  margin-bottom: 24rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.student-profile {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.avatar-container {
  margin-right: 20rpx;
}

.student-avatar {
  width: 100rpx;
  height: 100rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.student-info {
  flex: 1;
}

.student-name {
  margin-bottom: 6rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.student-class,
.student-id {
  margin-bottom: 4rpx;
  font-size: 24rpx;
  line-height: 1.2;
  color: #999;
}

.stats-container {
  display: flex;
  flex-wrap: nowrap;
  margin: 0;
}

.stat-item {
  flex: 1;
  min-width: 0;
  padding: 12rpx 6rpx;
  margin: 0 4rpx;
  text-align: center;
  background-color: #f8f8f8;
  border-radius: 12rpx;
}

.stat-value {
  overflow: hidden;
  font-size: 28rpx;
  font-weight: 600;
  line-height: 1.2;
  text-overflow: ellipsis;
  white-space: nowrap;

  &.text-success {
    color: #4caf50;
  }

  &.text-primary {
    color: #1677ff;
  }

  &.text-warning {
    color: #ff9800;
  }
}

.stat-label {
  margin-top: 4rpx;
  overflow: hidden;
  font-size: 20rpx;
  color: #999;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 教材列表标题
.section-title {
  margin: 30rpx 0 20rpx;
  font-size: 32rpx;
  font-weight: 600;
}

// 教材卡片样式
.book-list {
  margin-bottom: 30rpx;
}

.book-card {
  margin-bottom: 30rpx;
  overflow: hidden;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.book-header {
  display: flex;
  padding: 30rpx;
  border-bottom: 2rpx solid #f2f2f2;
}

.book-cover {
  width: 160rpx;
  height: 200rpx;
  margin-right: 30rpx;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.book-info {
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: center;
}

.book-title {
  margin-bottom: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  line-height: 1.3;
}

.book-author {
  margin-bottom: 8rpx;
  font-size: 24rpx;
  color: #999;
}

.book-publisher {
  font-size: 24rpx;
  color: #999;
}

.book-other-info {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #999;

  .ml-20 {
    margin-left: 20rpx;
  }
}

.book-content {
  padding: 20rpx 30rpx;
}

.book-item {
  display: flex;
  font-size: 28rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.book-status-item {
  padding-bottom: 16rpx;
  margin-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.book-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -10rpx;

  .book-item {
    box-sizing: border-box;
    width: calc(50% - 20rpx);
    margin: 0 10rpx 16rpx;
  }
}

.book-label {
  width: 160rpx;
  color: #999;
}

.book-value {
  flex: 1;
  color: #333;
}

.status-badge {
  display: inline-block;
  padding: 4rpx 16rpx;
  font-size: 24rpx;
  color: white;
  border-radius: 24rpx;

  &.bg-success {
    background-color: #4caf50;
  }

  &.bg-info {
    background-color: #2196f3;
  }

  &.bg-warning {
    background-color: #ff9800;
  }

  &.bg-primary {
    background-color: #1677ff;
  }
}

.book-actions {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx 30rpx;
  border-top: 2rpx solid #f2f2f2;
}

.action-btn {
  display: flex;
  align-items: center;
  margin-left: 30rpx;
  font-size: 28rpx;
  color: #666;

  .wd-icon {
    margin-right: 6rpx;
  }

  &.action-primary {
    color: #4caf50;
  }
}

// 教材领取须知样式
.notice-card {
  margin-bottom: 40rpx;
  overflow: hidden;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.notice-card-header {
  padding: 24rpx 30rpx;
  font-size: 32rpx;
  font-weight: 600;
  border-bottom: 2rpx solid #f2f2f2;
}

.notice-card-content {
  padding: 20rpx 30rpx;
}

.notice-item {
  margin-bottom: 16rpx;
  font-size: 28rpx;
  line-height: 1.5;
  color: #666;

  &:last-child {
    margin-bottom: 0;
  }
}

// 添加封面占位符和学期信息的样式
.book-cover-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 200rpx;
  margin-right: 30rpx;
  color: #1677ff;
  background-color: #f0f5ff;
  border-radius: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.book-term-info {
  display: inline-block;
  padding: 4rpx 12rpx;
  margin-top: 8rpx;
  font-size: 22rpx;
  color: #1677ff;
  background-color: #f0f5ff;
  border-radius: 6rpx;
}
</style>
