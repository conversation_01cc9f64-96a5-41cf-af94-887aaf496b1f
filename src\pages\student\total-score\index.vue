<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的总评成绩',
  },
}
</route>
<template>
  <view class="bg-gray-100 p-4 pb-20 pt-2 box-border w-full" style="min-height: 97vh">
    <!-- 学期和课程筛选 -->
    <view class="sticky-top z-10 mb-4">
      <view class="flex items-center justify-between gap-3">
        <!-- 学期选择器 -->
        <view class="w-20">
          <SemesterWeekPicker
            v-model:semesterValue="selectedSemester"
            :show-week-label="false"
            :show-week="false"
            :show-all-week="false"
            :show-all-semester="false"
            size="large"
            @semesterChange="handleSemesterChange"
          />
        </view>

        <!-- 课程类型筛选 -->
        <view class="flex items-center max-w-40">
          <text class="text-sm font-medium mr-2 flex-shrink-0">筛选：</text>
          <view
            class="filter-tag bg-white rounded-lg px-3 py-1.5 shadow-sm flex items-center flex-1 min-w-0"
            @click="showCourseFilterSheet = true"
          >
            <text class="text-sm font-medium mr-2 truncate flex-1">{{ selectedCourseName }}</text>
            <wd-icon name="filter" class="text-xs text-gray-500 flex-shrink-0" />
          </view>
        </view>
      </view>
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="flex justify-center items-center py-10">
      <wd-loading color="#3b82f6" />
    </view>

    <!-- 错误提示 -->
    <view v-else-if="error" class="flex flex-col items-center justify-center py-10">
      <wd-icon name="error-circle-filled" size="36px" class="text-red-500 mb-3" />
      <text class="text-gray-600">{{ error }}</text>
      <view class="mt-4">
        <wd-button type="primary" size="small" @click="loadScoreData">重新加载</wd-button>
      </view>
    </view>

    <!-- 内容区域 -->
    <view v-else class="w-full">
      <!-- 无数据提示 -->
      <view v-if="courses.length === 0" class="flex flex-col items-center justify-center py-10">
        <wd-icon name="info-circle" size="36px" class="text-gray-400 mb-3" />
        <text class="text-gray-500">暂无成绩数据</text>
      </view>

      <template v-else>
        <!-- 学期概览 -->
        <view class="section-card mb-6 p-4 w-full box-border">
          <text class="text-lg font-bold mb-4 block truncate">{{ currentSemesterLabel }}总览</text>
          <!-- 第一行统计 -->
          <view class="grid grid-cols-4 gap-3 mb-4">
            <view v-for="(stat, index) in statsFirstRow" :key="index" class="text-center">
              <text class="text-xl font-bold block" :class="stat.color">{{ stat.value }}</text>
              <text class="text-xs text-gray-500 mt-1 block">{{ stat.label }}</text>
            </view>
          </view>
          <!-- 第二行统计 -->
          <view class="grid grid-cols-4 gap-3">
            <view v-for="(stat, index) in statsSecondRow" :key="index" class="text-center">
              <text class="text-xl font-bold block" :class="stat.color">{{ stat.value }}</text>
              <text class="text-xs text-gray-500 mt-1 block">{{ stat.label }}</text>
            </view>
          </view>
        </view>

        <!-- 课程总评列表 -->
        <view
          v-for="(course, index) in courses"
          :key="index"
          class="section-card mb-5 overflow-hidden w-full box-border"
        >
          <view class="p-4 flex items-start">
            <view class="flex flex-col items-center flex-shrink-0">
              <view
                class="score-indicator text-center"
                :class="getScoreClass(course.score).bg"
                :style="{ color: getScoreClass(course.score).text }"
              >
                <text class="block">{{ course.score }}</text>
              </view>
              <!-- 成绩级别显示 -->
              <text class="score-level" :style="{ color: getScoreClass(course.score).text }">
                {{ getScoreLevel(course.score) }}
              </text>
              <!-- 通过状态显示 -->
              <view
                class="pass-status"
                :class="
                  course.passed === 1 ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                "
              >
                {{ course.passed === 1 ? '通过' : '不通过' }}
              </view>
            </view>
            <view class="ml-4 flex-1 min-w-0">
              <text class="text-base font-bold mb-1 block truncate">
                <text
                  v-if="course.scoreProperty"
                  class="text-xs bg-blue-100 text-blue-600 px-2 py-0.5 rounded mr-1 align-middle"
                >
                  {{ course.scoreProperty }}
                </text>
                {{ course.name }}
              </text>
              <text class="text-xs text-gray-500 mb-2 block truncate">
                {{ course.teacher }} | {{ getDictLabel(kcxzDict, course.type) || course.type }} |
                {{ course.scoreType }} | {{ course.credit }}学分 |
                {{ course.gpa }}
                <!-- {{ course.publishDate }} -->
              </text>

              <!-- 只有当存在实际的成绩构成数据时才显示 -->
              <template v-if="course.hasRealComponents">
                <view class="flex items-center justify-between mb-2">
                  <text class="text-sm font-medium">成绩构成：</text>
                  <text class="text-xs text-blue-500">(数据来源：日常成绩)</text>
                </view>
                <view
                  v-for="(component, cIndex) in course.components"
                  :key="cIndex"
                  class="text-sm text-gray-600 flex items-center mb-2"
                >
                  <text class="w-28 flex-shrink-0 block truncate">
                    {{ component.name }}({{ component.weight }}%)
                  </text>
                  <view class="flex-1 mx-2 overflow-hidden">
                    <wd-progress
                      :percentage="component.score"
                      :stroke-width="6"
                      :color="getScoreClass(course.score).progressColor"
                      hide-text
                    />
                  </view>
                  <text class="w-10 flex-shrink-0 text-right block">{{ component.score }}分</text>
                </view>
              </template>
            </view>
          </view>
          <view class="p-3 bg-gray-50">
            <view class="flex flex-wrap justify-between items-center mb-2">
              <view class="text-sm text-gray-500 flex items-center">
                <wd-icon name="usergroup" class="mr-1 flex-shrink-0" />
                <text class="truncate">班级平均分: 暂无</text>
              </view>
              <view class="text-sm text-gray-500 flex items-center">
                <wd-icon name="star" class="mr-1 flex-shrink-0" />
                <text class="truncate">班级排名: {{ course.rank }}</text>
              </view>
            </view>
            <!-- 成绩提交时间 -->
            <view class="text-sm text-gray-500 flex items-center justify-between">
              <view class="flex items-center">
                <wd-icon name="a-rootlist" class="mr-1 flex-shrink-0" />
                <text class="truncate">{{ course.className }}</text>
              </view>
              <view class="flex items-center">
                <wd-icon name="time" class="mr-1 flex-shrink-0" />
                <text class="truncate">提交时间: {{ course.publishDate }}</text>
              </view>
            </view>
          </view>
        </view>
      </template>
    </view>

    <!-- 课程筛选弹窗 -->
    <wd-popup v-model="showCourseFilterSheet" position="bottom" round>
      <view class="semester-picker">
        <view class="picker-header">
          <text class="picker-title">选择课程</text>
          <wd-icon name="close" size="32rpx" @click="showCourseFilterSheet = false"></wd-icon>
        </view>
        <scroll-view class="picker-content" scroll-y>
          <view
            v-for="item in courseFilterOptions"
            :key="item.value"
            :class="[
              'picker-item',
              {
                active: selectedCourseId === item.value || (!selectedCourseId && !item.value),
              },
            ]"
            @click="selectCourse(item.value)"
          >
            <text class="item-label">{{ item.label }}</text>
            <wd-icon
              v-if="selectedCourseId === item.value || (!selectedCourseId && !item.value)"
              name="check"
              size="32rpx"
              color="#3a8eff"
            ></wd-icon>
          </view>
        </scroll-view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
/**
 * 总评成绩查看页面
 */
import { ref, computed, onMounted, watch } from 'vue'
import { getStudentTotalScore, getStudentTotalScoreStat } from '@/service/score'
import { getStudentScore } from '@/service/student'
import type { TotalScoreQuery, ScoreItem, TotalScoreStatResponse } from '@/types/score'
import type { ScoreQueryParams, ScoreResponse, ScoreItem as DailyScoreItem } from '@/types/student'
import SemesterWeekPicker from '@/components/SemesterWeekPicker/index.vue'
import type { SemesterOption } from '@/types/semester'
import { loadDictData, getDictLabel } from '@/utils/dict'
import type { DictData } from '@/types/system'

interface SemesterStat {
  label: string
  value: number | string
  color: string
}

interface ScoreComponent {
  name: string
  weight: number
  score: number
}

interface Course {
  id: number
  name: string
  score: number
  teacher: string
  type: string
  credit: number
  publishDate: string
  classAvg: number
  rank: string
  components: ScoreComponent[]
  /** 是否有实际的成绩组成数据 */
  hasRealComponents?: boolean
  /** 学年 */
  schoolYear: string
  /** 学期 */
  semester: string
  /** 成绩评定类型 */
  scoreType: string
  /** 成绩绩点 */
  gpa: string
  /** 成绩属性 */
  scoreProperty: string
  /** 是否通过 */
  passed: number
  /** 班级名称 */
  className: string
}

interface MakeupExam {
  courseName: string
  teacher: string
  type: string
  credit: number
  semester: string
  status: string
  originalScore: number
  makeupScore: number
  finalScore: number
  examDate: string
  remark: string
}

// 页面加载状态
const loading = ref(false)
const error = ref('')

// 学期选择
const selectedSemester = ref('')
const currentSemesterLabel = ref('')

// 课程筛选
const selectedCourseId = ref<number | string>('')
const selectedCourseName = ref('全部课程')
const showCourseFilterSheet = ref(false)

// 课程筛选选项 - 动态计算
const courseFilterOptions = computed(() => {
  const options = [{ label: '全部课程', value: '' }]

  // 从原始课程数据中提取不重复的课程
  if (originalCourses.value.length > 0) {
    const courseOptions = originalCourses.value.map((course) => ({
      label: course.name,
      value: course.id,
    }))

    return [...options, ...courseOptions]
  }

  return options
})

// 统计数据 - 第一行
const statsFirstRow = ref<SemesterStat[]>([
  { label: '共计门课程', value: '0', color: 'text-blue-600' },
  { label: '平均分', value: '0', color: 'text-green-600' },
  { label: '平均绩点', value: '0', color: 'text-purple-600' },
  { label: '获得学分', value: '0', color: 'text-orange-600' },
])

// 统计数据 - 第二行
const statsSecondRow = ref<SemesterStat[]>([
  { label: '优秀', value: '0', color: 'text-green-600' },
  { label: '良好', value: '0', color: 'text-blue-600' },
  { label: '中等', value: '0', color: 'text-yellow-600' },
  { label: '不及格', value: '0', color: 'text-red-600' },
])

// 课程列表数据
const courses = ref<Course[]>([])

// 原始课程数据
const originalCourses = ref<Course[]>([])

// 补考/重修记录
const makeupExams = ref<MakeupExam[]>([])

// 字典数据
const kcxzDict = ref<DictData[]>([])

// 成绩统计数据
const scoreStatData = ref<TotalScoreStatResponse['data'] | null>(null)

/**
 * 处理学期变更
 * @param semesterInfo 所选学期信息
 */
const handleSemesterChange = (semesterInfo: { label: string; value: string }) => {
  currentSemesterLabel.value = semesterInfo.label
  loadScoreData()
  // updateStatDisplay 会在 loadScoreStatData 中被调用
}

/**
 * 加载总评成绩数据
 */
const loadScoreData = async () => {
  loading.value = true
  error.value = ''

  try {
    const queryParams: TotalScoreQuery = {
      page: 1,
      pageSize: 50,
      // 只有当选择了特定学期（不是全部学期）时才传递学期参数
      semesters: selectedSemester.value
        ? [selectedSemester.value.replace(/^(\d{4}-\d{4})-(\d)$/, '$1|$2')]
        : undefined,
      kcmc: '',
      zdjsxm: '',
      kcxf: '',
      cjpdlx: '',
      cj: '',
      cjjd: '',
      cjsx: '',
      tgbz: '',
      cjtjsj: [],
    }

    const res = await getStudentTotalScore(queryParams)

    // 处理成绩数据并转换为页面需要的格式
    if (res && res.items) {
      // 转换为课程列表数据格式
      originalCourses.value = convertApiDataToCourses(res.items)

      // 加载日常成绩数据（使用then-catch链式调用而不是await）
      loadDailyScoreData(queryParams.semesters)
        .then(() => {
          // 加载日常成绩成功后应用筛选
          applyFilters()
        })
        .catch((err) => {
          console.error('加载日常成绩数据失败', err)
          // 即使日常成绩加载失败，也继续应用筛选
          applyFilters()
        })

      // 加载成绩统计数据
      loadScoreStatData()

      // 获取补考/重修记录
      getMakeupExams(res.items)
    } else {
      originalCourses.value = []
      courses.value = []
      makeupExams.value = []

      // 初始化统计数据
      resetStatData()
    }
  } catch (err) {
    console.error('加载总评成绩失败', err)
    error.value = '加载总评成绩失败，请稍后重试'
    // 清空数据
    originalCourses.value = []
    courses.value = []
    resetStatData()
  } finally {
    loading.value = false
  }
}

/**
 * 加载成绩统计数据
 */
const loadScoreStatData = async () => {
  try {
    const statData = await getStudentTotalScoreStat()
    console.log('成绩统计数据:', statData)
    scoreStatData.value = statData
    // 更新统计数据显示
    updateStatDisplay()
  } catch (error) {
    console.error('加载成绩统计数据失败', error)
    resetStatData()
  }
}

/**
 * 更新统计数据显示
 */
const updateStatDisplay = () => {
  if (!scoreStatData.value) {
    resetStatData()
    return
  }

  const data = scoreStatData.value

  // 如果有选定的学期，则显示该学期的统计数据
  if (selectedSemester.value) {
    // 将选定的学期格式转换为 API 返回的格式 (例如: 2023-2024-1 -> 2023-2024学年第1学期)
    const year = selectedSemester.value.split('-').slice(0, 2).join('-')
    const term = selectedSemester.value.split('-')[2]
    const semesterKey = `${year}学年第${term}学期`

    // 从 pageData 中获取对应学期的统计数据
    const semesterData = data.pageData[semesterKey]

    if (semesterData) {
      console.log('找到对应学期统计数据:', semesterKey, semesterData)
      // 更新第一行统计数据
      statsFirstRow.value = [
        { label: '共计门课程', value: semesterData.rws.toString(), color: 'text-blue-600' },
        { label: '平均分', value: semesterData.pjf, color: 'text-green-600' },
        { label: '平均绩点', value: semesterData.pjjd, color: 'text-purple-600' },
        { label: '获得学分', value: semesterData.xf.toString(), color: 'text-orange-600' },
      ]

      // 更新第二行统计数据
      statsSecondRow.value = [
        { label: '优秀', value: semesterData.yxs.toString(), color: 'text-green-600' },
        { label: '良好', value: semesterData.lhs.toString(), color: 'text-blue-600' },
        { label: '中等', value: semesterData.zds.toString(), color: 'text-yellow-600' },
        { label: '不及格', value: semesterData.bjgs.toString(), color: 'text-red-600' },
      ]
      return
    } else {
      console.warn('未找到对应学期统计数据:', semesterKey)
    }
  }

  // 如果没有选定的学期或者没有找到对应学期的数据，则显示总体统计数据
  statsFirstRow.value = [
    { label: '共计门课程', value: data.rws.toString(), color: 'text-blue-600' },
    { label: '平均分', value: data.pjf, color: 'text-green-600' },
    { label: '平均绩点', value: data.pjjd, color: 'text-purple-600' },
    { label: '获得学分', value: data.xf.toString(), color: 'text-orange-600' },
  ]

  statsSecondRow.value = [
    { label: '优秀', value: data.yxs.toString(), color: 'text-green-600' },
    { label: '良好', value: data.lhs.toString(), color: 'text-blue-600' },
    { label: '中等', value: data.zds.toString(), color: 'text-yellow-600' },
    { label: '不及格', value: data.bjgs.toString(), color: 'text-red-600' },
  ]
}

/**
 * 重置统计数据
 */
const resetStatData = () => {
  statsFirstRow.value = [
    { label: '共计门课程', value: '0', color: 'text-blue-600' },
    { label: '平均分', value: '0', color: 'text-green-600' },
    { label: '平均绩点', value: '0', color: 'text-purple-600' },
    { label: '获得学分', value: '0', color: 'text-orange-600' },
  ]
  statsSecondRow.value = [
    { label: '优秀', value: '0', color: 'text-green-600' },
    { label: '良好', value: '0', color: 'text-blue-600' },
    { label: '中等', value: '0', color: 'text-yellow-600' },
    { label: '不及格', value: '0', color: 'text-red-600' },
  ]
}

/**
 * 加载日常成绩数据
 * @param semesters 学期参数
 */
const loadDailyScoreData = async (semesters?: string[]) => {
  try {
    // 如果没有课程数据，则无需加载日常成绩
    if (originalCourses.value.length === 0) return

    const params: ScoreQueryParams = {
      page: 1,
      pageSize: 200, // 增加页面大小以获取更多日常成绩项
      semesters: semesters || [],
    }

    // 获取日常成绩数据
    const res = await getStudentScore(params)

    if (res && res.items && res.items.length > 0) {
      console.log(`成功获取${res.items.length}条日常成绩数据`)
      // 合并日常成绩数据到对应的总评成绩课程中
      mergeDailyScoresToCourses(res.items)
    } else {
      console.warn('日常成绩数据为空')
    }
  } catch (error) {
    console.error('加载日常成绩失败', error)
    // 日常成绩加载失败不影响总评成绩的显示
  }
}

/**
 * 合并日常成绩数据到课程中
 * @param dailyScores 日常成绩数据
 */
const mergeDailyScoresToCourses = (dailyScores: DailyScoreItem[]) => {
  // 1. 按课程代码和学期分组日常成绩，形成映射
  const scoresByCodeAndSemester = new Map<string, DailyScoreItem[]>()

  dailyScores.forEach((item) => {
    // 使用课程代码和学期作为唯一标识
    const key = `${item.kcdm}-${item.semesters}`
    if (!scoresByCodeAndSemester.has(key)) {
      scoresByCodeAndSemester.set(key, [])
    }
    scoresByCodeAndSemester.get(key)!.push(item)
  })

  // 2. 遍历现有课程，合并日常成绩
  originalCourses.value.forEach((course) => {
    // 为每个课程从总评成绩API提取课程代码
    // 由于原始API返回中没有courseCode字段，我们需要通过courseName匹配
    const matchingDailyScores = dailyScores.filter((item) => item.kcmc === course.name)

    if (matchingDailyScores.length > 0) {
      // 获取第一个匹配的课程的代码和学期
      const courseCode = matchingDailyScores[0].kcdm
      const semesters = matchingDailyScores[0].semesters

      // 获取该课程的所有日常成绩
      const allCourseDailyScores = scoresByCodeAndSemester.get(`${courseCode}-${semesters}`) || []

      // 清空原有的成绩组成
      course.components = []

      // 3. 按照成绩类型分类
      const scoreTypes = new Map<string, DailyScoreItem[]>()
      allCourseDailyScores.forEach((item) => {
        // 忽略"总成绩"或"综合"类型，因为这些通常是总评而非具体组成部分
        if (item.rccjlxmc.includes('总') || item.rccjlxmc.includes('综合')) {
          return
        }

        if (!scoreTypes.has(item.rccjlxdm)) {
          scoreTypes.set(item.rccjlxdm, [])
        }
        scoreTypes.get(item.rccjlxdm)!.push(item)
      })

      // 4. 计算总权重并分配
      const totalTypes = scoreTypes.size
      // 如果找到日常成绩项，则均分权重；如果没有，则保持默认配置
      const weightPerType = totalTypes > 0 ? Math.floor(100 / totalTypes) : 0
      const remainingWeight = 100 - weightPerType * totalTypes

      // 5. 添加各类型成绩到components
      const components: ScoreComponent[] = []

      scoreTypes.forEach((items, typeCode) => {
        // 找到代表性的项目（取第一个）
        const representative = items[0]

        // 计算该类型的平均分
        const typeAvgScore = items.reduce((sum, item) => sum + item.bfzcj, 0) / items.length

        // 添加额外信息
        const examInfo = representative.rccjkssj ? ` (${representative.rccjkssj})` : ''
        const typeName = representative.rccjlxmc + examInfo

        // 计算当前类型权重（最后一项可能需要加上余数）
        let currentWeight = weightPerType
        if (components.length === totalTypes - 1 && remainingWeight > 0) {
          currentWeight += remainingWeight
        }

        components.push({
          name: typeName,
          weight: currentWeight,
          score: Math.round(typeAvgScore * 10) / 10, // 保留一位小数
        })
      })

      // 按权重降序排序
      components.sort((a, b) => b.weight - a.weight)
      course.components = components

      // 标记成绩组成来源于实际数据
      course.hasRealComponents = course.components.length > 0
    }
  })
}

/**
 * 将API返回的数据转换为页面课程格式
 */
const convertApiDataToCourses = (items: ScoreItem[]): Course[] => {
  return items.map((item) => ({
    id: item.id,
    name: item.kcmc,
    score: item.bfzcj,
    teacher: item.zdjsxm,
    type: item.kcxz,
    credit: item.kcxf,
    publishDate: item.cjtjsj.split(' ')[0], // 取日期部分
    classAvg: 0, // 暂时设为0，但实际展示时显示为"暂无"
    rank: '暂无', // 排名数据
    components: [],
    hasRealComponents: false,
    schoolYear: item.xn,
    semester: item.xq,
    scoreType: item.cjpdlx,
    gpa: item.cjjd,
    scoreProperty: item.cjsx,
    passed: item.tgbz,
    className: item.ssbjmc || '未知班级', // 添加班级名称
  }))
}

/**
 * 应用当前筛选条件
 */
const applyFilters = () => {
  let filteredCourses = [...originalCourses.value]

  // 根据选择的课程进行筛选
  if (selectedCourseId.value) {
    filteredCourses = filteredCourses.filter((course) => course.id === selectedCourseId.value)
  }

  courses.value = filteredCourses
}

/**
 * 计算学期统计数据
 */
const calculateSemesterStats = (items: ScoreItem[]) => {
  // 此方法不再需要，统计数据直接从API获取
}

/**
 * 计算成绩等级比例
 */
const calculateScoreRates = (items: ScoreItem[]) => {
  // 此方法不再需要，统计数据直接从API获取
}

/**
 * 获取补考/重修记录
 */
const getMakeupExams = (items: ScoreItem[]) => {
  // 从API数据中提取补考/重修记录
  // 这里假设成绩性质为"补考"或"重修"的记录为补考/重修记录
  const makeupRecords = items.filter(
    (item) => item.cjsx.includes('补考') || item.cjsx.includes('重修'),
  )

  // 如果有补考/重修记录，转换为页面需要的格式
  if (makeupRecords.length > 0) {
    makeupExams.value = makeupRecords.map((item) => ({
      courseName: item.kcmc,
      teacher: item.zdjsxm,
      type: item.kcxz,
      credit: item.kcxf,
      semester: `${item.xn}${item.xq}`,
      status: item.cjsx,
      originalScore: 0, // API中可能没有原始分数，需要后端提供
      makeupScore: item.bfzcj,
      finalScore: item.bfzcj,
      examDate: item.cjtjsj.split(' ')[0], // 取日期部分
      remark: '根据学校规定，补考通过成绩按60分计入总成绩', // 默认备注
    }))
  } else {
    makeupExams.value = []
  }
}

/**
 * 根据分数获取对应的样式类
 * @param score 分数
 * @returns 样式类对象
 */
const getScoreClass = (score: number) => {
  if (score >= 90) {
    return {
      bg: 'bg-green-100',
      text: '#10b981',
      progress: 'bg-green-500',
      progressColor: '#10b981',
    }
  } else if (score >= 80) {
    return {
      bg: 'bg-blue-100',
      text: '#3b82f6',
      progress: 'bg-blue-500',
      progressColor: '#3b82f6',
    }
  } else if (score >= 60) {
    return {
      bg: 'bg-yellow-100',
      text: '#f59e0b',
      progress: 'bg-yellow-500',
      progressColor: '#f59e0b',
    }
  } else {
    return {
      bg: 'bg-red-100',
      text: '#ef4444',
      progress: 'bg-red-500',
      progressColor: '#ef4444',
    }
  }
}

/**
 * 根据分数获取成绩级别
 * @param score 分数
 * @returns 成绩级别
 */
const getScoreLevel = (score: number) => {
  if (score >= 90) {
    return '优秀(90-100)'
  } else if (score >= 80) {
    return '良好(80-89)'
  } else if (score >= 70) {
    return '中等(70-79)'
  } else if (score >= 60) {
    return '及格(60-69)'
  } else {
    return '不及格(<60)'
  }
}

/**
 * 选择课程
 * @param courseId 课程ID
 */
const selectCourse = (courseId: number | string) => {
  if (!courseId) {
    selectedCourseId.value = ''
    selectedCourseName.value = '全部课程'
  } else {
    // 找到选择的课程
    const course = originalCourses.value.find((c) => c.id === courseId)

    if (course) {
      selectedCourseId.value = course.id
      selectedCourseName.value = course.name
    } else {
      uni.showToast({
        title: '未找到对应课程',
        icon: 'none',
      })
      selectedCourseId.value = ''
      selectedCourseName.value = '全部课程'
    }
  }
  showCourseFilterSheet.value = false
  applyFilters()
}

// 监听课程筛选条件变化
watch([selectedCourseId], () => {
  applyFilters()
})

// 监听学期变化
watch(selectedSemester, (newVal) => {
  if (newVal) {
    loadScoreData()
  }
})

/**
 * 加载字典数据
 */
const loadDictionaries = () => {
  loadDictData(['DM_KCXZ'])
    .then((dicts) => {
      kcxzDict.value = dicts.DM_KCXZ || []
    })
    .catch((error) => {
      console.error('加载字典数据失败', error)
    })
}

// 页面加载时的初始化
onMounted(() => {
  // 加载字典数据
  loadDictionaries()
  // 加载成绩统计数据
  loadScoreStatData()
  // 不需要主动调用loadScoreData
  // 因为当SemesterWeekPicker组件初始化时，会自动设置selectedSemester值
  // 然后通过watch触发数据加载
})
</script>

<style>
:deep(.picker-container) {
  width: 440rpx;
}
:deep(.semester-select) {
  background-color: #fff !important;
}
.sticky-top {
  /* position: sticky; */
  top: 0;
  z-index: 10;
  width: 100%;
  padding: 12rpx 0;
}

.section-card {
  background-color: #ffffff;
  border-radius: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.score-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 160rpx;
  height: 160rpx;
  font-size: 44rpx;
  font-weight: bold;
  border-radius: 80rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}
/* 成绩级别和通过状态样式 */
.score-level {
  margin-top: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
  text-align: center;
}

.pass-status {
  display: inline-block;
  width: 100rpx;
  padding: 4rpx 16rpx;
  margin-top: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  text-align: center;
  border-radius: 20rpx;
}

.filter-tag {
  transition: all 0.3s;
}

.filter-tag:active {
  opacity: 0.8;
  transform: scale(0.98);
}

.border-bottom {
  border-bottom: 1px solid #f0f0f0;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.break-words {
  word-break: break-word;
  word-wrap: break-word;
}
/* 选择器弹窗样式 */
.semester-picker {
  max-height: 70vh;
  padding: 32rpx;
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
}

.picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}

.picker-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.picker-content {
  max-height: calc(70vh - 100rpx);
}

.picker-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background: #f7f8fa;
  border-radius: 12rpx;
  transition: all 0.3s;
}

.picker-item:last-child {
  margin-bottom: 0;
}

.picker-item.active {
  color: #3a8eff;
  background: rgba(58, 142, 255, 0.1);
}

.item-label {
  font-size: 28rpx;
}

.item-seasonal {
  margin-left: 16rpx;
  font-size: 24rpx;
  color: #666;
}

.picker-item.active .item-seasonal {
  color: #3a8eff;
}
</style>
