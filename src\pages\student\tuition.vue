<route lang="json5">
{
  style: {
    navigationBarTitleText: '学费信息',
  },
}
</route>

<template>
  <view class="px-5 pt-4 pb-16">
    <!-- 学费总览 -->
    <view class="bg-white rounded-2xl p-4 mb-4">
      <view class="text-base font-bold mb-3">学费缴纳概览</view>
      <view class="grid grid-cols-3 gap-3">
        <view class="bg-blue-50 p-3 rounded-xl">
          <view class="text-xs text-gray-500">总学费</view>
          <view class="text-xl font-bold text-blue-600">¥35,200</view>
          <view class="text-xs text-gray-500">4年总计</view>
        </view>
        <view class="bg-green-50 p-3 rounded-xl">
          <view class="text-xs text-gray-500">已缴金额</view>
          <view class="text-xl font-bold text-green-600">¥26,400</view>
          <view class="text-xs text-gray-500">已缴3年</view>
        </view>
        <view class="bg-red-50 p-3 rounded-xl">
          <view class="text-xs text-gray-500">待缴金额</view>
          <view class="text-xl font-bold text-red-600">¥8,800</view>
          <view class="text-xs text-gray-500">待缴1年</view>
        </view>
      </view>
    </view>

    <!-- 当前学期缴费 -->
    <view class="bg-white rounded-2xl p-4 mb-4">
      <view class="text-base font-bold mb-3">2023-2024学年缴费</view>
      <view class="bg-gray-50 p-4 rounded-xl mb-4">
        <view class="flex justify-between items-center mb-2">
          <view class="font-medium">2023-2024学年学费</view>
          <view
            class="inline-block px-2.5 py-1 text-xs font-medium rounded-3xl bg-red-100 text-red-800"
          >
            待缴费
          </view>
        </view>
        <view class="text-sm text-gray-500 mb-3">截止日期：2024年8月30日</view>
        <view class="grid grid-cols-2 gap-2 text-sm mb-4">
          <view>
            <view class="text-gray-500">学费</view>
            <view class="font-medium">¥8,800</view>
          </view>
          <view>
            <view class="text-gray-500">住宿费</view>
            <view class="font-medium">¥1,200</view>
          </view>
          <view>
            <view class="text-gray-500">书本费</view>
            <view class="font-medium">¥600</view>
          </view>
          <view>
            <view class="text-gray-500">保险费</view>
            <view class="font-medium">¥120</view>
          </view>
        </view>
        <view class="flex justify-between items-center text-sm">
          <view>
            <text class="text-gray-500">总计：</text>
            <text class="font-bold text-red-500">¥10,720</text>
          </view>
          <wd-button type="primary" round size="small" class="px-4 py-2">立即缴费</wd-button>
        </view>
      </view>

      <view class="text-sm text-gray-500 mb-2">缴费方式</view>
      <view class="space-y-3">
        <view class="flex items-start">
          <view class="text-blue-500 mr-2 mt-1">
            <wd-icon name="creditcard" size="18px" />
          </view>
          <view class="text-sm">
            <view class="font-medium">银行汇款</view>
            <view class="text-gray-500">开户银行：中国建设银行</view>
            <view class="text-gray-500">账户名称：广东省高新技术职业学院</view>
            <view class="text-gray-500">账号：6217 0012 3456 7890</view>
          </view>
        </view>
        <view class="flex items-start">
          <view class="text-blue-500 mr-2 mt-1">
            <wd-icon name="mobile" size="18px" />
          </view>
          <view class="text-sm">
            <view class="font-medium">线上支付</view>
            <view class="text-gray-500">支持支付宝、微信支付等方式</view>
            <view class="text-gray-500">请使用学校官方APP或扫描下方二维码</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 缴费记录 -->
    <view class="bg-white rounded-2xl p-4 mb-4">
      <view class="text-base font-bold mb-3">缴费记录</view>
      <view class="space-y-3">
        <view
          v-for="(item, index) in paymentRecords"
          :key="index"
          class="relative rounded-3xl overflow-hidden bg-gray-50 p-4"
        >
          <view class="absolute left-0 top-0 w-1.5 h-full bg-green-500"></view>
          <view class="ml-2">
            <view class="flex justify-between items-center mb-1">
              <view class="font-medium">{{ item.title }}</view>
              <view
                class="inline-block px-2.5 py-1 text-xs font-medium rounded-3xl bg-green-100 text-green-800"
              >
                已缴费
              </view>
            </view>
            <view class="text-sm text-gray-500 mb-2">缴费日期：{{ item.date }}</view>
            <view class="flex justify-between items-center text-sm">
              <view>
                <text class="text-gray-500">总计：</text>
                <text class="font-medium">{{ item.amount }}</text>
              </view>
              <wd-button type="primary" plain size="mini" round class="text-xs py-1 px-3">
                查看详情
              </wd-button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 助学金情况 -->
    <view class="bg-white rounded-2xl p-4">
      <view class="text-base font-bold mb-3">奖助学金情况</view>
      <view class="space-y-3">
        <view
          v-for="(item, index) in scholarships"
          :key="index"
          class="flex items-center justify-between"
          :class="{ 'border-b border-gray-100 pb-3': index < scholarships.length - 1 }"
        >
          <view class="flex items-center">
            <view
              class="w-10 h-10 rounded-full flex items-center justify-center mr-3"
              :class="item.iconBg"
            >
              <wd-icon :name="item.icon" :color="item.iconColor" size="20px" />
            </view>
            <view>
              <view class="font-medium">{{ item.title }}</view>
              <view class="text-xs text-gray-500">{{ item.year }}</view>
            </view>
          </view>
          <view class="text-lg font-bold" :class="item.amountColor">{{ item.amount }}</view>
        </view>
      </view>
      <wd-button type="primary" plain block round class="mt-4">申请奖助学金</wd-button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 定义类型
interface PaymentRecord {
  title: string
  date: string
  amount: string
}

interface Scholarship {
  title: string
  year: string
  amount: string
  icon: string
  iconBg: string
  iconColor: string
  amountColor: string
}

// 缴费记录数据
const paymentRecords = ref<PaymentRecord[]>([
  {
    title: '2022-2023学年学费',
    date: '2022年8月15日',
    amount: '¥10,720',
  },
  {
    title: '2021-2022学年学费',
    date: '2021年8月20日',
    amount: '¥10,720',
  },
  {
    title: '2020-2021学年学费',
    date: '2020年9月1日',
    amount: '¥10,720',
  },
])

// 奖助学金数据
const scholarships = ref<Scholarship[]>([
  {
    title: '国家励志奖学金',
    year: '2022-2023学年',
    amount: '¥5,000',
    icon: 'star-on',
    iconBg: 'bg-blue-100',
    iconColor: '#3B82F6',
    amountColor: 'text-blue-500',
  },
  {
    title: '国家助学金',
    year: '2021-2022学年',
    amount: '¥3,000',
    icon: 'money-circle',
    iconBg: 'bg-green-100',
    iconColor: '#10B981',
    amountColor: 'text-green-500',
  },
  {
    title: '三好学生奖学金',
    year: '2021-2022学年',
    amount: '¥2,000',
    icon: 'star',
    iconBg: 'bg-purple-100',
    iconColor: '#8B5CF6',
    amountColor: 'text-purple-500',
  },
])
</script>
