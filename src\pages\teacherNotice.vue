<route lang="json5">
{
  style: {
    navigationBarTitleText: '通知公告',
  },
}
</route>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import type { NoticeItem, NoticeCategory } from '@/types/notice'
import { getNoticeList } from '@/service/notice'
import Pagination from '@/components/Pagination/index.vue'

// 当前选中的分类
const activeCategory = ref<NoticeCategory>('all')

// 通知列表数据
const noticeList = ref<NoticeItem[]>([])
const total = ref(0) // 当前筛选条件下的通知总数
const totalAll = ref(0) // 所有通知的总数，不受筛选影响

// 计算当前页面显示的未读通知数量
const unreadCount = computed(() => {
  return noticeList.value.filter((notice) => notice.isUnread).length
})

// 分页参数
const page = ref(1)
const pageSize = ref(10)

// 查看通知详情
const viewNoticeDetail = (notice: NoticeItem) => {
  // 将通知数据存储到localStorage
  uni.setStorageSync('NOTICE_DETAIL_DATA', JSON.stringify(notice))

  // 跳转到通知详情页面
  uni.navigateTo({
    url: `/pages/noticeDetail?id=${notice.id}`,
  })
}

// 标记所有通知为已读
const markAllAsRead = () => {
  uni.showToast({
    title: '已全部标为已读',
    icon: 'success',
  })
  noticeList.value = noticeList.value.map((item) => ({
    ...item,
    isUnread: false,
  }))
}

// 根据通知类型获取图标名称
const getIconName = (type: string) => {
  switch (type) {
    case '教学通知':
      return 'warning'
    case '学工通知':
      return 'setting'
    case '活动通知':
      return 'calendar'
    case '就业通知':
      return 'user'
    case '资源通知':
      return 'document'
    case '医疗通知':
      return 'star'
    case '通知公告':
      return 'info'
    default:
      return 'info'
  }
}

// 根据通知类型获取图标底色
const getIconBgColor = (type: string) => {
  switch (type) {
    case '教学通知':
      return '#e0f2fe' // 浅蓝色背景
    case '学工通知':
      return '#e0e7ff' // 浅靛蓝色背景
    case '活动通知':
      return '#dcfce7' // 浅绿色背景
    case '就业通知':
      return '#f3e8ff' // 浅紫色背景
    case '资源通知':
      return '#fef9c3' // 浅黄色背景
    case '医疗通知':
      return '#d1fae5' // 浅绿色背景
    case '通知公告':
      return '#e2e8f0' // 浅灰色背景
    default:
      return '#e2e8f0' // 浅灰色背景
  }
}

// 根据通知类型获取图标颜色
const getIconColor = (type: string) => {
  switch (type) {
    case '教学通知':
      return '#0284c7' // 深蓝色
    case '学工通知':
      return '#4f46e5' // 深靛蓝色
    case '活动通知':
      return '#16a34a' // 深绿色
    case '就业通知':
      return '#9333ea' // 深紫色
    case '资源通知':
      return '#ca8a04' // 黄色
    case '医疗通知':
      return '#10b981' // 绿色
    case '通知公告':
      return '#64748b' // 深灰色
    default:
      return '#64748b' // 深灰色
  }
}

// 根据通知类型获取分类颜色
const getCategoryColor = (type: string) => {
  switch (type) {
    case '教学通知':
      return 'blue'
    case '学工通知':
      return 'indigo'
    case '活动通知':
      return 'green'
    case '就业通知':
      return 'purple'
    case '资源通知':
      return 'yellow'
    case '医疗通知':
      return 'green'
    case '通知公告':
      return 'gray'
    default:
      return 'gray'
  }
}

// 获取部门名称的前两个字作为头像
const getDepartmentInitials = (department: string) => {
  if (!department) return '未知'
  return department.substring(0, 2)
}

// 构建API请求参数
const getRequestParams = () => {
  const params = {
    page: page.value,
    pageSize: pageSize.value,
  }

  // 根据分类筛选
  if (activeCategory.value === 'teaching') {
    return {
      ...params,
      publisherDeptName: '教务处',
    }
  } else if (activeCategory.value === 'student') {
    return {
      ...params,
      publisherDeptName: '学生工作部',
    }
  }

  return params
}

// 获取通知列表
const getList = async () => {
  try {
    const params = getRequestParams()
    const res = await getNoticeList(params)

    noticeList.value = res.items.map((item) => ({
      id: item.id,
      title: item.title,
      content: item.content,
      department: item.publisherDeptName,
      publishTime: item.publishTime,
      type: item.columnName,
      isImportant: false, // 添加默认值以匹配类型
      isUnread: item.readCount === 0,
      icon: getIconName(item.columnName),
      iconBgColor: getIconBgColor(item.columnName),
      iconColor: getIconColor(item.columnName),
      category: item.columnName,
      categoryColor: getCategoryColor(item.columnName),
    }))
    total.value = res.total
  } catch (error) {
    console.error('获取通知列表失败:', error)
  }
}

// 获取所有通知总数（不受筛选影响）
const getAllTotal = async () => {
  try {
    const res = await getNoticeList({
      page: 1,
      pageSize: 1,
    })
    totalAll.value = res.total
  } catch (error) {
    console.error('获取通知总数失败:', error)
  }
}

// 监听分类变化
watch(activeCategory, () => {
  page.value = 1 // 切换分类时重置到第一页
  getList()
})

// 监听页码变化
watch(page, () => {
  getList()
})

onMounted(() => {
  // 获取所有数据
  Promise.all([getAllTotal(), getList()]).catch((error) => {
    console.error('初始化数据失败:', error)
  })
})

// 获取分类颜色类名
const getCategoryColorClass = (color: string) => {
  return `bg-${color}-50 text-${color}-600`
}

// 处理分页变化
const handlePageChange = (newPage: number) => {
  page.value = newPage
}
</script>

<template>
  <view class="notice-container">
    <!-- 通知统计卡片区域 -->
    <view class="stats-cards-container mb-3">
      <!-- 未读通知卡片 -->
      <view class="stats-card">
        <view class="stats-card-content">
          <view class="flex flex-col items-center justify-center p-2">
            <view class="stats-icon bg-blue-50 mb-1">
              <wd-icon name="notification" color="#3b82f6" size="32rpx" />
            </view>
            <view class="text-lg font-bold text-blue-500 leading-none">{{ unreadCount }}</view>
            <view class="text-xxs text-gray-500 mt-1">未读通知</view>
          </view>
        </view>
      </view>

      <!-- 全部通知卡片 -->
      <view class="stats-card">
        <view class="stats-card-content">
          <view class="flex flex-col items-center justify-center p-2">
            <view class="stats-icon bg-green-50 mb-1">
              <wd-icon name="list" color="#10b981" size="32rpx" />
            </view>
            <view class="text-lg font-bold text-green-600 leading-none">{{ totalAll }}</view>
            <view class="text-xxs text-gray-500 mt-1">全部通知</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 选项卡 -->
    <view class="category-scroll-container mb-4">
      <scroll-view scroll-x class="category-scroll" :show-scrollbar="false">
        <view class="category-tabs">
          <view
            v-for="category in ['all', 'teaching', 'student']"
            :key="category"
            class="category-tab"
            :class="[activeCategory === category ? 'active-category' : '']"
            @click="activeCategory = category as NoticeCategory"
          >
            {{
              category === 'all' ? '全部通知' : category === 'teaching' ? '教学通知' : '学工通知'
            }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 通知列表 -->
    <view class="notice-list">
      <view
        v-for="notice in noticeList"
        :key="notice.id"
        class="notice-item p-3 clickable"
        @click="viewNoticeDetail(notice)"
      >
        <view class="flex">
          <view class="notice-icon" :style="{ backgroundColor: notice.iconBgColor }">
            <text class="department-initials" :style="{ color: notice.iconColor }">
              {{ getDepartmentInitials(notice.department) }}
            </text>
          </view>
          <view class="flex-1 overflow-hidden">
            <view class="notice-title">
              <text class="title-text" :class="{ 'unread-title': notice.isUnread }">
                {{ notice.title }}
              </text>
              <view v-if="notice.isImportant" class="importance-tag">重要</view>
            </view>
            <view class="notice-footer">
              <text class="notice-info">{{ notice.department }} · {{ notice.publishTime }}</text>
              <text :class="['read-status-tag', notice.isUnread ? 'unread-tag' : 'read-tag']">
                {{ notice.isUnread ? '未读' : '已读' }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 空状态显示 -->
      <view v-if="noticeList.length === 0" class="empty-state">
        <wd-icon name="info-circle" size="64rpx" color="#d1d5db"></wd-icon>
        <text class="empty-text">暂无通知</text>
      </view>
    </view>

    <!-- 分页 -->
    <Pagination
      v-if="total > 0"
      v-model:page="page"
      :total="total"
      :page-size="pageSize"
      @update:page="handlePageChange"
    />
  </view>
</template>

<style scoped lang="scss">
.notice-container {
  box-sizing: border-box;
  min-height: 100vh;
  padding: 32rpx;
  background-color: #f7f8fc;
}

// 统计卡片区域
.stats-cards-container {
  display: flex;
  gap: 16rpx;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.stats-card {
  flex: 1;
  overflow: hidden;
  background: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

  .stats-card-content {
    padding: 0;
    background: #ffffff;
  }
}

.stats-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  border-radius: 24rpx;
}

// 添加新的边距类
.mb-1 {
  margin-bottom: 6rpx;
}

.mt-1 {
  margin-top: 6rpx;
}

// 添加行高控制
.leading-none {
  line-height: 1;
}

// 分类选择器容器
.category-scroll-container {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-bottom: 32rpx;
}

// 分类选项卡
.category-scroll {
  width: 100%;
  text-align: center;
  white-space: nowrap;
}

.category-tabs {
  display: inline-flex;
  justify-content: center;
}

.category-tab {
  position: relative;
  padding: 16rpx 32rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #6b7280;
  letter-spacing: 2rpx;
  white-space: nowrap;
  transition: all 0.3s;

  &.active-category {
    font-weight: 600;
    color: #3b82f6;

    &::after {
      position: absolute;
      bottom: 0;
      left: 50%;
      width: 40rpx;
      height: 4rpx;
      content: '';
      background-color: #3b82f6;
      border-radius: 2rpx;
      transform: translateX(-50%);
    }
  }

  &:active {
    opacity: 0.8;
  }
}

// 通知列表
.notice-list {
  overflow: hidden;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

.notice-item {
  position: relative;
  padding: 32rpx 24rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s;

  &:hover,
  &:active {
    background-color: rgba(0, 0, 0, 0.02);
  }

  &:last-child {
    border-bottom: none;
  }
}

.notice-icon {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  margin-right: 16rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.06);
}

.department-initials {
  font-size: 28rpx;
  font-weight: 500;
}

.notice-title {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8rpx;
  line-height: 1.4;
}

.title-text {
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1.5;
  color: #333;
  word-break: break-all;
}

.unread-title {
  font-weight: 600;
  color: #000;
}

.importance-tag {
  display: inline-block;
  flex-shrink: 0;
  height: 32rpx;
  padding: 0 10rpx;
  margin-left: 12rpx;
  font-size: 20rpx;
  line-height: 32rpx;
  color: #fff;
  background-color: #ef4444;
  border-radius: 4rpx;
  transform: translateY(2rpx);
}

.notice-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 12rpx;
}

.notice-info {
  font-size: 22rpx;
  color: #9ca3af;
}

.read-status-tag {
  padding: 2rpx 12rpx;
  font-size: 22rpx;
  border-radius: 999rpx;
}

.read-tag {
  color: #64748b;
  background-color: rgba(148, 163, 184, 0.1);
}

.unread-tag {
  font-weight: 500;
  color: #3b82f6;
  background-color: rgba(59, 130, 246, 0.1);
}

.notice-category {
  padding: 2rpx 12rpx;
  font-size: 22rpx;
  border-radius: 999rpx;
}

// 空状态
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-text {
  margin-top: 24rpx;
  font-size: 28rpx;
  color: #9ca3af;
}

.clickable {
  cursor: pointer;
}

// 颜色类
.bg-red-50 {
  background-color: rgba(239, 68, 68, 0.05);
}

.bg-blue-50 {
  background-color: rgba(59, 130, 246, 0.05);
}

.bg-green-50 {
  background-color: rgba(5, 150, 105, 0.05);
}

.bg-purple-50 {
  background-color: rgba(139, 92, 246, 0.05);
}

.bg-yellow-50 {
  background-color: rgba(234, 179, 8, 0.05);
}

.bg-indigo-50 {
  background-color: rgba(99, 102, 241, 0.05);
}

.bg-gray-50 {
  background-color: rgba(100, 116, 139, 0.05);
}

.text-red-500 {
  color: #ef4444;
}

.text-blue-500 {
  color: #3b82f6;
}

.text-blue-700 {
  color: #1d4ed8;
}

.text-red-600 {
  color: #dc2626;
}

.text-blue-600 {
  color: #2563eb;
}

.text-green-600 {
  color: #059669;
}

.text-purple-600 {
  color: #7c3aed;
}

.text-yellow-600 {
  color: #ca8a04;
}

.text-indigo-600 {
  color: #4f46e5;
}

.text-gray-600 {
  color: #475569;
}

.mr-3 {
  margin-right: 16rpx;
}

.mr-4 {
  margin-right: 24rpx;
}

.mb-4 {
  margin-bottom: 32rpx;
}

.mt-2 {
  margin-top: 12rpx;
}

.p-0 {
  padding: 0;
}

.p-3 {
  padding: 16rpx;
}

.p-4 {
  padding: 24rpx;
}

.py-3 {
  padding-top: 20rpx;
  padding-bottom: 20rpx;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.font-medium {
  font-weight: 500;
}

.font-bold {
  font-weight: 700;
}

.text-xs {
  font-size: 22rpx;
}

.text-xl {
  font-size: 36rpx;
}

.notice-bell {
  display: none;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.text-gray-500 {
  color: #6b7280;
}

// 添加新的字体大小类
.text-lg {
  font-size: 32rpx;
}

.text-xxs {
  font-size: 20rpx;
}

// 添加新的间距类
.mb-3 {
  margin-bottom: 24rpx;
}

.p-2 {
  padding: 12rpx;
}
</style>
