<route lang="json5">
{
  style: {
    navigationBarTitleText: '调课申请',
  },
}
</route>
<template>
  <view class="adjust-course-page">
    <!-- 课程信息卡片 - 只在非补课页面显示 -->
    <view v-if="!isFromMakeup" class="course-info-card">
      <view class="card-title">原课程信息</view>
      <view class="info-list">
        <view class="info-item">
          <text class="info-label">课程名称</text>
          <text class="info-value">{{ courseInfo.courseName }}</text>
        </view>
        <!-- <view class="info-item">
          <text class="info-label">授课日期</text>
          <text class="info-value">{{ courseInfo.scheduleDate }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">调课节次</text>
          <text class="info-value">{{ courseInfo.sections }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">授课场地</text>
          <text class="info-value">{{ courseInfo.venue }}</text>
        </view> -->
      </view>
    </view>

    <!-- 模式选择器 -->
    <view class="mode-selector-card">
      <wd-tabs v-model="selectedMode" @change="handleModeChange">
        <wd-tab title="智能方案模式" name="intelligent" />
        <wd-tab title="手动指定模式" name="manual" />
      </wd-tabs>
    </view>

    <!-- 调课申请表单 -->
    <view class="form-card">
      <view class="card-title">调课信息</view>

      <!-- 课程选择 - 只在补课页面显示 -->
      <view v-if="isFromMakeup" class="form-item">
        <view class="form-label required">课程</view>
        <view class="form-content">
          <view class="picker-display" @click="showCourseSelector = true">
            <text :class="{ placeholder: !selectedCourse }">
              {{
                selectedCourse
                  ? `${selectedCourse.courseCode} - ${selectedCourse.courseName}`
                  : '请选择课程'
              }}
            </text>
            <wd-icon name="arrow-right" size="16" class="text-gray-300" />
          </view>
        </view>
      </view>

      <!-- 合班联动选项 -->
      <view v-if="shouldShowMergeTaskLinkage" class="form-item">
        <view class="form-label">合班联动</view>
        <view class="form-content">
          <view class="flex items-center">
            <checkbox-group @change="handleMergeTaskLinkageChange">
              <checkbox value="merge-linkage" :checked="mergeTaskLinkage" class="mr-3">
                <view class="text-sm text-gray-600">联动申请相同合班的日程</view>
              </checkbox>
            </checkbox-group>
          </view>
        </view>
      </view>

      <!-- 调课时间区域 - 智能方案模式 -->
      <view v-if="showIntelligentFields" class="time-section">
        <view class="time-section-label">调课时间</view>
        <!-- 调课日期 - 开始时间和结束时间在一行 -->
        <view class="form-item">
          <view class="time-row">
            <!-- 开始时间 -->
            <view class="time-item">
              <view class="form-label required">开始时间</view>
              <view class="form-content">
                <view class="picker-display" @click="openStartDatePicker">
                  <text :class="{ placeholder: !startDate }">
                    {{ startDate ? formatDate(startDate) : '请选择开始时间' }}
                  </text>
                  <wd-icon name="arrow-right" size="16" class="text-gray-300" />
                </view>
              </view>
            </view>

            <!-- 结束时间 -->
            <view class="time-item">
              <view class="form-label required">结束时间</view>
              <view class="form-content">
                <view class="picker-display" @click="openEndDatePicker">
                  <text :class="{ placeholder: !endDate }">
                    {{ endDate ? formatDate(endDate) : '请选择结束时间' }}
                  </text>
                  <wd-icon name="arrow-right" size="16" class="text-gray-300" />
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 意向日期 - 手动指定模式 -->
      <view v-if="isManualMode" class="form-item">
        <view class="form-label required">{{ dateFieldLabel }}</view>
        <view class="form-content">
          <view class="picker-display" @click="openStartDatePicker">
            <text :class="{ placeholder: !startDate }">
              {{ startDate ? formatDate(startDate) : `请选择${dateFieldLabel}` }}
            </text>
            <wd-icon name="arrow-right" size="16" class="text-gray-300" />
          </view>
        </view>
      </view>

      <!-- 调课节次 - 智能方案模式（多选） -->
      <view v-if="showIntelligentFields" class="form-item">
        <view class="form-label required">调课节次</view>
        <view class="form-content">
          <wd-select-picker
            style="border: 2rpx solid #e8e8e8; border-radius: 5rpx"
            v-model="selectedSections"
            :columns="sectionList"
            placeholder="请选择调课节次"
            type="checkbox"
            title="选择调课节次"
            :display-format="formatSectionDisplay"
            @change="handleSectionChange"
          />
        </view>
      </view>

      <!-- 意向节次 - 手动指定模式（picker） -->
      <view v-if="isManualMode" class="form-item">
        <view class="form-label required">{{ sectionFieldLabel }}</view>
        <view class="form-content">
          <picker
            mode="selector"
            :range="sectionPickerOptions"
            range-key="label"
            :value="selectedManualSectionIndex"
            @change="handleManualSectionPickerChange"
          >
            <view class="picker-display">
              <text :class="{ placeholder: selectedManualSectionIndex === -1 }">
                {{
                  selectedManualSectionIndex !== -1
                    ? sectionPickerOptions[selectedManualSectionIndex]?.label
                    : `请选择${sectionFieldLabel}`
                }}
              </text>
              <wd-icon name="arrow-right" size="16" class="text-gray-300" />
            </view>
          </picker>
        </view>
      </view>

      <!-- 授课场地 - 智能方案模式（多选） -->
      <view v-if="showIntelligentFields" class="form-item">
        <view class="form-label">授课场地</view>
        <view class="form-content">
          <view class="picker-display" @click="openVenuePicker">
            <text :class="{ placeholder: !venueDisplayText }">
              {{ venueDisplayText || '请选择授课场地' }}
            </text>
            <wd-icon name="arrow-right" size="16" class="text-gray-300" />
          </view>
          <!-- 已选择场地列表 -->
          <view v-if="selectedVenues.length > 0" class="selected-venues-list">
            <view v-for="venue in selectedVenues" :key="venue.id" class="selected-venue-item">
              <text class="venue-name">
                {{
                  (() => {
                    const campusBuilding = [venue.campusName, venue.buildingName]
                      .filter(Boolean)
                      .join('')
                    return campusBuilding
                      ? `${venue.siteName}（${campusBuilding}）`
                      : venue.siteName
                  })()
                }}
              </text>
              <view class="remove-venue" @click.stop="removeVenue(venue)">
                <wd-icon name="close" size="14px" class="text-gray-400" />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 授课场地 - 手动指定模式（单选） -->
      <view v-if="isManualMode" class="form-item">
        <view class="form-label">授课场地</view>
        <view class="form-content">
          <view class="picker-display" @click="openManualVenuePicker">
            <text :class="{ placeholder: !selectedManualVenue }">
              {{
                selectedManualVenue
                  ? (() => {
                      const campusBuilding = [
                        selectedManualVenue.campusName,
                        selectedManualVenue.buildingName,
                      ]
                        .filter(Boolean)
                        .join('')
                      return campusBuilding
                        ? `${selectedManualVenue.siteName}（${campusBuilding}）`
                        : selectedManualVenue.siteName
                    })()
                  : '请选择授课场地'
              }}
            </text>
            <wd-icon name="arrow-right" size="16" class="text-gray-300" />
          </view>
        </view>
      </view>

      <!-- 方案数量 - 仅智能方案模式显示 -->
      <view v-if="showIntelligentFields" class="form-item">
        <view class="form-label">方案数量</view>
        <view class="form-content">
          <input
            v-model="planCount"
            class="form-input"
            type="number"
            placeholder="请输入方案数量"
          />
        </view>
      </view>
    </view>

    <!-- 查找方案按钮 -->
    <view class="search-plan-section">
      <button class="search-plan-button" @click="handleSearchPlan">
        <wd-icon name="search" size="16" class="search-icon" />
        <text>{{ isManualMode ? '查找方案' : '查找方案' }}</text>
      </button>
    </view>

    <!-- 方案列表 - 仅智能方案模式显示 -->
    <view v-if="showIntelligentFields" class="plan-list-card">
      <view class="card-title">方案列表</view>
      <view v-if="planList.length === 0" class="empty-state">
        <text class="empty-text">暂无方案数据</text>
      </view>
      <view v-else class="plan-list">
        <view v-for="(plan, index) in planList" :key="plan.id || index" class="plan-item">
          <view class="plan-header">
            <text class="plan-title">方案 {{ index + 1 }}</text>
            <view class="plan-status" :class="plan.status" v-if="getPlanStatusText(plan)">
              {{ getPlanStatusText(plan) }}
            </view>
          </view>
          <view class="plan-content">
            <view class="plan-info-item">
              <text class="plan-label">授课日期</text>
              <text class="plan-value">{{ plan.scheduleDate }}</text>
            </view>
            <view class="plan-info-item">
              <text class="plan-label">节次</text>
              <text class="plan-value">{{ plan.sections }}</text>
            </view>
            <view class="plan-info-item">
              <text class="plan-label">教学场地</text>
              <text class="plan-value">{{ plan.venue || '无需' }}</text>
            </view>
          </view>
          <view class="plan-actions">
            <!-- 补课页面：多选模式 -->
            <template v-if="isFromMakeup">
              <button
                class="action-button select-button w-full"
                :class="{
                  disabled: plan.status === 'conflict',
                  selected: isPlanSelected(plan),
                }"
                :disabled="plan.status === 'conflict'"
                @click="togglePlanSelection(plan)"
              >
                {{
                  plan.status === 'conflict' ? '不可选择' : isPlanSelected(plan) ? '已选择' : '选择'
                }}
              </button>
            </template>
            <!-- 调课页面：单选模式 -->
            <template v-else>
              <button
                class="action-button select-button w-full"
                :class="{ disabled: plan.status === 'conflict' }"
                :disabled="plan.status === 'conflict'"
                @click="handleSelectPlan(plan)"
              >
                {{ plan.status === 'conflict' ? '不可选择' : '选择' }}
              </button>
            </template>
          </view>
        </view>
      </view>
    </view>

    <!-- 补课确认按钮 - 只在补课页面且智能方案模式显示 -->
    <view
      v-if="isFromMakeup && selectedPlans.length > 0 && showIntelligentFields"
      class="confirm-section"
    >
      <button class="confirm-button" @click="handleChooseMakeUp">
        <wd-icon name="check" size="16" class="confirm-icon" />
        <text>确认补课 ({{ selectedPlans.length }})</text>
      </button>
    </view>

    <!-- 开始日期选择器popup -->
    <wd-popup v-model="startDatePickerVisible" position="bottom" close-on-click-modal>
      <view class="popup-header">
        <view class="text-gray-500" @click="closeStartDatePicker">取消</view>
        <view class="popup-title">选择开始日期</view>
        <view class="text-blue-500" @click="confirmStartDate">确定</view>
      </view>
      <view class="calendar-container">
        <wd-calendar-view
          v-model="tempStartDate"
          :max-date="maxDate"
          :panel-height="400"
          :first-day-of-week="1"
          show-current
        />
      </view>
    </wd-popup>

    <!-- 结束日期选择器popup -->
    <wd-popup v-model="endDatePickerVisible" position="bottom" close-on-click-modal>
      <view class="popup-header">
        <view class="text-gray-500" @click="closeEndDatePicker">取消</view>
        <view class="popup-title">选择结束日期</view>
        <view class="text-blue-500" @click="confirmEndDate">确定</view>
      </view>
      <view class="calendar-container">
        <wd-calendar-view
          v-model="tempEndDate"
          :min-date="startDate"
          :max-date="maxDate"
          :panel-height="400"
          :first-day-of-week="1"
          show-current
        />
      </view>
    </wd-popup>

    <!-- 场地选择器popup - 智能方案模式（多选） -->
    <wd-popup
      v-model="venuePickerVisible"
      position="bottom"
      :close-on-click-modal="true"
      custom-style="height: 80vh; border-radius: 16px 16px 0 0;"
      @close="closeVenuePicker"
    >
      <view class="popup-header px-4 py-3 border-b border-gray-100">
        <view class="text-gray-500" @click="closeVenuePicker">取消</view>
        <view class="text-lg font-semibold">选择授课场地</view>
        <view class="text-blue-500" @click="confirmVenueSelection">确认</view>
      </view>
      <view class="popup-content">
        <VenueSelector
          multiple
          :selected-venues="selectedVenues"
          @select="handleVenueSelect"
          @multi-select="handleMultiVenueSelect"
        />
      </view>
    </wd-popup>

    <!-- 手动模式场地选择器popup（单选） -->
    <wd-popup
      v-model="manualVenuePickerVisible"
      position="bottom"
      :close-on-click-modal="true"
      custom-style="height: 80vh; border-radius: 16px 16px 0 0;"
      @close="closeManualVenuePicker"
    >
      <view class="popup-header px-4 py-3 border-b border-gray-100">
        <view class="text-gray-500" @click="closeManualVenuePicker">取消</view>
        <view class="text-lg font-semibold">选择授课场地</view>
        <view class="text-blue-500" @click="closeManualVenuePicker">确认</view>
      </view>
      <view class="popup-content">
        <VenueSelector :multiple="false" @select="handleManualVenueSelect" />
      </view>
    </wd-popup>

    <!-- 课程选择器弹窗 -->
    <wd-popup
      v-model="showCourseSelector"
      position="bottom"
      :close-on-click-modal="true"
      custom-style="height: 80vh; border-radius: 16px 16px 0 0;"
    >
      <view class="popup-header px-4 py-3 border-b border-gray-100">
        <view class="text-gray-500" @click="showCourseSelector = false">取消</view>
        <view class="text-lg font-semibold">选择课程</view>
        <view class="text-blue-500" @click="showCourseSelector = false">确认</view>
      </view>
      <view class="popup-content">
        <CourseSelector api-type="teachingTaskPagination" @select="handleCourseSelect" />
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import dayjs from 'dayjs'
import { getSectionListWithParams } from '@/service/section'
import { submitAdjustCourse, getScheduleChangePlan } from '@/service/mediationCourse'
import VenueSelector from '@/components/VenueSelector/index.vue'
import CourseSelector from '@/components/CourseSelector/index.vue'
import { useMediationCourseStore } from '@/store/mediationCourse'
import type { AdjustCourseData } from '@/store/mediationCourse'
import type { Section } from '@/types/section'
import type {
  AdjustCourseParams,
  ScheduleChangePlanQuery,
  ScheduleChangePlanItem,
} from '@/types/mediationCourse'
import type { Venue } from '@/types/venue'
import type { Course } from '@/types/course'
import type { TeachingTaskItem } from '@/types/teachingTask'

// 方案数据类型定义 - 基于 ScheduleChangePlanItem 扩展
interface Plan {
  id?: string
  scheduleDate: string
  sections: string
  venue: string
  status: 'pending' | 'approved' | 'rejected' | 'draft' | 'conflict'
  conflictType?: 'teacher' | 'venue' | 'class' // 冲突类型
  conflictMessage?: string // 冲突信息
  // 从 API 返回的原始数据
  originalData?: ScheduleChangePlanItem
}

// 获取调课store
const mediationCourseStore = useMediationCourseStore()

// 模式选择器状态
const selectedMode = ref('intelligent') // 默认选中智能方案模式

// 判断是否来自补课页面
const isFromMakeup = ref(false)

// 课程选择器相关
const showCourseSelector = ref(false)
const selectedCourse = ref<Course | TeachingTaskItem | null>(null)
// 合班联动开关状态
const mergeTaskLinkage = ref(false)

// 页面参数 - 从store获取，如果store中没有数据则使用默认值
const courseInfo = computed(() => {
  return (
    mediationCourseStore.getCurrentCourseInfo() || {
      courseName: '',
      scheduleDate: '',
      sections: '',
      venue: '',
    }
  )
})

// 调课计划查询所需的参数 - 从store获取，如果store中没有数据则使用默认值
const scheduleParams = computed(() => {
  const storeData = mediationCourseStore.getCurrentCourseInfo()
  return (
    storeData?.scheduleParams || {
      changeType: 4, // 调课类型，默认值
      scheduleId: 0, // 排课ID，默认值
      teachTaskId: 0, // 教学任务ID，默认值
    }
  )
})

// 判断是否显示合班联动选项
const shouldShowMergeTaskLinkage = computed(() => {
  // 补课页面：根据选中课程的mergeTaskId判断
  if (isFromMakeup.value) {
    return selectedCourse.value && selectedCourse.value.mergeTaskId
  }

  // 调课页面：根据传入课程的sshb字段判断
  const storeData = mediationCourseStore.getCurrentCourseInfo()
  const originalData = storeData?.originalData
  return originalData && originalData.sshb
})

// 表单数据
const startDate = ref<number | null>(Date.now()) // 默认今天
const endDate = ref<number | null>(Date.now() + 7 * 24 * 60 * 60 * 1000) // 默认7天后
const newSection = ref('')
const newVenue = ref('')
const planCount = ref('3') // 默认方案数量为1

// popup控制
const startDatePickerVisible = ref(false)
const endDatePickerVisible = ref(false)
const venuePickerVisible = ref(false)
const manualVenuePickerVisible = ref(false)
const tempStartDate = ref<number>(Date.now())
const tempEndDate = ref<number>(Date.now() + 7 * 24 * 60 * 60 * 1000)

// 选中的场地信息（支持多选）
const selectedVenues = ref<Venue[]>([])
const selectedVenue = ref<Venue | null>(null)
// 手动模式下的单选场地
const selectedManualVenue = ref<Venue | null>(null)

// 方案列表数据
const planList = ref<Plan[]>([])
// 选中的方案列表（补课时支持多选）
const selectedPlans = ref<Plan[]>([])

// 构建冲突检测Map - 从store中获取已选择的调课数据
const buildConflictMaps = () => {
  const teacherMap = new Map<string, any[]>()
  const venueMap = new Map<string, any[]>()
  const classMap = new Map<string, any[]>()

  // 获取store中的所有调课数据
  const adjustCourseDataMap = mediationCourseStore.getAllAdjustCourseData()

  Object.values(adjustCourseDataMap).forEach((data) => {
    const date = data.targetDate
    const sectionName = data.targetSection

    // 构建教师冲突检测key
    if (data.teacherCode) {
      const teacherKey = `${date}_${sectionName}_${data.teacherCode}`
      if (!teacherMap.has(teacherKey)) {
        teacherMap.set(teacherKey, [])
      }
      teacherMap.get(teacherKey)!.push(data)
    }

    // 构建场地冲突检测key
    if (data.targetVenueCode) {
      const venueKey = `${date}_${sectionName}_${data.targetVenueCode}`
      if (!venueMap.has(venueKey)) {
        venueMap.set(venueKey, [])
      }
      venueMap.get(venueKey)!.push(data)
    }

    // 构建班级冲突检测key
    if (data.teachingClassCode) {
      const classKey = `${date}_${sectionName}_${data.teachingClassCode}`
      if (!classMap.has(classKey)) {
        classMap.set(classKey, [])
      }
      classMap.get(classKey)!.push(data)
    }
  })

  return { teacherMap, venueMap, classMap }
}

// 日期范围
const minDate = Date.now()
const maxDate = Date.now() + 180 * 24 * 60 * 60 * 1000

// 计算属性：场地显示文本
const venueDisplayText = computed(() => {
  if (selectedVenues.value.length === 0) {
    return ''
  } else if (selectedVenues.value.length === 1) {
    const venue = selectedVenues.value[0]
    // 格式：场地名+（校区+建筑楼）
    const campusBuilding = [venue.campusName, venue.buildingName].filter(Boolean).join('')
    return campusBuilding ? `${venue.siteName}（${campusBuilding}）` : venue.siteName
  } else {
    return `已选择 ${selectedVenues.value.length} 个场地`
  }
})

// 格式化日期
const formatDate = (timestamp: number) => {
  return dayjs(timestamp).format('YYYY-MM-DD')
}

// 打开开始日期选择器
const openStartDatePicker = () => {
  tempStartDate.value = startDate.value || Date.now()
  startDatePickerVisible.value = true
}

// 关闭开始日期选择器
const closeStartDatePicker = () => {
  startDatePickerVisible.value = false
}

// 确认开始日期选择
const confirmStartDate = () => {
  startDate.value = tempStartDate.value
  startDatePickerVisible.value = false

  // 确保结束日期不小于开始日期，如果小于则设置为开始日期的7天后
  if (endDate.value && endDate.value < startDate.value) {
    endDate.value = startDate.value + 7 * 24 * 60 * 60 * 1000
  }
}

// 打开结束日期选择器
const openEndDatePicker = () => {
  tempEndDate.value =
    endDate.value ||
    (startDate.value
      ? startDate.value + 7 * 24 * 60 * 60 * 1000
      : Date.now() + 7 * 24 * 60 * 60 * 1000)
  endDatePickerVisible.value = true
}

// 关闭结束日期选择器
const closeEndDatePicker = () => {
  endDatePickerVisible.value = false
}

// 确认结束日期选择
const confirmEndDate = () => {
  endDate.value = tempEndDate.value
  endDatePickerVisible.value = false
}

// 节次选择器相关
const sectionList = ref<Section[]>([])
const selectedSections = ref<string[]>([])
// 手动模式下的picker节次选择
const selectedManualSectionIndex = ref<number>(-1)
const sectionPickerOptions = computed(() => sectionList.value)

// 获取节次列表
const fetchSectionList = async () => {
  try {
    // 根据是否为补课页面决定是否传入jcs参数
    const params: any = {
      lrttbk: 1,
    }

    // 如果不是补课页面，则传入jcs参数
    if (!isFromMakeup.value) {
      params.jcs = 2
    }

    const res = await getSectionListWithParams(params)

    // 处理节次数据，确保 value 字段是安全的（避免特殊字符导致CSS选择器错误）
    const processedSections = res.map((section, index) => ({
      ...section,
      value: section.value.replace(/[|#[\]]/g, '_') || `section_${index}`, // 替换特殊字符
      originalValue: section.value, // 保存原始值用于提交
    }))

    sectionList.value = processedSections

    // 如果不是补课页面，默认选中指定节次（1-2节、3-4节、5-6节、7-8节、9-10节）
    if (processedSections.length > 0 && !isFromMakeup.value) {
      // 根据节次名称筛选需要默认选中的节次
      const targetSectionNames = ['1-2节', '3-4节', '5-6节', '7-8节', '9-10节']
      const defaultSections = processedSections.filter((section) =>
        targetSectionNames.includes(section.name || section.label),
      )
      selectedSections.value = defaultSections.map((section) => section.value)
      // 更新 newSection 字段用于提交（使用API需要的简洁格式）
      const apiFormatSections = defaultSections.map((section) => {
        const originalValue = section.originalValue || section.value
        // 从节次标签中提取数字部分，如 "第1-2节" -> "1-2"
        const match = originalValue.match(/(\d+-\d+)/)
        return match ? match[1] : originalValue
      })
      newSection.value = apiFormatSections.join(',')
    } else if (isFromMakeup.value) {
      // 补课页面不默认选中任何节次
      selectedSections.value = []
      newSection.value = ''
    }
  } catch (error) {
    console.error('获取节次列表失败:', error)
  }
}

// 格式化节次显示
const formatSectionDisplay = (items: string[], columns: Section[]) => {
  if (items.length === 0) {
    return '请选择调课节次'
  }

  // 获取选中节次的标签并显示
  const selectedLabels = items.map((val) => {
    const section = columns.find((col) => col.value === val)
    return section ? section.label : val
  })

  return selectedLabels.join('、')
}

// 处理节次选择变化（智能方案模式 - 多选）
const handleSectionChange = (event: any) => {
  // wot-design-uni 的 select-picker 事件参数可能是 { value } 格式
  const value = event?.value || event

  // 确保 value 是数组
  const selectedValues = Array.isArray(value) ? value : []

  selectedSections.value = selectedValues

  // 更新 newSection 字段用于提交（使用原始值或标签）
  const selectedLabels = selectedValues.map((val) => {
    const section = sectionList.value.find((s) => s.value === val)
    return section ? section.label : val
  })

  // 用于提交的原始值 - 转换为API需要的简洁格式
  const selectedOriginalValues = selectedValues.map((val) => {
    const section = sectionList.value.find((s) => s.value === val)
    const originalValue = section ? section.originalValue || section.value : val
    // 从节次标签中提取数字部分，如 "第1-2节" -> "1-2"
    const match = originalValue.match(/(\d+-\d+)/)
    return match ? match[1] : originalValue
  })

  // 用于API调用的简洁格式，如 "1-2,2-3,3-4"
  newSection.value = selectedOriginalValues.join(',')

  console.log(
    '选中的节次:',
    selectedValues,
    '标签:',
    selectedLabels,
    '原始值:',
    selectedOriginalValues,
    'API格式:',
    newSection.value,
  )
}

// 处理手动模式picker节次选择变化
const handleManualSectionPickerChange = (event: any) => {
  const index = event.detail.value
  selectedManualSectionIndex.value = index

  // 更新 newSection 字段用于提交
  if (index >= 0 && index < sectionList.value.length) {
    const section = sectionList.value[index]
    const originalValue = section.originalValue || section.value
    // 从节次标签中提取数字部分，如 "第1-2节" -> "1-2"
    const match = originalValue.match(/(\d+-\d+)/)
    newSection.value = match ? match[1] : originalValue
  } else {
    newSection.value = ''
  }

  console.log('手动模式选中节次索引:', index, 'API格式:', newSection.value)
}

// 手动模式调课处理
const handleManualAdjust = async (planData: ScheduleChangePlanItem) => {
  console.log('手动模式调课:', planData)

  // 构造PC端格式的调课数据
  const adjustCourseData: AdjustCourseData = {
    type: 'change', // 调课类型
    scheduleId: planData.oldScheduleId || 0,
    key: planData.key || `[调课]${planData.targetDate} ${planData.targetSectionName.join('-')}`,
    teachingTaskId: planData.teachingTaskId || scheduleParams.value.teachTaskId,
    sourceRemark:
      courseInfo.value.courseName +
      ' ' +
      courseInfo.value.scheduleDate +
      ' ' +
      courseInfo.value.sections,
    targetRemark: planData.targetDate + ' ' + planData.targetSectionName.join('-'),
    ks: 2, // 课时，默认2
    targetWeek: planData.targetWeek || 0,
    targetDate: planData.targetDate || '',
    targetSection: planData.targetSectionName?.join('-') || '',
    targetVenueCode: planData.targetVenue || undefined,
    targetVenueName: planData.targetVenueName || undefined, // 添加场地名称
    teacherCode: planData.teacherCode || '',
    teachingClassCode: planData.teachingClassCode || '',
    mergeTeachingTaskId: planData.mergeTeachingTaskId || null,
    _X_ROW_KEY: `row_${Date.now()}`,
  }

  // 如果勾选了合班联动且存在otherMergeSchedule数据，先删除旧的合班数据
  if (
    mergeTaskLinkage.value &&
    planData.otherMergeSchedule &&
    planData.otherMergeSchedule.length > 0 &&
    planData.mergeTeachingTaskId
  ) {
    console.log('处理合班联动数据，先删除旧数据:', planData.mergeTeachingTaskId)
    const allData = mediationCourseStore.getAllAdjustCourseData()
    Object.keys(allData).forEach((key) => {
      if (allData[key].mergeTeachingTaskId === planData.mergeTeachingTaskId) {
        mediationCourseStore.removeAdjustCourseData(key)
      }
    })
  }

  // 存储到store中
  const scheduleIdKey = String(adjustCourseData.scheduleId)
  mediationCourseStore.addAdjustCourseData(scheduleIdKey, adjustCourseData)

  // 如果勾选了合班联动且存在otherMergeSchedule数据，也需要存储
  if (
    mergeTaskLinkage.value &&
    planData.otherMergeSchedule &&
    planData.otherMergeSchedule.length > 0
  ) {
    console.log('处理合班联动数据:', planData.otherMergeSchedule)

    planData.otherMergeSchedule.forEach((mergeItem, index) => {
      const mergeAdjustCourseData: AdjustCourseData = {
        type: 'change', // 调课类型
        scheduleId: mergeItem.oldScheduleId || 0,
        key:
          mergeItem.key ||
          `[调课]${planData.targetDate} ${planData.targetSectionName.join('-')}_merge_${index}`,
        teachingTaskId: mergeItem.teachingTaskId || mergeItem.teachTaskId,
        sourceRemark: mergeItem.teachingCourseName + ' ' + mergeItem.teachingClassName,
        targetRemark: planData.targetDate + ' ' + planData.targetSectionName.join('-'),
        ks: 2, // 课时，默认2
        targetWeek: mergeItem.targetWeek || 0,
        targetDate: mergeItem.targetDate || '',
        targetSection: mergeItem.targetSectionName?.join('-') || '',
        targetVenueCode: mergeItem.targetVenue || undefined,
        targetVenueName: mergeItem.targetVenueName || undefined, // 添加场地名称
        teacherCode: mergeItem.teacherCode || '',
        teachingClassCode: mergeItem.teachingClassCode || '',
        mergeTeachingTaskId: mergeItem.mergeTeachingTaskId || null,
        _X_ROW_KEY: `row_${Date.now()}_merge_${index}`,
      }

      // 使用合并项的key作为标识存储
      mediationCourseStore.addAdjustCourseData(mergeItem.oldScheduleId, mergeAdjustCourseData)
    })
  }

  uni.showToast({
    title: '调课方案已选择并保存',
    icon: 'success',
  })

  console.log('调课数据已保存:', adjustCourseData)

  // 延迟跳转回调课页面
  setTimeout(() => {
    uni.navigateBack({ delta: 1 })
  }, 1500)
}

// 手动模式补课处理
const handleManualMakeUp = async (planData: ScheduleChangePlanItem) => {
  console.log('手动模式补课:', planData)

  // 获取选中课程的信息
  const selectedCourseInfo = selectedCourse.value as TeachingTaskItem
  const courseDisplayName = selectedCourseInfo
    ? `${selectedCourseInfo.courseName}(${selectedCourseInfo.className})`
    : '未知课程'

  // 构造补课数据，按PC端格式
  const makeUpData: AdjustCourseData = {
    type: 'make_up', // 补课类型
    scheduleId: 0, // 补课时scheduleId为0
    key: planData.key || `[补课]${planData.targetDate} ${planData.targetSectionName.join('-')}`,
    teachingTaskId: planData.teachingTaskId || selectedCourseInfo?.id || 0,
    sourceRemark: courseDisplayName, // 补课时sourceRemark存储课程名称和班级信息
    targetRemark: planData.targetDate + ' ' + planData.targetSectionName.join('-'),
    ks: 2, // 课时，默认2
    targetWeek: planData.targetWeek || 0,
    targetDate: planData.targetDate || '',
    targetSection: planData.targetSectionName?.join('-') || '',
    targetVenueCode: planData.targetVenue || undefined,
    targetVenueName: planData.targetVenueName || undefined, // 添加场地名称
    teacherCode: planData.teacherCode || '',
    teachingClassCode: planData.teachingClassCode || '',
    mergeTeachingTaskId: planData.mergeTeachingTaskId || null,
    _X_ROW_KEY: `row_${Date.now()}_${Math.random()}`,
    // 补课特有字段
    jxrwid: planData.teachingTaskId || selectedCourseInfo?.id || 0,
  }

  // 如果勾选了合班联动且存在otherMergeSchedule数据，先删除旧的合班数据
  if (
    mergeTaskLinkage.value &&
    planData.otherMergeSchedule &&
    planData.otherMergeSchedule.length > 0 &&
    planData.mergeTeachingTaskId
  ) {
    console.log('处理补课合班联动数据，先删除旧数据:', planData.mergeTeachingTaskId)
    const allData = mediationCourseStore.getAllAdjustCourseData()
    Object.keys(allData).forEach((key) => {
      if (allData[key].mergeTeachingTaskId === planData.mergeTeachingTaskId) {
        mediationCourseStore.removeAdjustCourseData(key)
      }
    })
  }

  // 存储到store中，使用key作为标识
  mediationCourseStore.addAdjustCourseData(makeUpData.key, makeUpData)

  // 如果勾选了合班联动且存在otherMergeSchedule数据，也需要存储
  if (
    mergeTaskLinkage.value &&
    planData.otherMergeSchedule &&
    planData.otherMergeSchedule.length > 0
  ) {
    console.log('处理补课合班联动数据:', planData.otherMergeSchedule)

    planData.otherMergeSchedule.forEach((mergeItem, index) => {
      const mergeMakeUpData: AdjustCourseData = {
        type: 'make_up', // 补课类型
        scheduleId: 0, // 补课时scheduleId为0
        key:
          mergeItem.key ||
          `[补课]${planData.targetDate} ${planData.targetSectionName.join('-')}_merge_${index}`,
        teachingTaskId: mergeItem.teachingTaskId || mergeItem.teachTaskId,
        sourceRemark: mergeItem.teachingCourseName + ' ' + mergeItem.teachingClassName,
        targetRemark: planData.targetDate + ' ' + planData.targetSectionName.join('-'),
        ks: 2, // 课时，默认2
        targetWeek: mergeItem.targetWeek || 0,
        targetDate: mergeItem.targetDate || '',
        targetSection: mergeItem.targetSectionName?.join('-') || '',
        targetVenueCode: mergeItem.targetVenue || undefined,
        targetVenueName: mergeItem.targetVenueName || undefined, // 添加场地名称
        teacherCode: mergeItem.teacherCode || '',
        teachingClassCode: mergeItem.teachingClassCode || '',
        mergeTeachingTaskId: mergeItem.mergeTeachingTaskId || null,
        _X_ROW_KEY: `row_${Date.now()}_merge_${index}_${Math.random()}`,
        // 补课特有字段
        jxrwid: mergeItem.teachingTaskId || mergeItem.teachTaskId,
      }

      // 使用合并项的key作为标识存储
      mediationCourseStore.addAdjustCourseData(mergeItem.key, mergeMakeUpData)
    })
  }

  uni.showToast({
    title: '补课方案已选择并保存',
    icon: 'success',
  })

  console.log('补课数据已保存:', makeUpData)

  // 延迟跳转回调课页面
  setTimeout(() => {
    uni.navigateBack({ delta: 1 })
  }, 1500)
}

// 打开场地选择器（智能方案模式 - 多选）
const openVenuePicker = () => {
  venuePickerVisible.value = true
}

// 关闭场地选择器
const closeVenuePicker = () => {
  venuePickerVisible.value = false
}

// 打开手动模式场地选择器（单选）
const openManualVenuePicker = () => {
  manualVenuePickerVisible.value = true
}

// 关闭手动模式场地选择器
const closeManualVenuePicker = () => {
  manualVenuePickerVisible.value = false
}

// 确认场地选择（多选模式）
const confirmVenueSelection = () => {
  venuePickerVisible.value = false

  if (selectedVenues.value.length > 0) {
    // 更新newVenue字段用于提交 - 优先使用场地代码，如果没有则使用场地名称
    newVenue.value = selectedVenues.value
      .map((v) => {
        // 优先使用 venueCode，如果没有则使用 siteName 作为场地标识
        return v.siteCode || v.siteName
      })
      .join(',')

    uni.showToast({
      title: `已选择 ${selectedVenues.value.length} 个场地`,
      icon: 'success',
    })
  }
}

// 处理场地选择（单选模式，保留兼容性）
const handleVenueSelect = (venue: Venue) => {
  selectedVenue.value = venue
  newVenue.value = `${venue.siteName}`
  venuePickerVisible.value = false

  uni.showToast({
    title: `已选择：${venue.siteName}`,
    icon: 'success',
  })
}

// 处理多选场地选择
const handleMultiVenueSelect = (venues: Venue[]) => {
  selectedVenues.value = venues
}

// 处理手动模式场地选择（单选）
const handleManualVenueSelect = (venue: Venue) => {
  selectedManualVenue.value = venue
  newVenue.value = venue.siteCode || venue.siteName
  manualVenuePickerVisible.value = false

  uni.showToast({
    title: `已选择：${venue.siteName}`,
    icon: 'success',
  })
}

// 移除单个场地
const removeVenue = (venue: Venue) => {
  const index = selectedVenues.value.findIndex((v) => v.id === venue.id)
  if (index > -1) {
    selectedVenues.value.splice(index, 1)
    // 更新newVenue字段 - 优先使用场地代码，如果没有则使用场地名称
    newVenue.value = selectedVenues.value.map((v) => v.siteCode || v.siteName).join(',')
  }
}

// 计算属性：是否为手动指定模式
const isManualMode = computed(() => selectedMode.value === 'manual')

// 计算属性：是否显示智能方案模式的字段
const showIntelligentFields = computed(() => !isManualMode.value)

// 计算属性：日期字段标签
const dateFieldLabel = computed(() => {
  if (isManualMode.value) {
    return isFromMakeup.value ? '补课意向日期' : '调课意向日期'
  }
  return '开始时间'
})

// 计算属性：节次字段标签
const sectionFieldLabel = computed(() => {
  if (isManualMode.value) {
    return isFromMakeup.value ? '补课意向节次' : '调课意向节次'
  }
  return '调课节次'
})

// 处理模式切换
const handleModeChange = (mode: string) => {
  console.log('模式切换:', mode)

  // 切换到手动模式时清空所有选择
  if (mode === 'manual') {
    selectedSections.value = []
    selectedManualSectionIndex.value = -1
    newSection.value = ''
    selectedVenues.value = []
    selectedManualVenue.value = null
    newVenue.value = ''
  } else if (mode === 'intelligent') {
    // 切换回智能模式时清空手动模式的选择，并恢复默认选择
    selectedManualSectionIndex.value = -1
    selectedManualVenue.value = null
    if (!isFromMakeup.value && sectionList.value.length > 0) {
      const targetSectionNames = ['1-2节', '3-4节', '5-6节', '7-8节', '9-10节']
      const defaultSections = sectionList.value.filter((section) =>
        targetSectionNames.includes(section.name || section.label),
      )
      selectedSections.value = defaultSections.map((section) => section.value)
      const apiFormatSections = defaultSections.map((section) => {
        const originalValue = section.originalValue || section.value
        const match = originalValue.match(/(\d+-\d+)/)
        return match ? match[1] : originalValue
      })
      newSection.value = apiFormatSections.join(',')
    } else {
      // 补课模式下清空选择
      selectedSections.value = []
      newSection.value = ''
    }
    // 恢复默认场地选择
    initDefaultVenue()
  }
}

// 处理合班联动状态变化
const handleMergeTaskLinkageChange = (e: any) => {
  // checkbox-group的change事件返回选中的值数组
  const selectedValues = e.detail.value
  mergeTaskLinkage.value = selectedValues.includes('merge-linkage')
  console.log('合班联动状态变化:', e, mergeTaskLinkage.value)
}

// 处理课程选择
const handleCourseSelect = (course: Course | TeachingTaskItem) => {
  selectedCourse.value = course
  console.log(course.mergeTaskId)

  // 重置合班联动状态
  mergeTaskLinkage.value = false

  showCourseSelector.value = false
  // 如果是补课页面，需要获取教学任务ID
  if (isFromMakeup.value && 'id' in course) {
    // 更新调课计划所需的参数
    const currentParams = scheduleParams.value
    const updatedParams = {
      ...currentParams,
      changeType: 3, // 补课类型
      teachTaskId: course.id, // 使用选中课程的ID作为教学任务ID
      scheduleId: 0, // 补课时scheduleId为0
    }

    // 更新store中的参数
    const courseData = {
      ...courseInfo.value,
      scheduleParams: updatedParams,
    }
    mediationCourseStore.setCurrentCourseInfo(courseData)

    console.log('补课页面选中课程，更新参数:', updatedParams)
  }

  uni.showToast({
    title: `已选择：${course.courseName}`,
    icon: 'success',
  })
}

// 冲突检测函数 - 基于PC端逻辑
const checkConflictMap = (map: Map<string, any[]>, key: string, row: ScheduleChangePlanItem) => {
  if (!map || !map.has(key)) {
    return false
  }

  const conflictRows = map.get(key)

  if (!conflictRows || conflictRows.length === 0) {
    return false
  }
  if (!row.mergeTeachingTaskId) {
    return true // 如果不是合班的任务，只要有冲突情况就直接返回冲突
  }

  // 循环conflictRows, 只要有一条不是相同合班的，就返回冲突
  for (const conflictRow of conflictRows) {
    if (conflictRow.mergeTeachingTaskId !== row.mergeTeachingTaskId) {
      return true
    }
    if (conflictRow.teachingTaskId === row.teachingTaskId) {
      return true
    }
  }
  return false
}

// 检测方案冲突状态
const checkPlanConflict = (row: ScheduleChangePlanItem) => {
  const date = row.targetDate
  const sectionName = row.targetSection.join(',')
  const targetVenue = row.targetVenue
  const teacherCode = row.teacherCode
  const teachingClassCode = row.teachingClassCode

  // 从store中构建冲突检测Map
  const { teacherMap, venueMap, classMap } = buildConflictMaps()

  // 检查教师冲突
  if (teacherCode && checkConflictMap(teacherMap, `${date}_${sectionName}_${teacherCode}`, row)) {
    return { hasConflict: true, type: 'teacher', message: '已使用（教师）' }
  }

  // 检查场地冲突
  if (targetVenue && checkConflictMap(venueMap, `${date}_${sectionName}_${targetVenue}`, row)) {
    return { hasConflict: true, type: 'venue', message: '已使用（场地）' }
  }

  // 检查班级冲突
  if (
    teachingClassCode &&
    checkConflictMap(classMap, `${date}_${sectionName}_${teachingClassCode}`, row)
  ) {
    return { hasConflict: true, type: 'class', message: '已使用（班级）' }
  }

  return { hasConflict: false, type: null, message: '' }
}

// 方案状态文本映射
const getPlanStatusText = (plan: Plan) => {
  if (plan.status === 'conflict') {
    return plan.conflictMessage || '冲突'
  }

  const statusMap = {
    pending: '待审核',
    approved: '已通过',
    rejected: '已拒绝',
    draft: '',
  }
  return statusMap[plan.status as keyof typeof statusMap] || ''
}

// 切换方案选择状态（补课时支持多选）
const togglePlanSelection = (plan: Plan) => {
  // 检查是否为冲突方案
  if (plan.status === 'conflict') {
    uni.showToast({
      title: plan.conflictMessage || '该方案存在冲突，无法选择',
      icon: 'none',
    })
    return
  }

  const index = selectedPlans.value.findIndex((p) => p.id === plan.id)
  if (index > -1) {
    // 已选中，取消选择
    selectedPlans.value.splice(index, 1)
  } else {
    // 未选中，添加到选中列表
    selectedPlans.value.push(plan)
  }
}

// 检查方案是否已选中
const isPlanSelected = (plan: Plan) => {
  return selectedPlans.value.some((p) => p.id === plan.id)
}

// 选择方案 - 按PC端格式存储到store中（调课时使用）
const handleSelectPlan = (plan: Plan) => {
  console.log('选择方案:', plan)

  // 检查是否为冲突方案
  if (plan.status === 'conflict') {
    uni.showToast({
      title: plan.conflictMessage || '该方案存在冲突，无法选择',
      icon: 'none',
    })
    return
  }

  // 获取原始数据
  const originalData = plan.originalData
  if (!originalData) {
    uni.showToast({
      title: '方案数据异常',
      icon: 'none',
    })
    return
  }

  // 构造PC端格式的调课数据
  const adjustCourseData: AdjustCourseData = {
    type: 'change', // 调课类型
    scheduleId: originalData.oldScheduleId || 0,
    key: originalData.key || `[调课]${plan.scheduleDate} ${plan.sections}`,
    teachingTaskId: originalData.teachingTaskId || scheduleParams.value.teachTaskId,
    sourceRemark:
      courseInfo.value.courseName +
      ' ' +
      courseInfo.value.scheduleDate +
      ' ' +
      courseInfo.value.sections,
    targetRemark: plan.scheduleDate + ' ' + plan.sections,
    ks: 2, // 课时，默认2
    targetWeek: originalData.targetWeek || 0,
    targetDate: originalData.targetDate || '',
    targetSection: originalData.targetSectionName?.join('-') || plan.sections,
    targetVenueCode: originalData.targetVenue || undefined,
    targetVenueName: originalData.targetVenueName || undefined, // 添加场地名称
    teacherCode: originalData.teacherCode || '',
    teachingClassCode: originalData.teachingClassCode || '',
    mergeTeachingTaskId: originalData.mergeTeachingTaskId || null,
    _X_ROW_KEY: `row_${Date.now()}`,
  }

  // 如果勾选了合班联动且存在otherMergeSchedule数据，先删除旧的合班数据
  if (
    mergeTaskLinkage.value &&
    originalData.otherMergeSchedule &&
    originalData.otherMergeSchedule.length > 0 &&
    originalData.mergeTeachingTaskId
  ) {
    console.log('处理合班联动数据，先删除旧数据:', originalData.mergeTeachingTaskId)
    const allData = mediationCourseStore.getAllAdjustCourseData()
    Object.keys(allData).forEach((key) => {
      if (allData[key].mergeTeachingTaskId === originalData.mergeTeachingTaskId) {
        mediationCourseStore.removeAdjustCourseData(key)
      }
    })
  }

  // 存储到store中
  const scheduleIdKey = String(adjustCourseData.scheduleId)
  mediationCourseStore.addAdjustCourseData(scheduleIdKey, adjustCourseData)

  // 如果勾选了合班联动且存在otherMergeSchedule数据，也需要存储
  if (
    mergeTaskLinkage.value &&
    originalData.otherMergeSchedule &&
    originalData.otherMergeSchedule.length > 0
  ) {
    console.log('处理合班联动数据:', originalData.otherMergeSchedule)

    originalData.otherMergeSchedule.forEach((mergeItem, index) => {
      const mergeAdjustCourseData: AdjustCourseData = {
        type: 'change', // 调课类型
        scheduleId: mergeItem.oldScheduleId || 0,
        key: mergeItem.key || `[调课]${plan.scheduleDate} ${plan.sections}_merge_${index}`,
        teachingTaskId: mergeItem.teachingTaskId || mergeItem.teachTaskId,
        sourceRemark: mergeItem.teachingCourseName + ' ' + mergeItem.teachingClassName,
        targetRemark: plan.scheduleDate + ' ' + plan.sections,
        ks: 2, // 课时，默认2
        targetWeek: mergeItem.targetWeek || 0,
        targetDate: mergeItem.targetDate || '',
        targetSection: mergeItem.targetSectionName?.join('-') || plan.sections,
        targetVenueCode: mergeItem.targetVenue || undefined,
        targetVenueName: mergeItem.targetVenueName || undefined, // 添加场地名称
        teacherCode: mergeItem.teacherCode || '',
        teachingClassCode: mergeItem.teachingClassCode || '',
        mergeTeachingTaskId: mergeItem.mergeTeachingTaskId || null,
        _X_ROW_KEY: `row_${Date.now()}_merge_${index}`,
      }

      // 使用合并项的key作为标识存储
      mediationCourseStore.addAdjustCourseData(
        mergeAdjustCourseData.scheduleId + '',
        mergeAdjustCourseData,
      )
    })
  }

  uni.showToast({
    title: '方案已选择并保存',
    icon: 'success',
  })

  console.log('调课数据已保存:', adjustCourseData)

  // 延迟跳转回调课页面
  setTimeout(() => {
    uni.navigateBack({ delta: 1 })
  }, 1500)
}

// 补课确认 - 按PC端逻辑处理选中的补课方案
const handleChooseMakeUp = () => {
  if (selectedPlans.value.length === 0) {
    uni.showToast({
      title: '请先选择补课方案',
      icon: 'none',
    })
    return
  }

  console.log('选中补课方案:', selectedPlans.value)

  selectedPlans.value.forEach((plan) => {
    const originalData = plan.originalData
    if (!originalData) return

    // 获取选中课程的信息
    const selectedCourseInfo = selectedCourse.value as TeachingTaskItem
    const courseDisplayName = selectedCourseInfo
      ? `${selectedCourseInfo.courseName}(${selectedCourseInfo.className})`
      : '未知课程'

    // 构造补课数据，按PC端格式
    const makeUpData: AdjustCourseData = {
      type: 'make_up', // 补课类型
      scheduleId: 0, // 补课时scheduleId为0
      key: originalData.key || plan.id,
      teachingTaskId: originalData.teachingTaskId || selectedCourseInfo?.id || 0,
      sourceRemark: courseDisplayName, // 补课时sourceRemark存储课程名称和班级信息
      targetRemark: plan.scheduleDate + ' ' + plan.sections,
      ks: 2, // 课时，默认2
      targetWeek: originalData.targetWeek || 0,
      targetDate: originalData.targetDate || '',
      targetSection: originalData.targetSectionName?.join('-') || plan.sections,
      targetVenueCode: originalData.targetVenue || undefined,
      teacherCode: originalData.teacherCode || '',
      teachingClassCode: originalData.teachingClassCode || '',
      mergeTeachingTaskId: originalData.mergeTeachingTaskId || null,
      _X_ROW_KEY: `row_${Date.now()}_${Math.random()}`,
      // 补课特有字段
      jxrwid: originalData.teachingTaskId || selectedCourseInfo?.id || 0,
    }

    // 如果勾选了合班联动且存在otherMergeSchedule数据，先删除旧的合班数据
    if (
      mergeTaskLinkage.value &&
      originalData.otherMergeSchedule &&
      originalData.otherMergeSchedule.length > 0 &&
      originalData.mergeTeachingTaskId
    ) {
      console.log('处理补课合班联动数据，先删除旧数据:', originalData.mergeTeachingTaskId)
      const allData = mediationCourseStore.getAllAdjustCourseData()
      Object.keys(allData).forEach((key) => {
        if (allData[key].mergeTeachingTaskId === originalData.mergeTeachingTaskId) {
          mediationCourseStore.removeAdjustCourseData(key)
        }
      })
    }

    // 存储到store中，使用key作为标识
    mediationCourseStore.addAdjustCourseData(makeUpData.key, makeUpData)

    // 如果勾选了合班联动且存在otherMergeSchedule数据，也需要存储
    if (
      mergeTaskLinkage.value &&
      originalData.otherMergeSchedule &&
      originalData.otherMergeSchedule.length > 0
    ) {
      console.log('处理补课合班联动数据:', originalData.otherMergeSchedule)

      originalData.otherMergeSchedule.forEach((mergeItem, index) => {
        const mergeMakeUpData: AdjustCourseData = {
          type: 'make_up', // 补课类型
          scheduleId: 0, // 补课时scheduleId为0
          key: mergeItem.key || `${plan.id}_merge_${index}`,
          teachingTaskId: mergeItem.teachingTaskId || mergeItem.teachTaskId,
          sourceRemark: mergeItem.teachingCourseName + ' ' + mergeItem.teachingClassName,
          targetRemark: plan.scheduleDate + ' ' + plan.sections,
          ks: 2, // 课时，默认2
          targetWeek: mergeItem.targetWeek || 0,
          targetDate: mergeItem.targetDate || '',
          targetSection: mergeItem.targetSectionName?.join('-') || plan.sections,
          targetVenueCode: mergeItem.targetVenue || undefined,
          teacherCode: mergeItem.teacherCode || '',
          teachingClassCode: mergeItem.teachingClassCode || '',
          mergeTeachingTaskId: mergeItem.mergeTeachingTaskId || null,
          _X_ROW_KEY: `row_${Date.now()}_merge_${index}_${Math.random()}`,
          // 补课特有字段
          jxrwid: mergeItem.teachingTaskId || mergeItem.teachTaskId,
        }

        // 使用合并项的key作为标识存储
        mediationCourseStore.addAdjustCourseData(mergeItem.key, mergeMakeUpData)
      })
    }
  })

  uni.showToast({
    title: `已选择 ${selectedPlans.value.length} 个补课方案`,
    icon: 'success',
  })

  console.log('补课数据已保存')

  // 延迟跳转回调课页面
  setTimeout(() => {
    uni.navigateBack({ delta: 1 })
  }, 1500)
}

// 查找方案
const handleSearchPlan = async () => {
  // 表单验证
  if (isManualMode.value) {
    // 手动模式验证
    if (!startDate.value) {
      uni.showToast({
        title: `请选择${dateFieldLabel.value}`,
        icon: 'none',
      })
      return
    }

    if (selectedManualSectionIndex.value === -1) {
      uni.showToast({
        title: `请选择${sectionFieldLabel.value}`,
        icon: 'none',
      })
      return
    }
  } else {
    // 智能方案模式验证
    if (!startDate.value) {
      uni.showToast({
        title: '请选择开始时间',
        icon: 'none',
      })
      return
    }

    if (!endDate.value) {
      uni.showToast({
        title: '请选择结束时间',
        icon: 'none',
      })
      return
    }

    if (!selectedSections.value || selectedSections.value.length === 0) {
      uni.showToast({
        title: '请选择调课节次',
        icon: 'none',
      })
      return
    }
  }

  try {
    uni.showLoading({
      title: '查找方案中...',
      mask: true,
    })
    console.log(mergeTaskLinkage.value)

    // 准备调课计划查询参数
    const currentScheduleParams = scheduleParams.value
    const queryParams: ScheduleChangePlanQuery = {
      changeType: currentScheduleParams.changeType,
      scheduleId: currentScheduleParams.scheduleId,
      teachTaskId: currentScheduleParams.teachTaskId,
      beginTargetDate: dayjs(startDate.value).format('YYYY-MM-DD'),
      endTargetDate: isManualMode.value
        ? dayjs(startDate.value).format('YYYY-MM-DD') // 手动模式：使用意向日期作为结束日期
        : dayjs(endDate.value).format('YYYY-MM-DD'), // 智能模式：使用结束日期
      planCount: isManualMode.value ? 1 : parseInt(planCount.value) || 10, // 手动模式固定为1个方案
      section: newSection.value, // 节次信息，如 "1-2,2-3,3-4"
      hbld: mergeTaskLinkage.value ? 1 : 0, // 合班联动选中时为1，否则为0
    }

    // 补课页面的特殊处理
    if (isFromMakeup.value) {
      if (!selectedCourse.value) {
        uni.showToast({
          title: '请先选择课程',
          icon: 'none',
        })
        return
      }

      // 补课时只需要传teachTaskId，changeType为3
      queryParams.changeType = 3
      queryParams.teachTaskId = (selectedCourse.value as TeachingTaskItem).id
      queryParams.scheduleId = 0 // 补课时scheduleId为0
    }

    // 如果有选择场地，添加场地代码
    if (newVenue.value) {
      // 手动模式和智能模式都使用场地代码
      queryParams.venueCode = newVenue.value
    }

    console.log('调课计划查询参数:', queryParams)

    // 调用调课计划API
    const response = await getScheduleChangePlan(queryParams)

    uni.hideLoading()

    if (response && response.length > 0) {
      if (isManualMode.value) {
        // 手动模式：直接选择第一个方案并执行调课/补课
        const firstPlan = response[0]
        const conflictResult = checkPlanConflict(firstPlan)

        if (conflictResult.hasConflict) {
          uni.showToast({
            title: conflictResult.message || '该方案存在冲突，无法选择',
            icon: 'none',
          })
          return
        }

        // 直接执行调课或补课逻辑
        if (isFromMakeup.value) {
          // 补课逻辑
          await handleManualMakeUp(firstPlan)
        } else {
          // 调课逻辑
          await handleManualAdjust(firstPlan)
        }
      } else {
        // 智能模式：显示方案列表供用户选择
        const newPlans: Plan[] = response.map((item: ScheduleChangePlanItem) => {
          const conflictResult = checkPlanConflict(item)

          return {
            id: item.key || `plan_${Date.now()}_${Math.random()}`,
            scheduleDate: `${item.targetDate} 第${item.targetWeek}周 星期${item.targetNumberWeek}`,
            sections: item.targetSectionName.join('-'),
            venue: item.targetVenueName || item.targetVenue,
            status: conflictResult.hasConflict ? ('conflict' as const) : ('draft' as const),
            conflictType: conflictResult.type as 'teacher' | 'venue' | 'class' | undefined,
            conflictMessage: conflictResult.message,
            originalData: item, // 保存原始数据以备后用
          }
        })

        // 替换方案列表
        planList.value = newPlans
        // 清空已选择的方案列表
        selectedPlans.value = []

        uni.showToast({
          title: `找到 ${newPlans.length} 个可用方案`,
          icon: 'success',
        })
      }
    } else {
      uni.showToast({
        title: '未找到可用的调课方案',
        icon: 'none',
      })
    }
  } catch (error) {
    uni.hideLoading()
    console.error('查找方案失败:', error)
  }
}

// 初始化默认场地选择
const initDefaultVenue = () => {
  const storeData = mediationCourseStore.getCurrentCourseInfo()
  const originalData = storeData?.originalData

  // 检查是否有场地代码（skcddm）和场地名称（skcdmc）
  if (originalData && originalData.skcddm && originalData.skcdmc) {
    console.log('检测到默认场地:', originalData.skcddm, originalData.skcdmc)

    // 构造场地对象
    const defaultVenue: Venue = {
      id: Date.now(), // 使用时间戳作为临时ID
      siteCode: originalData.skcddm,
      siteName: originalData.skcdmc,
      categoryName: '默认场地',
      campusName: '', // 默认场地暂时没有校区信息
      buildingName: '', // 默认场地暂时没有建筑楼信息
      studentCapacity: 0,
    }

    // 设置为默认选中的场地
    selectedVenues.value = [defaultVenue]

    // 更新newVenue字段用于提交
    newVenue.value = defaultVenue.siteCode

    console.log('已设置默认场地:', defaultVenue)
  }
}

// 页面加载时获取课程信息
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  // @ts-expect-error - 忽略类型检查，因为uni-app的类型定义可能不完整
  const options = currentPage.options || {}

  console.log('页面参数:', options)

  // 检查是否来自补课页面
  if (options.source === 'makeup') {
    isFromMakeup.value = true
    console.log('来自补课页面，隐藏课程信息卡片')
  }

  // TODO: 根据传入的课程ID获取课程详细信息
  // 从页面参数获取课程信息，如果store中没有数据则使用页面参数
  if (!mediationCourseStore.getCurrentCourseInfo()) {
    const courseData = {
      courseName: options.courseName || '',
      scheduleDate: options.scheduleDate || '',
      sections: options.sections || '',
      venue: options.venue || '',
      // 从页面参数中获取调课计划所需的参数
      scheduleParams: {
        changeType: options.changeType ? parseInt(options.changeType) : 4,
        scheduleId: options.scheduleId ? parseInt(options.scheduleId) : 0,
        teachTaskId: options.teachTaskId ? parseInt(options.teachTaskId) : 0,
      },
    }
    mediationCourseStore.setCurrentCourseInfo(courseData)
  }

  // 初始化默认场地选择
  initDefaultVenue()

  // 获取节次列表
  fetchSectionList()
})
</script>

<style lang="scss" scoped>
.adjust-course-page {
  min-height: 100vh;
  padding: 20rpx;
  background-color: #f5f5f5;
}

.page-header {
  margin-bottom: 20rpx;
}

.page-title {
  padding: 20rpx 0;
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  text-align: center;
}

.course-info-card,
.form-card {
  padding: 24rpx;
  margin-bottom: 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-title {
  padding-bottom: 16rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  border-bottom: 1px solid #f0f0f0;
}

.info-list {
  .info-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16rpx 0;
    border-bottom: 1px solid #f8f8f8;

    &:last-child {
      border-bottom: none;
    }

    .info-label {
      width: 160rpx;
      font-size: 28rpx;
      color: #666666;
    }

    .info-value {
      flex: 1;
      font-size: 28rpx;
      color: #333333;
      text-align: right;
    }
  }
}

.form-item {
  margin-bottom: 24rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333333;

  &.required::before {
    margin-right: 4rpx;
    color: #f5222d;
    content: '*';
  }
}

.form-content {
  position: relative;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;

  .placeholder {
    color: #999999;
  }
}

.form-input {
  box-sizing: border-box;
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}

.textarea-input {
  box-sizing: border-box;
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}

.char-count {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #999999;
  text-align: right;
}

// 弹窗样式
.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100rpx;
  padding: 0 32rpx;
  font-size: 32rpx;
  font-weight: 500;
  background-color: #ffffff;
  border-bottom: 1px solid #f2f2f2;
}

.popup-title {
  flex: 1;
  font-size: 34rpx;
  font-weight: 600;
  color: #333333;
  text-align: center;
}

.popup-content {
  height: calc(80vh - 60px);
  overflow: hidden;
}

.calendar-container {
  height: auto;
  max-height: 80vh;
  padding: 20rpx;
  background-color: #fff;
}
/* 调课时间区域样式 */
.time-section {
  padding: 20rpx;
  margin-bottom: 30rpx;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}

.time-section-label {
  padding-bottom: 10rpx;
  margin-bottom: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #333333;
  border-bottom: 1px dashed #e8e8e8;
}

.time-section-content {
  padding: 16rpx;
  background-color: #ffffff;
  border-radius: 6rpx;
}
/* 时间选择器一行布局样式 */
.time-row {
  display: flex;
  gap: 20rpx;
  align-items: flex-start;
}

.time-item {
  flex: 1;

  .form-label {
    margin-bottom: 12rpx;
    font-size: 28rpx;
    color: #333333;

    &.required::before {
      margin-right: 4rpx;
      color: #f5222d;
      content: '*';
    }
  }

  .form-content {
    position: relative;
  }

  .picker-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    color: #333333;
    background-color: #ffffff;
    border: 1px solid #e8e8e8;
    border-radius: 8rpx;

    .placeholder {
      color: #999999;
    }
  }
}
/* 已选择场地列表样式 */
.selected-venues-list {
  padding: 16rpx;
  margin-top: 16rpx;
  background-color: #f9f9f9;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}

.selected-venue-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 16rpx;
  margin-bottom: 8rpx;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  border-radius: 6rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .venue-name {
    flex: 1;
    font-size: 28rpx;
    color: #333333;
  }

  .remove-venue {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32rpx;
    height: 32rpx;
    background-color: #f5f5f5;
    border-radius: 50%;
    transition: all 0.3s;

    &:active {
      background-color: #e8e8e8;
      transform: scale(0.95);
    }
  }
}
/* 查找方案按钮样式 */
.search-plan-section {
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.search-plan-button {
  display: flex;
  gap: 12rpx;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 80rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #ffffff;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: none;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;

  .search-icon {
    color: #ffffff;
  }

  &:active {
    box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.4);
    transform: translateY(2rpx);
  }

  &:disabled {
    color: #bfbfbf;
    background: #f5f5f5;
    box-shadow: none;
    transform: none;
  }
}
/* 方案列表样式 */
.plan-list-card {
  padding: 24rpx;
  margin-bottom: 20rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.empty-state {
  padding: 60rpx 0;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
}

.plan-list {
  .plan-item {
    padding: 20rpx;
    margin-bottom: 16rpx;
    background-color: #f9f9f9;
    border: 1px solid #e8e8e8;
    border-radius: 8rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .plan-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 12rpx;
      margin-bottom: 16rpx;
      border-bottom: 1px solid #e0e0e0;

      .plan-title {
        font-size: 30rpx;
        font-weight: 500;
        color: #333333;
      }

      .plan-status {
        padding: 4rpx 12rpx;
        font-size: 24rpx;
        border-radius: 12rpx;

        &.pending {
          color: #fa8c16;
          background-color: #fff7e6;
          border: 1px solid #ffd591;
        }

        &.approved {
          color: #52c41a;
          background-color: #f6ffed;
          border: 1px solid #b7eb8f;
        }

        &.rejected {
          color: #f5222d;
          background-color: #fff2f0;
          border: 1px solid #ffccc7;
        }

        &.draft {
          color: #666666;
          background-color: #f5f5f5;
          border: 1px solid #d9d9d9;
        }

        &.conflict {
          color: #f5222d;
          background-color: #fff2f0;
          border: 1px solid #ffccc7;
        }
      }
    }

    .plan-content {
      margin-bottom: 16rpx;

      .plan-info-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8rpx 0;

        .plan-label {
          width: 140rpx;
          font-size: 26rpx;
          color: #666666;
        }

        .plan-value {
          flex: 1;
          font-size: 26rpx;
          color: #333333;
          text-align: right;
        }
      }
    }

    .plan-actions {
      display: flex;
      justify-content: center;

      .action-button {
        height: 60rpx;
        font-size: 24rpx;
        line-height: 60rpx;
        text-align: center;
        border: none;
        border-radius: 6rpx;

        &.select-button {
          color: #ffffff;
          background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
          border: 1px solid #52c41a;
          box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);
          transition: all 0.3s ease;

          &:active {
            box-shadow: 0 1rpx 4rpx rgba(82, 196, 26, 0.4);
            opacity: 0.8;
            transform: scale(0.98);
          }

          &.disabled {
            color: #bfbfbf;
            cursor: not-allowed;
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            box-shadow: none;

            &:active {
              box-shadow: none;
              opacity: 1;
              transform: none;
            }
          }

          &.selected {
            color: #ffffff;
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            border: 1px solid #1890ff;
            box-shadow: 0 2rpx 8rpx rgba(24, 144, 255, 0.4);

            &:active {
              box-shadow: 0 1rpx 4rpx rgba(24, 144, 255, 0.5);
              opacity: 0.9;
              transform: scale(0.98);
            }
          }
        }
      }
    }
  }
}
/* 补课确认按钮样式 */
.confirm-section {
  padding: 20rpx;
  margin-bottom: 20rpx;
}

.confirm-button {
  display: flex;
  gap: 12rpx;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 80rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #ffffff;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border: none;
  border-radius: 40rpx;
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
  transition: all 0.3s ease;

  .confirm-icon {
    color: #ffffff;
  }

  &:active {
    box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.4);
    transform: translateY(2rpx);
  }

  &:disabled {
    color: #bfbfbf;
    background: #f5f5f5;
    box-shadow: none;
    transform: none;
  }
}
/* 模式选择器样式 */
.mode-selector-card {
  margin-bottom: 20rpx;
  background-color: #ffffff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
</style>
