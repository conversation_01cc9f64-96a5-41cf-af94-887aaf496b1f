<route lang="json5">
{
  style: {
    navigationBarTitleText: '调停补课申请',
  },
}
</route>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import ScheduleContent from './components/ScheduleContent.vue'
import PlanContent from './components/PlanContent.vue'
import FormWithApproval from '@/components/FormWithApproval/index.vue'
import { getMediationCourseApplyDetail } from '@/service/mediationCourse'
import type { MediationCourseDetail } from '@/types/mediationCourse'
import { useMediationCourseStore } from '@/store/mediationCourse'

// 获取调停课store
const mediationCourseStore = useMediationCourseStore()
// 当前激活的步骤
const currentStep = ref(0)

// 页面参数
const pageParams = ref({
  id: '',
  mode: '',
})

// 详情数据
const detailData = ref<MediationCourseDetail | null>(null)

// 加载状态
const loading = ref(false)

// 获取页面参数
onMounted(async () => {
  mediationCourseStore.clearAdjustCourseData()
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}

  pageParams.value = {
    id: options.id || '',
    mode: options.mode || '',
  }

  console.log('页面参数:', pageParams.value)

  // 如果是编辑模式或查看模式且有ID，则加载详情数据
  if (
    (pageParams.value.mode === 'edit' || pageParams.value.mode === 'view') &&
    pageParams.value.id
  ) {
    currentStep.value = 1
    await loadDetailData()
  }
})

// 加载详情数据
const loadDetailData = async () => {
  if (!pageParams.value.id) return

  loading.value = true
  try {
    const response = await getMediationCourseApplyDetail({
      id: pageParams.value.id,
      type: 'detail',
    })
    detailData.value = response
    console.log('详情数据:', detailData.value)
  } catch (error) {
    console.error('加载详情数据失败:', error)
    uni.showToast({
      title: '加载数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 处理步骤切换
const handleStepChange = (step: number) => {
  console.log('步骤切换:', step)
  currentStep.value = step
}

// 下一步
const nextStep = () => {
  if (currentStep.value < 1) {
    currentStep.value++
  }
}

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 处理返回事件
const handleReturn = () => {
  uni.navigateBack()
}
</script>

<template>
  <!-- 如果有ID，使用FormWithApproval组件包装 -->
  <FormWithApproval
    v-if="pageParams.id"
    :id="pageParams.id"
    code="ttksq"
    :show-workflow="true"
    @return="handleReturn"
  >
    <template #form-content>
      <view class="container">
        <!-- 步骤条 - 查看模式下隐藏 -->
        <view v-if="pageParams.mode !== 'view'" class="steps-card">
          <wd-steps :active="currentStep" align-center>
            <wd-step title="授课日程" description="" />
            <wd-step title="调停补课方案" description="" />
          </wd-steps>
        </view>

        <!-- 根据步骤动态渲染对应组件 - 使用v-show保持组件状态 -->
        <ScheduleContent
          v-show="currentStep === 0"
          :id="pageParams.id"
          :mode="pageParams.mode"
          :detail-data="detailData"
          :loading="loading"
          @next-step="nextStep"
        />
        <PlanContent
          v-show="currentStep === 1"
          :id="pageParams.id"
          :mode="pageParams.mode"
          :detail-data="detailData"
          :loading="loading"
          @prev-step="prevStep"
        />
      </view>
    </template>
  </FormWithApproval>

  <!-- 如果没有ID，直接显示原有内容 -->
  <view v-else class="container p-4">
    <!-- 步骤条 - 查看模式下隐藏 -->
    <view v-if="pageParams.mode !== 'view'" class="steps-card">
      <wd-steps :active="currentStep" align-center>
        <wd-step title="授课日程" description="" />
        <wd-step title="调停补课方案" description="" />
      </wd-steps>
    </view>

    <!-- 根据步骤动态渲染对应组件 - 使用v-show保持组件状态 -->
    <ScheduleContent
      v-show="currentStep === 0"
      :id="pageParams.id"
      :mode="pageParams.mode"
      :detail-data="detailData"
      :loading="loading"
      @next-step="nextStep"
    />
    <PlanContent
      v-show="currentStep === 1"
      :id="pageParams.id"
      :mode="pageParams.mode"
      :detail-data="detailData"
      :loading="loading"
      @prev-step="prevStep"
    />
  </view>
</template>

<style lang="scss" scoped>
.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  background-color: #f7f8fc;
}

.course-info-card {
  box-sizing: border-box;
  padding: 24rpx;
  margin-bottom: 24rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.course-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.steps-card {
  box-sizing: border-box;
  padding: 24rpx;
  margin-bottom: 24rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}
</style>
