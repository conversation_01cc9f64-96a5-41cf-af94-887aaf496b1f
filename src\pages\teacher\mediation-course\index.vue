<route lang="json5">
{
  style: {
    navigationBarTitleText: '调停补课记录',
  },
}
</route>

<template>
  <!--  TODO: 学年学期选择器-->
  <view class="container bg-gray-100 min-h-screen p-4 box-border">
    <!-- 本学期统计 (从底部移到顶部) -->
    <view class="bg-white rounded-lg shadow-sm p-4 mb-4">
      <view class="text-base font-medium mb-4">本学期统计</view>
      <view class="flex">
        <view class="flex-1 bg-gray-50 rounded-md p-3 text-center mx-1 first:ml-0 last:mr-0">
          <view class="text-gray-500 text-sm mb-1">调课次数</view>
          <view class="text-xl font-bold text-primary">{{ statistics.adjustCount || 0 }}</view>
        </view>
        <view class="flex-1 bg-gray-50 rounded-md p-3 text-center mx-1">
          <view class="text-gray-500 text-sm mb-1">补课次数</view>
          <view class="text-xl font-bold text-success">{{ statistics.makeupCount || 0 }}</view>
        </view>
        <view class="flex-1 bg-gray-50 rounded-md p-3 text-center mx-1">
          <view class="text-gray-500 text-sm mb-1">停课次数</view>
          <view class="text-xl font-bold text-warning">{{ statistics.cancelCount || 0 }}</view>
        </view>
      </view>
    </view>

    <!-- 调停补课申请按钮 -->
    <view class="rounded-lg shadow-sm mb-4">
      <view class="w-full bg-primary text-white text-center py-2 rounded-lg" @click="handleApply">
        <wd-icon name="add-circle" color="#fff" class="mr-2" />
        <text>调停补课申请</text>
      </view>
    </view>

    <!-- 搜索栏 - 使用SearchInput组件 -->
    <view class="mb-4">
      <view class="bg-white rounded-lg shadow-sm">
        <search-input
          v-model="searchKeyword"
          placeholder="搜索申请原因"
          @search="handleSearch"
          @clear="handleClearSearch"
        />
      </view>
    </view>
    <!-- 分段控制器 -->
    <view class="bg-white rounded-lg shadow-sm mb-4 p-1">
      <view class="flex">
        <view
          v-for="tab in tabs"
          :key="tab.value"
          class="flex-1 text-center py-2.5 text-sm relative transition-all duration-300"
          :class="activeTab === tab.value ? 'text-primary font-medium' : 'text-gray-600'"
          @click="handleTabChange(tab.value)"
        >
          {{ tab.name }}
          <view
            v-if="activeTab === tab.value"
            class="absolute bottom-0 left-1/2 w-10 h-1 bg-primary rounded-t-sm transform -translate-x-1/2"
          ></view>
        </view>
      </view>
    </view>

    <!-- 调停补课卡片列表 -->
    <view class="space-y-4" v-if="courseList.length > 0">
      <view
        v-for="item in courseList"
        :key="item.id"
        class="bg-white rounded-lg shadow-sm overflow-hidden"
        :class="[
          getStatusClass(item.spzt),
          item.spzt === 0 || item.spzt === 1 ? 'cursor-pointer' : '',
        ]"
        @click="handleCardClick(item)"
      >
        <view class="p-4">
          <view class="flex justify-between items-center mb-3">
            <view class="text-base font-medium">调停课申请</view>
            <view class="px-2 py-1 text-xs text-white rounded" :class="getStatusBgClass(item.spzt)">
              {{ getStatusText(item.spzt) }}
            </view>
          </view>
          <view class="space-y-2">
            <view class="flex text-sm">
              <view class="w-20 text-gray-500">申请编号:</view>
              <view class="flex-1">{{ item.id }}</view>
            </view>
            <view class="flex text-sm">
              <view class="w-20 text-gray-500">申请类型:</view>
              <view class="flex-1">{{ getAdjustmentTypeText(item.ttklx) }}</view>
            </view>
            <view class="flex text-sm">
              <view class="w-20 text-gray-500">调课内容:</view>
              <rich-text :nodes="item.ttkfasm" class="flex-1" />
            </view>
            <view class="flex text-sm">
              <view class="w-20 text-gray-500">申请人:</view>
              <view class="flex-1">{{ item.czrxm }}</view>
            </view>
            <view class="flex text-sm">
              <view class="w-20 text-gray-500">申请原因:</view>
              <view class="flex-1">{{ item.ttkyy }}</view>
            </view>
            <view class="flex text-sm">
              <view class="w-20 text-gray-500">申请时间:</view>
              <view class="flex-1">{{ formatTime(item.create_time) }}</view>
            </view>
            <view class="flex text-sm" v-if="item.spzt === 2 && item.spsj">
              <view class="w-20 text-gray-500">拒绝原因:</view>
              <view class="flex-1 text-danger">暂无</view>
            </view>
          </view>
          <view class="mt-3 pt-3 flex justify-end gap-2 border-t border-gray-100">
            <!--  <ActionButton
              v-if="item.spzt === 2"
              type="primary"
              text="重新申请"
              @click="handleReapply(item)"
            /> -->
            <ActionButton
              v-if="item.spzt === 0"
              type="primary"
              text="编辑"
              @click="handleEditPendingItem(item)"
            />
            <ActionButton
              v-if="item.spzt === 0"
              type="danger"
              text="撤销申请"
              @click="handleCancel(item)"
            />
            <ActionButton
              v-if="item.spzt === 1"
              type="primary"
              text="查看"
              @click="handleViewApprovedItem(item)"
            />
          </view>
        </view>
      </view>
    </view>

    <view v-else class="flex flex-col items-center justify-center py-10">
      <wd-icon name="warning" size="40" color="#999" />
      <view class="text-gray-500 mt-3">暂无调停补课记录</view>
    </view>

    <!-- 添加按钮 -->
    <!-- <view
      class="fixed right-10 bottom-30 w-14 h-14 rounded-full bg-primary shadow-lg flex items-center justify-center z-10"
      @click="handleAddNew"
    >
      <wd-icon name="add" color="#fff" size="28" />
    </view> -->
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { getMediationCourseApplyList, deleteScheduleChange } from '@/service/mediationCourse'
import type {
  MediationCourseItem,
  MediationCourseQuery,
  MediationCourseResponse,
} from '@/types/mediationCourse'
import SearchInput from '@/components/SearchInput/index.vue'
import ActionButton from '@/components/common/ActionButton.vue'

// 选项卡数据
const tabs = [
  { name: '全部', value: 'all' },
  { name: '待审批', value: 'pending' },
  { name: '已通过', value: 'approved' },
  { name: '已拒绝', value: 'rejected' },
]

// 当前激活的选项卡
const activeTab = ref('all')

// 搜索关键词
const searchKeyword = ref('')

// 课程列表数据
const courseList = ref<MediationCourseItem[]>([])

// 统计数据
const statistics = ref({
  adjustCount: 0,
  makeupCount: 0,
  cancelCount: 0,
})

// 查询参数
const queryParams = ref<MediationCourseQuery>({
  page: 1,
  pageSize: 20,
  type: 'union',
})

// 总数
const total = ref(0)

// 加载状态
const loading = ref(false)

// 初始化加载数据
onMounted(() => {
  fetchData()
})

// 页面显示时自动刷新数据
onShow(() => {
  fetchData()
})

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    // 根据当前选中的tab设置查询参数
    if (activeTab.value !== 'all') {
      const statusMap: Record<string, string | number> = {
        pending: 0,
        approved: 1,
        rejected: 2,
      }
      queryParams.value.spzt = statusMap[activeTab.value]
    } else {
      delete queryParams.value.spzt
    }

    // 添加搜索关键词
    if (searchKeyword.value) {
      queryParams.value.ttkyy = searchKeyword.value
    } else {
      delete queryParams.value.ttkyy
    }

    const res = await getMediationCourseApplyList(queryParams.value)
    courseList.value = res.items || []
    total.value = res.total || 0

    // 统计不同类型的数量
    calculateStatistics(res.items || [])
  } catch (error) {
    console.error('获取调停课数据失败', error)
  } finally {
    loading.value = false
  }
}

// 计算统计数据
const calculateStatistics = (items: MediationCourseItem[]) => {
  let adjustCount = 0
  let makeupCount = 0
  let cancelCount = 0

  items.forEach((item) => {
    // 解析教学任务信息，计算各类型数量
    try {
      if (item.jxrwxx) {
        const taskInfo = JSON.parse(item.jxrwxx)
        taskInfo.forEach((task: any) => {
          if (task.type === 'change') {
            adjustCount++
          } else if (task.type === 'make_up') {
            makeupCount++
          } else if (task.type === 'stop') {
            cancelCount++
          }
        })
      }
    } catch (e) {
      console.error('解析教学任务信息失败', e)
    }
  })

  statistics.value = {
    adjustCount,
    makeupCount,
    cancelCount,
  }
}

// 切换选项卡
const handleTabChange = (tab: string) => {
  activeTab.value = tab
  queryParams.value.page = 1
  fetchData()
}

// 搜索
const handleSearch = (value: string) => {
  searchKeyword.value = value
  queryParams.value.page = 1
  fetchData()
}

// 清除搜索
const handleClearSearch = () => {
  searchKeyword.value = ''
  delete queryParams.value.ttkyy
  queryParams.value.page = 1
  fetchData()
}

// 获取状态类名
const getStatusClass = (status: number) => {
  const statusMap: Record<number, string> = {
    0: 'border-l-4 border-warning',
    1: 'border-l-4 border-success',
    2: 'border-l-4 border-danger',
  }
  return statusMap[status] || ''
}

// 获取状态背景类名
const getStatusBgClass = (status: number) => {
  const statusMap: Record<number, string> = {
    0: 'bg-warning',
    1: 'bg-success',
    2: 'bg-danger',
  }
  return statusMap[status] || ''
}

// 获取状态文本
const getStatusText = (status: number) => {
  const statusMap: Record<number, string> = {
    0: '待审批',
    1: '已通过',
    2: '已拒绝',
  }
  return statusMap[status] || '未知状态'
}

// 获取申请类型文本
const getAdjustmentTypeText = (type: number) => {
  const typeMap: Record<number, string> = {
    1: '因公',
    2: '因私',
  }
  return typeMap[type] || '未知类型'
}

// 获取时间标签
const getTimeLabel = (type: number) => {
  const labelMap: Record<number, string> = {
    1: '调整时间',
    2: '补课时间',
  }
  return labelMap[type] || '时间'
}

// 格式化时间
const formatTime = (timestamp: number) => {
  if (!timestamp) return ''
  const date = new Date(timestamp * 1000) // 转换为毫秒
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 处理编辑操作
const handleEdit = (item: MediationCourseItem) => {
  // TODO: 跳转到编辑页面
  uni.navigateTo({
    url: `/pages/teacher/teaching-affairs/mediation-edit?id=${item.id}`,
  })
}

// 处理撤销申请操作
const handleCancel = async (item: MediationCourseItem) => {
  uni.showModal({
    title: '确认撤销',
    content: '确定要撤销该调课申请吗？撤销后无法恢复。',
    success: async function (res) {
      if (res.confirm) {
        try {
          // 调用删除接口
          await deleteScheduleChange({ id: item.id })
          uni.showToast({
            title: '撤销成功',
            icon: 'success',
          })
          // 刷新页面数据
          fetchData()
        } catch (error) {
          console.error('撤销申请失败', error)
          uni.showToast({
            title: '撤销失败，请重试',
            icon: 'none',
          })
        }
      }
    },
  })
}

// 处理重新申请操作
const handleReapply = (item: MediationCourseItem) => {
  // TODO: 跳转到重新申请页面
  uni.navigateTo({
    url: `/pages/teacher/teaching-affairs/mediation-edit?id=${item.id}&reapply=1`,
  })
}

// 处理查看详情操作
const handleDetail = (item: MediationCourseItem) => {
  // TODO: 跳转到详情页面
  uni.navigateTo({
    url: `/pages/teacher/teaching-affairs/mediation-detail?id=${item.id}`,
  })
}

// 处理添加新申请
const handleAddNew = () => {
  uni.navigateTo({
    url: '/pages/teacher/teaching-affairs/mediation-edit',
  })
}

// 处理调停补课申请
const handleApply = () => {
  uni.navigateTo({
    url: '/pages/teacher/mediation-course/form',
  })
}

// 处理待审核item点击事件 - 跳转到编辑页面
const handleEditPendingItem = (item: MediationCourseItem) => {
  uni.navigateTo({
    url: `/pages/teacher/mediation-course/form?id=${item.id}&mode=edit`,
  })
}

// 处理已通过item查看事件 - 跳转到查看页面
const handleViewApprovedItem = (item: MediationCourseItem) => {
  uni.navigateTo({
    url: `/pages/teacher/mediation-course/form?id=${item.id}&mode=view`,
  })
}

// 处理卡片点击事件
const handleCardClick = (item: MediationCourseItem) => {
  if (item.spzt === 0) {
    // 待审批状态，跳转到编辑页面
    handleEditPendingItem(item)
  } else if (item.spzt === 1) {
    // 已通过状态，跳转到查看页面
    handleViewApprovedItem(item)
  }
}
</script>

<style lang="scss">
// UnoCSS 样式扩展
.text-primary {
  color: #007aff !important;
}

.bg-primary {
  background-color: #007aff !important;
}

.border-primary {
  border-color: #007aff !important;
}

.text-success {
  color: #4caf50 !important;
}

.bg-success {
  background-color: #4caf50 !important;
}

.border-success {
  border-color: #4caf50 !important;
}

.text-warning {
  color: #ff9800 !important;
}

.bg-warning {
  background-color: #ff9800 !important;
}

.border-warning {
  border-color: #ff9800 !important;
}

.text-danger {
  color: #f44336 !important;
}

.bg-danger {
  background-color: #f44336 !important;
}

.border-danger {
  border-color: #f44336 !important;
}
</style>
