<route lang="json5">
{
  style: {
    navigationBarTitleText: '听课记录详情',
  },
}
</route>
<script setup lang="ts">
import { ref, reactive, onMounted, watch, computed } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import CourseSelector from '@/components/CourseSelector/index.vue'
import type { TeachOfficeTeachingTaskItem } from '@/types/teachOfficeTeachingTask'
import { useAttendLectureStore } from '@/store/attend-lecture'
import { getCourseDateSection } from '@/service/course'
import type { CourseDateSection } from '@/types/course'
import useUpload, { useUploadAnyFile } from '@/hooks/useUpload'
// 导入评价项目API
import {
  getEvaluationProject,
  addAttendLecture,
  getAttendLectureEvaluationData,
  updateAttendLecture,
} from '@/service/attendLecture'
import type {
  EvaluationProjectItem,
  AttendLectureAddQuery,
  AttendLectureEvaluationDataItem,
  AttendLectureUpdateQuery,
} from '@/types/attendLecture'
// 导入FormWithApproval组件
import FormWithApproval from '@/components/FormWithApproval/index.vue'

// 获取听课记录store
const attendLectureStore = useAttendLectureStore()

// 表单与审批组件相关
const formWithApprovalRef = ref() // FormWithApproval组件引用
const workflowId = ref<number | null>(null) // 工作流ID
const workflowCode = ref<string>('jstkjrsh') // 工作流代码

// 添加fromFeedback标识，用于区分是否从听课反馈信息页面进入
const isFromFeedback = ref<boolean>(false)

// 添加一个来源页面的标识
const fromPage = ref('')

// 添加一个标记，用于避免重复处理编辑数据
const editDataProcessed = ref<boolean>(false)

// 学年选择
const yearValue = ref<string>('')

// 评价项目列表
const evaluationItems = ref<EvaluationProjectItem[]>([])

// 表单数据
const formData = reactive({
  course: '', // 选课课程ID
  courseName: '', // 选课课程名称
  courseCode: '', // 课程代码
  lecturePeriod: '', // 听课日期节次
  courseType: '', // 课型
  teachingContent: '', // 教学内容
  lectureRecord: '', // 听课记录
  teachingProgress: '', // 授课进度及教案查看情况
  feedbackSituation: '', // 课间了解的教师、学生反映情况
  suggestion: '', // 听课人意见及建议
  yearValue: '', // 学年
  attachments: [], // 附件列表
  // 评价信息相关字段
  courseEvaluation: {
    teacherName: '', // 被听课教师
    evaluationItems: [] as {
      id: number
      content: string
      score: string
      tmlx: string
      tmlxmc: string
      pxh: number
      options: { label: string; value: string }[]
    }[],
    otherSuggestion: '', // 其它评价与建议
  },
})

// 附件处理相关
const attachmentList = ref<Array<{ url: string; name: string }>>([])

// 图片上传相关
const {
  loading: uploadImageLoading,
  error: uploadImageError,
  data: uploadImageData,
  run: uploadImage,
} = useUpload<any>({
  type: 'attendance', // 上传类型
})

// 文件上传相关
const {
  loading: uploadLoading,
  error: uploadError,
  data: uploadData,
  run: uploadFile,
} = useUploadAnyFile<any>({
  count: 5, // 允许一次选择最多5个文件
  extension: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
  formData: {
    type: 'attendance', // 上传类型
  },
})

// 处理图片上传
const handleUploadImage = () => {
  uploadImage()
}

// 处理文件上传
const handleUpload = () => {
  uploadFile()
}

// 监听图片上传结果并处理
watch(uploadImageData, (newVal) => {
  if (!newVal) return

  try {
    const result = typeof newVal === 'string' ? JSON.parse(newVal) : newVal

    // 处理图片上传结果
    if (result.code === 1 && result.data?.file) {
      addAttachment(result.data.file.url, result.data.file.name || '图片.jpg')
      showSuccessToast('上传成功')
    } else {
      showErrorToast(result.msg || '上传失败')
    }
  } catch (e) {
    console.error('处理上传结果失败:', e)
    showErrorToast('上传失败')
  }
})

// 监听上传结果并处理
watch(uploadData, (newVal) => {
  if (!newVal) return

  try {
    const result = typeof newVal === 'string' ? JSON.parse(newVal) : newVal

    // 处理多文件上传结果
    if (Array.isArray(result.data?.fileList)) {
      result.data.fileList.forEach((file) => {
        addAttachment(file.url, file.name)
      })
      showSuccessToast('上传成功')
    }
    // 处理单文件上传结果
    else if (result.code === 1 && result.data?.file) {
      addAttachment(result.data.file.url, result.data.file.name)
      showSuccessToast('上传成功')
    } else {
      showErrorToast(result.msg || '上传失败')
    }
  } catch (e) {
    console.error('处理上传结果失败:', e)
    showErrorToast('上传失败')
  }
})

// 添加附件到列表
const addAttachment = (url: string, name: string) => {
  attachmentList.value.push({ url, name })
  formData.attachments.push(url)
}

// 删除附件
const deleteAttachment = (index: number) => {
  attachmentList.value.splice(index, 1)
  formData.attachments.splice(index, 1)
}

// 预览附件
const previewAttachment = (file: { url: string; name: string }) => {
  const ext = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()

  // 图片文件直接预览
  if (['jpg', 'jpeg', 'png', 'gif'].includes(ext)) {
    uni.previewImage({
      urls: [file.url],
      current: file.url,
    })
    return
  }

  // 其他文件根据平台处理
  // #ifdef H5
  window.open(file.url, '_blank')
  // #endif

  // #ifdef APP-PLUS
  uni.downloadFile({
    url: file.url,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.openDocument({
          filePath: res.tempFilePath,
          success: () => console.log('打开文档成功'),
          fail: (err) => {
            console.error('打开文档失败', err)
            showErrorToast('无法预览该类型文件')
          },
        })
      }
    },
    fail: () => showErrorToast('文件下载失败'),
  })
  // #endif

  // #ifdef MP
  showErrorToast('小程序暂不支持该类型文件预览')
  // #endif
}

// 辅助函数 - 显示成功提示
const showSuccessToast = (message: string) => {
  uni.showToast({
    title: message,
    icon: 'success',
  })
}

// 辅助函数 - 显示错误提示
const showErrorToast = (message: string) => {
  uni.showToast({
    title: message,
    icon: 'none',
  })
}

// 课程选择器相关
const showCourseSelector = ref(false)
const selectedCourse = ref<TeachOfficeTeachingTaskItem | null>(null)

// 处理课程选择
const handleCourseSelect = (course: TeachOfficeTeachingTaskItem) => {
  selectedCourse.value = course

  console.log(JSON.stringify(course))

  formData.course = String(course.id)
  formData.courseName = course.courseName
  formData.courseCode = course.courseCode

  // 添加教师名称赋值逻辑
  if (course.leaderTeacherName) {
    formData.courseEvaluation.teacherName = course.leaderTeacherName
  }

  showCourseSelector.value = false

  // 重置听课日期节次
  formData.lecturePeriod = ''

  // 加载听课日期节次数据
  loadLecturePeriods(course.id)
}

// 加载听课日期节次数据
const loadLecturePeriods = (courseId: number, selectedValue?: string) => {
  loading.value = true

  getCourseDateSection({
    select: 1,
    jxrwid: courseId,
  })
    .then((result) => {
      console.log('获取听课日期节次数据:', result)

      // 将API返回的数据转换为选项格式
      lecturePeriodOptions.value = result.map((item) => ({
        label: item.skjhmc,
        value: String(item.skjhid),
      }))

      // 保存原始数据，用于课程信息展示
      courseDateSections.value = result

      // 如果传入了selectedValue参数，设置当前选中的节次
      if (selectedValue) {
        console.log('设置编辑模式下选中的节次:', selectedValue)
        formData.lecturePeriod = selectedValue

        // 如果没有找到匹配的节次选项，可能需要额外处理
        const found = lecturePeriodOptions.value.find((option) => option.value === selectedValue)
        if (!found) {
          console.warn('未找到匹配的节次选项:', selectedValue)
        }
      }
    })
    .catch((error) => {
      console.error('加载听课日期节次数据失败:', error)
      showErrorToast('加载听课日期节次数据失败')
    })
    .finally(() => {
      loading.value = false
    })
}

// 保存原始的听课日期节次数据
const courseDateSections = ref<CourseDateSection[]>([])

// 加载状态
const loading = ref(false)

// 字典选项
const lecturePeriodOptions = ref<{ label: string; value: string }[]>([])
const courseTypeOptions = ref<{ label: string; value: string }[]>([
  { label: '讲授', value: '讲授' },
  { label: '实训', value: '实训' },
  { label: '实习', value: '实习' },
  { label: '其它', value: '其它' },
])

// 获取当前页面参数
const getPageOptions = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  // @ts-expect-error 当前页面对象可能没有$page属性
  return currentPage.$page?.options || {}
}

// 加载已有的评价数据
const loadExistingEvaluationData = (lectureRecordId: number) => {
  loading.value = true

  getAttendLectureEvaluationData({ tkjlid: lectureRecordId })
    .then((res) => {
      console.log('获取已有评价数据成功:', res)

      // 如果有评价数据
      if (res && res.length > 0) {
        // 更新评价内容
        parseExistingEvaluationData(res)
      } else {
        // 如果没有评价数据，则加载评价项目
        console.log('未找到已有评价数据，加载评价项目')
        loadEvaluationProjects()
        showErrorToast('未找到评价数据，已加载评价项目模板')
      }
    })
    .catch((error) => {
      console.error('获取已有评价数据失败:', error)
      showErrorToast('获取评价数据失败，已加载评价项目模板')
      // 如果获取失败，则退回到加载评价项目
      loadEvaluationProjects()
    })
    .finally(() => {
      loading.value = false
    })
}

// 解析已有的评价数据
const parseExistingEvaluationData = (evaluationData: AttendLectureEvaluationDataItem[]) => {
  // 清空当前评价项目
  formData.courseEvaluation.evaluationItems = []

  // 处理评价数据并按排序号排序
  const parsedItems = evaluationData
    .map((item) => ({
      id: item.cptmid, // 使用测评题目ID
      content: item.cptm, // 测评题目
      score: String(item.cpfz), // 已选择的分值
      tmlx: item.sydx, // 使用sydx(使用对象)作为题目类型
      tmlxmc: item.tmlxmc, // 题目类型名称
      pxh: item.pxh, // 排序号
      options: parseItemOptions(item.cpxx), // 评分选项
    }))
    .sort((a, b) => a.pxh - b.pxh)

  // 更新表单数据
  formData.courseEvaluation.evaluationItems = parsedItems
  console.log('解析后的评价项目:', formData.courseEvaluation.evaluationItems)
}

// 解析评价项目
const parseEvaluationItems = (items: EvaluationProjectItem[]) => {
  // 转换评价项目格式并按排序号排序
  const parsedItems = items
    .map((item) => {
      const options = parseItemOptions(item.cpxx)
      return {
        id: item.id,
        content: item.cptm, // 评价项目内容
        score: options.length > 0 ? options[0].value : '', // 默认选择第一项
        tmlx: item.tmlx, // 题目类型
        tmlxmc: item.tmlxmc, // 题目类型名称
        pxh: item.pxh, // 排序号
        options, // 评分选项
      }
    })
    .sort((a, b) => a.pxh - b.pxh)

  // 更新表单数据
  formData.courseEvaluation.evaluationItems = parsedItems
}

// 解析单个项目的选项
const parseItemOptions = (cpxx: string): { label: string; value: string }[] => {
  if (!cpxx) {
    console.warn('评分选项字符串为空')
    return []
  }

  try {
    // 分割选项 "9分|9&&8分|8&&7分|7" => ["9分|9", "8分|8", "7分|7"]
    return cpxx.split('&&').map((option) => {
      const [label, value] = option.split('|')
      return { label, value }
    })
  } catch (error) {
    console.error('解析评分选项失败:', error, cpxx)
    return []
  }
}

// 验证表单
const validateForm = () => {
  // 必填字段列表
  const requiredFields = [
    { field: formData.course, message: '请选择课程' },
    { field: formData.yearValue, message: '请选择学年' },
    { field: formData.lecturePeriod, message: '请选择听课日期节次' },
    { field: formData.courseType, message: '请选择课型' },
    { field: formData.teachingContent, message: '请输入教学内容' },
    { field: formData.lectureRecord, message: '请输入听课记录' },
    { field: formData.teachingProgress, message: '请输入授课进度及教案查看情况' },
    { field: formData.feedbackSituation, message: '请输入课间了解的教师、学生反映情况' },
    { field: formData.suggestion, message: '请输入听课人意见及建议' },
  ]

  // 检查必填字段
  for (const item of requiredFields) {
    if (!item.field) {
      showErrorToast(item.message)
      return false
    }
  }

  // 评价表单验证
  const allItemsScored = formData.courseEvaluation.evaluationItems.every(
    (item) => item.score !== '',
  )
  if (!allItemsScored) {
    showErrorToast('请完成所有评价项目')
    return false
  }

  return true
}

// 修改handleBack方法
const handleBack = () => {
  // 获取页面参数
  const options = getPageOptions()

  // 检查是否有fromPage参数
  if (options.fromPage) {
    fromPage.value = options.fromPage
  }

  // 如果来自推门听课页面，则返回到列表页
  if (fromPage.value === 'push-door-lecture') {
    // 返回到列表页，可能需要根据实际情况调整路径
    uni.redirectTo({
      url: '/pages/teacher/professional-development/attend-lecture',
    })
  } else {
    // 正常返回上一页
    uni.navigateBack()
  }
}

// 修改handleSubmit方法
const handleSubmit = () => {
  // 如果是只读模式，则直接返回上一页
  if (isDisabled.value) {
    handleBack()
    return
  }

  if (!validateForm()) {
    return
  }

  // 获取页面参数，判断是否是编辑模式
  const options = getPageOptions()
  const isEditMode = options.mode === 'edit' && options.id

  // 准备提交的数据
  const submitData = prepareSubmitData(isEditMode ? Number(options.id) : undefined)

  console.log('提交数据:', submitData)

  uni.showLoading({
    title: '提交中...',
  })

  // 根据模式选择调用不同的API
  const apiPromise = isEditMode
    ? updateAttendLecture(submitData as AttendLectureUpdateQuery)
    : addAttendLecture(submitData as AttendLectureAddQuery)

  // 调用API提交表单数据
  apiPromise
    .then((res) => {
      console.log('提交成功:', res)
      uni.hideLoading()

      showSuccessToast(isEditMode ? '更新成功' : '保存成功')
      setTimeout(() => {
        // 使用handleBack方法处理返回逻辑
        handleBack()
      }, 2000)
    })
    .catch((error) => {
      console.error('提交失败:', error)
      uni.hideLoading()
      showErrorToast(error.msg || '提交失败，请稍后重试')
    })
}

// 准备提交数据
const prepareSubmitData = (id?: number) => {
  // 基础数据
  const data: any = {
    // 基本信息
    jxrwid: formData.course,
    skjhid: formData.lecturePeriod,
    kx: formData.courseType,

    // 听课详情
    jxnr: formData.teachingContent,
    tkjl: formData.lectureRecord,
    skjdja: formData.teachingProgress,
    jsxsqk: formData.feedbackSituation,
    tkyj: formData.suggestion,

    // 评价信息
    tkpj: formData.courseEvaluation.otherSuggestion,

    // 评价项目，格式为ID_分值描述_分值
    cpxx: formData.courseEvaluation.evaluationItems.map((item) => {
      const option = item.options.find((opt) => opt.value === item.score)
      return `${item.id}_${option?.label || ''}_${item.score}`
    }),

    // 附件列表，格式为文件路径|文件名
    fjlb: attachmentList.value.map((file) => `${file.url}|${file.name}`),
  }

  // 如果是编辑模式，添加id字段
  if (id) {
    data.id = id
  }

  return data
}

// 计算总分
const totalScore = computed(() => {
  let total = 0
  formData.courseEvaluation.evaluationItems.forEach((item) => {
    if (item.score) {
      total += parseInt(item.score, 10) || 0
    }
  })
  return total
})

// 按题目类型分组的评价项目
const groupedEvaluationItems = computed(() => {
  // 获取所有不同的题目类型
  const types = new Set<string>()
  formData.courseEvaluation.evaluationItems.forEach((item) => {
    if (item.tmlx) {
      types.add(item.tmlx)
    }
  })

  // 创建分组结果
  const result: { title: string; items: any[] }[] = []

  // 按题目类型分组
  types.forEach((type) => {
    const items = formData.courseEvaluation.evaluationItems.filter((item) => item.tmlx === type)
    if (items.length > 0) {
      result.push({
        title: items[0].tmlxmc || `题目类型${type}`,
        items,
      })
    }
  })

  return result
})

// 添加计算属性，用于课程信息展示
const selectedCoursePeriod = computed(() => {
  if (!formData.lecturePeriod || !courseDateSections.value.length) return null
  return courseDateSections.value.find((item) => String(item.skjhid) === formData.lecturePeriod)
})

// 在 <script setup> 部分添加跳转方法
const navigateToTeachingLog = () => {
  uni.navigateTo({
    url: '/pages/teacher/push-door-lecture/index',
  })
}

// FormWithApproval组件相关方法

// 处理返回按钮点击
const handleApprovalReturn = () => {
  // 如果需要处理审批流程页面的返回事件，可以在这里处理
  console.log('从审批流程页面返回')
}

// 初始化工作流相关参数
const initWorkflowParams = () => {
  // 获取页面参数
  const options = getPageOptions()
  // 这里可以根据需要设置工作流相关参数
}

// 添加一个新的ref来标识是否为只读模式
const isDisabled = ref(false)

// 从URL参数或store中获取学年信息
const getYearValueFromParams = () => {
  // 获取页面参数
  const options = getPageOptions()

  // 优先从URL参数中获取
  if (options.yearValue) {
    const urlYearValue = decodeURIComponent(options.yearValue)
    yearValue.value = urlYearValue
    formData.yearValue = urlYearValue
    console.log('从URL参数获取学年:', urlYearValue)
    return
  }

  // 如果URL参数中没有，则从store中获取
  const storeYearValue = attendLectureStore.selectedYear
  const storeTermValue = attendLectureStore.selectedTerm
  if (storeYearValue) {
    yearValue.value = `${storeYearValue}|${storeTermValue}`
    formData.yearValue = storeYearValue
    console.log('从store获取学年:', storeYearValue)
    return
  }

  console.log('未找到学年参数')
}

// 加载评价项目列表
const loadEvaluationProjects = () => {
  // 确保有学年学期值
  if (!yearValue.value) {
    console.warn('学年学期未设置，无法加载评价项目')
    return
  }

  loading.value = true

  getEvaluationProject({ semesters: yearValue.value })
    .then((res) => {
      console.log('获取评价项目成功:', res)
      // 保存评价项目列表
      evaluationItems.value = res.items
      // 解析评价项目
      parseEvaluationItems(res.items)
    })
    .catch((error) => {
      console.error('获取评价项目失败:', error)
      showErrorToast('获取评价项目失败')
    })
    .finally(() => {
      loading.value = false
    })
}

// 检查是否为编辑模式，如果是则从store获取听课记录数据并回填
const checkEditMode = () => {
  // 获取页面参数
  const options = getPageOptions()

  // 检查是否为只读模式
  if (options.disable === 'true') {
    isDisabled.value = true
    console.log('进入只读模式')
  }

  // 检查是否来自听课反馈信息页面
  if (options.fromFeedback === 'true') {
    isFromFeedback.value = true
    console.log('来自听课反馈信息页面')
  }

  // 检查是否有id参数和mode=edit参数
  if (options.id && options.mode === 'edit') {
    console.log('进入编辑模式，记录ID:', options.id)

    // 如果已经处理过编辑数据，就不再重复处理
    if (editDataProcessed.value) {
      console.log('编辑数据已处理，跳过重复处理')
      return true
    }

    // 从store获取完整的听课记录数据
    const lectureRecord = attendLectureStore.selectedLectureRecord

    if (!lectureRecord) {
      console.warn('没有找到对应的听课记录数据')
      return false
    }

    console.log('从store获取到听课记录数据:', lectureRecord)

    // 回填基本信息
    fillFormBasicData(lectureRecord)

    // 回填评价信息
    fillFormEvaluationData(lectureRecord)

    // 处理附件数据
    processAttachmentData(lectureRecord)

    // 重新加载听课日期节次数据
    if (formData.course) {
      loadLecturePeriods(Number(lectureRecord.teachingTasksId || 0))
    }

    // 数据处理完成后，清除store中的数据
    attendLectureStore.clearSelectedLectureRecord()
    console.log('数据处理完成，已清除store中的听课记录数据')

    // 标记编辑数据已处理
    editDataProcessed.value = true

    return true
  }
  return false
}

// 回填表单基本信息
const fillFormBasicData = (lectureRecord: any) => {
  formData.course = String(lectureRecord.teachingTasksId) // 教学任务ID
  formData.courseName = lectureRecord.courseName // 课程名称
  formData.courseCode = lectureRecord.courseCode // 课程代码
  formData.lecturePeriod = String(lectureRecord.teachingPlanId || '') // 听课日期节次
  formData.courseType = lectureRecord.courseType || '' // 课型
  formData.teachingContent = lectureRecord.teachingContent || '' // 教学内容
  formData.lectureRecord = lectureRecord.lectureRecord || '' // 听课记录
  formData.teachingProgress = lectureRecord.teachingProgressAndPlan || '' // 教学进度及教案查看情况
  formData.feedbackSituation = lectureRecord.teacherStudentFeedback || '' // 课间了解的教师、学生反映情况
  formData.suggestion = lectureRecord.lecturerOpinion || '' // 听课人意见及建议
  formData.yearValue = lectureRecord.lectureYear || '' // 学年

  workflowId.value = lectureRecord.id

  // 设置选中的课程，使课程选择器显示正确课程信息
  selectedCourse.value = {
    id: Number(lectureRecord.teachingTasksId || 0),
    jxrwid: Number(lectureRecord.teachingTasksId || 0), // 使用teachingTasksId替代jxrwid
    courseName: lectureRecord.courseName || '',
    courseCode: lectureRecord.courseCode || '',
  } as TeachOfficeTeachingTaskItem
}

// 回填表单评价信息
const fillFormEvaluationData = (lectureRecord: any) => {
  console.log(lectureRecord)

  // 设置教师信息
  formData.courseEvaluation.teacherName = lectureRecord.leaderTeacherName || ''

  // 设置其他评价与建议
  formData.courseEvaluation.otherSuggestion = lectureRecord.lectureEvaluation || ''

  // 检查是否已有评价数据
  if (lectureRecord.evaluationStatusName === '是') {
    console.log('记录已有评价数据，调用获取评价数据接口')
    // 调用获取评价数据接口
    loadExistingEvaluationData(lectureRecord.id)
  } else {
    // 如果没有评价数据，则加载评价项目
    console.log('记录没有评价数据，加载评价项目')
    loadEvaluationProjects()
  }
}

// 处理附件数据
const processAttachmentData = (lectureRecord: any) => {
  // 如果有附件数据，解析附件列表
  if (lectureRecord.attachmentList) {
    try {
      // 假设attachmentList是按"路径|名称"格式存储的字符串数组
      const attachments = lectureRecord.attachmentList.split(',').map((item: string) => {
        const [url, name] = item.split('|')
        return { url, name: name || '附件' }
      })

      attachmentList.value = attachments
      formData.attachments = attachments.map((item: any) => item.url)
      console.log('解析附件列表:', attachmentList.value)
    } catch (e) {
      console.error('解析附件列表失败:', e)
    }
  }
}

// 组件挂载时加载字典数据和获取学年参数
onMounted(() => {
  initPageData()
})

// 处理页面显示时的逻辑
onShow(() => {
  handleCourseSelection()
})

// 处理课程选择
const handleCourseSelection = () => {
  // 从store中获取选中的课程信息
  const storeCourse = attendLectureStore.selectedCourse

  // 获取页面参数
  const options = getPageOptions()

  // 如果是编辑模式但selectedCourse为空，尝试从checkEditMode重新加载数据
  // 只有在editDataProcessed为false时才调用checkEditMode
  if (options.mode === 'edit' && !selectedCourse.value && !editDataProcessed.value) {
    checkEditMode()
    return
  }

  // 如果store中有选中的课程信息，则设置到选课课程选择器中
  if (storeCourse && storeCourse.id) {
    console.log('从store获取选中课程信息:', storeCourse)

    // 创建一个TeachOfficeTeachingTaskItem对象，只设置必要的属性
    const courseItem: Partial<TeachOfficeTeachingTaskItem> = {
      id: Number(storeCourse.id),
      jxrwid: storeCourse.jxrwid,
      courseName: storeCourse.courseName || '',
      courseCode: storeCourse.courseCode || '',
    }

    // 设置选中的课程
    selectedCourse.value = courseItem as TeachOfficeTeachingTaskItem
    formData.course = String(courseItem.jxrwid)
    formData.courseName = courseItem.courseName || ''
    formData.courseCode = courseItem.courseCode || ''

    // 设置被听课教师名称
    if (storeCourse.teacherName) {
      formData.courseEvaluation.teacherName = storeCourse.teacherName
    }

    // 加载听课日期节次数据
    if (courseItem.jxrwid) {
      loadLecturePeriods(courseItem.jxrwid, String(courseItem.id))
    }

    // 清空store中的选中课程，避免下次进入页面时重复设置
    attendLectureStore.clearSelectedCourse()
  }

  // 如果不是编辑模式，也清除selectedLectureRecord，避免数据残留
  if (options.mode !== 'edit') {
    attendLectureStore.clearSelectedLectureRecord()
    console.log('非编辑模式，清除store中的听课记录数据')
  }
}

// 初始化页面数据
const initPageData = () => {
  getYearValueFromParams()
  initWorkflowParams() // 初始化工作流参数

  // 检查是否为编辑模式
  const isEdit = checkEditMode()
  console.log('isEdit', isEdit, yearValue.value)
  // 如果不是编辑模式，则正常加载评价项目
  if (!isEdit && yearValue.value) {
    loadEvaluationProjects()
  }
}
</script>

<template>
  <FormWithApproval
    ref="formWithApprovalRef"
    :id="workflowId"
    :code="workflowCode"
    :showWorkflow="!isFromFeedback"
    @return="handleApprovalReturn"
  >
    <template #form-content>
      <view class="min-h-auto p-0 bg-transparent">
        <view class="form-container">
          <!-- 只读模式提示 -->
          <view v-if="isDisabled" class="readonly-hint">
            <wd-icon name="info-circle" size="16px" class="mr-1"></wd-icon>
            <text>当前为只读模式，不可编辑</text>
          </view>

          <!-- 选课课程 -->
          <view class="form-section">
            <view class="section-title">基本信息</view>

            <!-- 选课课程 -->
            <view class="form-item">
              <view class="form-row-flex">
                <text class="form-label required">选课课程</text>
                <view class="form-content">
                  <view class="course-selection-container">
                    <view
                      class="picker-wrapper"
                      @click="!isDisabled && (showCourseSelector = true)"
                    >
                      <view class="picker-display" :class="{ disabled: isDisabled }">
                        <text v-if="selectedCourse">
                          {{ selectedCourse.courseCode }} - {{ selectedCourse.courseName }}
                        </text>
                        <text v-else class="placeholder">请选择课程</text>
                        <wd-icon
                          v-if="!isDisabled"
                          name="arrow-right"
                          size="16px"
                          class="text-gray-300"
                        ></wd-icon>
                      </view>
                    </view>
                    <view
                      v-if="!isDisabled"
                      class="teaching-log-btn"
                      @click="navigateToTeachingLog"
                    >
                      <wd-icon name="note" size="16px" class="mr-1"></wd-icon>
                      <text>授课日志</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 听课日期节次 -->
            <view class="form-item">
              <view class="form-row-flex">
                <text class="form-label required">听课日期节次</text>
                <view class="form-content">
                  <wd-picker
                    v-model="formData.lecturePeriod"
                    :columns="lecturePeriodOptions"
                    placeholder="请选择听课日期节次"
                    :disabled="loading || isDisabled"
                  >
                    <wd-cell
                      title="选择听课日期节次"
                      :value="
                        loading
                          ? '加载中...'
                          : lecturePeriodOptions.find(
                              (item) => item.value === formData.lecturePeriod,
                            )?.label
                      "
                      :is-link="!loading && !isDisabled"
                    />
                  </wd-picker>
                </view>
              </view>
            </view>

            <!-- 课型 -->
            <view class="form-item">
              <view class="form-row-flex">
                <text class="form-label required">课型</text>
                <view class="form-content">
                  <wd-picker
                    v-model="formData.courseType"
                    :columns="courseTypeOptions"
                    placeholder="请选择课型"
                    :disabled="loading || isDisabled"
                  >
                    <wd-cell
                      title="选择课型"
                      :value="
                        loading
                          ? '加载中...'
                          : courseTypeOptions.find((item) => item.value === formData.courseType)
                              ?.label
                      "
                      :is-link="!loading && !isDisabled"
                    />
                  </wd-picker>
                </view>
              </view>
            </view>

            <!-- 学年学期 -->
          </view>

          <!-- 听课详情 -->
          <view class="form-section">
            <view class="section-title">听课详情</view>

            <!-- 教学内容 -->
            <view class="form-item">
              <view class="form-row-column">
                <text class="form-label required">教学内容</text>
                <view class="form-content">
                  <textarea
                    class="form-textarea"
                    v-model="formData.teachingContent"
                    placeholder="请输入教学内容"
                    :disabled="isDisabled"
                  />
                </view>
              </view>
            </view>

            <!-- 听课记录 -->
            <view class="form-item">
              <view class="form-row-column">
                <text class="form-label required">听课记录</text>
                <view class="form-content">
                  <textarea
                    class="form-textarea"
                    v-model="formData.lectureRecord"
                    placeholder="请输入听课记录"
                    :disabled="isDisabled"
                  />
                </view>
              </view>
            </view>

            <!-- 授课进度及教案查看情况 -->
            <view class="form-item">
              <view class="form-row-column">
                <text class="form-label required">授课进度及教案查看情况</text>
                <view class="form-content">
                  <textarea
                    class="form-textarea"
                    v-model="formData.teachingProgress"
                    placeholder="请输入授课进度及教案查看情况"
                    :disabled="isDisabled"
                  />
                </view>
              </view>
            </view>

            <!-- 课间了解的教师、学生反映情况 -->
            <view class="form-item">
              <view class="form-row-column">
                <text class="form-label required">课间了解的教师、学生反映情况</text>
                <view class="form-content">
                  <textarea
                    class="form-textarea"
                    v-model="formData.feedbackSituation"
                    placeholder="请输入课间了解的教师、学生反映情况"
                    :disabled="isDisabled"
                  />
                </view>
              </view>
            </view>

            <!-- 听课人意见及建议 -->
            <view class="form-item">
              <view class="form-row-column">
                <text class="form-label required">听课人意见及建议</text>
                <view class="form-content">
                  <textarea
                    class="form-textarea"
                    v-model="formData.suggestion"
                    placeholder="请输入听课人意见及建议"
                    :disabled="isDisabled"
                  />
                </view>
              </view>
            </view>

            <!-- 附件上传 - 从独立区块移到这里 -->
            <view class="form-item">
              <view class="form-row-column">
                <text class="form-label">附件</text>
                <view class="form-content">
                  <view class="upload-area" v-if="!isDisabled">
                    <view class="upload-buttons">
                      <button
                        class="upload-button upload-image-button"
                        @click="handleUploadImage"
                        :disabled="uploadImageLoading"
                      >
                        <wd-icon name="camera" size="16px" class="mr-1"></wd-icon>
                        {{ uploadImageLoading ? '上传中...' : '上传图片' }}
                      </button>
                      <button class="upload-button" @click="handleUpload" :disabled="uploadLoading">
                        <wd-icon name="add" size="16px" class="mr-1"></wd-icon>
                        {{ uploadLoading ? '上传中...' : '上传附件' }}
                      </button>
                    </view>
                    <text class="upload-tip">
                      支持jpg、png、pdf、word、excel、ppt等常见文件格式
                    </text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 附件列表 -->
            <view class="attachment-list" v-if="attachmentList.length > 0">
              <view class="attachment-item" v-for="(file, index) in attachmentList" :key="index">
                <view class="attachment-info" @click="previewAttachment(file)">
                  <wd-icon name="file-icon" size="18px" class="mr-2"></wd-icon>
                  <text class="attachment-name">{{ file.name }}</text>
                </view>
                <view class="attachment-actions">
                  <view class="attachment-action" @click="previewAttachment(file)">
                    <wd-icon name="view" size="18px" color="#1890ff"></wd-icon>
                  </view>
                  <view
                    v-if="!isDisabled"
                    class="attachment-action"
                    @click="deleteAttachment(index)"
                  >
                    <wd-icon name="delete" size="18px" color="#ff4d4f"></wd-icon>
                  </view>
                </view>
              </view>
            </view>
            <view class="empty-tip" v-else>
              <text>暂无附件</text>
            </view>
          </view>

          <!-- 评价课程信息 -->
          <view class="form-section">
            <view class="section-title">评价课程信息</view>

            <!-- 课程信息展示 -->
            <view class="course-info-box">
              <view class="course-info-header">
                <text class="course-info-title">{{ formData.courseName || '请选择课程' }}</text>
                <text class="course-info-date">
                  {{ selectedCoursePeriod ? selectedCoursePeriod.skjhmc : '请选择听课日期节次' }}
                </text>
              </view>
              <view class="course-info-teacher">
                <text class="teacher-label">被听课教师：</text>
                <text class="teacher-name">{{ formData.courseEvaluation.teacherName }}</text>
              </view>
            </view>

            <!-- 评价表格 -->
            <view class="evaluation-table">
              <view class="table-header">
                <view class="th th-number">序号</view>
                <view class="th th-content">测评项目内容</view>
                <view class="th th-score">选择项</view>
              </view>

              <!-- 按题目类型分组显示 -->
              <template v-for="(group, groupIndex) in groupedEvaluationItems" :key="groupIndex">
                <!-- 题目类型标题 -->
                <view class="table-group-header" v-if="group.items.length > 0">
                  <text class="group-title">{{ group.title }}</text>
                </view>

                <!-- 题目列表 -->
                <view class="table-row" v-for="item in group.items" :key="item.id">
                  <view class="td td-number">{{ item.pxh }}</view>
                  <view class="td td-content">{{ item.content }}</view>
                  <view class="td td-score">
                    <wd-picker
                      v-model="item.score"
                      :columns="item.options"
                      placeholder=""
                      title="选择分数"
                      :show-value="false"
                      class="custom-picker"
                      :disabled="isDisabled"
                    >
                      <wd-cell
                        title=""
                        :value="item.score ? `${item.score}分` : '请选择'"
                        :is-link="!isDisabled"
                      />
                    </wd-picker>
                  </view>
                </view>
              </template>
            </view>

            <!-- 总分显示 -->
            <view class="total-score-container">
              <text class="total-score-text">总分：{{ totalScore }} 分</text>
            </view>

            <!-- 其它评价与建议 -->
            <view class="form-item">
              <view class="form-row-column">
                <text class="form-label">其它评价与建议</text>
                <view class="form-content">
                  <textarea
                    class="form-textarea"
                    v-model="formData.courseEvaluation.otherSuggestion"
                    placeholder="请输入其它评价与建议"
                    :disabled="isDisabled"
                  />
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 课程选择器弹窗 -->
        <wd-popup
          v-model="showCourseSelector"
          position="bottom"
          round
          custom-style="height: 80vh; border-radius: 32rpx 32rpx 0 0;"
        >
          <view class="popup-header">
            <view class="popup-title">选择课程</view>
            <wd-icon name="close" @click="showCourseSelector = false" size="20px"></wd-icon>
          </view>
          <view class="popup-content">
            <CourseSelector
              apiType="teachingTask"
              :semesters="yearValue"
              @select="handleCourseSelect"
            />
          </view>
        </wd-popup>
      </view>
    </template>

    <template #form-buttons>
      <!-- 提交按钮 - 只读模式下只显示返回按钮 -->
      <view class="button-group">
        <button v-if="!isDisabled" class="submit-button" @click="handleSubmit">提交</button>
        <button class="cancel-button" @click="handleBack">
          {{ isDisabled ? '返回' : '取消' }}
        </button>
      </view>
    </template>
  </FormWithApproval>
</template>

<style lang="scss" scoped>
.attend-lecture-add-container {
  min-height: auto;
  padding: 0;
  background-color: transparent;
}

.page-header {
  margin-bottom: 40rpx;

  .header-title {
    margin-bottom: 16rpx;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }

  .header-desc {
    font-size: 28rpx;
    color: #999;
  }
}

.form-container {
  width: 100%;
}

.form-section {
  box-sizing: border-box;
  padding: 24rpx;
  margin-bottom: 24rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  padding-bottom: 16rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  border-bottom: 1px solid #f0f0f0;
}

.form-item {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 20rpx;
}

.form-row-flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.form-row-column {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.form-label {
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333333;

  &.required::before {
    margin-right: 4rpx;
    color: #f5222d;
    content: '*';
  }
}

.form-content {
  box-sizing: border-box;
  width: 100%;
}

.form-input {
  box-sizing: border-box;
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}

.form-textarea {
  box-sizing: border-box;
  width: 100%;
  height: 180rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}

.picker-display {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border-radius: 8rpx;

  .placeholder {
    color: #999999;
  }
}

.button-group {
  display: flex;
  gap: 32rpx;
  justify-content: center;
  margin-top: 48rpx;
  margin-bottom: 80rpx;
}

.submit-button {
  width: 240rpx;
  height: 80rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  color: #ffffff;
  text-align: center;
  background-color: #1890ff;
  border-radius: 40rpx;
}

.cancel-button {
  width: 240rpx;
  height: 80rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  color: #666666;
  text-align: center;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 40rpx;
}

.upload-area {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.upload-buttons {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.upload-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 240rpx;
  height: 80rpx;
  font-size: 28rpx;
  color: #ffffff;
  background-color: #1890ff;
  border: none;
  border-radius: 40rpx;
}

.upload-image-button {
  background-color: #52c41a;
}

.upload-tip {
  margin-top: 12rpx;
  font-size: 24rpx;
  color: #999;
}

.attachment-list {
  width: 100%;
  margin-top: 24rpx;
}

.attachment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  margin-bottom: 16rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  transition: all 0.3s;
}

.attachment-item:hover {
  background-color: #e6f7ff;
}

.attachment-info {
  display: flex;
  align-items: center;
  max-width: 80%;
  cursor: pointer;
}

.attachment-name {
  overflow: hidden;
  font-size: 28rpx;
  color: #333;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.attachment-actions {
  display: flex;
  gap: 16rpx;
}

.attachment-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  cursor: pointer;
  background-color: #fff;
  border-radius: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.attachment-action:hover {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transform: translateY(-2rpx);
}

.empty-tip {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #999;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}
/* 弹窗样式 */
.popup-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 16rpx 24rpx;
  border-bottom: 1px solid #ebedf0;

  .popup-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }
}

.popup-content {
  height: calc(100% - 120rpx);
  overflow: hidden;
}
/* 课程评价部分样式 */
.course-info-box {
  box-sizing: border-box;
  width: 100%;
  padding: 20rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  background-color: #f8f9fc;
  border-radius: 8rpx;
}

.course-info-header {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-bottom: 12rpx;
}

.course-info-title {
  width: 100%;
  margin-bottom: 8rpx;
  overflow: hidden;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.course-info-date {
  width: 100%;
  overflow: hidden;
  font-size: 26rpx;
  color: #666;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.course-info-teacher {
  display: flex;
  align-items: center;
  width: 100%;
  margin-top: 8rpx;
  overflow: hidden;
  font-size: 28rpx;
}

.teacher-label {
  flex-shrink: 0;
  color: #666;
}

.teacher-name {
  overflow: hidden;
  font-weight: 500;
  color: #333;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.evaluation-table {
  width: 100%;
  margin-bottom: 24rpx;
  overflow: hidden;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}

.table-header {
  display: flex;
  font-weight: 500;
  color: #333;
  background-color: #f5f7fa;
}

.th,
.td {
  padding: 16rpx 12rpx;
  font-size: 26rpx;
  text-align: center;
  border-right: 1px solid #e8e8e8;
}

.th:last-child,
.td:last-child {
  border-right: none;
}

.th-number,
.td-number {
  width: 80rpx;
  min-width: 80rpx;
}

.th-content,
.td-content {
  flex: 1;
  text-align: left;
}

.th-score,
.td-score {
  width: 200rpx;
  min-width: 200rpx;
}

.table-row {
  display: flex;
  border-top: 1px solid #e8e8e8;
}

.table-row:nth-child(even) {
  background-color: #f9fafc;
}

.score-label {
  overflow: hidden;
  font-size: 26rpx;
  line-height: 60rpx;
  color: #333;
  text-align: center;
  white-space: nowrap;
}
/* 这里使用自定义类名来应用选择器样式，避免使用:deep语法 */
.custom-picker {
  width: 100%;
  height: 100%;
}

.custom-picker-input {
  padding: 0 !important;
  text-align: center !important;
  border: none !important;
}

.custom-picker-arrow {
  right: 4rpx !important;
}
/* 评分单元格样式优化 */
.td-score {
  padding: 0 !important;
}

.td-score .wd-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 0 !important;
  background-color: transparent;
  border: none;
}

.td-score .wd-cell::after {
  display: none;
}

.td-score .wd-cell__title {
  display: none;
}

.td-score .wd-cell__value {
  width: 100%;
  margin: 0 auto;
  font-size: 26rpx;
  color: #333;
  text-align: center;
}

.td-score .wd-cell__arrow {
  margin-right: 4rpx;
  margin-left: 4rpx;
}
/* 题目类型分组样式 */
.table-group-header {
  padding: 16rpx 12rpx;
  background-color: #f0f7ff;
  border-top: 1px solid #e8e8e8;
}

.group-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #1890ff;
}

.total-score-container {
  display: flex;
  justify-content: center;
  padding: 20rpx 0;
  border-top: 1px solid #e8e8e8;
}

.total-score-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.course-selection-container {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.picker-wrapper {
  flex: 1;
}

.teaching-log-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #ffffff;
  background-color: #52c41a;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

// 添加禁用样式
.disabled {
  color: #999;
  cursor: not-allowed;
  background-color: #f5f5f5;
}

// 只读模式提示样式
.readonly-hint {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #1890ff;
  background-color: #e6f7ff;
  border-radius: 8rpx;
}

// 禁用状态下的文本框样式
.form-textarea:disabled {
  color: #666;
  cursor: not-allowed;
  background-color: #f5f5f5;
}
</style>
