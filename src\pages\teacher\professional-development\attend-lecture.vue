<route lang="json5">
{
  style: {
    navigationBarTitleText: '教师听课信息',
  },
}
</route>
<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import * as echarts from 'echarts'
import { getAttendLectureList } from '@/service/teacher'
import {
  getAttendLectureStatistics,
  getAttendLectureList as getBeAttendLectureList,
} from '@/service/attendLecture'
import type { AttendLectureQuery, AttendLectureItem } from '@/types/teacher'
import type {
  AttendLectureQuery as BeAttendLectureQuery,
  AttendLectureItem as BeAttendLectureItem,
  AttendLectureResponse,
} from '@/types/attendLecture'
import SchoolYearPicker from '@/components/SchoolYearPicker/index.vue'
import Pagination from '@/components/Pagination/index.vue'
import SearchInput from '@/components/SearchInput/index.vue'
import { useAttendLectureStore } from '@/store/attend-lecture'
import { onShow } from '@dcloudio/uni-app'

// 获取听课记录store
const attendLectureStore = useAttendLectureStore()

// 分页参数
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 被听课记录相关参数
const beLecturePage = ref(1)
const beLecturePageSize = ref(10)
const beLectureTotal = ref(0)
const beLectureList = ref<BeAttendLectureItem[]>([])
const beLectureLoading = ref(false)

// 分段控制器选项
const segmentOptions = ['我的听课信息', '听课反馈信息']
const activeSegment = ref('我的听课信息')

// 搜索关键词
const searchKeyword = ref('')

// 学年选择
const yearValue = ref<string>('')

// 是否正在加载数据
const loading = ref(false)

// 听课记录列表
const lectureList = ref<AttendLectureItem[]>([])

// 统计数据
const statistics = ref({
  lectureCount: 0, // 听课次数
  feedbackCount: 0, // 听课反馈数
  pendingCount: 0, // 未审次数
  averageScore: 0.0, // 平均评价分
})

// 听课评分分布数据
const ratingDistribution = ref([
  { label: '教学内容', value: 0, percentage: 0 },
  { label: '教学方法', value: 0, percentage: 0 },
  { label: '教学效果', value: 0, percentage: 0 },
  { label: '教学态度', value: 0, percentage: 0 },
])

// 获取统计数据
const fetchStatistics = async () => {
  try {
    const res = await getAttendLectureStatistics()
    console.log('统计数据:', res)
    console.log('当前选择的学年:', yearValue.value)

    // 如果没有选择学年，并且有学年统计数据，则选择第一个学年
    if (!yearValue.value && res.xnStatistics && res.xnStatistics.length > 0) {
      const latestYearData = res.xnStatistics[0]
      // 从title中提取学年部分，例如从"2024-2025(1)"提取"2024-2025"
      const titleParts = latestYearData.title.split('(')
      if (titleParts.length > 0) {
        const year = titleParts[0]
        console.log('自动选择学年:', year)
        yearValue.value = year
        // 同步到store中
        attendLectureStore.setSelectedYear(year)
        // 加载对应学年的听课列表
        fetchLectureList()
      }
    }

    // 如果有选中的学年学期，则显示对应的统计数据
    if (yearValue.value && res.xnStatistics && res.xnStatistics.length > 0) {
      // 从yearValue中提取学年和学期信息
      // yearValue格式可能是"2024-2025"或"2024-2025|2"
      let year = yearValue.value
      let term = ''

      if (yearValue.value.includes('|')) {
        const parts = yearValue.value.split('|')
        year = parts[0]
        term = parts[1]
      }

      // 查找匹配当前选中学年学期的统计数据
      let currentYearStats = null

      // 如果有学期信息，尝试精确匹配学年和学期
      if (term) {
        // API返回的格式是"2024-2025(1)"，需要将"|2"转换为"(2)"进行匹配
        currentYearStats = res.xnStatistics.find((item) => item.title === `${year}(${term})`)
      }

      // 如果没有找到精确匹配，则尝试只匹配学年部分
      if (!currentYearStats) {
        currentYearStats = res.xnStatistics.find((item) => item.title.includes(year))
      }

      if (currentYearStats) {
        // 更新为当前学年学期的统计数据
        statistics.value = {
          lectureCount: currentYearStats.total || 0, // 听课总次数
          feedbackCount: currentYearStats.beTotal || 0, // 被听课数量
          pendingCount: currentYearStats.total - currentYearStats.alreadyTotal || 0, // 未审核数量
          averageScore: Number(currentYearStats.avgScore) || 0, // 平均评价分数，转换为数字
        }
        return
      }
    }

    // 如果没有选中学年学期或没有找到匹配的数据，则显示总的统计数据
    statistics.value = {
      lectureCount: res.total || 0, // 听课总次数
      feedbackCount: res.beTotal || 0, // 通过的评价数量
      pendingCount: res.auditTotal || 0, // 待审核数量
      averageScore: Number(res.evaluationScore) || 0, // 平均评价分数，转换为数字
    }
  } catch (error) {
    console.error('获取统计数据失败', error)
    uni.showToast({
      title: '获取统计数据失败',
      icon: 'none',
    })
  }
}

// 按照评价状态筛选显示的听课记录
const filteredLectureList = computed(() => {
  let filtered = lectureList.value

  // 按搜索关键词筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(
      (item) =>
        item.courseName.toLowerCase().includes(keyword) ||
        item.lectureTeacherName.toLowerCase().includes(keyword) ||
        item.className.toLowerCase().includes(keyword) ||
        item.deptName.toLowerCase().includes(keyword),
    )
  }

  // 根据分段控制器筛选
  if (activeSegment.value === '我的听课信息') {
    // 显示我的听课信息，保持原有逻辑
    return filtered
  } else if (activeSegment.value === '听课反馈信息') {
    // 暂时返回空数组，因为听课反馈信息暂不实现
    return []
  }

  return filtered
})

// 获取听课记录列表
const fetchLectureList = async () => {
  try {
    loading.value = true

    const params: AttendLectureQuery = {
      page: page.value,
      pageSize: pageSize.value,
      semesters: yearValue.value ? yearValue.value : '', // 使用选择的学年
      // 使用搜索关键词，根据实际接口定义匹配
      courseName: searchKeyword.value || undefined,
      // 其他筛选条件可根据需要添加
    }

    const res = await getAttendLectureList(params)
    lectureList.value = res.items
    total.value = res.total

    // 计算评分分布
    calculateRatings()

    loading.value = false
  } catch (error) {
    console.error('获取听课记录失败', error)
    uni.showToast({
      title: '获取数据失败，请稍后重试',
      icon: 'none',
    })
    loading.value = false
  }
}

// 计算评分分布
const calculateRatings = () => {
  // 这里根据实际数据结构来计算评分分布
  // 由于API返回的数据中可能没有详细的评分分布，这里暂时简化处理
  // 实际项目中应根据具体业务需求调整

  const evaluatedItems = lectureList.value.filter((item) => item.evaluationStatus === '是')
  if (evaluatedItems.length === 0) {
    return
  }

  // 假设评分都在evaluationScore字段中
  const avgScore =
    evaluatedItems.reduce((sum, item) => sum + item.evaluationScore, 0) / evaluatedItems.length

  // 随机分配到各个维度（实际应用中应根据真实数据计算）
  ratingDistribution.value = [
    {
      label: '教学内容',
      value: Number((avgScore * 1.02).toFixed(1)),
      percentage: Math.min(Number((avgScore * 1.02 * 20).toFixed(0)), 100),
    },
    {
      label: '教学方法',
      value: Number((avgScore * 0.98).toFixed(1)),
      percentage: Math.min(Number((avgScore * 0.98 * 20).toFixed(0)), 100),
    },
    {
      label: '教学效果',
      value: Number((avgScore * 0.95).toFixed(1)),
      percentage: Math.min(Number((avgScore * 0.95 * 20).toFixed(0)), 100),
    },
    {
      label: '教学态度',
      value: Number((avgScore * 1.05).toFixed(1)),
      percentage: Math.min(Number((avgScore * 1.05 * 20).toFixed(0)), 100),
    },
  ]

  // 更新图表
}

// 获取被听课记录列表
const fetchBeLectureList = async () => {
  try {
    beLectureLoading.value = true

    const params: BeAttendLectureQuery = {
      page: beLecturePage.value,
      pageSize: beLecturePageSize.value,
      semesters: yearValue.value ? yearValue.value : '', // 使用选择的学年
      giveLessonsDate: [], // 空数组，可根据需要填充
      // 使用搜索关键词，根据实际接口定义匹配
      courseName: searchKeyword.value || undefined,
      // 其他筛选条件可根据需要添加
    }

    const res = await getBeAttendLectureList(params)
    beLectureList.value = res.items
    beLectureTotal.value = res.total

    beLectureLoading.value = false
  } catch (error) {
    console.error('获取被听课记录失败', error)
    uni.showToast({
      title: '获取数据失败，请稍后重试',
      icon: 'none',
    })
    beLectureLoading.value = false
  }
}

// 切换分段控制器
const changeSegment = (segment: string) => {
  activeSegment.value = segment

  // 当切换到听课反馈信息时，加载被听课记录列表
  if (segment === '听课反馈信息') {
    fetchBeLectureList()
  } else {
    fetchLectureList()
  }
}

// 学年变化处理
const handleYearChange = (data: { label: string; value: string }) => {
  console.log('学年变化:', data)
  yearValue.value = data.value
  // 同步到store中
  attendLectureStore.setSelectedYear(data.value)
  page.value = 1
  beLecturePage.value = 1 // 重置被听课页码
  // 重新加载数据
  fetchLectureList()
  // 如果当前是听课反馈标签页，也需要刷新被听课列表
  if (activeSegment.value === '听课反馈信息') {
    fetchBeLectureList()
  }
  // 重新获取统计数据
  fetchStatistics()
}

// 搜索处理
const handleSearch = (value: string) => {
  console.log('搜索:', value)
  searchKeyword.value = value
  // 重新加载数据
  fetchLectureList()
  // 如果当前是听课反馈标签页，也需要刷新被听课列表
  if (activeSegment.value === '听课反馈信息') {
    fetchBeLectureList()
  }
}

// 清除搜索处理
const handleClearSearch = () => {
  console.log('清除搜索')
  if (searchKeyword.value) {
    searchKeyword.value = ''
    // 重新加载数据
    fetchLectureList()
    // 如果当前是听课反馈标签页，也需要刷新被听课列表
    if (activeSegment.value === '听课反馈信息') {
      fetchBeLectureList()
    }
  }
}

// 页码变化处理
const handlePageChange = (newPage: number) => {
  page.value = newPage
  fetchLectureList()
}

// 被听课分页变化处理
const handleBeLecturePageChange = (newPage: number) => {
  beLecturePage.value = newPage
  fetchBeLectureList()
}

// 获取状态文本
const getStatusText = (status: string) => {
  return status === '是' ? '已评价' : '未评价'
}

// 获取状态样式
const getStatusClass = (status: string) => {
  return status === '通过' ? 'bg-success' : 'bg-warning'
}

// 格式化日期和节次
const formatDateTime = (date: string, section: string) => {
  return `${date} ${section}节`
}

// 下拉刷新
const onPullDownRefresh = () => {
  const promises = [fetchLectureList(), fetchStatistics()]

  // 如果当前是听课反馈标签页，也需要刷新被听课列表
  if (activeSegment.value === '听课反馈信息') {
    promises.push(fetchBeLectureList())
  }

  Promise.all(promises).then(() => {
    uni.stopPullDownRefresh()
  })
}

// 格式化听课内容（简化显示）
const formatTeachingContent = (content: string) => {
  if (!content) return '未记录'
  // 如果内容太长，截取前30个字符
  return content.length > 30 ? content.substring(0, 30) + '...' : content
}

// 跳转到新增听课记录页面
const navigateToAddLecture = () => {
  // 构建URL，将当前选择的学年作为参数传递
  const url = `/pages/teacher/professional-development/attend-lecture-add?yearValue=${encodeURIComponent(yearValue.value)}`

  // 同时更新store中的学年信息
  attendLectureStore.setSelectedYear(yearValue.value)

  uni.navigateTo({
    url,
    success: () => {
      console.log('跳转到新增听课记录页面成功，传递学年:', yearValue.value)
    },
    fail: (err) => {
      console.error('跳转到新增听课记录页面失败', err)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none',
      })
    },
  })
}

// 跳转到听课记录编辑页面
const navigateToEditLecture = (lecture: AttendLectureItem, fromFeedback: boolean = false) => {
  console.log('跳转到编辑页面，听课记录：', lecture)

  // 将完整的听课记录数据存入store
  attendLectureStore.setSelectedLectureRecord(lecture)

  // 同时保持原有的courseData存储兼容原有逻辑
  const courseData = {
    id: lecture.id,
    jxrwid: lecture.teachingTasksId,
    courseName: lecture.courseName,
    courseCode: lecture.courseCode,
    teacherName: lecture.lectureTeacherName,
    className: lecture.className,
    location: lecture.siteName || '',
    dateTime: {
      date: lecture.giveLessonsDate,
      weekday: '', // 这里没有weekday数据，传空字符串
      time: '', // 这里没有time数据，传空字符串
      section: lecture.sectionShow || '',
    },
  }

  // 将选中的课程信息存入store
  attendLectureStore.setSelectedCourse(courseData)

  // 同时设置学年信息
  attendLectureStore.setSelectedYear(lecture.lectureYear)
  attendLectureStore.setSelectedTerm(lecture.lectureTerm)

  // 构建URL，将当前听课记录ID作为参数传递，添加fromFeedback参数
  const url = `/pages/teacher/professional-development/attend-lecture-add?id=${lecture.id}&mode=edit${fromFeedback ? '&fromFeedback=true' : ''}`

  uni.navigateTo({
    url,
    success: () => {
      console.log('跳转到编辑听课记录页面成功，传递ID:', lecture.id, '来自反馈页面:', fromFeedback)
    },
    fail: (err) => {
      console.error('跳转到编辑听课记录页面失败', err)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none',
      })
    },
  })
}

// 跳转到听课记录详情页面（只读模式）
const navigateToViewLecture = (lecture: AttendLectureItem, fromFeedback: boolean = false) => {
  console.log('跳转到详情页面，听课记录：', lecture)

  // 将完整的听课记录数据存入store
  attendLectureStore.setSelectedLectureRecord(lecture)

  // 同时保持原有的courseData存储兼容原有逻辑
  const courseData = {
    id: lecture.id,
    jxrwid: lecture.teachingTasksId,
    courseName: lecture.courseName,
    courseCode: lecture.courseCode,
    teacherName: lecture.lectureTeacherName,
    className: lecture.className,
    location: lecture.siteName || '',
    dateTime: {
      date: lecture.giveLessonsDate,
      weekday: '', // 这里没有weekday数据，传空字符串
      time: '', // 这里没有time数据，传空字符串
      section: lecture.sectionShow || '',
    },
  }

  // 将选中的课程信息存入store
  attendLectureStore.setSelectedCourse(courseData)

  // 同时设置学年信息
  attendLectureStore.setSelectedYear(lecture.lectureYear)
  attendLectureStore.setSelectedTerm(lecture.lectureTerm)

  // 构建URL，将当前听课记录ID作为参数传递，并添加disable=true参数和fromFeedback参数
  const url = `/pages/teacher/professional-development/attend-lecture-add?id=${lecture.id}&mode=edit&disable=true${fromFeedback ? '&fromFeedback=true' : ''}`

  uni.navigateTo({
    url,
    success: () => {
      console.log('跳转到查看听课记录页面成功，传递ID:', lecture.id, '来自反馈页面:', fromFeedback)
    },
    fail: (err) => {
      console.error('跳转到查看听课记录页面失败', err)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none',
      })
    },
  })
}

// 处理听课卡片点击事件
const handleCardClick = (lecture: AttendLectureItem) => {
  console.log('听课卡片点击，状态:', lecture.auditStatusName)

  // 如果状态是"通过"，进入查看模式，否则进入编辑模式
  if (lecture.auditStatusName === '通过') {
    navigateToViewLecture(lecture)
  } else {
    navigateToEditLecture(lecture)
  }
}

// 处理被听课卡片点击事件
const handleBeLectureCardClick = (lecture: BeAttendLectureItem) => {
  console.log('被听课卡片点击，状态:', lecture.auditStatusName)

  // 如果状态是"通过"，进入查看模式，否则进入编辑模式
  if (lecture.auditStatusName === '通过') {
    // 这里使用与听课记录相同的查看函数，传递fromFeedback=true
    navigateToViewLecture(lecture as unknown as AttendLectureItem, true)
  } else {
    // 这里使用与听课记录相同的编辑函数，传递fromFeedback=true
    navigateToEditLecture(lecture as unknown as AttendLectureItem, true)
  }
}

const isFirstLoad = ref(true)
// 页面加载后初始化数据
onMounted(() => {
  fetchStatistics() // 获取统计数据
})

// 实现onShow生命周期钩子
onShow(() => {
  if (isFirstLoad.value) {
    isFirstLoad.value = false
    return
  }
  // 刷新统计数据
  fetchStatistics()

  // 根据当前选中的标签页刷新相应的列表数据
  if (activeSegment.value === '我的听课信息') {
    fetchLectureList()
  } else if (activeSegment.value === '听课反馈信息') {
    fetchBeLectureList()
  }
})
</script>

<template>
  <view class="attend-lecture-container pt-4">
    <!-- 学年选择器 -->
    <view class="mb-2 mx-4">
      <SchoolYearPicker
        v-model:yearValue="yearValue"
        @change="handleYearChange"
        allYearLabel="全部学年"
        size="large"
      />
    </view>

    <!-- 统计分析 -->
    <view class="section statistics mx-4 mb-4">
      <view class="statistics-container">
        <view class="stat-card">
          <view class="stat-label">听课次数</view>
          <view class="stat-value">{{ statistics.lectureCount }}</view>
        </view>
        <view class="stat-card">
          <view class="stat-label">听课反馈数</view>
          <view class="stat-value">{{ statistics.feedbackCount }}</view>
        </view>
        <view class="stat-card">
          <view class="stat-label">未审次数</view>
          <view class="stat-value">{{ statistics.pendingCount }}</view>
        </view>
        <view class="stat-card">
          <view class="stat-label">平均评价分</view>
          <view class="stat-value">{{ statistics.averageScore }}</view>
        </view>
      </view>
    </view>

    <!-- 分段控制器 -->
    <view class="segment-container px-4 mt-4">
      <view class="segment-group">
        <view
          v-for="(segment, index) in segmentOptions"
          :key="index"
          class="segment-item"
          :class="{ active: activeSegment === segment }"
          @click="changeSegment(segment)"
        >
          {{ segment }}
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 搜索组件 -->
      <view class="search-container mb-4">
        <SearchInput
          v-model="searchKeyword"
          placeholder="搜索课程名称"
          :debounceDelay="300"
          :showHint="false"
          class="custom-search-input"
          @search="handleSearch"
          @clear="handleClearSearch"
        ></SearchInput>
      </view>

      <!-- 加载中提示 -->
      <view v-if="loading" class="loading-container">
        <wd-icon name="refresh" size="24" class="loading-icon" />
        <text>加载中...</text>
      </view>

      <!-- 我的听课信息内容 -->
      <view v-else-if="activeSegment === '我的听课信息'">
        <!-- 空数据提示 -->
        <view v-if="filteredLectureList.length === 0" class="empty-container">
          <wd-icon name="browse" size="40" class="empty-icon" />
          <text>暂无听课记录</text>
        </view>

        <!-- 听课卡片列表 -->
        <view v-else class="lecture-list">
          <view
            v-for="lecture in filteredLectureList"
            :key="lecture.id"
            class="lecture-card"
            @click="handleCardClick(lecture)"
          >
            <view class="lecture-card-header">
              <view class="info-group">
                <view class="course-name">{{ lecture.courseName }}</view>
                <view class="teacher-info">{{ lecture.teacherName }} · {{ lecture.deptName }}</view>
              </view>
              <view class="status-tag" :class="getStatusClass(lecture.auditStatusName)">
                {{ lecture.auditStatusName }}
              </view>
            </view>

            <view class="lecture-card-content">
              <!-- 添加学年学期信息 -->
              <view class="info-item">
                <view class="info-label">学年学期:</view>
                <view class="info-value">
                  {{ lecture.lectureYear }}学年第{{ lecture.lectureTerm }}学期
                </view>
              </view>
              <view class="info-item">
                <view class="info-label">听课时间:</view>
                <view class="info-value">
                  {{ formatDateTime(lecture.giveLessonsDate, lecture.sectionShow) }}
                </view>
              </view>
              <view class="info-item">
                <view class="info-label">听课地点:</view>
                <view class="info-value">{{ lecture.siteName || '未记录' }}</view>
              </view>
              <view class="info-item">
                <view class="info-label">听课班级:</view>
                <view class="info-value">{{ lecture.className }}</view>
              </view>
              <view class="info-item">
                <view class="info-label">教学内容:</view>
                <view class="info-value">{{ formatTeachingContent(lecture.teachingContent) }}</view>
              </view>

              <!-- 只有已评价的课程才显示评分 -->
              <view
                class="info-item"
                v-if="lecture.evaluationStatus === '是' && lecture.evaluationScore"
              >
                <view class="info-label">综合评分:</view>
                <view class="info-value rating-display">
                  <view class="star-container">
                    <wd-icon
                      v-for="i in 5"
                      :key="i"
                      :name="
                        i <= Math.floor(lecture.evaluationScore / 20)
                          ? 'star-on'
                          : i <= lecture.evaluationScore / 20
                            ? 'star-on'
                            : 'star'
                      "
                      size="16"
                      class="star-icon"
                    />
                  </view>
                  <text class="rating-text">{{ lecture.evaluationScore }}</text>
                </view>
              </view>

              <!-- 添加评价意见显示 -->
              <view class="info-item">
                <view class="info-label">评价分:</view>
                <view class="info-value comment-text">
                  {{ lecture.evaluationScore || '未评分' }}
                </view>
              </view>
              <view class="info-item">
                <view class="info-label">建议意见:</view>
                <view class="info-value comment-text">
                  {{ lecture.lecturerOpinion || '无评价意见' }}
                </view>
              </view>

              <!-- 添加提交时间显示 -->
              <view class="info-item">
                <view class="info-label">提交时间:</view>
                <view class="info-value">{{ lecture.create_time }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 听课反馈信息内容 -->
      <view v-else-if="activeSegment === '听课反馈信息'" class="feedback-container">
        <!-- 加载中提示 -->
        <view v-if="beLectureLoading" class="loading-container">
          <wd-icon name="refresh" size="24" class="loading-icon" />
          <text>加载中...</text>
        </view>

        <!-- 空数据提示 -->
        <view v-else-if="beLectureList.length === 0" class="empty-container">
          <wd-icon name="browse" size="40" class="empty-icon" />
          <text>暂无听课反馈记录</text>
        </view>

        <!-- 被听课卡片列表 -->
        <view v-else class="lecture-list">
          <view
            v-for="lecture in beLectureList"
            :key="lecture.id"
            class="lecture-card"
            @click="handleBeLectureCardClick(lecture)"
          >
            <view class="lecture-card-header">
              <view class="info-group">
                <view class="course-name">{{ lecture.courseName }}</view>
                <view class="teacher-info">
                  {{ lecture.deptName }}
                </view>
              </view>
              <view class="status-tag" :class="getStatusClass(lecture.auditStatusName)">
                {{ lecture.auditStatusName }}
              </view>
            </view>

            <view class="lecture-card-content">
              <!-- 添加学年学期信息 -->
              <view class="info-item">
                <view class="info-label">学年学期:</view>
                <view class="info-value">
                  {{ lecture.lectureYear }}学年第{{ lecture.lectureTerm }}学期
                </view>
              </view>
              <view class="info-item">
                <view class="info-label">听课时间:</view>
                <view class="info-value">
                  {{ formatDateTime(lecture.giveLessonsDate, lecture.sectionShow) }}
                </view>
              </view>
              <view class="info-item">
                <view class="info-label">听课地点:</view>
                <view class="info-value">{{ lecture.siteName || '未记录' }}</view>
              </view>
              <view class="info-item">
                <view class="info-label">听课班级:</view>
                <view class="info-value">{{ lecture.className }}</view>
              </view>
              <view class="info-item">
                <view class="info-label">教学内容:</view>
                <view class="info-value">{{ formatTeachingContent(lecture.teachingContent) }}</view>
              </view>

              <!-- 只有已评价的课程才显示评分 -->
              <view
                class="info-item"
                v-if="lecture.evaluationStatus === '是' && lecture.evaluationScore"
              >
                <view class="info-label">综合评分:</view>
                <view class="info-value rating-display">
                  <view class="star-container">
                    <wd-icon
                      v-for="i in 5"
                      :key="i"
                      :name="
                        i <= Math.floor(lecture.evaluationScore / 20)
                          ? 'star-on'
                          : i <= lecture.evaluationScore / 20
                            ? 'star-on'
                            : 'star'
                      "
                      size="16"
                      class="star-icon"
                    />
                  </view>
                  <text class="rating-text">{{ lecture.evaluationScore }}</text>
                </view>
              </view>

              <!-- 添加评价意见显示 -->
              <view class="info-item">
                <view class="info-label">评价分:</view>
                <view class="info-value comment-text">
                  {{ lecture.evaluationScore || '未评分' }}
                </view>
              </view>
              <view class="info-item">
                <view class="info-label">建议意见:</view>
                <view class="info-value comment-text">
                  {{ lecture.lecturerOpinion || '无评价意见' }}
                </view>
              </view>

              <!-- 添加提交时间显示 -->
              <view class="info-item">
                <view class="info-label">提交时间:</view>
                <view class="info-value">{{ lecture.create_time }}</view>
              </view>
            </view>
          </view>
        </view>

        <!-- 分页器 -->
        <view class="pagination-container" v-if="beLectureTotal > 0">
          <Pagination
            :page="beLecturePage"
            :total="beLectureTotal"
            :pageSize="beLecturePageSize"
            @update:page="handleBeLecturePageChange"
          />
        </view>
      </view>

      <!-- 新增按钮 -->
      <view
        v-if="activeSegment === '我的听课信息'"
        class="add-button"
        @click="navigateToAddLecture"
      >
        <wd-icon name="add" size="28" class="add-icon" />
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.attend-lecture-container {
  position: relative;
  min-height: 100vh;
  padding-bottom: 100rpx;
  background-color: #f7f8fa;
}

// 统计分析样式
.section {
  margin-bottom: 32rpx;
}

.statistics-container {
  display: flex;
  gap: 16rpx;
  justify-content: space-between;

  .stat-card {
    flex: 1;
    padding: 24rpx 12rpx;
    text-align: center;
    background-color: #fff;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .stat-label {
      margin-bottom: 12rpx;
      font-size: 26rpx;
      color: #999;
    }

    .stat-value {
      font-size: 32rpx;
      font-weight: bold;
      color: #465cff;
    }
  }
}

// 分段控制器样式
.segment-container {
  margin-bottom: 20rpx;

  .segment-group {
    display: flex;
    overflow: hidden;
    background-color: #fff;
    border-radius: 8rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .segment-item {
      flex: 1;
      padding: 20rpx 0;
      font-size: 28rpx;
      color: #666;
      text-align: center;
      border-bottom: 4rpx solid transparent;
      transition: all 0.3s;

      &.active {
        font-weight: 500;
        color: #1890ff;
        border-bottom-color: #1890ff;
      }
    }
  }
}

// 内容区域样式
.content-container {
  padding: 0 30rpx;
}

// 搜索容器样式
.search-container {
  background-color: #fff;
  border-radius: 12rpx;
}

.custom-search-input {
  :deep(.search-input) {
    &:focus {
      border-color: #3a8eff;
      box-shadow: 0 0 0 2px rgba(58, 142, 255, 0.1);
    }
  }
}

// 加载中和空数据样式
.loading-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  color: #999;
  text-align: center;

  .loading-icon {
    margin-bottom: 20rpx;
    animation: rotate 1.5s linear infinite;
  }

  .empty-icon {
    margin-bottom: 20rpx;
    color: #ccc;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 听课卡片样式
.lecture-list {
  margin-bottom: 30rpx;

  .lecture-card {
    margin-bottom: 30rpx;
    overflow: hidden;
    cursor: pointer; /* 添加指针样式 */
    background-color: #fff;
    border-radius: 16rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease; /* 添加过渡效果 */
    /* 添加悬停效果 */
    &:hover {
      box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.1);
      transform: translateY(-2rpx);
    }
    /* 添加点击效果 */
    &:active {
      background-color: #f5f7fa;
      transform: scale(0.99);
    }

    .lecture-card-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 30rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .info-group {
        .course-name {
          margin-bottom: 8rpx;
          font-size: 32rpx;
          font-weight: 500;
          color: #333;
        }

        .teacher-info {
          font-size: 24rpx;
          color: #999;
        }
      }

      .status-tag {
        padding: 6rpx 16rpx;
        font-size: 24rpx;
        color: #fff;
        border-radius: 8rpx;

        &.bg-success {
          background-color: #4caf50;
        }

        &.bg-warning {
          background-color: #ff9800;
        }

        &.bg-danger {
          background-color: #f44336;
        }

        &.bg-info {
          background-color: #2196f3;
        }
      }
    }

    .lecture-card-content {
      padding: 30rpx;

      .info-item {
        display: flex;
        margin-bottom: 16rpx;
        font-size: 28rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .info-label {
          width: 140rpx;
          color: #999;
        }

        .info-value {
          flex: 1;
          color: #333;

          &.comment-text {
            line-height: 1.4;
            word-break: break-all;
          }
        }

        .rating-display {
          display: flex;
          align-items: center;

          .star-container {
            display: flex;
            margin-right: 16rpx;

            .star-icon {
              margin-right: 4rpx;
              color: #ffb400;
            }
          }

          .rating-text {
            font-weight: 500;
            color: #333;
          }
        }
      }
    }
  }
}

// 添加按钮样式
.add-button {
  position: fixed;
  right: 40rpx;
  bottom: 160rpx;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100rpx;
  height: 100rpx;
  background-color: #1890ff;
  border-radius: 50rpx;
  box-shadow: 0 4rpx 20rpx rgba(24, 144, 255, 0.4);

  .add-icon {
    color: #fff;
  }
}

// 分页容器样式
.pagination-container {
  display: flex;
  justify-content: center;
  margin: 24rpx 0;
}

// 听课反馈信息样式
.feedback-container {
  min-height: 400rpx;
}
</style>
