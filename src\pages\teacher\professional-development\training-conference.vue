<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的培训会议',
  },
}
</route>
<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { getTrainingConferenceList } from '@/service/training'
import type { TrainingConferenceItem } from '@/types/training'
import SemesterWeekPicker from '@/components/SemesterWeekPicker/index.vue'
import Pagination from '@/components/Pagination/index.vue'
import SearchInput from '@/components/SearchInput/index.vue'

/**
 * 标签页选项
 */
const tabs = reactive(['全部', '培训', '会议', '讲座'])
const activeTab = ref(0)

/**
 * 搜索框相关
 */
const searchValue = ref('')

/**
 * 分页相关
 */
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

/**
 * 培训会议列表数据
 */
const trainingList = ref<TrainingConferenceItem[]>([])

/**
 * 统计数据
 */
const statistics = reactive([
  {
    label: '参培总数',
    value: 0,
  },
  {
    label: '已获学时',
    value: 0,
  },
  {
    label: '缺勤次数',
    value: 0,
  },
])

/**
 * 学期周次选择相关
 */
const semesterValue = ref<string>('')
const weekValue = ref<number | null>(null)

/**
 * 处理学期周次变化
 */
const handleSemesterWeekChange = (data: {
  semester: { value: string }
  week: { value: number | null }
}) => {
  console.log('学期周次变化:', data)
  // 重新加载数据，根据学期和周次筛选
  loadTrainingConferenceList()
}

/**
 * 获取状态标签文本
 */
const getStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    upcoming: '即将开始',
    ongoing: '进行中',
    completed: '已完成',
    canceled: '已取消',
  }
  return statusMap[status] || status
}

/**
 * 获取培训类型标签文本
 */
const getTypeText = (type: string): string => {
  const typeMap: Record<string, string> = {
    online: '线上',
    offline: '线下',
    hybrid: '混合式',
  }
  return typeMap[type] || type
}

/**
 * 获取状态标签类名
 */
const getStatusClass = (status: string): string => {
  const classMap: Record<string, string> = {
    upcoming: 'status-upcoming',
    ongoing: 'status-ongoing',
    completed: 'status-completed',
    canceled: 'status-canceled',
  }
  return classMap[status] || ''
}

/**
 * 获取类型标签类名
 */
const getTypeClass = (type: string): string => {
  const classMap: Record<string, string> = {
    online: 'type-online',
    offline: 'type-offline',
    hybrid: 'type-hybrid',
  }
  return classMap[type] || ''
}

/**
 * 处理标签页变化
 */
const handleTabChange = (event: any) => {
  console.log('标签页切换:', event)
  // 切换标签时重新加载数据
  activeTab.value = event.index
  loadTrainingConferenceList()
}

/**
 * 获取培训会议图标
 */
const getTrainingIcon = (item: TrainingConferenceItem): { name: string; color: string } => {
  // 根据项目类型或名称判断使用哪个图标
  if (item.projectName.includes('培训')) {
    return { name: 'computer', color: '#2196f3' }
  } else if (item.projectName.includes('会议')) {
    return { name: 'usergroup', color: '#f44336' }
  } else if (item.projectName.includes('讲座')) {
    return { name: 'note', color: '#9c27b0' }
  } else {
    return { name: 'subscribe', color: '#4caf50' }
  }
}

/**
 * 获取培训会议类型
 */
const getTrainingType = (item: TrainingConferenceItem): string => {
  if (item.projectDescription.includes('线上') || item.projectDescription.includes('腾讯会议')) {
    return 'online'
  } else if (item.projectDescription.includes('线下')) {
    return 'offline'
  } else {
    return 'hybrid' // 默认为混合式
  }
}

/**
 * 获取培训会议状态
 */
const getTrainingStatus = (item: TrainingConferenceItem): string => {
  const now = new Date().getTime()
  const endTimeStr = item.endTime

  // 如果已签退，视为已完成
  if (item.signOutStatus === '已签退') {
    return 'completed'
  }

  // 如果有结束时间，可以判断是否已结束
  if (endTimeStr) {
    try {
      const endTime = new Date(endTimeStr).getTime()
      if (endTime < now) {
        return 'completed'
      } else {
        return 'upcoming'
      }
    } catch (e) {
      // 如果日期解析出错，则根据签到状态判断
      return item.signInStatus === '已签到' ? 'ongoing' : 'upcoming'
    }
  }

  return 'upcoming' // 默认状态为即将开始
}

/**
 * 处理搜索操作
 */
const handleSearch = (value: string) => {
  console.log('搜索:', value)
  // 重新加载数据，根据搜索关键词筛选
  loadTrainingConferenceList()
}

/**
 * 处理清除搜索
 */
const handleClearSearch = () => {
  console.log('清除搜索')
  // 如果当前有搜索关键词，则清除后重新加载数据
  if (searchValue.value) {
    searchValue.value = ''
  }
  loadTrainingConferenceList()
}

/**
 * 加载培训会议列表数据
 */
const loadTrainingConferenceList = async () => {
  try {
    loading.value = true
    const params = {
      page: currentPage.value,
      pageSize: pageSize.value,
      sortBy: 'id',
      sortOrder: 'desc',
      semesters: semesterValue.value ? [semesterValue.value] : [], // 使用选定的学期
      deptName: '',
      projectName: searchValue.value, // 搜索条件
      auditNumber: '',
      projectTreatmentValue: '',
      responsibleTeacherName: '',
      signInStatus: '',
      signInTime: '',
      signOutStatus: '',
      signOutTime: '',
      participationStatus: '',
    }

    const res = await getTrainingConferenceList(params)

    // 更新数据
    trainingList.value = res.items
    total.value = res.total

    // 更新统计数据
    updateStatistics(res.items)
  } catch (error) {
    console.error('获取培训会议列表失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 更新统计数据
 */
const updateStatistics = (items: TrainingConferenceItem[]) => {
  const totalCount = items.length

  // 计算总学时
  let totalHours = 0
  items.forEach((item) => {
    if (item.projectTreatmentValue) {
      totalHours += parseFloat(item.projectTreatmentValue) || 0
    }
  })

  // 计算缺勤次数
  const absenceCount = items.filter(
    (item) =>
      item.participationStatus !== '有效' ||
      (item.signInStatus !== '已签到' && new Date(item.endTime) < new Date()),
  ).length

  // 更新统计数据
  statistics[0].value = totalCount
  statistics[1].value = totalHours
  statistics[2].value = absenceCount
}

/**
 * 获取培训会议详情按钮
 */
const getTrainingActions = (item: TrainingConferenceItem): string[] => {
  const actions = ['查看详情']

  // 如果已完成并且有效，可以获取证书
  if (item.participationStatus === '有效' && item.signOutStatus === '已签退') {
    actions.push('获取证书')
  }

  // 如果未开始，可以添加到日历
  if (getTrainingStatus(item) === 'upcoming') {
    actions.unshift('添加到日历')
  }

  return actions
}

/**
 * 处理页码变化
 */
const handlePageChange = (page: number) => {
  currentPage.value = page
  loadTrainingConferenceList()
}

/**
 * 页面加载时获取数据
 */
onMounted(() => {
  loadTrainingConferenceList()
})
</script>

<template>
  <view class="training-conference">
    <!-- 学期周次选择器 -->
    <view class="mb-2">
      <SemesterWeekPicker
        v-model:semesterValue="semesterValue"
        v-model:weekValue="weekValue"
        @change="handleSemesterWeekChange"
        :showAllSemester="true"
        :format-semester="true"
        :show-week="false"
        size="large"
      />
    </view>

    <!-- 统计信息 -->
    <view class="section">
      <view class="statistics">
        <view class="stat-card" v-for="(stat, index) in statistics" :key="index">
          <view class="stat-label">{{ stat.label }}</view>
          <view class="stat-value">{{ stat.value }}</view>
        </view>
      </view>
    </view>

    <!-- 搜索栏 -->
    <view class="search-container">
      <SearchInput
        v-model="searchValue"
        placeholder="搜索培训或会议名称"
        :debounceDelay="300"
        :showHint="false"
        class="custom-search-input"
        @search="handleSearch"
        @clear="handleClearSearch"
      ></SearchInput>
    </view>

    <!-- 分段控制器 -->
    <!--    <view class="tabs-container">
      <wd-tabs v-model="activeTab" @change="handleTabChange" auto-line-width>
        <wd-tab v-for="(item, index) in tabs" :key="index" :title="item"></wd-tab>
      </wd-tabs>
    </view>-->

    <!-- 培训会议列表 -->
    <view class="section">
      <wd-loading v-if="loading" />
      <view v-else-if="trainingList.length === 0" class="empty-state">
        <wd-icon name="warning" size="64rpx" color="#999"></wd-icon>
        <text>暂无培训会议数据</text>
      </view>

      <view v-else v-for="item in trainingList" :key="item.id" class="training-card">
        <view class="training-card-header">
          <view class="training-card-title">
            <wd-icon
              :name="getTrainingIcon(item).name"
              :color="getTrainingIcon(item).color"
            ></wd-icon>
            <text class="title-text">{{ item.projectName }}</text>
          </view>
          <view :class="['training-type', getTypeClass(getTrainingType(item))]">
            {{ getTypeText(getTrainingType(item)) }}
          </view>
        </view>
        <view class="training-card-content">
          <view class="training-card-info">
            <view class="training-time">
              <wd-icon name="time" size="14px"></wd-icon>
              <text>{{ item.startTime }}</text>
            </view>
            <view :class="['status-tag', getStatusClass(getTrainingStatus(item))]">
              {{ getStatusText(getTrainingStatus(item)) }}
            </view>
          </view>

          <view class="training-item" v-if="item.deptName">
            <view class="label">组织方:</view>
            <view class="value">{{ item.deptName }}</view>
          </view>

          <view class="training-item" v-if="item.openDeptName">
            <view class="label">参与部门:</view>
            <view class="value">{{ item.openDeptName }}</view>
          </view>

          <view class="training-item" v-if="item.responsibleTeacherName">
            <view class="label">负责人:</view>
            <view class="value">{{ item.responsibleTeacherName }}</view>
          </view>

          <view class="training-item" v-if="item.projectTreatmentValue">
            <view class="label">培训学时:</view>
            <view class="value">{{ item.projectTreatmentValue }}</view>
          </view>

          <view class="training-item">
            <view class="label">签到状态:</view>
            <view class="value attendance">{{ item.signInStatus }}</view>
          </view>

          <view class="training-item" v-if="item.signOutStatus">
            <view class="label">签退状态:</view>
            <view class="value attendance">{{ item.signOutStatus }}</view>
          </view>

          <!-- <view class="training-actions">
            <wd-button
              v-for="(action, idx) in getTrainingActions(item)"
              :key="idx"
              size="small"
              :plain="idx !== 0"
              custom-class="action-btn"
            >
              {{ action }}
            </wd-button>
          </view> -->
        </view>
      </view>
    </view>

    <!-- 分页器 -->
    <view class="pagination-container" v-if="total > 0">
      <Pagination
        :page="currentPage"
        :total="total"
        :pageSize="pageSize"
        @update:page="handlePageChange"
      />
    </view>
  </view>
</template>

<style scoped lang="scss">
.training-conference {
  min-height: 100vh;
  padding: 24rpx;
  background-color: #f5f5f5;
}

.tabs-container {
  margin-bottom: 24rpx;
  background-color: #fff;
  border-radius: 12rpx;
}

.search-container {
  margin-bottom: 24rpx;
  background-color: #fff;
  border-radius: 12rpx;
}

.section {
  margin-bottom: 32rpx;

  .section-title {
    margin-bottom: 16rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  text-align: center;
  background-color: #fff;
  border-radius: 12rpx;

  text {
    font-size: 28rpx;
    color: #999;
  }
}

.training-card {
  margin-bottom: 24rpx;
  overflow: hidden;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.training-card-header {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 24rpx;
  background-color: #f9f9f9;
  border-bottom: 1px solid #eee;
}

.training-card-title {
  display: flex;
  flex: 1;
  gap: 12rpx;
  align-items: center;
  min-width: 0;
  max-width: 75%;
  font-size: 32rpx;
  font-weight: 500;

  .title-text {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.training-card-content {
  padding: 24rpx;
}

.training-card-info {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;

  .training-time {
    display: flex;
    gap: 8rpx;
    align-items: center;
    min-width: 0;
    font-size: 28rpx;
    color: #999;
    white-space: nowrap;

    text {
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}

.training-item {
  display: flex;
  margin-bottom: 16rpx;
  font-size: 28rpx;

  .label {
    flex-shrink: 0;
    width: 140rpx;
    color: #999;
  }

  .value {
    flex: 1;
    overflow: hidden;
    color: #333;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .attendance {
    color: #4caf50;
  }
}

.training-actions {
  display: flex;
  gap: 16rpx;
  justify-content: flex-end;
  margin-top: 24rpx;

  .action-btn {
    font-size: 28rpx;
  }
}

.training-type {
  padding: 4rpx 16rpx;
  font-size: 24rpx;
  color: white;
  white-space: nowrap;
  border-radius: 24rpx;
}

.type-online {
  background-color: #2196f3;
}

.type-offline {
  background-color: #f44336;
}

.type-hybrid {
  background-color: #9c27b0;
}

.status-tag {
  padding: 4rpx 16rpx;
  font-size: 24rpx;
  color: white;
  white-space: nowrap;
  border-radius: 8rpx;
}

.status-upcoming {
  background-color: #ff9800;
}

.status-ongoing {
  background-color: #4caf50;
}

.status-completed {
  background-color: #9e9e9e;
}

.status-canceled {
  background-color: #f44336;
}

.statistics {
  display: flex;
  gap: 24rpx;
  justify-content: space-between;

  .stat-card {
    flex: 1;
    padding: 24rpx;
    text-align: center;
    background-color: #fff;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

    .stat-label {
      margin-bottom: 12rpx;
      font-size: 28rpx;
      color: #999;
    }

    .stat-value {
      font-size: 36rpx;
      font-weight: bold;
      color: #465cff;
    }
  }
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin: 24rpx 0;
}

.custom-search-input {
  :deep(.search-input) {
    &:focus {
      border-color: #3a8eff;
      box-shadow: 0 0 0 2px rgba(58, 142, 255, 0.1);
    }
  }
}
</style>
