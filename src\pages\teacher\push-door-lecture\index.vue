<route lang="json5">
{
  style: {
    navigationBarTitleText: '推门听课',
  },
}
</route>
<template>
  <view class="push-door-lecture-container">
    <view class="p-2">
      <view class="flex items-center">
        <view class="flex-1">
          <SchoolYearPicker
            ref="schoolYearPickerRef"
            v-model:yearValue="yearValue"
            @yearChange="handleYearChange"
            :showYearLabel="false"
            size="large"
          />
        </view>
      </view>
    </view>
    <!-- 搜索栏 -->
    <view class="search-bar bg-white p-2 flex items-center">
      <view class="flex items-center flex-1 bg-gray-100 rounded px-4 py-2">
        <wd-icon name="search" size="18" color="#666666" />
        <input
          class="flex-1 ml-2 text-sm"
          type="text"
          placeholder="搜索课程名称"
          v-model="searchKeyword"
          @input="handleSearchInput"
        />
      </view>
      <view class="ml-3" @click="showFilter = true">
        <wd-icon name="filter" size="24" color="#333333" />
      </view>
    </view>
    <view class="flex bg-white gap-1 pb-2 px-2 items-center justify-between">
      <view class="flex items-center">
        <view
          class="px-3 py-1 rounded-full text-sm mr-2 flex items-center justify-center"
          :class="
            quickDateFilter === 'today' ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600'
          "
          @click="setQuickDateFilter('today')"
        >
          今日
        </view>
        <view
          class="px-3 py-1 rounded-full text-sm flex items-center justify-center"
          :class="
            quickDateFilter === 'thisWeek' ? 'bg-primary text-white' : 'bg-gray-100 text-gray-600'
          "
          @click="setQuickDateFilter('thisWeek')"
        >
          本周
        </view>
      </view>
      <!-- 校区选择器 -->
      <view class="flex items-center">
        <wd-picker
          v-model="queryParams.ssxq"
          :columns="campusOptions"
          @confirm="handleCampusChange"
          placeholder="请选择校区"
        >
          <view class="flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-600">
            <text class="mr-1">{{ getCurrentCampusName() }}</text>
            <wd-icon name="chevron-down" size="14" color="#666666" />
          </view>
        </wd-picker>
      </view>
    </view>

    <!-- 筛选标签 -->
    <!-- <view class="filter-tags bg-white p-4 border-b border-gray-100">
      <view class="flex overflow-x-auto">
        <view class="bg-primary text-white px-3 py-1 rounded-full text-sm mr-2 whitespace-nowrap">
          全部
        </view>
        <view
          class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm mr-2 whitespace-nowrap"
        >
          今日课程
        </view>
        <view
          class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm mr-2 whitespace-nowrap"
        >
          本周课程
        </view>
        <view
          class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm mr-2 whitespace-nowrap"
        >
          数学学院
        </view>
        <view
          class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm mr-2 whitespace-nowrap"
        >
          计算机学院
        </view>
      </view>
    </view> -->

    <!-- 主要内容区域 -->
    <view class="content-area p-4">
      <view class="flex justify-between mb-4">
        <text class="text-sm text-gray-500">找到 {{ total }} 门课程</text>
        <!-- <view class="flex items-center">
          <text class="text-sm text-gray-500 mr-2">排序:</text>
          <text class="text-sm text-primary font-medium">时间</text>
        </view> -->
      </view>

      <!-- 课程列表 -->
      <view class="course-list">
        <!-- 循环渲染课程列表 -->
        <view
          v-for="(item, index) in teachingLogList"
          :key="index"
          class="bg-white rounded-2xl p-4 shadow-sm mb-3"
        >
          <view class="flex justify-between h-full">
            <view class="flex-1 flex flex-col justify-between">
              <view>
                <text class="font-semibold text-lg text-gray-800 block mb-1">
                  {{ item.kcmc || '未知课程' }}
                </text>
                <text class="text-gray-600 text-sm block mb-2">
                  {{ item.skjsxm || '未知教师' }} · {{ item.ssxbmc || '未知学院' }}
                </text>
                <text class="text-gray-600 text-sm block mb-2">
                  <wd-icon name="location" size="14" color="#666666" class="mr-1" />
                  {{ item.skcdmc || '未知地点' }}
                </text>
                <text class="text-sm bg-blue-100 text-blue-600 px-2 py-1 rounded-lg text-xs mr-2">
                  {{ item.skfsmc || '未知授课方式' }}
                </text>
              </view>
              <view class="flex flex-wrap mt-2">
                <text class="bg-blue-100 text-blue-600 px-2 py-1 rounded-lg text-xs mr-2">
                  {{ item.skbjmc || '未知班级' }}
                </text>
                <text class="bg-gray-100 text-gray-600 px-2 py-1 rounded-lg text-xs mr-2">
                  {{ item.syrs || 0 }}人
                </text>
                <text class="px-2 py-1 rounded-lg text-xs" :class="getStatusClass(item.skrq)">
                  {{ getStatusText(item.skrq) }}
                </text>
              </view>
            </view>
            <view class="ml-4 flex flex-col">
              <view>
                <text class="text-sm text-gray-500 block mb-2">
                  {{ formatDate(item.skrq) }}
                </text>
                <text class="text-sm text-gray-500 block mb-2">
                  {{ formatWeekday(item.xqs) }}
                </text>
                <text class="text-sm text-gray-600 block">{{ item.jcshow || '节次未知' }}节</text>
                <!-- <text class="text-sm font-medium text-gray-800 block mb-2">
                  {{ formatTime(item.skkssj, item.skjssj) }}
                </text> -->
              </view>
              <view
                class="px-3 py-1 rounded-lg text-sm text-center mt-2"
                :class="getActionButtonClass(item.skrq)"
                @click="handleAction(item)"
              >
                听课
              </view>
            </view>
          </view>
        </view>

        <!-- 加载更多 -->
        <view v-if="loading" class="flex justify-center items-center py-4">
          <wd-loading size="24" color="#4338ca" />
          <text class="ml-2 text-sm text-gray-500">加载中...</text>
        </view>

        <!-- 没有更多数据 -->
        <view
          v-if="!loading && teachingLogList.length === 0"
          class="flex justify-center items-center py-8"
        >
          <text class="text-gray-400">暂无数据</text>
        </view>
      </view>

      <!-- 分页组件 -->
      <Pagination
        v-if="total > 0"
        v-model:page="queryParams.page"
        :total="total"
        :pageSize="queryParams.pageSize"
        @update:page="handlePageChange"
      />
    </view>

    <!-- 筛选弹窗 -->
    <wd-popup v-model="showFilter" position="right">
      <view class="filter-popup">
        <!-- 筛选弹窗内容 -->
        <view class="p-4">
          <view class="flex justify-between items-center mb-4">
            <text class="text-lg font-bold">筛选条件</text>
            <wd-icon name="close" size="20" @click="showFilter = false" />
          </view>

          <!-- 筛选表单 -->
          <view class="form-container">
            <!-- 校区 -->
            <view class="form-item">
              <view class="form-row-flex">
                <text class="form-label">校区</text>
                <view class="form-content">
                  <wd-picker
                    v-model="queryParams.ssxq"
                    :columns="campusOptions"
                    placeholder="请选择校区"
                    @confirm="handleCampusChange"
                  >
                    <wd-cell title="选择校区" :value="getCurrentCampusName()" is-link />
                  </wd-picker>
                </view>
              </view>
            </view>

            <!-- 状态 -->
            <view class="form-item">
              <view class="form-row-flex">
                <text class="form-label">状态</text>
                <view class="form-content">
                  <wd-picker
                    v-model="queryParams.status"
                    :columns="statusOptions"
                    placeholder="请选择状态"
                    @confirm="handleStatusChange"
                  >
                    <wd-cell
                      title="选择状态"
                      :value="queryParams.status !== 'all' ? '已选择' : '全部状态'"
                      is-link
                    />
                  </wd-picker>
                </view>
              </view>
            </view>

            <!-- 星期 -->
            <view class="form-item">
              <view class="form-row-flex">
                <text class="form-label">星期</text>
                <view class="form-content">
                  <wd-picker
                    v-model="queryParams.xqs"
                    :columns="weekdayOptions"
                    placeholder="请选择星期"
                    @confirm="handleWeekdayChange"
                  >
                    <wd-cell
                      title="选择星期"
                      :value="queryParams.xqs ? '已选择' : '请选择'"
                      is-link
                    />
                  </wd-picker>
                </view>
              </view>
            </view>

            <!-- 时间范围 -->
            <view class="form-item">
              <view class="form-row-flex">
                <text class="form-label">时间范围</text>
                <view class="form-content">
                  <view class="date-range-container">
                    <view class="date-picker-item">
                      <text class="date-label">开始日期</text>
                      <view class="date-input" @click="openStartDatePicker">
                        {{ startDateText || '请选择开始日期' }}
                      </view>
                    </view>
                    <view class="date-picker-item">
                      <text class="date-label">结束日期</text>
                      <view class="date-input" @click="openEndDatePicker">
                        {{ endDateText || '请选择结束日期' }}
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
            <!-- 部门 -->
            <view class="form-item">
              <view class="form-row-flex">
                <text class="form-label">部门</text>
                <view class="form-content">
                  <wd-picker
                    v-model="queryParams.ssxb"
                    :columns="departmentOptions"
                    placeholder="请选择部门"
                    @confirm="handleDepartmentChange"
                  >
                    <wd-cell
                      title="选择部门"
                      :value="queryParams.ssxb ? '已选择' : '请选择'"
                      is-link
                    />
                  </wd-picker>
                </view>
              </view>
            </view>

            <!-- 场地 -->
            <view class="form-item">
              <view class="form-row-flex">
                <text class="form-label">场地</text>
                <view class="form-content">
                  <input class="form-input" placeholder="请输入场地" v-model="queryParams.skcdmc" />
                </view>
              </view>
            </view>

            <!-- 节次 -->
            <view class="form-item">
              <view class="form-row-flex">
                <text class="form-label">节次</text>
                <view class="form-content">
                  <input
                    class="form-input"
                    placeholder="请输入节次"
                    v-model="queryParams.jcshow"
                    type="number"
                    @input="validateSectionInput"
                  />
                </view>
              </view>
            </view>
            <!-- 课程名称 -->
            <view class="form-item">
              <view class="form-row-flex">
                <text class="form-label">课程名称</text>
                <view class="form-content">
                  <input
                    class="form-input"
                    placeholder="请输入课程名称"
                    v-model="queryParams.kcmc"
                  />
                </view>
              </view>
            </view>

            <!-- 班级名称 -->
            <view class="form-item">
              <view class="form-row-flex">
                <text class="form-label">班级名称</text>
                <view class="form-content">
                  <input
                    class="form-input"
                    placeholder="请输入班级名称"
                    v-model="queryParams.skbjmc"
                  />
                </view>
              </view>
            </view>

            <!-- 确认按钮 -->
            <view class="button-group">
              <button class="cancel-button" @click="resetFilter">重置</button>
              <button class="submit-button" @click="applyFilter">确定</button>
            </view>
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 日期选择器弹出层 -->
    <wd-popup v-model="showStartDatePicker" position="bottom">
      <view class="calendar-container">
        <view class="calendar-header">
          <text class="calendar-title">选择开始日期</text>
          <wd-icon name="close" size="20" @click="showStartDatePicker = false" />
        </view>
        <wd-calendar-view v-model="startDate" type="date" @change="handleStartDateChange" />
        <view class="calendar-footer">
          <wd-button type="primary" block @click="confirmStartDate">确定</wd-button>
        </view>
      </view>
    </wd-popup>

    <wd-popup v-model="showEndDatePicker" position="bottom">
      <view class="calendar-container">
        <view class="calendar-header">
          <text class="calendar-title">选择结束日期</text>
          <wd-icon name="close" size="20" @click="showEndDatePicker = false" />
        </view>
        <wd-calendar-view v-model="endDate" type="date" @change="handleEndDateChange" />
        <view class="calendar-footer">
          <wd-button type="primary" block @click="confirmEndDate">确定</wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { getTeachingLogManageList } from '@/service/teachingLog'
import { getCampusList } from '@/service/campus'
import { getSubstanceListWithParams } from '@/service/organization'
import type { TeachingLogManageQueryParams, TeachingLogManageItem } from '@/types/teachingLog'
import { useAttendLectureStore } from '@/store/attend-lecture'
import SchoolYearPicker from '@/components/SchoolYearPicker/index.vue'
import Pagination from '@/components/Pagination/index.vue'
import { loadDictData, getDictOptions } from '@/utils/dict'

// 获取听课记录store
const attendLectureStore = useAttendLectureStore()

// 学年学期选择器
const yearValue = ref('')

// 快速日期筛选
const quickDateFilter = ref('')

// 查询参数
const queryParams = ref<TeachingLogManageQueryParams>({
  page: 1,
  pageSize: 10,
  semesters: '',
  status: 'all',
  // 筛选条件字段
  ssxq: '',
  timeRange: '',
  skrq: [] as string[], // 确保是字符串数组类型
  xqs: '',
  jcshow: '',
  skcdmc: '',
  ssxb: '' as string, // 明确指定为字符串类型
  kcmc: '',
  skbjmc: '',
})

// 搜索关键词
const searchKeyword = ref('')

// 是否显示筛选弹窗
const showFilter = ref(false)

// 教学日志列表
const teachingLogList = ref<TeachingLogManageItem[]>([])

// 总数
const total = ref(0)

// 加载状态
const loading = ref(false)

// 学年学期选择器引用
const schoolYearPickerRef = ref()

// 筛选条件选项
const campusOptions = ref<{ label: string; value: string }[]>([{ label: '全部校区', value: '' }])

const timeRangeOptions = ref([
  { label: '全部时间', value: '' },
  { label: '今天', value: 'today' },
  { label: '明天', value: 'tomorrow' },
  { label: '本周', value: 'thisWeek' },
  { label: '下周', value: 'nextWeek' },
])

// 星期选项，将使用字典数据替换
const weekdayOptions = ref([{ label: '全部星期', value: '' }])

// 场地选项
const locationOptions = ref([
  { label: '全部场地', value: '' },
  { label: '教学楼A', value: 'A' },
  { label: '教学楼B', value: 'B' },
  { label: '教学楼C', value: 'C' },
  { label: '实验楼', value: 'Lab' },
])

// 部门选项
const departmentOptions = ref([{ label: '全部部门', value: '' }])

// 状态选项
const statusOptions = ref([
  { label: '全部状态', value: 'all' },
  { label: '已执行', value: '1' },
  { label: '未执行', value: '0' },
  { label: '教师确认', value: 'jsqk' },
  { label: '教师未确认', value: 'jswqk' },
  { label: '已认定', value: '3' },
  { label: '未认定', value: '4' },
  { label: '学生未确认', value: 'xswqk' },
  { label: '已执行未认定', value: '5' },
])

// 日期选择相关
const startDate = ref<number>(0)
const endDate = ref<number>(0)
const startDateText = ref<string>('')
const endDateText = ref<string>('')
const showStartDatePicker = ref(false)
const showEndDatePicker = ref(false)

// 添加日期数组变量，用于存储日期范围
const dateRange = ref<string[]>(['', ''])

// 打开开始日期选择器
const openStartDatePicker = () => {
  showStartDatePicker.value = true
}

// 打开结束日期选择器
const openEndDatePicker = () => {
  showEndDatePicker.value = true
}

// 处理开始日期选择变化
const handleStartDateChange = (e: any) => {
  startDate.value = e.value
}

// 处理结束日期选择变化
const handleEndDateChange = (e: any) => {
  endDate.value = e.value
}

// 确认开始日期
const confirmStartDate = () => {
  const date = new Date(startDate.value)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const formattedDate = `${year}-${month}-${day}`

  startDateText.value = formattedDate
  dateRange.value[0] = formattedDate
  // 更新skrq字段
  updateSkrqField()
  // 重置页码为1
  queryParams.value.page = 1
  showStartDatePicker.value = false
}

// 确认结束日期
const confirmEndDate = () => {
  const date = new Date(endDate.value)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const formattedDate = `${year}-${month}-${day}`

  endDateText.value = formattedDate
  dateRange.value[1] = formattedDate
  // 更新skrq字段
  updateSkrqField()
  // 重置页码为1
  queryParams.value.page = 1
  showEndDatePicker.value = false
}

// 更新skrq字段，将日期范围直接使用数组格式
const updateSkrqField = () => {
  if (dateRange.value[0] && dateRange.value[1]) {
    queryParams.value.skrq = dateRange.value
  } else {
    queryParams.value.skrq = [] as string[] // 修复类型错误，确保是字符串数组
  }
}

// 加载字典数据
const loadDicts = async () => {
  try {
    const dicts = await loadDictData(['SYS_WEEK'])
    if (dicts.SYS_WEEK) {
      // 获取星期选项并添加"全部星期"选项
      const weekOptions = getDictOptions(dicts.SYS_WEEK)
      weekdayOptions.value = [{ label: '全部星期', value: '' }, ...weekOptions]
    }
  } catch (error) {
    console.error('加载字典数据失败:', error)
    uni.showToast({
      title: '加载字典数据失败',
      icon: 'none',
    })
  }
}

// 获取校区列表
const fetchCampusList = async () => {
  try {
    const campusList = await getCampusList({ format: 'select' })
    // 添加全部校区选项
    campusOptions.value = [
      { label: '全部校区', value: '' },
      ...campusList.map((item) => ({
        label: item.campusName,
        value: item.campusCode,
      })),
    ]
  } catch (error) {
    console.error('获取校区列表失败:', error)
    uni.showToast({
      title: '获取校区数据失败',
      icon: 'none',
    })
  }
}

// 获取部门列表
const fetchDepartmentList = async () => {
  try {
    const res = await getSubstanceListWithParams({ isSubstance: 1 })
    // 添加全部部门选项
    departmentOptions.value = [
      { label: '全部部门', value: '' },
      ...res.map((item) => ({
        label: item.name,
        value: item.code,
      })),
    ]
  } catch (error) {
    console.error('获取部门列表失败:', error)
    uni.showToast({
      title: '获取部门数据失败',
      icon: 'none',
    })
  }
}

// 获取教学日志列表
const fetchTeachingLogList = async () => {
  try {
    loading.value = true
    const res = await getTeachingLogManageList(queryParams.value)
    teachingLogList.value = res.items
    total.value = res.total
  } catch (error) {
    console.error('获取教学日志列表失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 处理学年学期变化
const handleYearChange = (data: { value: string; label: string }) => {
  queryParams.value.semesters = data.value
  queryParams.value.page = 1 // 重置页码为1
  fetchTeachingLogList()
}

// 添加快速日期筛选方法
const setQuickDateFilter = (type: string) => {
  // 如果点击的是已选中的选项，则清除选择
  if (quickDateFilter.value === type) {
    quickDateFilter.value = ''
    // 清除日期范围和显示
    startDateText.value = ''
    endDateText.value = ''
    dateRange.value = ['', '']
    queryParams.value.skrq = [] as string[]
  } else {
    quickDateFilter.value = type
    const today = new Date()

    if (type === 'today') {
      // 设置为今天
      const year = today.getFullYear()
      const month = String(today.getMonth() + 1).padStart(2, '0')
      const day = String(today.getDate()).padStart(2, '0')
      const formattedDate = `${year}-${month}-${day}`

      // 更新日期显示和日期范围
      startDateText.value = formattedDate
      endDateText.value = formattedDate
      dateRange.value = [formattedDate, formattedDate]
      queryParams.value.skrq = [formattedDate, formattedDate]
    } else if (type === 'thisWeek') {
      // 设置为本周（周一到周日）
      const currentDay = today.getDay() || 7 // 将周日的0转为7
      const monday = new Date(today)
      monday.setDate(today.getDate() - (currentDay - 1))

      const sunday = new Date(today)
      sunday.setDate(today.getDate() + (7 - currentDay))

      const mondayFormatted = `${monday.getFullYear()}-${String(monday.getMonth() + 1).padStart(2, '0')}-${String(monday.getDate()).padStart(2, '0')}`
      const sundayFormatted = `${sunday.getFullYear()}-${String(sunday.getMonth() + 1).padStart(2, '0')}-${String(sunday.getDate()).padStart(2, '0')}`

      // 更新日期显示和日期范围
      startDateText.value = mondayFormatted
      endDateText.value = sundayFormatted
      dateRange.value = [mondayFormatted, sundayFormatted]
      queryParams.value.skrq = [mondayFormatted, sundayFormatted]
    }
  }

  // 重置页码并获取数据
  queryParams.value.page = 1
  // 添加延迟以确保值已更新
  setTimeout(() => {
    fetchTeachingLogList()
  }, 0)
}

// 重置筛选条件
const resetFilter = () => {
  queryParams.value = {
    page: 1,
    pageSize: 10,
    sortBy: 'id',
    sortOrder: 'desc',
    semesters: yearValue.value,
    status: 'all',
    ssxq: '',
    timeRange: '',
    skrq: [] as string[], // 修复类型错误，确保是字符串数组
    xqs: '',
    jcshow: '',
    skcdmc: '',
    ssxb: '',
    kcmc: searchKeyword.value,
    skbjmc: '',
  }
  // 清空日期显示文本和日期范围
  startDateText.value = ''
  endDateText.value = ''
  dateRange.value = ['', '']
  quickDateFilter.value = '' // 重置快速筛选状态
}

// 应用筛选条件
const applyFilter = () => {
  queryParams.value.page = 1
  if (searchKeyword.value) {
    queryParams.value.kcmc = searchKeyword.value
  }
  // 确保更新skrq字段
  updateSkrqField()
  showFilter.value = false
  // 添加延迟以确保值已更新
  setTimeout(() => {
    fetchTeachingLogList()
  }, 0)
}

// 根据状态获取样式类
const getStatusClass = (dateStr: string) => {
  const courseDate = new Date(dateStr.split(' ')[0])
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  if (courseDate.getDate() === today.getDate()) {
    return 'bg-green-500 text-white font-medium' // 进行中，使用更鲜明的绿色
  } else if (courseDate < today) {
    return 'bg-gray-500 text-white font-medium' // 已结束，使用更鲜明的灰色
  } else {
    return 'bg-blue-500 text-white font-medium' // 未开始，使用更鲜明的蓝色
  }
}

// 根据状态获取文本
const getStatusText = (dateStr: string) => {
  const courseDate = new Date(dateStr.split(' ')[0])
  const today = new Date()

  if (courseDate.getDate() === today.getDate()) {
    return '进行中'
  } else if (courseDate < today) {
    return '已结束'
  } else {
    return '未开始'
  }
}

// 根据状态获取操作按钮样式
const getActionButtonClass = (dateStr: string) => {
  return 'bg-primary text-white'
}

// 格式化时间
const formatTime = (startTime: string, endTime: string) => {
  if (!startTime || !endTime) return '时间未知'

  // 提取时间部分 HH:MM
  const startHourMin = startTime.split(' ')[1]?.slice(0, 5)
  const endHourMin = endTime.split(' ')[1]?.slice(0, 5)

  return startHourMin && endHourMin ? `${startHourMin}-${endHourMin}` : '时间未知'
}

// 格式化日期 YYYY-MM-DD
const formatDate = (dateTimeStr: string) => {
  if (!dateTimeStr) return '日期未知'

  const datePart = dateTimeStr.split(' ')[0]
  if (!datePart) return '日期未知'

  const [year, month, day] = datePart.split('-')
  return `${year}-${month}-${day}`
}

// 格式化星期
const formatWeekday = (weekday: number) => {
  if (!weekday) return '星期未知'

  const weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日']
  return weekdays[weekday - 1]
}

// 处理操作按钮点击
const handleAction = (item: TeachingLogManageItem) => {
  console.log('操作课程:', item)
  // 根据状态执行不同操作
  switch (item.skjhzxzt) {
    case '1':
      uni.navigateTo({
        url: `/pages/teacher/professional-development/attend-lecture-add?id=${item.id}&yearValue=${encodeURIComponent(yearValue.value)}&fromPage=push-door-lecture`,
      })
      break
    case '2':
      uni.showToast({
        title: '预约听课功能开发中',
        icon: 'none',
      })
      break
    case '0':
    default:
      // 将选中的课程信息保存到store
      attendLectureStore.setSelectedCourse({
        id: item.id,
        jxrwid: item.jxrwid,
        courseName: item.kcmc || '未知课程',
        courseCode: item.kcdm || '',
        teacherName: item.skjsxm || '未知教师',
        className: item.skbjmc || '未知班级',
        location: item.skcdmc || '未知地点',
        dateTime: {
          date: formatDate(item.skkssj),
          weekday: formatWeekday(Number(item.xqs)), // 修复类型错误
          time: formatTime(item.skkssj, item.skjssj),
          section: item.jcshow || '节次未知',
        },
      })
      // 返回上一页并传递学年学期参数和来源页面参数
      uni.redirectTo({
        url: `/pages/teacher/professional-development/attend-lecture-add?yearValue=${encodeURIComponent(yearValue.value)}&fromPage=push-door-lecture`,
      })
      break
  }
}

// 验证节次输入，只允许输入0以上的数字
const validateSectionInput = (e: any) => {
  const value = e.detail.value
  // 如果输入的不是数字或者小于0，则清空输入
  if (value && (isNaN(Number(value)) || Number(value) < 0)) {
    queryParams.value.jcshow = ''
    uni.showToast({
      title: '请输入0以上的数字',
      icon: 'none',
    })
  } else if (value) {
    // 如果输入有效，重置页码为1
    queryParams.value.page = 1
    // 添加延迟以确保值已更新
    setTimeout(() => {
      fetchTeachingLogList()
    }, 300)
  }
}

// 处理搜索输入
const handleSearchInput = () => {
  // 重置页码
  queryParams.value.page = 1
  // 将搜索关键词赋值给课程名称查询参数
  queryParams.value.kcmc = searchKeyword.value
  // 获取数据
  fetchTeachingLogList()
}

// 处理校区选择变化
const handleCampusChange = (value: any) => {
  queryParams.value.ssxq = typeof value === 'object' ? value.value : value
  queryParams.value.page = 1
  // 添加延迟以确保值已更新
  setTimeout(() => {
    fetchTeachingLogList()
  }, 0)
}

// 获取当前选中的校区名称
const getCurrentCampusName = () => {
  if (!queryParams.value.ssxq) {
    return '全部校区'
  }
  const selectedCampus = campusOptions.value.find((item) => item.value === queryParams.value.ssxq)
  return selectedCampus ? selectedCampus.label : '全部校区'
}

// 处理页码变化
const handlePageChange = (page: number) => {
  queryParams.value.page = page
  fetchTeachingLogList()
}

// 处理部门选择变化
const handleDepartmentChange = (value: any) => {
  queryParams.value.ssxb =
    typeof value === 'object' && value !== null ? value.value : String(value || '')
  queryParams.value.page = 1
  // 添加延迟以确保值已更新
  setTimeout(() => {
    fetchTeachingLogList()
  }, 0)
}

// 处理状态选择变化
const handleStatusChange = (value: any) => {
  queryParams.value.status = typeof value === 'object' ? value.value : value
  queryParams.value.page = 1 // 重置页码为1
  // 添加延迟以确保值已更新
  setTimeout(() => {
    fetchTeachingLogList()
  }, 0)
}

// 处理星期选择变化
const handleWeekdayChange = (value: any) => {
  queryParams.value.xqs = typeof value === 'object' ? value.value : value
  queryParams.value.page = 1 // 重置页码为1
  // 添加延迟以确保值已更新
  setTimeout(() => {
    fetchTeachingLogList()
  }, 0)
}

// 页面加载时获取数据
onMounted(() => {
  // 加载字典数据
  loadDicts()

  // 获取部门列表
  fetchDepartmentList()

  // 先获取校区列表
  fetchCampusList()
    .then(() => {
      // 等待学年学期组件初始化完成
      const checkInitialized = () => {
        if (schoolYearPickerRef.value?.isInitialized) {
          // 使用学年学期组件的值更新查询参数
          queryParams.value.semesters = yearValue.value
          // 默认选中今日
          setQuickDateFilter('today')
          // 获取数据
          fetchTeachingLogList()
        } else {
          setTimeout(checkInitialized, 100)
        }
      }
      checkInitialized()
    })
    .catch((error) => {
      console.error('初始化失败:', error)
    })
})
</script>

<style lang="scss">
.push-door-lecture-container {
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
  background-color: #f8f8f8;
}

.search-bar {
  box-sizing: border-box;
  width: 100%;
}

.content-area {
  box-sizing: border-box;
  width: 100%;
  padding-bottom: 20px;
}

.text-primary {
  color: #4338ca;
}

.bg-primary {
  background-color: #4338ca;
}

.filter-popup {
  width: 80vw;
  height: 100vh;
  overflow-y: auto;
  background-color: #fff;
}
/* 表单样式 */
.form-container {
  box-sizing: border-box;
  width: 100%;
}

.form-section {
  box-sizing: border-box;
  padding: 24rpx;
  margin-bottom: 24rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  padding-bottom: 16rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  border-bottom: 1px solid #f0f0f0;
}

.form-item {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 20rpx;
}

.form-row-flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.form-label {
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333333;

  &.required::before {
    margin-right: 4rpx;
    color: #f5222d;
    content: '*';
  }
}

.form-content {
  box-sizing: border-box;
  width: 100%;
}

.form-input {
  box-sizing: border-box;
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}

.button-group {
  display: flex;
  gap: 32rpx;
  justify-content: center;
  margin-top: 48rpx;
  margin-bottom: 30rpx;
}

.submit-button {
  width: 240rpx;
  height: 80rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  color: #ffffff;
  text-align: center;
  background-color: #4338ca;
  border-radius: 40rpx;
}

.cancel-button {
  width: 240rpx;
  height: 80rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  color: #666666;
  text-align: center;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 40rpx;
}

.date-range-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  width: 100%;
}

.date-picker-item {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.date-label {
  margin-bottom: 8rpx;
  font-size: 24rpx;
  color: #666;
}

.date-input {
  box-sizing: border-box;
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}

.calendar-container {
  overflow: hidden;
  background-color: #fff;
  border-radius: 16rpx 16rpx 0 0;
}

.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 1px solid #f0f0f0;
}

.calendar-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.calendar-footer {
  padding: 24rpx;
  border-top: 1px solid #f0f0f0;
}
</style>
