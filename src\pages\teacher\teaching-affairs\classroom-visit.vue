<route lang="json5">
{
  style: {
    navigationBarTitleText: '推门听课',
  },
}
</route>
<script lang="ts" setup>
import { ref } from 'vue'
import type {
  TabItem,
  FilterItem,
  AvailableCourse,
  RecommendedCourse,
  StatisticsItem,
} from '@/types/classroom-visit'

// 选项卡状态
const activeTab = ref<string>('available')
const tabs: TabItem[] = [
  { id: 'available', name: '可听课程' },
  { id: 'records', name: '我的记录' },
  { id: 'evaluations', name: '听课评价' },
]

// 课程类型过滤器状态
const activeFilter = ref<string>('all')
const filters: FilterItem[] = [
  { id: 'all', name: '全部课程' },
  { id: 'theory', name: '理论课' },
  { id: 'experiment', name: '实验课' },
  { id: 'engineering', name: '工科类' },
  { id: 'literature', name: '文科类' },
  { id: 'business', name: '商科类' },
  { id: 'art', name: '艺术类' },
]

// 今日可听课程数据
const todayCourses: AvailableCourse[] = [
  {
    id: '1',
    name: 'Java程序设计',
    status: '可预约',
    teacher: '张教授',
    time: '10:00-11:40',
    tags: [
      { name: '专业课', bgColor: 'bg-blue-100', textColor: 'text-blue-600' },
      { name: '工科类', bgColor: 'bg-green-100', textColor: 'text-green-600' },
    ],
    location: '信息楼B-302',
  },
  {
    id: '2',
    name: '市场营销学',
    status: '可预约',
    teacher: '李教授',
    time: '14:00-15:40',
    tags: [
      { name: '公共课', bgColor: 'bg-yellow-100', textColor: 'text-yellow-600' },
      { name: '商科类', bgColor: 'bg-red-100', textColor: 'text-red-600' },
    ],
    location: '经管楼A-401',
  },
  {
    id: '3',
    name: '网页设计与制作',
    status: '可预约',
    teacher: '王教授',
    time: '16:00-17:40',
    tags: [
      { name: '专业课', bgColor: 'bg-blue-100', textColor: 'text-blue-600' },
      { name: '实验课', bgColor: 'bg-purple-100', textColor: 'text-purple-600' },
    ],
    location: '实验楼C-201',
  },
]

// 推荐课程数据
const recommendedCourses: RecommendedCourse[] = [
  {
    id: '4',
    name: '高等数学',
    tag: { name: '热门', bgColor: 'bg-red-100', textColor: 'text-red-600' },
    teacher: '陈教授',
    schedule: '周四 08:00-09:40',
    rating: 4.8,
    reviewCount: 325,
  },
  {
    id: '5',
    name: '大学英语',
    tag: { name: '精品', bgColor: 'bg-green-100', textColor: 'text-green-600' },
    teacher: '张教授',
    schedule: '周三 10:00-11:40',
    rating: 4.1,
    reviewCount: 289,
  },
]

// 听课统计数据
const statistics: StatisticsItem[] = [
  { name: '本周听课', value: 4, color: 'text-blue-500' },
  { name: '本月听课', value: 12, color: 'text-green-500' },
  { name: '本学期听课', value: 36, color: 'text-orange-500' },
  { name: '未评价', value: 8, color: 'text-purple-500' },
]

// 处理筛选条件点击
const handleFilterClick = (filterId: string) => {
  activeFilter.value = filterId
  // 实际项目中这里可以根据筛选条件请求数据
}

// 处理选项卡点击
const handleTabClick = (tabId: string) => {
  activeTab.value = tabId
  // 实际项目中这里可以根据选项卡切换显示不同的内容
}

// 处理搜索
const handleSearch = (keyword: string) => {
  console.log('搜索关键词:', keyword)
  // 实际项目中这里可以根据关键词搜索数据
}

// 处理课程预约
const handleReservation = (courseId: string) => {
  console.log('预约课程:', courseId)
  // 实际项目中这里可以处理预约逻辑
}
</script>

<template>
  <view class="bg-gray-100 min-h-screen">
    <!-- 内容区域 -->
    <view class="p-4 pb-6">
      <!-- 选项卡 -->
      <view class="flex bg-gray-200 rounded-lg p-0.5 mb-4 shadow-sm">
        <view
          v-for="tab in tabs"
          :key="tab.id"
          class="flex-1 text-center py-2 px-3 rounded-md text-sm transition-colors duration-200"
          :class="{
            'bg-white shadow font-medium': activeTab === tab.id,
            'text-gray-600': activeTab !== tab.id,
          }"
          @click="handleTabClick(tab.id)"
        >
          {{ tab.name }}
        </view>
      </view>

      <!-- 搜索栏 -->
      <view class="bg-[#f2f2f7] rounded-lg flex items-center px-4 py-2 mb-4">
        <wd-icon name="search1" class="text-gray-500 mr-2" />
        <text class="text-sm text-gray-400">搜索课程、教师或教室</text>
      </view>

      <!-- 筛选条件 -->
      <scroll-view
        scroll-x
        class="whitespace-nowrap mb-4"
        :show-scrollbar="false"
        style="padding: 8px 0"
      >
        <view
          v-for="filter in filters"
          :key="filter.id"
          class="inline-block py-1.5 px-3 bg-gray-200 rounded-full mr-2 text-xs text-gray-500 transition-colors duration-200"
          :class="{ 'bg-blue-100 text-blue-500': activeFilter === filter.id }"
          @click="handleFilterClick(filter.id)"
        >
          {{ filter.name }}
        </view>
      </scroll-view>

      <!-- 可听课程内容区域 -->
      <view v-if="activeTab === 'available'">
        <!-- 今日可听课程 -->
        <view class="mb-4">
          <view class="flex justify-between items-center mb-3">
            <text class="text-base font-medium">今日可听课程</text>
            <view class="text-blue-500 text-sm flex items-center cursor-pointer">
              <wd-icon name="calendar" class="mr-1" />
              <text>选择日期</text>
            </view>
          </view>

          <view class="bg-white rounded-lg p-4 shadow">
            <!-- 课程列表 -->
            <view
              v-for="(course, index) in todayCourses"
              :key="course.id"
              class="py-3"
              :class="{ 'border-b border-gray-100': index < todayCourses.length - 1 }"
            >
              <view class="flex justify-between mb-2">
                <text class="font-medium">{{ course.name }}</text>
                <text
                  class="text-sm text-blue-500 cursor-pointer"
                  @click="handleReservation(course.id || '')"
                >
                  {{ course.status }}
                </text>
              </view>
              <view class="flex justify-between text-sm text-gray-600 mb-2">
                <view class="flex items-center">
                  <wd-icon name="user" class="mr-1" />
                  <text>{{ course.teacher }}</text>
                </view>
                <view class="flex items-center">
                  <wd-icon name="time" class="mr-1" />
                  <text>{{ course.time }}</text>
                </view>
              </view>
              <view class="flex justify-between">
                <view class="flex flex-wrap">
                  <text
                    v-for="(tag, tagIndex) in course.tags"
                    :key="tagIndex"
                    class="inline-block px-2 py-0.5 rounded text-xs mr-1.5 mb-1"
                    :class="[tag.bgColor, tag.textColor]"
                  >
                    {{ tag.name }}
                  </text>
                </view>
                <view class="flex items-center text-sm text-gray-600">
                  <wd-icon name="location" class="mr-1" />
                  <text>{{ course.location }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 推荐课程 -->
        <view class="mb-4">
          <text class="text-base font-medium mb-3 block">推荐课程</text>
          <view class="bg-white rounded-lg p-4 shadow">
            <!-- 课程列表 -->
            <view
              v-for="(course, index) in recommendedCourses"
              :key="course.id"
              class="py-3"
              :class="{ 'border-b border-gray-100': index < recommendedCourses.length - 1 }"
            >
              <view class="flex justify-between mb-2">
                <text class="font-medium">{{ course.name }}</text>
                <view>
                  <text
                    class="inline-block px-2 py-0.5 rounded text-xs"
                    :class="[course.tag.bgColor, course.tag.textColor]"
                  >
                    {{ course.tag.name }}
                  </text>
                </view>
              </view>
              <view class="flex justify-between text-sm text-gray-600 mb-2">
                <view class="flex items-center">
                  <wd-icon name="user" class="mr-1" />
                  <text>{{ course.teacher }}</text>
                </view>
                <view class="flex items-center">
                  <wd-icon name="calendar" class="mr-1" />
                  <text>{{ course.schedule }}</text>
                </view>
              </view>
              <view class="flex items-center">
                <view class="flex text-yellow-400 mr-2 space-x-0.5">
                  <wd-icon
                    name="star-on"
                    v-for="n in Math.floor(course.rating)"
                    :key="`star-full-${n}`"
                    class="text-yellow-400"
                  />
                  <wd-icon
                    name="star-on"
                    v-if="course.rating % 1 >= 0.5"
                    class="opacity-50 text-yellow-400"
                  />
                  <wd-icon
                    name="star"
                    v-for="n in Math.floor(5 - Math.ceil(course.rating % 1))"
                    :key="`star-empty-${n}`"
                    class="text-yellow-200"
                  />
                </view>
                <text class="text-sm text-gray-600">
                  {{ course.rating }}分 ({{ course.reviewCount }}人评价)
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 我的记录内容区域 -->
      <view v-else-if="activeTab === 'records'" class="bg-white rounded-lg p-4 shadow mb-4">
        <text class="text-gray-500 text-center block py-8">我的听课记录将在这里显示</text>
      </view>

      <!-- 听课评价内容区域 -->
      <view v-else-if="activeTab === 'evaluations'" class="bg-white rounded-lg p-4 shadow mb-4">
        <text class="text-gray-500 text-center block py-8">听课评价信息将在这里显示</text>
      </view>

      <!-- 听课统计 -->
      <view class="bg-white rounded-lg p-4 shadow">
        <text class="text-base font-medium mb-3 block">我的听课统计</text>
        <view class="flex justify-between">
          <view v-for="(stat, index) in statistics" :key="index" class="text-center">
            <text class="block text-xl font-semibold mb-1" :class="stat.color">
              {{ stat.value }}
            </text>
            <text class="block text-xs text-[#8e8e93]">{{ stat.name }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
