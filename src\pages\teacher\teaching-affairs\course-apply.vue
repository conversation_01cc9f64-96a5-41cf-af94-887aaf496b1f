<route lang="json5">
{
  style: {
    navigationBarTitleText: '申报新课程',
  },
}
</route>
<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { getSemesterSelect } from '@/service/system'
import { createSelectCourse, updateSelectCourse, getSelectCourseInfo } from '@/service/selectCourse'
import { getTeachOfficeList } from '@/service/teachOffice'
import { getSubstanceList, getTreeClassList } from '@/service/organization'
import { loadDictData, getDictOptions } from '@/utils/dict'
import { getCampusList } from '@/service/campus'
import { getCourseYearList } from '@/service/course'
import CourseSelector from '@/components/CourseSelector/index.vue'
import TextbookSelector from '@/components/TextbookSelector/index.vue'
import ClassTreeSelector from '@/components/ClassTreeSelector/index.vue'
import type { DictData } from '@/types/system'
import type { SemesterOption } from '@/types/semester'
import type { SelectCourseParams, TeachingInfo, TeachingMaterial } from '@/types/selectCourse'
import type { TeachOffice, TeachOfficeParams } from '@/types/teachOffice'
import type { SubstanceOrg } from '@/types/organization'
import type { Campus, CampusSelectParams } from '@/types/campus'
import type { CourseYear, CourseYearParams } from '@/types/course'
import type { TeachingMaterial as TextbookInfo } from '@/types/teachingMaterial'

// 弹窗引用
const popup = ref(null)

// 步骤页面相关状态
const currentPage = ref(1)
const totalPages = ref(4)
const progressWidth = computed(() => {
  // 进度条总长度减去两端圆形部分的宽度，再乘以当前进度比例
  // 为最后两步添加微调，避免超出
  if (currentPage.value === 3) {
    return '60%' // 略微减少第3步的宽度
  } else if (currentPage.value === 4) {
    return 'calc(100% - 36rpx - 4rpx)' // 确保正好到达最后一步
  }
  return `${Math.min(((currentPage.value - 1) / (totalPages.value - 1)) * 100, 100)}%`
})

// 步骤标题
const stepTitles = [
  {
    title: '第一步 基本信息',
    desc: '课程名称、教研室、学分等基本信息',
  },
  {
    title: '第二步 学时与班级信息',
    desc: '学时安排、班级信息、开放信息等',
  },
  {
    title: '第三步 上课时间及场地要求',
    desc: '星期、节次、场地要求等信息',
  },
  {
    title: '第四步 教材信息',
    desc: '教材名称、作者、出版社等信息',
  },
]

// 字典数据
const dict = reactive<Record<string, any>>({
  SYS_TEACH_TASK_TYPE: [],
  SYS_COURSE_CATEGORY: [],
  SYS_TEACHING_METHOD: [],
  SYS_ASSESSMENT_METHOD: [],
  SYS_CAMPUS: [],
  SYS_VENUE_TYPE: [], // 场地要求类型
  SYS_SELECTION_TYPE: [], // 教材选用类别
  SYS_WEEKDAY: [
    { value: '1', label: '星期一' },
    { value: '2', label: '星期二' },
    { value: '3', label: '星期三' },
    { value: '4', label: '星期四' },
    { value: '5', label: '星期五' },
    { value: '6', label: '星期六' },
    { value: '7', label: '星期日' },
  ],
  SYS_PERIOD: [
    { value: '1,2', label: '第1-2节' },
    { value: '3,4', label: '第3-4节' },
    { value: '5,6', label: '第5-6节' },
    { value: '7,8', label: '第7-8节' },
    { value: '9,10', label: '第9-10节' },
    { value: '11,12', label: '第11-12节' },
  ],
})

// 字典选项（直接引用，不再使用computed）
const weekdayOptions = dict.SYS_WEEKDAY
const periodOptions = dict.SYS_PERIOD
// 场地要求类型选项
const venueTypeOptions = computed(() => getDictOptions(dict.SYS_VENUE_TYPE))
// 教材选用类别选项
const selectionTypeOptions = computed(() => getDictOptions(dict.SYS_SELECTION_TYPE))

// 字典选项（转换后的选项数据）
const dictOptions = reactive({
  courseCategoryOptions: computed(() => getDictOptions(dict.SYS_COURSE_CATEGORY)),
  assessmentMethodOptions: computed(() => getDictOptions(dict.SYS_ASSESSMENT_METHOD)),
  teachingMethodOptions: computed(() => getDictOptions(dict.SYS_TEACHING_METHOD)),
  courseTypeOptions: computed(() => getDictOptions(dict.SYS_TEACH_TASK_TYPE)),
})

// 校区选项
const campusOptions = ref<{ label: string; value: string }[]>([])
// 年级选项
const gradeOptions = ref<{ label: string; value: string }[]>([])
// 系部选项也用于开放系部
const startClassDeptOptions = computed(() => departmentOptions.value)

// 班级选项
const classOptions = [
  { label: '计算机2023级1班', value: '1001' },
  { label: '计算机2023级2班', value: '1002' },
  { label: '数学2023级1班', value: '1003' },
  { label: '物理2023级1班', value: '1004' },
]

// 周选项
const weekOptions = Array.from({ length: 20 }, (_, i) => ({
  label: `第${i + 1}周`,
  value: String(i + 1),
}))

// 表单数据
const formData = reactive<SelectCourseParams>({
  semesters: '', // 学年学期
  deptCode: '', // 系部代码
  courseCode: {
    courseName: '',
    courseCode: '',
  },
  teachOfficeCode: '', // 教研室代码
  teachOfficeName: '', // 教研室名称
  taskType: '', // 任务类型
  courseCategory: '', // 课程分类
  weekHours: '', // 周学时
  creditHour: 0, // 学分
  courseTotalHours: '', // 总学时
  teachingHours: '', // 教学学时
  experimentHours: '', // 实验学时
  computerHours: '', // 上机学时
  teachingMethod: '', // 教学方式
  assessmentMethod: '', // 考核方式
  campusCode: '', // 校区代码
  startingGrade: [], // 开放年级
  startClassDeptCode: [], // 开放系部
  class: [], // 班级
  limitCount: 1, // 限制人数，默认为1
  maxCount: 100, // 最大人数，默认为100
  startWeek: 1, // 开始周，默认为1
  endWeek: 1, // 结束周，默认为16
  remark: '', // 备注
  teachingInfo: [], // 教学安排
  teachingMaterialList: [], // 教材列表
  teacherCode: '', // 教师代码
  xkxxid: '0', // 选课信息ID
  optype: 'select', // 操作类型
  xktjid: 0, // 选课条件ID
})

// 上课时间及场地列表
const scheduleList = ref<
  Array<{
    id: number
    weekday: string // 星期
    period: string // 节次
    venue: string // 场地要求
    week?: string // 周次
    _X_ROW_KEY?: string // 行标识
  }>
>([{ id: 1, weekday: '1', period: '1,2', venue: '1' }])

// 教材信息列表
const textbookList = ref<
  Array<{
    id: number
    name: string // 教材名称
    author: string // 作者
    publisher: string // 出版社
    isbn: string // ISBN
    code: string // 代码
    selectionType: string // 教材选用类别
    textbookId?: number // 后端教材ID
  }>
>([
  {
    id: 1,
    name: '',
    author: '',
    publisher: '',
    isbn: '',
    code: '',
    selectionType: '1', // 默认必选
    textbookId: undefined,
  },
])

// 添加上课时间记录
const addSchedule = () => {
  const newId =
    scheduleList.value.length > 0 ? Math.max(...scheduleList.value.map((item) => item.id)) + 1 : 1
  scheduleList.value.push({
    id: newId,
    weekday: '',
    period: '',
    venue: '',
    week: formData.startWeek ? String(formData.startWeek) : '1',
    _X_ROW_KEY: `row_${733 + newId}`,
  })
}

// 删除上课时间记录
const deleteSchedule = (id: number) => {
  const index = scheduleList.value.findIndex((item) => item.id === id)
  if (index !== -1) {
    scheduleList.value.splice(index, 1)
  }
}

// 添加教材信息
const addTextbook = () => {
  const newId =
    textbookList.value.length > 0 ? Math.max(...textbookList.value.map((item) => item.id)) + 1 : 1
  textbookList.value.push({
    id: newId,
    name: '',
    author: '',
    publisher: '',
    isbn: '',
    code: '',
    selectionType: '',
  })
}

// 删除教材信息
const deleteTextbook = (id: number) => {
  const index = textbookList.value.findIndex((item) => item.id === id)
  if (index !== -1) {
    textbookList.value.splice(index, 1)
  }
}

// 用于表单校验的字段
interface FormDisplay {
  totalHours: string
  lectureHours: string
  labHours: string
  computerHours: string
  virtualHours: string
  campus: string
  grade: string
  teachingDepartment: string
  classCapacity: string
  courseCapacity: string
  teachingClass: string
  courseDescription: string
  courseObjectives: string
  courseContent: string
  courseTextbook: string
  courseEvaluation: string
  courseRemarks: string
}

// 表单界面显示数据
const formDisplay = reactive<FormDisplay>({
  totalHours: '',
  lectureHours: '',
  labHours: '',
  computerHours: '',
  virtualHours: '',
  campus: '',
  grade: '',
  teachingDepartment: '',
  classCapacity: '',
  courseCapacity: '',
  teachingClass: '',
  courseDescription: '',
  courseObjectives: '',
  courseContent: '',
  courseTextbook: '',
  courseEvaluation: '',
  courseRemarks: '',
})

// 计算总学时
const calculateTotalHours = () => {
  const lecture = parseFloat(formDisplay.lectureHours) || 0
  const lab = parseFloat(formDisplay.labHours) || 0
  const computer = parseFloat(formDisplay.computerHours) || 0
  formDisplay.totalHours = (lecture + lab + computer).toString()

  // 同步到formData
  formData.courseTotalHours = formDisplay.totalHours
  formData.teachingHours = formDisplay.lectureHours
  formData.experimentHours = formDisplay.labHours
  formData.computerHours = formDisplay.computerHours
}

// 验证数值不能为0或负数
const validatePositiveNumber = (value: string | number): boolean => {
  const num = typeof value === 'string' ? parseFloat(value) : value
  return !isNaN(num) && num > 0
}

// 监听学时输入变化
const handleHoursInput = (
  field: keyof Pick<FormDisplay, 'lectureHours' | 'labHours' | 'computerHours'>,
  value: string,
) => {
  // 确保输入的是正数
  const numValue = parseFloat(value)
  if (numValue <= 0) {
    uni.showToast({
      title: '学时必须大于0',
      icon: 'none',
    })
    return
  }

  formDisplay[field] = value
  calculateTotalHours()
}

// 输入框当前状态
const focusState = reactive({
  currentField: '',
  isInputActive: false,
})

// 处理输入框获取焦点
const handleFocus = (fieldName: string) => {
  focusState.currentField = fieldName
  focusState.isInputActive = true
  // 在某些平台上可能需要额外处理
  setTimeout(() => {
    // 解决某些平台上的延迟响应问题
  }, 50)
}

// 处理输入框失去焦点
const handleBlur = () => {
  focusState.isInputActive = false
  focusState.currentField = ''
}

// 处理数值输入框变化，确保值大于0
const handleNumberInput = (fieldName: string, event: any) => {
  let value = ''
  if (typeof event === 'object' && event.detail && event.detail.value !== undefined) {
    value = event.detail.value
  } else if (typeof event === 'string') {
    value = event
  }

  // 如果输入为空，不做处理
  if (value === '') return

  const numValue = parseFloat(value)
  if (isNaN(numValue) || numValue <= 0) {
    uni.showToast({
      title: '请输入大于0的数值',
      icon: 'none',
    })
    // 不更新值
    return
  }

  // 更新值
  formData[fieldName] = numValue
}

// 处理输入变化
const handleInput = (fieldName: string, event: any) => {
  if (typeof event === 'object' && event.detail && event.detail.value !== undefined) {
    // 某些平台返回event.detail.value
    formData[fieldName] = event.detail.value
  } else if (typeof event === 'string') {
    // 某些平台直接返回值
    formData[fieldName] = event
  }
}

// 学年学期选项数据
const semesterOptions = ref<SemesterOption[]>([])

// 获取学年学期选项
const loadSemesterOptions = async () => {
  try {
    const options = await getSemesterSelect()
    semesterOptions.value = options.data
  } catch (error) {
    console.error('获取学年学期选项失败:', error)
    uni.showToast({
      title: '获取学年学期选项失败',
      icon: 'none',
    })
  }
}

// 系部选项
const departmentOptions = ref<{ label: string; value: string }[]>([])
// 教研室选项
const researchOfficeOptions = ref<{ label: string; value: string }[]>([])

// 加载系部列表
const loadDepartmentOptions = async () => {
  try {
    const response = await getSubstanceList()
    // 处理新的响应格式，API已经返回了data部分内容
    departmentOptions.value = response.map((item) => ({
      label: item.name,
      value: item.code,
    }))
  } catch (error) {
    console.error('获取系部列表失败:', error)
    uni.showToast({
      title: '获取系部列表失败',
      icon: 'none',
    })
  }
}

// 加载教研室列表
const loadResearchOfficeOptions = async (deptCode: string) => {
  if (!deptCode) {
    researchOfficeOptions.value = []
    return
  }

  try {
    const params: TeachOfficeParams = {
      deptCode,
      select: 1,
    }
    const list = await getTeachOfficeList(params)
    researchOfficeOptions.value = list.map((item) => ({
      label: item.teachOfficeName,
      value: item.teachOfficeCode,
    }))
  } catch (error) {
    console.error('获取教研室列表失败:', error)
    uni.showToast({
      title: '获取教研室列表失败',
      icon: 'none',
    })
  }
}

// 监听系部变化
watch(
  () => formData.deptCode,
  (newDeptCode, o) => {
    loadResearchOfficeOptions(newDeptCode)
    if (newDeptCode && o) {
      // 清空已选的教研室
      formData.teachOfficeCode = ''
      formData.teachOfficeName = ''
    }
  },
)

// 处理系部选择变化
const handleDepartmentChange = (e: any) => {
  const index = e.detail.value
  const selected = departmentOptions.value[index]
  formData.deptCode = selected.value
}

// 处理教研室选择变化
const handleResearchOfficeChange = (e: any) => {
  const index = e.detail.value
  const selected = researchOfficeOptions.value[index]
  formData.teachOfficeCode = selected.value
  formData.teachOfficeName = selected.label
}

// 加载校区列表
const loadCampusOptions = async () => {
  try {
    const params: CampusSelectParams = {
      format: 'select',
    }
    const list = await getCampusList(params)
    campusOptions.value = list.map((item) => ({
      label: item.campusName,
      value: item.campusCode,
    }))
  } catch (error) {
    console.error('获取校区列表失败:', error)
    uni.showToast({
      title: '获取校区列表失败',
      icon: 'none',
    })
  }
}

// 加载年级列表
const loadGradeOptions = async () => {
  try {
    const params: CourseYearParams = {
      format: 'select',
    }
    const list = await getCourseYearList(params)
    gradeOptions.value = list.map((item) => ({
      label: `${item.year}级`,
      value: String(item.year),
    }))
  } catch (error) {
    console.error('获取年级列表失败:', error)
    uni.showToast({
      title: '获取年级列表失败',
      icon: 'none',
    })
  }
}

// 测试单个字典加载
const testSingleDictLoad = async (dictType: string) => {
  try {
    const result = await loadDictData([dictType])
    return result
  } catch (error) {
    console.error(`测试加载字典 ${dictType} 失败:`, error)
    return null
  }
}

// 编辑模式标志和课程ID
const isEditMode = ref(false)
const courseId = ref<number | null>(null)

// 准备表单数据
const prepareFormData = () => {
  // 转换教材数据
  const teachingMaterialList = textbookList.value.map((item, index) => ({
    textbookInfoId: item.textbookId || 0,
    name: item.name,
    mainEditor: item.author,
    isbn: item.isbn,
    publishingHouse: item.publisher,
    publicationTime: '',
    selectionType: item.selectionType,
    // 以下是必填字段，设置默认值
    remark: '',
    isFirstSelection: 1,
    reasonForSelection: '',
    isTeachingReference: 0,
    isExperimentalGuide: 0,
    textbookEvaluationOpinion: '',
    evaluationReviewStatus: 0,
    category: '',
    categoryName: '',
    type: '',
    typeName: '',
    _X_ROW_KEY: `row_${732 + index}`, // 添加行标识
  })) as TeachingMaterial[]

  // 转换上课时间数据
  const teachingInfo = scheduleList.value.map((item, index) => {
    const week = formData.startWeek ? String(formData.startWeek) : '1' // 使用表单开始周

    // 获取节次的显示标签，例如 "第1-2节"
    const periodLabel = periodOptions.find((o) => o.value === item.period)?.label || ''

    // 处理 sectionKey，将 "1,2" 转换为 "1-2|1-2" 格式
    const sectionKey = item.period
      ? item.period.replace(',', '-') + '|' + item.period.replace(',', '-')
      : ''

    // section 直接使用 periodLabel，如 "第1-2节"
    const section = periodLabel

    return {
      week,
      section,
      sectionKey,
      siteType: item.venue,
      id: String(Date.now() + index), // 生成唯一ID
      weekday: item.weekday, // 保留星期字段
      _X_ROW_KEY: `row_${733 + index}`, // 添加行标识
    }
  }) as TeachingInfo[]

  // 组装提交数据
  return {
    ...formData,
    teachingInfo,
    teachingMaterialList,
    limitCount: typeof formData.limitCount === 'number' ? formData.limitCount : 1,
    maxCount: typeof formData.maxCount === 'number' ? formData.maxCount : 2,
    // 添加SelectCourseParams接口所需的必填字段
    xkxxid: isEditMode.value && courseId.value ? String(courseId.value) : '0',
    optype: 'select' as const, // 使用类型断言确保是字面量类型
    xktjid: 0,
  }
}

onLoad((options) => {
  console.log('onLoad options:', options)
  if (options && options.id) {
    courseId.value = Number(options.id)
    isEditMode.value = true
    console.log('编辑模式，课程ID:', courseId.value)
    // 加载课程数据
    loadCourseData(courseId.value)
  } else {
    console.log('新增模式')
    isEditMode.value = false
  }
})

// 加载课程数据
const loadCourseData = async (id: number) => {
  try {
    uni.showLoading({ title: '加载数据中...' })
    const response = await getSelectCourseInfo(id)
    console.log('获取到的课程数据:', response)

    // 提取课程数据
    const courseData = response

    // 填充表单数据
    fillFormData(courseData)

    // 处理教材数据
    if (courseData.teachingMaterialList && courseData.teachingMaterialList.length > 0) {
      // 将后端教材数据转换为前端格式
      textbookList.value = courseData.teachingMaterialList.map((item) => ({
        textbookId: item.textbookInfoId,
        name: item.name,
        author: item.mainEditor,
        publisher: item.publishingHouse,
        isbn: item.isbn,
        selectionType: item.selectionType,
      }))
    }

    // 处理上课时间数据
    if (courseData.siteInfoList && courseData.siteInfoList.length > 0) {
      console.log('原始上课时间数据:', courseData.siteInfoList)

      // 将后端上课时间数据转换为前端格式
      scheduleList.value = courseData.siteInfoList.map((item, index) => {
        // 提取周几信息，如果没有默认为1（周一）
        const weekday = item.weekday || '1'

        // 处理节次，将3-4节转换为3,4格式
        let period = '1,2' // 默认值
        if (item.section) {
          period = item.section.replace('-', ',').replace('节', '')
        }

        // 场地类型
        const venue = item.siteType || '1'

        return {
          id: index + 1,
          weekday,
          period,
          venue,
          week: item.week || '1',
          _X_ROW_KEY: `row_${733 + index}`,
        }
      })

      console.log('处理后的上课时间数据:', scheduleList.value)
    }

    uni.showToast({
      title: '数据加载成功',
      icon: 'success',
    })
  } catch (error) {
    console.error('加载课程数据失败:', error)
    uni.showToast({
      title: '加载数据失败',
      icon: 'error',
    })
  } finally {
    uni.hideLoading()
  }
}

// 回填表单数据
const fillFormData = (data: any) => {
  // 基本信息
  formData.semesters = data.studyYear + '|' + data.studyTerm
  formData.deptCode = data.deptCode
  formData.teachOfficeCode = data.teachOfficeCode
  formData.teachOfficeName = data.teachOfficeName
  formData.taskType = data.taskType
  formData.courseCategory = data.courseCategory
  formData.weekHours = data.weekHours
  formData.creditHour = data.creditHour
  formData.remark = data.remark

  // 课程信息
  formData.courseCode = {
    courseName: data.courseName,
    courseCode: data.courseCode,
  }

  // 教学信息
  formData.courseTotalHours = data.courseTotalHours
  formData.teachingHours = data.teachingHours
  formData.experimentHours = data.experimentHours
  formData.computerHours = data.computerHours
  formData.virtualHours = data.virtualHours
  formData.teachingMethod = data.teachingMethod
  formData.assessmentMethod = data.assessmentMethod
  formData.campusCode = data.campusCode

  // 修复处理 startingGrade 和 startClassDeptCode 的逻辑
  // 检查字段类型，如果已经是数组则直接使用，否则再尝试分割字符串
  formData.startingGrade = Array.isArray(data.startingGrade)
    ? data.startingGrade
    : data.startingGrade
      ? data.startingGrade.split(',')
      : []

  formData.startClassDeptCode = Array.isArray(data.startClassDeptCode)
    ? data.startClassDeptCode
    : data.startClassDeptCode
      ? data.startClassDeptCode.split(',')
      : []

  formData.class = data.class
    ? typeof data.class === 'string'
      ? data.class.split(',')
      : data.class
    : []
  formData.limitCount = data.limitCount
  formData.maxCount = data.maxCount
  formData.startWeek = data.startWeek
  formData.endWeek = data.endWeek

  // 更新显示信息
  formDisplay.campus = data.campusName
  formDisplay.teachingDepartment = data.deptName
  formDisplay.totalHours = data.courseTotalHours
  formDisplay.lectureHours = data.teachingHours
  formDisplay.labHours = data.experimentHours
  formDisplay.computerHours = data.computerHours
  formDisplay.virtualHours = data.virtualHours
  formDisplay.courseDescription = data.remark
  formDisplay.courseCapacity = data.maxCount
  formDisplay.classCapacity = data.limitCount
}

// 在页面加载时获取选项数据
onMounted(() => {
  console.log('页面已挂载，进行初始化...')

  // 加载学期选项
  loadSemesterOptions().then(() => {
    // 仅在非编辑模式下设置默认学期
    if (!isEditMode.value) {
      const currentSemester =
        semesterOptions.value.find((item) => item.isCurrent) || semesterOptions.value[0]
      if (currentSemester) {
        formData.semesters = currentSemester.value
      }
    }
  })

  // 加载其他选项数据
  loadDepartmentOptions()
  loadCampusOptions()
  loadGradeOptions()
  loadAllDictData()

  // 加载树形班级数据
  loadTreeClassData()
})

// 加载字典数据
const loadAllDictData = async () => {
  try {
    const dictTypes = [
      'DM_XKKCFLDM', // 课程分类
      'SYS_ASSESSMENT_METHOD', // 考核方式
      'DM_XKSKFSDM', // 授课方式
      'SYS_COURSE_TYPE', // 课程类型 - 修正为前端使用的键名
      'SYS_WEEKDAY', // 星期
      'SYS_PERIOD', // 节次
      'DM_JSSYSLBDM', // 场地要求类型
      'SYS_SELECTION_TYPE', // 教材选用类别
    ]

    const dictResult = await loadDictData(dictTypes)

    // 直接将结果赋值给响应式字典数据对象，使用前端定义的键名
    Object.keys(dictResult).forEach((key) => {
      if (key === 'DM_XKKCFLDM') {
        dict.SYS_COURSE_CATEGORY = dictResult[key]
      } else if (key === 'DM_XKSKFSDM') {
        dict.SYS_TEACHING_METHOD = dictResult[key]
      } else if (key === 'SYS_COURSE_TYPE') {
        dict.SYS_TEACH_TASK_TYPE = dictResult[key]
      } else if (key === 'DM_JSSYSLBDM') {
        dict.SYS_VENUE_TYPE = dictResult[key]
      } else if (key === 'SYS_SELECTION_TYPE') {
        dict.SYS_SELECTION_TYPE = dictResult[key]
      } else {
        dict[key] = dictResult[key]
      }
    })

    // 如果数据为空，可以设置默认值
    if (!dict.SYS_TEACH_TASK_TYPE || dict.SYS_TEACH_TASK_TYPE.length === 0) {
      console.warn('课程类型数据为空，设置默认值')
      dict.SYS_TEACH_TASK_TYPE = [
        { dictLabel: '必修课', dictValue: '1' },
        { dictLabel: '选修课', dictValue: '2' },
      ]
    }

    if (!dict.SYS_COURSE_CATEGORY || dict.SYS_COURSE_CATEGORY.length === 0) {
      console.warn('课程分类数据为空，设置默认值')
      dict.SYS_COURSE_CATEGORY = [
        { dictLabel: '公共基础课', dictValue: '1' },
        { dictLabel: '专业基础课', dictValue: '2' },
        { dictLabel: '专业课', dictValue: '3' },
      ]
    }

    if (!dict.SYS_TEACHING_METHOD || dict.SYS_TEACHING_METHOD.length === 0) {
      console.warn('教学方式数据为空，设置默认值')
      dict.SYS_TEACHING_METHOD = [
        { dictLabel: '课堂讲授', dictValue: '1' },
        { dictLabel: '实验', dictValue: '2' },
        { dictLabel: '上机', dictValue: '3' },
      ]
    }

    if (!dict.SYS_ASSESSMENT_METHOD || dict.SYS_ASSESSMENT_METHOD.length === 0) {
      console.warn('考核方式数据为空，设置默认值')
      dict.SYS_ASSESSMENT_METHOD = [
        { dictLabel: '考试', dictValue: '1' },
        { dictLabel: '考查', dictValue: '2' },
      ]
    }

    if (!dict.SYS_VENUE_TYPE || dict.SYS_VENUE_TYPE.length === 0) {
      console.warn('场地要求类型数据为空，设置默认值')
      dict.SYS_VENUE_TYPE = [
        { dictLabel: '多媒体教室', dictValue: '1' },
        { dictLabel: '普通教室', dictValue: '2' },
        { dictLabel: '实验室', dictValue: '3' },
        { dictLabel: '计算机房', dictValue: '4' },
        { dictLabel: '体育场', dictValue: '5' },
      ]
    }

    if (!dict.SYS_SELECTION_TYPE || dict.SYS_SELECTION_TYPE.length === 0) {
      console.warn('教材选用类别数据为空，设置默认值')
      dict.SYS_SELECTION_TYPE = [
        { dictLabel: '必选', dictValue: '1' },
        { dictLabel: '选修', dictValue: '2' },
      ]
    }

    // 设置默认值（如果需要）
    if (dict.SYS_TEACH_TASK_TYPE.length > 0 && !formData.taskType) {
      formData.taskType = dict.SYS_TEACH_TASK_TYPE[0].dictValue
    }

    if (dict.SYS_COURSE_CATEGORY.length > 0 && !formData.courseCategory) {
      formData.courseCategory = dict.SYS_COURSE_CATEGORY[0].dictValue
    }
  } catch (error) {
    console.error('加载字典数据失败:', error)
    uni.showToast({
      title: '加载字典数据失败',
      icon: 'none',
    })
  }
}

// 添加协同教师
const addCoTeacher = (teacherId: number) => {
  uni.showToast({
    title: '已添加为协同教师',
    icon: 'success',
  })
}

// 表单页面切换
const goToPage = (pageNum: number) => {
  if (pageNum >= 1 && pageNum <= totalPages.value) {
    currentPage.value = pageNum
  }
}

// 提交表单
const submitForm = async () => {
  try {
    uni.showLoading({
      title: '提交中...',
      mask: true,
    })

    const submitData = prepareFormData()
    console.log('最终提交数据:', submitData)

    // 根据编辑模式选择不同的API
    let response
    if (isEditMode.value && courseId.value) {
      // 编辑模式，添加课程ID
      submitData.id = courseId.value
      submitData.selectCourseId = courseId.value
      response = await updateSelectCourse(submitData)
    } else {
      // 新增模式
      response = await createSelectCourse(submitData)
    }

    uni.showToast({
      title: isEditMode.value ? '更新成功' : '提交成功',
      icon: 'success',
    })

    // 延迟返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } catch (error) {
    console.error('提交表单失败:', error)
    uni.showToast({
      title: error.msg || '提交失败',
      icon: 'none',
    })
  } finally {
    uni.hideLoading()
  }
}

// 表单验证
const validateCurrentPage = () => {
  // 这里只是简单的示例验证，实际项目中应该有更完善的验证逻辑
  let isValid = true
  console.log('验证当前页面:', currentPage.value)

  if (currentPage.value === 1) {
    if (
      !formData.semesters ||
      !formData.deptCode ||
      !formData.courseCode.courseCode ||
      !formData.taskType ||
      !formData.weekHours ||
      !formData.creditHour
    ) {
      isValid = false
      uni.showToast({
        title: '请填写所有必填项',
        icon: 'none',
      })
      return false
    }

    // 验证周学时和学分必须为正数
    if (!validatePositiveNumber(formData.weekHours)) {
      uni.showToast({
        title: '周学时必须大于0',
        icon: 'none',
      })
      return false
    }

    if (!validatePositiveNumber(formData.creditHour)) {
      uni.showToast({
        title: '学分必须大于0',
        icon: 'none',
      })
      return false
    }
  } else if (currentPage.value === 2) {
    if (
      !formData.courseTotalHours ||
      !formData.teachingMethod ||
      !formData.assessmentMethod ||
      !formDisplay.campus ||
      !formDisplay.courseCapacity
    ) {
      isValid = false
      uni.showToast({
        title: '请填写所有必填项',
        icon: 'none',
      })
      return false
    }

    // 验证学时必须为正数
    if (!validatePositiveNumber(formData.courseTotalHours)) {
      uni.showToast({
        title: '学期总学时必须大于0',
        icon: 'none',
      })
      return false
    }

    if (formData.teachingHours && !validatePositiveNumber(formData.teachingHours)) {
      uni.showToast({
        title: '讲课学时必须大于0',
        icon: 'none',
      })
      return false
    }

    if (formData.experimentHours && !validatePositiveNumber(formData.experimentHours)) {
      uni.showToast({
        title: '实验学时必须大于0',
        icon: 'none',
      })
      return false
    }

    if (formData.computerHours && !validatePositiveNumber(formData.computerHours)) {
      uni.showToast({
        title: '上机学时必须大于0',
        icon: 'none',
      })
      return false
    }

    // 验证班级限定人数和课程限定人数必须为正数
    if (!validatePositiveNumber(formData.limitCount)) {
      uni.showToast({
        title: '班级限定人数必须大于0',
        icon: 'none',
      })
      return false
    }

    if (!validatePositiveNumber(formData.maxCount)) {
      uni.showToast({
        title: '课程限定人数必须大于0',
        icon: 'none',
      })
      return false
    }

    // 验证任务开始周和结束周必须为正数
    if (formData.startWeek && !validatePositiveNumber(formData.startWeek)) {
      uni.showToast({
        title: '任务开始周必须大于0',
        icon: 'none',
      })
      return false
    }

    if (formData.endWeek && !validatePositiveNumber(formData.endWeek)) {
      uni.showToast({
        title: '任务结束周必须大于0',
        icon: 'none',
      })
      return false
    }

    // 验证结束周必须大于等于开始周
    if (formData.startWeek && formData.endWeek && formData.startWeek > formData.endWeek) {
      uni.showToast({
        title: '结束周必须大于等于开始周',
        icon: 'none',
      })
      return false
    }
  } else if (currentPage.value === 3) {
    // 验证上课时间安排
    const hasInvalidSchedule = scheduleList.value.some(
      (item) => !item.weekday || !item.period || !item.venue,
    )

    if (hasInvalidSchedule) {
      uni.showToast({
        title: '请填写完整的上课时间信息',
        icon: 'none',
      })
      return false
    }
  } else if (currentPage.value === 4) {
    // 验证教材信息
    const hasInvalidTextbook = textbookList.value.some(
      (item) => !item.name || !item.author || !item.publisher,
    )

    if (hasInvalidTextbook) {
      uni.showToast({
        title: '请填写完整的教材信息',
        icon: 'none',
      })
      return false
    }
  }

  return isValid
}

// 下一步按钮点击事件
const handleNextClick = () => {
  if (currentPage.value < totalPages.value) {
    if (validateCurrentPage()) {
      goToPage(currentPage.value + 1)
    }
  } else {
    submitForm()
  }
}

// 上一步按钮点击事件
const handlePrevClick = () => {
  if (currentPage.value > 1) {
    goToPage(currentPage.value - 1)
  }
}

// 课程选择器相关状态
const showCourseSelector = ref(false)

// 教材选择器相关状态
const showTextbookSelector = ref(false)
const currentEditingTextbookId = ref<number | null>(null)

// 处理课程选择
const handleCourseSelect = (course: any) => {
  // 保存完整的课程对象到courseCode字段
  formData.courseCode = {
    courseName: course.courseName || '',
    courseCode: course.courseCode || '',
    ...course, // 保留其他字段
  }
  showCourseSelector.value = false
}

// 显示课程选择器
const showCourseSelectorModal = () => {
  showCourseSelector.value = true
}

// 关闭课程选择器
const closeCourseSelector = () => {
  showCourseSelector.value = false
}

// 显示教材选择器
const openTextbookSelector = (id: number) => {
  currentEditingTextbookId.value = id
  showTextbookSelector.value = true
}

// 关闭教材选择器
const closeTextbookSelector = () => {
  showTextbookSelector.value = false
  currentEditingTextbookId.value = null
}

// 处理教材选择
const handleTextbookSelect = (textbook: TextbookInfo) => {
  if (currentEditingTextbookId.value === null) return

  const index = textbookList.value.findIndex((item) => item.id === currentEditingTextbookId.value)
  if (index !== -1) {
    textbookList.value[index] = {
      ...textbookList.value[index],
      name: textbook.name,
      author: textbook.mainEditor,
      publisher: textbook.publishingHouse,
      isbn: textbook.isbn,
      code: textbook.booksCode || '',
      textbookId: textbook.id, // 设置后端教材ID
    }
  }

  closeTextbookSelector()
}

// 加载树形班级数据
const loadTreeClassData = async () => {
  try {
    const treeData = await getTreeClassList()
    // 这里不需要修改组件内部数据，ClassTreeSelector组件会自动使用API返回的数据
  } catch (error) {
    console.error('获取树形班级数据失败:', error)
    uni.showToast({
      title: '获取班级数据失败',
      icon: 'none',
    })
  }
}
</script>

<template>
  <view class="course-apply-page">
    <!--     表单头部
    <view class="form-header flex items-center mb-5">
      <view
        class="header-icon flex-shrink-0 flex items-center justify-center bg-primary text-white rounded-lg mr-3"
      >
        <text class="iconfont icon-plus"></text>
      </view>
      <view class="header-text flex-1">
        <view class="header-title font-semibold text-lg">申报新课程</view>
        <view class="header-subtitle text-gray-500 text-sm">2023-2024学年第二学期</view>
      </view>
    </view>-->

    <!-- 步骤指示器 -->
    <view class="step-indicator relative mb-6">
      <view class="progress-line absolute" :style="{ width: progressWidth }"></view>
      <view
        class="step flex items-center justify-center rounded-full relative z-1"
        :class="{ active: currentPage === 1, completed: currentPage > 1 }"
      >
        <text v-if="currentPage === 1">1</text>
        <text class="iconfont icon-check" v-else></text>
        <view class="step-label absolute text-xs" :class="{ active: currentPage === 1 }">
          基本信息
        </view>
      </view>
      <view
        class="step flex items-center justify-center rounded-full relative z-1"
        :class="{ active: currentPage === 2, completed: currentPage > 2 }"
      >
        <text v-if="currentPage <= 2">2</text>
        <text class="iconfont icon-check" v-else></text>
        <view class="step-label absolute text-xs" :class="{ active: currentPage === 2 }">
          学时与班级
        </view>
      </view>
      <view
        class="step flex items-center justify-center rounded-full relative z-1"
        :class="{ active: currentPage === 3, completed: currentPage > 3 }"
      >
        <text v-if="currentPage <= 3">3</text>
        <text class="iconfont icon-check" v-else></text>
        <view class="step-label absolute text-xs" :class="{ active: currentPage === 3 }">
          上课时间
        </view>
      </view>
      <view
        class="step flex items-center justify-center rounded-full relative z-1"
        :class="{ active: currentPage === 4 }"
      >
        <text>4</text>
        <view class="step-label absolute text-xs" :class="{ active: currentPage === 4 }">
          教材信息
        </view>
      </view>
    </view>

    <!-- 表单内容 -->
    <form>
      <!-- 第一步：基本信息 -->
      <view v-show="currentPage === 1">
        <view class="form-section mb-5">
          <view class="form-section-title pb-2 mb-3 border-b border-gray-100 font-semibold">
            课程基本信息
          </view>

          <view class="form-group mb-4">
            <text class="form-label block mb-2 text-gray-700">
              学年学期
              <text class="text-danger">*</text>
            </text>
            <view class="form-select bg-gray-100 rounded-lg p-3 w-full text-base readonly">
              {{
                formData.semesters
                  ? semesterOptions.find((item) => item.value === formData.semesters)?.label
                  : '获取学年学期中...'
              }}
            </view>
          </view>

          <view class="form-group mb-4">
            <text class="form-label block mb-2 text-gray-700">
              挂靠系部
              <text class="text-danger">*</text>
            </text>
            <picker
              mode="selector"
              :range="departmentOptions"
              range-key="label"
              @change="handleDepartmentChange"
            >
              <view class="form-select bg-gray-100 rounded-lg p-3 w-full text-base">
                <text v-if="formData.deptCode">
                  {{ departmentOptions.find((item) => item.value === formData.deptCode)?.label }}
                </text>
                <text v-else class="text-gray-400">请选择挂靠系部</text>
              </view>
            </picker>
          </view>

          <view class="form-group mb-4">
            <text class="form-label block mb-2 text-gray-700">
              选课课程
              <text class="text-danger">*</text>
            </text>
            <view class="relative">
              <view
                class="form-select bg-gray-100 rounded-lg p-3 w-full flex justify-between items-center"
                @click="showCourseSelectorModal"
              >
                <text v-if="formData.courseCode.courseName">
                  {{ formData.courseCode.courseName }} ({{ formData.courseCode.courseCode }})
                </text>
                <text v-else class="text-gray-400">请选择课程</text>
                <wd-icon name="arrow-right" size="16px" class="text-gray-400"></wd-icon>
              </view>
            </view>
          </view>

          <view class="form-group mb-4">
            <text class="form-label block mb-2 text-gray-700">
              挂靠教研室
              <text class="text-danger">*</text>
            </text>
            <picker
              mode="selector"
              :range="researchOfficeOptions"
              range-key="label"
              @change="handleResearchOfficeChange"
              :disabled="!formData.deptCode"
            >
              <view
                class="form-select bg-gray-100 rounded-lg p-3 w-full text-base"
                :class="{ disabled: !formData.deptCode }"
              >
                <text v-if="formData.teachOfficeCode">
                  {{
                    researchOfficeOptions.find((item) => item.value === formData.teachOfficeCode)
                      ?.label
                  }}
                </text>
                <text v-else class="text-gray-400">
                  {{ !formData.deptCode ? '请先选择挂靠系部' : '请选择挂靠教研室' }}
                </text>
              </view>
            </picker>
          </view>

          <view class="form-row flex gap-3 mb-4">
            <view class="form-col flex-1">
              <text class="form-label block mb-2 text-gray-700">
                课程类型
                <text class="text-danger">*</text>
              </text>
              <picker
                mode="selector"
                :range="dictOptions.courseTypeOptions"
                range-key="label"
                @change="
                  (e) => (formData.taskType = dictOptions.courseTypeOptions[e.detail.value].value)
                "
              >
                <view class="form-select bg-gray-100 rounded-lg p-3 w-full text-base">
                  <text v-if="formData.taskType">
                    {{
                      dictOptions.courseTypeOptions.find((item) => item.value === formData.taskType)
                        ?.label
                    }}
                  </text>
                  <text v-else class="text-gray-400">请选择课程类型</text>
                </view>
              </picker>
            </view>
            <view class="form-col flex-1">
              <text class="form-label block mb-2 text-gray-700">课程分类</text>
              <picker
                mode="selector"
                :range="dictOptions.courseCategoryOptions"
                range-key="label"
                @change="
                  (e) =>
                    (formData.courseCategory =
                      dictOptions.courseCategoryOptions[e.detail.value].value)
                "
              >
                <view class="form-select bg-gray-100 rounded-lg p-3 w-full text-base">
                  <text v-if="formData.courseCategory">
                    {{
                      dictOptions.courseCategoryOptions.find(
                        (item) => item.value === formData.courseCategory,
                      )?.label
                    }}
                  </text>
                  <text v-else class="text-gray-400">请选择课程分类</text>
                </view>
              </picker>
            </view>
          </view>

          <view class="form-row flex gap-3 mb-4">
            <view class="form-col flex-1">
              <text class="form-label block mb-2 text-gray-700">
                周学时
                <text class="text-danger">*</text>
              </text>
              <input
                type="digit"
                v-model="formData.weekHours"
                class="form-input bg-gray-100 rounded-lg p-3 w-full"
                placeholder="请输入周学时"
                confirm-type="done"
                @focus="handleFocus('weekHours')"
                @blur="handleBlur"
                @input="(e) => handleNumberInput('weekHours', e)"
              />
            </view>
            <view class="form-col flex-1">
              <text class="form-label block mb-2 text-gray-700">
                学分
                <text class="text-danger">*</text>
              </text>
              <input
                type="digit"
                v-model="formData.creditHour"
                class="form-input bg-gray-100 rounded-lg p-3 w-full"
                placeholder="请输入学分"
                confirm-type="done"
                @focus="handleFocus('creditHour')"
                @blur="handleBlur"
                @input="(e) => handleNumberInput('creditHour', e)"
              />
            </view>
          </view>

          <view class="form-group mb-4">
            <text class="form-label block mb-2 text-gray-700">主要内容简介</text>
            <textarea
              v-model="formDisplay.courseDescription"
              class="form-textarea bg-gray-100 rounded-lg p-3 w-full min-h-30"
              placeholder="请简要介绍课程内容、目标等信息"
              @input="
                (e) => {
                  formData.remark = e.detail.value
                }
              "
              @focus="handleFocus('courseDescription')"
              @blur="handleBlur"
              auto-height
            ></textarea>
          </view>
        </view>
      </view>

      <!-- 第二步：学时与班级信息 -->
      <view v-show="currentPage === 2">
        <view class="form-section mb-5">
          <view class="form-section-title pb-2 mb-3 border-b border-gray-100 font-semibold">
            学时安排
          </view>

          <view class="form-group mb-4">
            <text class="form-label block mb-2 text-gray-700">
              学期总学时
              <text class="text-danger">*</text>
            </text>
            <input
              type="number"
              v-model="formDisplay.totalHours"
              class="form-input bg-gray-100 rounded-lg p-3 w-full readonly"
              placeholder="自动计算"
              readonly
            />
            <view class="form-note text-xs text-gray-500 mt-1">
              = 讲课学时 + 实验学时 + 上机学时
            </view>
          </view>

          <view class="form-row flex gap-3 mb-4">
            <view class="form-col flex-1">
              <text class="form-label block mb-2 text-gray-700">讲课学时</text>
              <input
                type="number"
                v-model="formDisplay.lectureHours"
                class="form-input bg-gray-100 rounded-lg p-3 w-full"
                placeholder="请输入讲课学时"
                confirm-type="done"
                @input="(e) => handleHoursInput('lectureHours', e.detail.value)"
                @focus="handleFocus('lectureHours')"
                @blur="handleBlur"
              />
            </view>
            <view class="form-col flex-1">
              <text class="form-label block mb-2 text-gray-700">实验学时</text>
              <input
                type="number"
                v-model="formDisplay.labHours"
                class="form-input bg-gray-100 rounded-lg p-3 w-full"
                placeholder="请输入实验学时"
                confirm-type="done"
                @input="(e) => handleHoursInput('labHours', e.detail.value)"
                @focus="handleFocus('labHours')"
                @blur="handleBlur"
              />
            </view>
          </view>

          <view class="form-group mb-4">
            <text class="form-label block mb-2 text-gray-700">上机学时</text>
            <input
              type="number"
              v-model="formDisplay.computerHours"
              class="form-input bg-gray-100 rounded-lg p-3 w-full"
              placeholder="请输入上机学时"
              confirm-type="done"
              @input="(e) => handleHoursInput('computerHours', e.detail.value)"
              @focus="handleFocus('computerHours')"
              @blur="handleBlur"
            />
          </view>

          <view class="form-row flex gap-3 mb-4">
            <view class="form-col flex-1">
              <text class="form-label block mb-2 text-gray-700">
                授课方式
                <text class="text-danger">*</text>
              </text>
              <picker
                mode="selector"
                :range="dictOptions.teachingMethodOptions"
                range-key="label"
                @change="
                  (e) =>
                    (formData.teachingMethod =
                      dictOptions.teachingMethodOptions[e.detail.value].value)
                "
              >
                <view class="form-select bg-gray-100 rounded-lg p-3 w-full text-base">
                  <text v-if="formData.teachingMethod">
                    {{
                      dictOptions.teachingMethodOptions.find(
                        (item) => item.value === formData.teachingMethod,
                      )?.label
                    }}
                  </text>
                  <text v-else class="text-gray-400">请选择授课方式</text>
                </view>
              </picker>
            </view>
            <view class="form-col flex-1">
              <text class="form-label block mb-2 text-gray-700">
                考核方式
                <text class="text-danger">*</text>
              </text>
              <picker
                mode="selector"
                :range="dictOptions.assessmentMethodOptions"
                range-key="label"
                @change="
                  (e) =>
                    (formData.assessmentMethod =
                      dictOptions.assessmentMethodOptions[e.detail.value].value)
                "
              >
                <view class="form-select bg-gray-100 rounded-lg p-3 w-full text-base">
                  <text v-if="formData.assessmentMethod">
                    {{
                      dictOptions.assessmentMethodOptions.find(
                        (item) => item.value === formData.assessmentMethod,
                      )?.label
                    }}
                  </text>
                  <text v-else class="text-gray-400">请选择考核方式</text>
                </view>
              </picker>
            </view>
          </view>

          <view class="form-row flex gap-3 mb-4">
            <view class="form-col flex-1">
              <text class="form-label block mb-2 text-gray-700">
                开放校区
                <text class="text-danger">*</text>
              </text>
              <picker
                mode="selector"
                :range="campusOptions"
                range-key="label"
                @change="
                  (e) => {
                    formDisplay.campus = campusOptions[e.detail.value].label
                    formData.campusCode = campusOptions[e.detail.value].value
                  }
                "
              >
                <view class="form-select bg-gray-100 rounded-lg p-3 w-full text-base">
                  <text v-if="formDisplay.campus">{{ formDisplay.campus }}</text>
                  <text v-else class="text-gray-400">请选择校区</text>
                </view>
              </picker>
            </view>
            <view class="form-col flex-1">
              <text class="form-label block mb-2 text-gray-700">开放年级</text>
              <wd-select-picker
                v-model="formData.startingGrade"
                :columns="gradeOptions"
                placeholder="请选择年级"
                type="checkbox"
                style="background-color: #eeeeee !important"
                @change="
                  (value) => {
                    formDisplay.grade = value.length ? `已选${value.length}个年级` : ''
                  }
                "
              />
            </view>
          </view>

          <view class="form-group mb-4">
            <text class="form-label block mb-2 text-gray-700">开放系部</text>
            <wd-select-picker
              v-model="formData.startClassDeptCode"
              :columns="startClassDeptOptions"
              placeholder="请选择开放系部"
              type="checkbox"
              @change="
                (value) => {
                  formDisplay.teachingDepartment = value.length ? `已选${value.length}个系部` : ''
                }
              "
            />
          </view>

          <view class="form-group mb-4">
            <text class="form-label block mb-2 text-gray-700">开放班级</text>
            <ClassTreeSelector
              v-model="formData.class"
              placeholder="请选择开放班级"
              :multiple="true"
              @change="
                (value) => {
                  // 计算选中的学院和班级数量
                  const collegePrefix = 'college_'
                  const classPrefix = 'class_'

                  const collegeCount = value.filter((v) => v.startsWith(collegePrefix)).length
                  const classCount = value.filter((v) => v.startsWith(classPrefix)).length

                  let displayText = ''
                  if (collegeCount > 0 && classCount > 0) {
                    displayText = `已选${collegeCount}个学院，${classCount}个班级`
                  } else if (collegeCount > 0) {
                    displayText = `已选${collegeCount}个学院`
                  } else if (classCount > 0) {
                    displayText = `已选${classCount}个班级`
                  }

                  formDisplay.teachingClass = displayText
                }
              "
            />
          </view>

          <view class="form-row flex gap-3 mb-4">
            <view class="form-col flex-1">
              <text class="form-label block mb-2 text-gray-700">
                班级限定人数
                <text class="text-danger">*</text>
              </text>
              <input
                type="number"
                v-model="formDisplay.classCapacity"
                class="form-input bg-gray-100 rounded-lg p-3 w-full"
                placeholder="请输入人数"
                confirm-type="done"
                @input="
                  (e) => {
                    handleNumberInput('limitCount', e)
                    formDisplay.classCapacity = e.detail.value
                  }
                "
                @focus="handleFocus('classCapacity')"
                @blur="handleBlur"
              />
            </view>
            <view class="form-col flex-1">
              <text class="form-label block mb-2 text-gray-700">
                课程限定人数
                <text class="text-danger">*</text>
              </text>
              <input
                type="number"
                v-model="formDisplay.courseCapacity"
                class="form-input bg-gray-100 rounded-lg p-3 w-full"
                placeholder="请输入人数"
                confirm-type="done"
                @input="
                  (e) => {
                    handleNumberInput('maxCount', e)
                    formDisplay.courseCapacity = e.detail.value
                  }
                "
                @focus="handleFocus('courseCapacity')"
                @blur="handleBlur"
              />
            </view>
          </view>

          <view class="form-row flex gap-3 mb-4">
            <view class="form-col flex-1">
              <text class="form-label block mb-2 text-gray-700">任务开始周</text>
              <input
                type="number"
                v-model="formData.startWeek"
                class="form-input bg-gray-100 rounded-lg p-3 w-full"
                placeholder="请输入开始周"
                confirm-type="done"
                @input="(e) => handleNumberInput('startWeek', e)"
                @focus="handleFocus('startWeek')"
                @blur="handleBlur"
              />
            </view>
            <view class="form-col flex-1">
              <text class="form-label block mb-2 text-gray-700">任务结束周</text>
              <input
                type="number"
                v-model="formData.endWeek"
                class="form-input bg-gray-100 rounded-lg p-3 w-full"
                placeholder="请输入结束周"
                confirm-type="done"
                @input="(e) => handleNumberInput('endWeek', e)"
                @focus="handleFocus('endWeek')"
                @blur="handleBlur"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 第三步：上课时间及场地要求（原第四步） -->
      <view v-show="currentPage === 3">
        <view class="bg-white rounded-lg p-3 mb-4">
          <view class="text-base font-bold mb-3">上课时间及场地要求</view>

          <!-- 数据行为空状态 -->
          <view v-if="scheduleList.length === 0" class="py-8 text-center text-gray-400">
            <wd-icon name="info" size="24px" class="mb-2" />
            <view>暂无数据，请添加上课时间</view>
          </view>

          <!-- 上课时间卡片列表 -->
          <view class="space-y-3">
            <view
              v-for="item in scheduleList"
              :key="item.id"
              class="bg-gray-50 rounded-lg p-3 border border-gray-100"
            >
              <view class="flex justify-between items-center mb-3 pb-2 border-b border-gray-100">
                <view class="flex items-center">
                  <view
                    class="w-6 h-6 flex items-center justify-center bg-primary-light rounded-full mr-2 text-primary font-medium"
                  >
                    {{ item.id }}
                  </view>
                  <text class="font-medium">第{{ item.id }}条</text>
                </view>
                <view
                  v-if="scheduleList.length > 1"
                  class="flex items-center bg-red-50 text-red-500 rounded-full p-1"
                  @click="deleteSchedule(item.id)"
                >
                  <wd-icon name="delete" size="16px" />
                </view>
              </view>

              <view class="space-y-3">
                <view>
                  <text class="text-sm text-gray-600 mb-1.5 block">星期</text>
                  <picker
                    :range="weekdayOptions"
                    range-key="label"
                    @change="(e) => (item.weekday = weekdayOptions[e.detail.value].value)"
                  >
                    <view
                      class="form-select bg-gray-100 rounded-lg p-3 w-full flex justify-between items-center"
                    >
                      <text v-if="weekdayOptions.find((o) => o.value === item.weekday)">
                        {{ weekdayOptions.find((o) => o.value === item.weekday)?.label }}
                      </text>
                      <text v-else class="text-gray-400">请选择星期</text>
                      <wd-icon name="arrow-down" size="12px" class="text-gray-400" />
                    </view>
                  </picker>
                </view>

                <view>
                  <text class="text-sm text-gray-600 mb-1.5 block">节次</text>
                  <picker
                    :range="periodOptions"
                    range-key="label"
                    @change="(e) => (item.period = periodOptions[e.detail.value].value)"
                  >
                    <view
                      class="form-select bg-gray-100 rounded-lg p-3 w-full flex justify-between items-center"
                    >
                      <text v-if="periodOptions.find((o) => o.value === item.period)">
                        {{ periodOptions.find((o) => o.value === item.period)?.label }}
                      </text>
                      <text v-else class="text-gray-400">请选择节次</text>
                      <wd-icon name="arrow-down" size="12px" class="text-gray-400" />
                    </view>
                  </picker>
                </view>

                <view>
                  <text class="text-sm text-gray-600 mb-1.5 block">场地要求</text>
                  <picker
                    :range="venueTypeOptions"
                    range-key="label"
                    @change="(e) => (item.venue = venueTypeOptions[e.detail.value].value)"
                  >
                    <view
                      class="form-select bg-gray-100 rounded-lg p-3 w-full flex justify-between items-center"
                    >
                      <text v-if="venueTypeOptions.find((o) => o.value === item.venue)">
                        {{ venueTypeOptions.find((o) => o.value === item.venue)?.label }}
                      </text>
                      <text v-else class="text-gray-400">请选择场地要求</text>
                      <wd-icon name="arrow-down" size="12px" class="text-gray-400" />
                    </view>
                  </picker>
                </view>
              </view>
            </view>
          </view>

          <!-- 添加按钮 -->
          <view class="mt-4">
            <button
              class="bg-primary text-white w-full rounded-lg flex items-center justify-center"
              @click="addSchedule"
            >
              <wd-icon name="add-circle" size="16px" class="mr-1" />
              添加上课时间
            </button>
          </view>

          <!-- 底部提示 -->
          <view class="mt-3 text-xs text-gray-500">
            请选择每周上课时间并填写场地需求，同一课程可添加多个时间段
          </view>
        </view>
      </view>

      <!-- 第四步：教材信息（原第五步） -->
      <view v-show="currentPage === 4">
        <view class="bg-white rounded-lg p-3 mb-4">
          <view class="text-base font-bold mb-3">教材信息</view>

          <!-- 数据行为空状态 -->
          <view v-if="textbookList.length === 0" class="py-8 text-center text-gray-400">
            <wd-icon name="info" size="24px" class="mb-2" />
            <view>暂无数据，请添加教材信息</view>
          </view>

          <!-- 教材卡片列表 -->
          <view class="space-y-3 px-0.5">
            <view
              v-for="item in textbookList"
              :key="item.id"
              class="bg-gray-50 rounded-lg p-3 border border-gray-100 box-border"
            >
              <view class="flex justify-between items-center mb-3 pb-2 border-b border-gray-100">
                <view class="flex items-center">
                  <view
                    class="w-6 h-6 flex items-center justify-center bg-primary-light rounded-full mr-2 text-primary font-medium"
                  >
                    {{ item.id }}
                  </view>
                  <text class="font-medium">教材 #{{ item.id }}</text>
                </view>
                <view
                  v-if="textbookList.length > 1"
                  class="flex items-center bg-red-50 text-red-500 rounded-full p-1"
                  @click="deleteTextbook(item.id)"
                >
                  <wd-icon name="delete" size="16px" />
                </view>
              </view>

              <view class="space-y-3">
                <view>
                  <text class="text-sm text-gray-600 mb-1.5 block">教材名称</text>
                  <view
                    class="form-select bg-gray-100 rounded-lg p-3 w-full flex justify-between items-center"
                    @click="openTextbookSelector(item.id)"
                  >
                    <text v-if="item.name" class="flex-1 text-ellipsis overflow-hidden pr-1">
                      {{ item.name }}
                    </text>
                    <text v-else class="text-gray-400">点击选择教材</text>
                    <wd-icon
                      name="arrow-right"
                      size="16px"
                      class="text-gray-400 flex-shrink-0 ml-1"
                    />
                  </view>
                </view>

                <view v-if="item.name">
                  <text class="text-sm text-gray-600 mb-1.5 block">作者</text>
                  <view
                    class="form-input bg-gray-100 rounded-lg p-3 w-full box-border text-gray-700 readonly"
                  >
                    {{ item.author || '暂无作者信息' }}
                  </view>
                </view>

                <view v-if="item.name">
                  <text class="text-sm text-gray-600 mb-1.5 block">出版社</text>
                  <view
                    class="form-input bg-gray-100 rounded-lg p-3 w-full box-border text-gray-700 readonly"
                  >
                    {{ item.publisher || '暂无出版社信息' }}
                  </view>
                </view>

                <view v-if="item.name">
                  <text class="text-sm text-gray-600 mb-1.5 block">ISBN</text>
                  <view
                    class="form-input bg-gray-100 rounded-lg p-3 w-full box-border text-gray-700 readonly"
                  >
                    {{ item.isbn || '暂无ISBN信息' }}
                  </view>
                </view>
                <view v-if="item.name">
                  <text class="text-sm text-gray-600 mb-1.5 block">教材代码</text>
                  <view
                    class="form-input bg-gray-100 rounded-lg p-3 w-full box-border text-gray-700 readonly"
                  >
                    {{ item.textbookId || '暂无教材ID' }}
                  </view>
                </view>

                <view>
                  <text class="text-sm text-gray-600 mb-1.5 block">教材选用类别</text>
                  <picker
                    :range="selectionTypeOptions"
                    range-key="label"
                    @change="
                      (e) => (item.selectionType = selectionTypeOptions[e.detail.value].value)
                    "
                  >
                    <view
                      class="form-select bg-gray-100 rounded-lg p-3 w-full flex justify-between items-center"
                    >
                      <text v-if="selectionTypeOptions.find((o) => o.value === item.selectionType)">
                        {{
                          selectionTypeOptions.find((o) => o.value === item.selectionType)?.label
                        }}
                      </text>
                      <text v-else class="text-gray-400">请选择教材选用类别</text>
                      <wd-icon
                        name="arrow-down"
                        size="12px"
                        class="text-gray-400 flex-shrink-0 ml-1"
                      />
                    </view>
                  </picker>
                </view>
              </view>
            </view>
          </view>

          <!-- 添加按钮 -->
          <view class="mt-4">
            <button
              class="bg-primary text-white w-full rounded-lg flex items-center justify-center"
              @click="addTextbook"
            >
              <wd-icon name="add-circle" size="16px" class="mr-1" />
              添加教材信息
            </button>
          </view>

          <!-- 底部提示 -->
          <view class="mt-3 text-xs text-gray-500">
            请填写教材基本信息，如有多本教材可点击添加按钮新增
          </view>
        </view>
      </view>

      <!-- 底部空白，为固定按钮腾出空间 -->
      <view class="pb-30"></view>
    </form>

    <!-- 课程选择器弹窗 -->
    <wd-popup
      v-model="showCourseSelector"
      position="bottom"
      :close-on-click-modal="true"
      closable
      custom-style="height: 80vh; border-radius: 16px 16px 0 0;"
      @close="closeCourseSelector"
    >
      <view class="popup-header px-4 py-3 border-b border-gray-100">
        <view class="text-lg font-semibold">选择课程</view>
      </view>
      <view class="popup-content h-full">
        <CourseSelector @select="handleCourseSelect" />
      </view>
    </wd-popup>

    <!-- 教材选择器弹窗 -->
    <wd-popup
      v-model="showTextbookSelector"
      position="bottom"
      :close-on-click-modal="true"
      closable
      custom-style="height: 80vh; border-radius: 16px 16px 0 0;"
      @close="closeTextbookSelector"
    >
      <view class="popup-header px-4 py-3 border-b border-gray-100">
        <view class="text-lg font-semibold">选择教材</view>
      </view>
      <view class="popup-content h-full">
        <TextbookSelector @select="handleTextbookSelect" />
      </view>
    </wd-popup>

    <!-- 固定在底部的按钮 -->
    <view
      class="form-submit-button fixed bottom-0 left-0 right-0 p-4 bg-white border-t border-gray-100"
    >
      <view class="flex gap-4">
        <view
          v-if="currentPage > 1"
          @click="handlePrevClick"
          class="btn-outline flex-1 rounded-lg py-3 px-5 border border-primary text-primary text-center"
        >
          上一步
        </view>
        <view
          @click="handleNextClick"
          class="btn-primary flex-1 rounded-lg py-3 px-5 bg-primary text-white text-center"
        >
          <text v-if="currentPage < 4">下一步</text>
          <text v-else>提交申请</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
:deep(.wd-select-picker__cell) {
  background-color: #eeeeee !important;
}

.course-apply-page {
  min-height: 100vh;
  padding: 30rpx;
  background-color: #f7f7f7;
}

.header-icon {
  width: 72rpx;
  height: 72rpx;
  font-size: 36rpx;
}

.step-indicator {
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  width: 90%;
  max-width: 750rpx;
  padding: 0;
  margin-right: auto;
  margin-bottom: 70rpx;
  margin-left: auto;
}

.step-indicator::before {
  position: absolute;
  top: 16rpx;
  right: 18rpx;
  left: 18rpx;
  z-index: 0;
  height: 4rpx;
  content: '';
  background-color: #f2f2f7;
}

.progress-line {
  position: absolute;
  top: 16rpx;
  left: 18rpx;
  z-index: 0;
  max-width: calc(100% - 36rpx - 4rpx); /* 再减去一点点余量，确保不会超出 */
  height: 4rpx;
  background-color: var(--primary-color, #2979ff);
  transition: width 0.3s;
}

.step {
  z-index: 2;
  width: 36rpx;
  height: 36rpx;
  font-size: 24rpx;
  color: #999;
  background-color: #f2f2f7;
}

.step.active {
  color: white;
  background-color: var(--primary-color, #2979ff);
}

.step.completed {
  color: white;
  background-color: var(--success-color, #07c160);
}

.step-label {
  position: absolute;
  top: 48rpx;
  left: 50%;
  width: 150rpx;
  font-size: 20rpx;
  line-height: 1.2;
  color: #999;
  text-align: center;
  white-space: normal;
  transform: translateX(-50%);
}

.step-label.active {
  font-weight: 500;
  color: var(--primary-color, #2979ff);
}

// 统一表单元素样式
.form-input,
.form-textarea,
.form-select {
  box-sizing: border-box;
  width: 100%;
  min-height: 80rpx;
  padding: 12rpx 24rpx;
  line-height: 1.5;
  color: #333333;
  background-color: #eeeeee;
  border: none;
  border-radius: 12rpx;
}

input.form-input {
  height: 80rpx;
}

.form-textarea {
  height: auto;
  min-height: 200rpx;
  padding: 20rpx;
}

// 统一placeholder颜色
input::placeholder,
textarea::placeholder {
  color: #999999;
}

// 统一只读和禁用状态样式
input:disabled,
input[readonly],
.form-select.disabled,
.form-select.readonly,
.opacity-50 {
  color: #666666;
  background-color: #eeeeee;
  opacity: 1;
}

// 统一wd组件选择器样式
:deep(.wd-select-picker__field) {
  min-height: 80rpx !important;
  background-color: #eeeeee !important;
  border: none !important;
  border-radius: 12rpx !important;
}

:deep(.wd-select-picker__field-placeholder) {
  color: #999999 !important;
}

:deep(.wd-select-picker__field-value) {
  color: #333333 !important;
}

:deep(.wd-select-picker__field.is-disabled) {
  color: #666666 !important;
  background-color: #eeeeee !important;
  opacity: 1 !important;
}

// 第三步上课时间和第四步教材信息中选择器样式
.border {
  border-color: #e6e6e6 !important;
}

view.border.p-2.rounded-lg.bg-white {
  background-color: #f5f5f5 !important;
}

.text-gray-400 {
  color: #999999 !important;
}

// 统一已选信息展示样式
.p-2.rounded-lg.bg-gray-50 {
  color: #666666 !important;
  background-color: #eeeeee !important;
}

.schedule-item {
  background-color: white;
  border-radius: 20rpx;
}

.add-schedule-button {
  background-color: rgba(41, 121, 255, 0.1);
}

.teacher-avatar {
  color: var(--primary-color, #2979ff);
  background-color: #e6f7ff;
}

.form-submit-button {
  z-index: 1;
  padding-bottom: calc(env(safe-area-inset-bottom) + 30rpx);
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

// UnoCSS 补充
.text-primary {
  color: var(--primary-color, #2979ff);
}

.text-danger {
  color: var(--danger-color, #fa5151);
}

.bg-primary {
  background-color: var(--primary-color, #2979ff);
}

.bg-primary-light {
  background-color: rgba(41, 121, 255, 0.1);
}

.border-primary {
  border-color: var(--primary-color, #2979ff);
}

// 增强输入框响应式
.form-group {
  position: relative;
  margin-bottom: 30rpx;
}

.form-label {
  display: block;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  color: #333;
}

// 课程选择器弹窗样式
.course-selector-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.course-selector-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.4);
}

.course-selector-container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
}

.course-selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border-bottom: 2rpx solid #eee;
}

.close-icon {
  padding: 10rpx;
  font-size: 32rpx;
  color: #999;
}

// 课程选择器弹窗样式
.popup-header {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  border-bottom: 1px solid #f2f2f2;
}

.popup-content {
  height: calc(80vh - 60px);
}

// 添加教材输入框的特殊样式
input.border {
  box-sizing: border-box;
  max-width: 100%;
  text-overflow: ellipsis;
}

// 增加教材名称文本溢出控制
.text-ellipsis {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
