<route lang="json5">
{
  style: {
    navigationBarTitleText: '申请超时授课确认',
  },
}
</route>
<template>
  <!-- 使用FormWithApproval组件，提供基本信息和审批流程两个标签页 -->
  <FormWithApproval
    :id="detail.id"
    :code="formCode"
    :show-workflow="true"
    v-model="activeTab"
    @return="handleBack"
  >
    <!-- 基本信息表单内容 -->
    <template #form-content>
      <view class="p-3 bg-white">
        <!-- 详情信息表格 -->
        <view class="info-table mb-4">
          <view class="info-row" v-for="(item, index) in infoItems" :key="index">
            <view class="info-label">{{ item.label }}</view>
            <view class="info-value">
              <template v-if="item.type === 'status'">
                <text :class="getDictClass(submitStatusDict, item.value.toString())">
                  {{ getDictLabel(submitStatusDict, item.value.toString()) }}
                </text>
              </template>
              <template v-else>
                {{ item.value }}
              </template>
            </view>
          </view>
        </view>

        <!-- 申请说明 -->
        <view class="mb-4">
          <view class="text-sm font-medium mb-1">
            申请说明
            <text class="text-red-500">*</text>
          </view>
          <textarea
            class="form-textarea"
            v-model="applyReason"
            placeholder="请输入申请说明（必填）"
          />
        </view>

        <!-- 使用文件上传组件 -->
        <FileUploader v-model="attachmentList" upload-type="scheduleConfirm" title="附件" />
      </view>
    </template>

    <template #form-buttons>
      <view class="flex space-x-2 p-3 bg-white">
        <view
          class="flex-1 bg-gray-100 text-gray-700 py-2 rounded-md text-center"
          @click="handleBack"
        >
          返回
        </view>
        <view
          class="flex-1 bg-blue-500 text-white py-2 rounded-md text-center"
          @click="handleSubmit"
        >
          提交确认
        </view>
      </view>
    </template>
  </FormWithApproval>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import {
  getTeacherScheduleApplyConfirm,
  submitTeacherScheduleApplyConfirm,
} from '@/service/teacher'
import type { TeacherScheduleApplyConfirmResponse } from '@/types/teacher'
import { useToast } from 'wot-design-uni'
import { loadDictData, getDictLabel, getDictClass } from '@/utils/dict'
import type { DictData } from '@/types/system'
import FileUploader from '@/components/common/FileUploader.vue'
import FormWithApproval from '@/components/FormWithApproval/index.vue'

// 添加toast提示
const toast = useToast()

// 获取页面参数
const query = defineProps<{ id: string }>()

// 表单代码
const formCode = ref('skdjcsqr')

// 设置页面标题
uni.setNavigationBarTitle({ title: '申请超时授课确认' })

// ===== 数据定义 =====
const detail = ref<TeacherScheduleApplyConfirmResponse>({} as TeacherScheduleApplyConfirmResponse)
const applyReason = ref<string>('')
const attachmentList = ref<Array<{ url: string; name: string }>>([])

// 当前激活的标签页
const activeTab = ref(0)

// ===== 字典相关 =====
const submitStatusDict = ref<DictData[]>([])

// ===== 计算属性 =====
// 信息项配置
const infoItems = computed(() => [
  { label: '课程', value: detail.value.jxrw?.kcmc || '', type: 'text' },
  { label: '授课教师', value: detail.value.skjsxm || '', type: 'text' },
  { label: '班级', value: detail.value.skbjmc || '', type: 'text' },
  { label: '授课人数', value: detail.value.syrs || '', type: 'text' },
  { label: '授课日期', value: detail.value.skrq || '', type: 'text' },
  { label: '节次', value: detail.value.jcshow || '', type: 'text' },
  { label: '授课方式', value: detail.value.skfsmc || '未设置', type: 'text' },
  { label: '场地', value: detail.value.skcdmc || '未使用', type: 'text' },
])

// ===== 方法定义 =====
// 加载字典数据
const loadDicts = async () => {
  try {
    const dictData = await loadDictData(['SYS_SUBMIT_STATUS'])
    submitStatusDict.value = dictData.SYS_SUBMIT_STATUS || []
  } catch (error) {
    console.error('加载字典数据失败', error)
  }
}

// 加载详情数据
const loadDetail = async () => {
  if (!query.id) {
    toast.show({ msg: '缺少必要参数', iconName: 'error' })
    return
  }

  try {
    toast.loading('加载中...')
    await loadDicts()
    const res = await getTeacherScheduleApplyConfirm(Number(query.id))

    detail.value = res

    // 回填申请说明
    if (res.sqyy) {
      applyReason.value = res.sqyy
    }

    // 回填附件列表
    if (res.sqfjlb) {
      // 处理附件字符串，格式为：url|name,url|name
      const attachments = res.sqfjlb.split(',').filter((item) => item)
      attachmentList.value = attachments.map((item) => {
        const [url, name] = item.split('|')
        return { url, name: name || '附件' }
      })
    }

    toast.close()
  } catch (error) {
    toast.close()
    console.error('获取课程安排确认详情失败', error)
  }
}

// 返回上一页
const handleBack = () => {
  uni.navigateBack()
}

// 提交确认
const handleSubmit = async () => {
  // 验证申请说明是否填写
  if (!applyReason.value.trim()) {
    toast.show({ msg: '请填写申请说明', iconName: 'error' })
    return
  }

  try {
    toast.loading('提交中...')

    // 构建附件列表字符串
    const attachments = attachmentList.value.map((item) => `${item.url}|${item.name}`).join(',')

    // 调用API提交确认
    const result = await submitTeacherScheduleApplyConfirm({
      id: Number(query.id),
      jxrwid: detail.value.jxrwid,
      sqyy: applyReason.value,
      sqfjlb: attachments,
    })

    toast.close()
    toast.show({ msg: result.msg || '提交成功', iconName: 'success' })

    // 提交成功后返回上一页
    setTimeout(() => uni.navigateBack(), 1500)
  } catch (error) {
    toast.close()
    console.error('提交课程安排确认失败', error)
  }
}

// 页面加载时获取数据
onMounted(() => loadDetail())
</script>

<style lang="scss" scoped>
.info-table {
  overflow: hidden;
  border: 1px solid #eee;
  border-radius: 4px;
}

.info-row {
  display: flex;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  width: 120rpx;
  padding: 16rpx;
  font-size: 24rpx;
  color: #4b5563;
  background-color: #f9fafb;
  border-right: 1px solid #eee;
}

.info-value {
  flex: 1;
  padding: 16rpx;
  font-size: 24rpx;
  color: #1f2937;
}

.form-textarea {
  box-sizing: border-box;
  width: 100%;
  height: 180rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}
</style>
