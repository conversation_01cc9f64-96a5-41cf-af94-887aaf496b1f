<route lang="json5">
{
  style: {
    navigationBarTitleText: '教师选课信息',
  },
}
</route>

<script setup lang="ts">
import { ref, onMounted, computed, watch, onUnmounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import {
  getSelectCourseList,
  getCurrentSemesterCourses,
  getSelectCourseStatistics,
} from '@/service/selectCourse'
import type { SelectCourseItem, SelectCourseStatisticsResponse } from '@/types/selectCourse'
import { useUserStore } from '@/store/user'
import Pagination from '@/components/Pagination/index.vue'
import { getWorkflowTask } from '@/service/workflow'
import { getSemesterSelect } from '@/service/system'
import type { SemesterSelectResponse } from '@/types/semester'

interface Announcement {
  title: string
  date: string
  content: string
}

interface Schedule {
  day: string
  isToday: boolean
  courses: {
    name: string
    time: string
    location: string
  }[]
}

// 分段控制数据
const activeTab = ref('application-tab')

// API获取的选课列表
const selectCourseList = ref<SelectCourseItem[]>([])
const isLoading = ref(false)
const hasError = ref(false)
const currentSemester = ref('')
// 申报课程分页
const applicationPage = ref(1)
const applicationPageSize = ref(10)
const applicationTotal = ref(0)

// 本学期课程
const currentCourses = ref<SelectCourseItem[]>([])
const currentCoursesTotal = ref(0)
const currentCoursesLoading = ref(false)
const currentCoursesError = ref(false)
const currentSemesterValue = ref('')

// 本学期课程分页
const currentPage = ref(1)
const currentPageSize = ref(10)

// 筛选项
const filterOptions = ref([
  { icon: 'list', name: '全部', active: true },
  { icon: 'book', name: '专业必修', active: false },
  { icon: 'bookmark', name: '专业选修', active: false },
  { icon: 'globe', name: '公共课', active: false },
  { icon: 'sort', name: '排序', active: false },
])

// 切换标签页
const changeTab = (tabId: string) => {
  activeTab.value = tabId
}

// 切换筛选选项
const toggleFilter = (index: number) => {
  filterOptions.value.forEach((item, idx) => {
    item.active = idx === index
  })
  // 这里可以添加实际的筛选逻辑
}

// 从用户存储中获取用户信息
const userStore = useUserStore()

// 教师信息
const teacherInfo = computed(() => {
  return {
    name: userStore.userInfo.realname || '未知姓名',
    department: userStore.userInfo.department || '未知部门',
    title: userStore.userInfo.roleName || '教师',
  }
})

// 申报状态数据
const applyStatus = ref([
  { icon: 'file', number: 0, name: '已申报', color: 'primary' },
  { icon: 'check-circle', number: 0, name: '已通过', color: 'success' },
  { icon: 'clock', number: 0, name: '审核中', color: 'warning' },
  { icon: 'close-circle', number: 0, name: '未通过', color: 'danger' },
  { icon: 'book', number: 0, name: '总门数', color: 'purple' },
])

// 快捷操作
const quickActions = ref([
  { icon: 'add', name: '申报课程', path: '/pages/teacher/teaching-affairs/course-apply' },
])

// 课程公告
const announcements = ref<Announcement[]>()

// 今日授课
const todaySchedule = ref<Schedule>({
  day: '',
  isToday: false,
  courses: [],
})

// 通知信息
const noticeInfo = ref({
  title: '课程申报通知',
  time: '',
  content: '',
})

// 课程申报倒计时
const courseCountdown = ref({
  days: 0,
  hours: 0,
  minutes: 0,
  seconds: 0,
  totalSeconds: 0,
  isActive: false,
})

// 申报截止状态
const isApplicationDeadlinePassed = ref(false)

// 存储计时器引用，方便清除
const countdownTimer = ref<number | null>(null)

// 提示信息
const tipMessage = ref('')
// 通知ID
const noticeId = ref<number | null>(null)
// 存储完整的通知数据
const noticeData = ref<any>(null)

// 路由跳转
const navigateTo = (path: string) => {
  // 如果是申报课程路径且申报截止，则显示提示并阻止导航
  if (
    path === '/pages/teacher/teaching-affairs/course-apply' &&
    isApplicationDeadlinePassed.value
  ) {
    uni.showToast({
      title: '申报已截止，不允许申报新课程',
      icon: 'none',
      duration: 2000,
    })
    return
  }
  uni.navigateTo({ url: path })
}

// 跳转到编辑课程页面
const navigateToEditCourse = (courseId: number, course: SelectCourseItem) => {
  // 首先检查是否已过申报截止时间
  if (isApplicationDeadlinePassed.value) {
    uni.showToast({
      title: '申报已截止，不可编辑课程',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 判断课程是否已经全部审核通过
  if (
    course.teachOfficeAudit === 1 &&
    course.deptAudit === 1 &&
    course.academicAffairsApproval === 1
  ) {
    // 显示提示信息
    uni.showToast({
      title: '课程已审核通过，不可编辑',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 判断是否为可编辑状态：教研室审核阶段或被拒绝状态
  const isRejected =
    course.teachOfficeAudit === 2 || course.deptAudit === 2 || course.academicAffairsApproval === 2
  const isInReview = course.teachOfficeAudit === 0 && course.deptAudit === 0

  if (!isInReview && !isRejected) {
    // 显示提示信息
    uni.showToast({
      title: '当前课程不在可编辑状态',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  console.log('跳转到编辑课程页面, ID:', courseId)
  uni.navigateTo({
    url: `/pages/teacher/teaching-affairs/course-apply?id=${courseId}`,
  })
}

// 跳转到通知详情页
const navigateToNoticeDetail = () => {
  if (noticeId.value === null) {
    return
  }

  // 如果没有完整的通知数据但有ID，创建一个基本通知对象
  const noticeToSave = noticeData.value || {
    id: noticeId.value,
    title: '系统通知',
    content: tipMessage.value,
    department: '系统',
    publishTime: new Date().toLocaleDateString(),
    type: '通知公告',
    isImportant: false,
    isUnread: true,
    // 添加noticeDetail.vue中需要的其他字段
    categoryColor: 'blue', // 默认使用蓝色
    iconBgColor: '#e0f2fe',
    iconColor: '#0284c7',
  }

  try {
    // 将通知数据存储到localStorage
    uni.setStorageSync('NOTICE_DETAIL_DATA', JSON.stringify(noticeToSave))

    // 跳转到通知详情页面
    uni.navigateTo({
      url: `/pages/noticeDetail?id=${noticeId.value}`,
      fail: (err) => {
        console.error('跳转通知详情页失败:', err)
        uni.showToast({
          title: '跳转通知详情页失败',
          icon: 'none',
        })
      },
    })
  } catch (error) {
    console.error('处理通知数据失败:', error)
    uni.showToast({
      title: '处理通知数据失败',
      icon: 'none',
    })
  }
}

// 格式化选课状态
const formatCourseStatus = (course: SelectCourseItem): string => {
  // 已通过：三个审核环节都为1
  if (
    course.teachOfficeAudit === 1 &&
    course.deptAudit === 1 &&
    course.academicAffairsApproval === 1
  ) {
    return '已通过'
  }
  // 已驳回：任何一个审核环节为2或非0非1
  else if (
    course.teachOfficeAudit === 2 ||
    course.deptAudit === 2 ||
    course.academicAffairsApproval === 2
  ) {
    return '已驳回'
  }
  // 审核中：只要有一个是0（尚未审核）
  else if (
    course.teachOfficeAudit === 0 ||
    course.deptAudit === 0 ||
    course.academicAffairsApproval === 0
  ) {
    return '审核中'
  }
  // 其他情况视为已驳回
  else {
    return '已驳回'
  }
}

// 格式化选课审核状态描述
const formatApplicationStatus = (course: SelectCourseItem): string => {
  // 全部通过
  if (
    course.teachOfficeAudit === 1 &&
    course.deptAudit === 1 &&
    course.academicAffairsApproval === 1
  ) {
    return `申报通过：${new Date(course.update_time * 1000).toLocaleDateString()}`
  }
  // 教研室拒绝
  else if (course.teachOfficeAudit === 2) {
    return '教研室审核未通过'
  }
  // 学院拒绝
  else if (course.deptAudit === 2) {
    return '学院审核未通过'
  }
  // 教务处拒绝
  else if (course.academicAffairsApproval === 2) {
    return '教务处审核未通过'
  }
  // 教研室审核中
  else if (course.teachOfficeAudit === 0) {
    return '教研室审核中'
  }
  // 学院审核中
  else if (course.deptAudit === 0 && course.teachOfficeAudit === 1) {
    return '学院审核中'
  }
  // 教务处审核中
  else if (course.academicAffairsApproval === 0 && course.deptAudit === 1) {
    return '教务处审核中'
  }
  // 任何未通过的情况
  else if (
    course.teachOfficeAudit !== 1 ||
    course.deptAudit !== 1 ||
    course.academicAffairsApproval !== 1
  ) {
    // 处理submitStatus可能为字符串或数字类型
    let submitStatusText = '审核未通过'

    if (course.submitStatus === 0 || course.submitStatus === '0') {
      submitStatusText = '未提交'
    } else if (typeof course.submitStatus === 'string' && course.submitStatus !== '0') {
      submitStatusText = course.submitStatus // 如果是非0字符串，直接使用该值
    }

    return `驳回原因：${submitStatusText}`
  }
  return '未知状态'
}

// 格式化申请日期
const formatApplicationDate = (timestamp: number): string => {
  return new Date(timestamp * 1000).toLocaleDateString()
}

// 标记是否是首次加载
const isFirstLoad = ref(true)

// 选课统计数据
const statData = ref<Partial<SelectCourseStatisticsResponse>>({})

// 学年学期数据
const semesterData = ref<SemesterSelectResponse | null>(null)
const defaultSemester = ref('') // 默认值，在获取到实际数据后将会更新

// 获取学年学期选项
const fetchSemesterSelect = async () => {
  try {
    const response = await getSemesterSelect()
    semesterData.value = response
    // 如果有默认值，则更新默认学期
    if (response && response.xkxn) {
      defaultSemester.value = response.xkxn || defaultSemester.value
    }
    console.log('学年学期数据:', semesterData.value)
    console.log('默认学年学期:', defaultSemester.value)
  } catch (error) {
    console.error('获取学年学期数据失败:', error)
  }
}

// 组件挂载时获取数据
onMounted(() => {
  // 首次加载时在onMounted中请求数据，后续在onShow中处理
  // 不做任何请求操作，全部放到onShow中处理
})

// 页面显示时刷新数据
onShow(() => {
  if (isFirstLoad.value) {
    // 首先获取学年学期数据
    fetchSemesterSelect().then(() => {
      // 获取学年学期数据后再获取课程列表和统计数据
      fetchSelectCourseList()
      fetchSelectCourseStatistics()
    })
    isFirstLoad.value = false
  } else {
    // 后续显示页面时，根据当前标签页刷新相应的数据
    if (activeTab.value === 'application-tab') {
      fetchSelectCourseList()
    } else if (activeTab.value === 'current-tab') {
      fetchCurrentSemesterCourses()
    }
    // 每次显示页面时刷新统计数据
    fetchSelectCourseStatistics()
  }
})

// 监听标签页切换，加载数据
watch(
  () => activeTab.value,
  (newTab) => {
    if (newTab === 'current-tab') {
      fetchCurrentSemesterCourses()
    } else if (newTab === 'application-tab') {
      fetchSelectCourseList()
    }
  },
)

// 监听分页变化
watch([currentPage, currentPageSize], () => {
  if (activeTab.value === 'current-tab') {
    fetchCurrentSemesterCourses()
  }
})

// 监听申报课程分页变化
watch([applicationPage, applicationPageSize], () => {
  if (activeTab.value === 'application-tab') {
    fetchSelectCourseList()
  }
})

// 获取选课统计数据
const fetchSelectCourseStatistics = async () => {
  try {
    const response = await getSelectCourseStatistics()

    // 保存完整的统计数据
    statData.value = response

    // 更新统计数据
    applyStatus.value[0].number = response.sbms // 申报门数
    applyStatus.value[1].number = response.tgxx.tgms // 通过门数
    applyStatus.value[2].number = response.shms // 审核门数
    applyStatus.value[3].number = response.btgms // 不通过门数
    applyStatus.value[4].number = response.zmsxx.zms // 总门数
  } catch (error) {
    console.error('获取选课统计数据失败:', error)
  }
}

// 从嵌套对象中获取统计值
const getStatValue = (path: string): number => {
  if (!statData.value) return 0

  const keys = path.split('.')
  let result: any = statData.value

  for (const key of keys) {
    if (result && result[key] !== undefined) {
      result = result[key]
    } else {
      return 0
    }
  }

  return typeof result === 'number' ? result : 0
}

// 获取选课列表
const fetchSelectCourseList = async () => {
  isLoading.value = true
  hasError.value = false

  try {
    const response = await getSelectCourseList({
      page: applicationPage.value,
      pageSize: applicationPageSize.value,
      sortBy: 'id',
      sortOrder: 'desc',
      semesters: [defaultSemester.value], // 使用获取到的默认学年学期
      courseName: '',
      leaderTeacherName: '',
      className: '',
      courseTotalHours: '',
      weekHours: '',
      creditHour: '',
      workloadNum: '',
      deptName: '',
      teachOfficeName: '',
      campusCode: '',
      startingGrade: '',
      startClassDeptName: '',
      status: '',
      limitCount: '',
      maxCount: '',
      selectedCount: '',
      startWeek: '',
      teachingInfo: '',
      siteName: '',
      taskExecutionStatus: '',
      teachOfficeAudit: '',
      deptAudit: '',
      academicAffairsApproval: '',
      submitStatus: '',
      courseCategory: '',
    })

    // 根据request.ts的实现，response已经是data部分了
    selectCourseList.value = response.items || []
    applicationTotal.value = response.total || 0
    // 使用xnxq字段作为当前学期
    currentSemester.value = response.xnxq || ''

    // 更新通知信息
    if (response.info) {
      updateNoticeInfo(response.info)
    }

    // 如果有提示信息，设置tips
    if (response.tips) {
      tipMessage.value = response.tips
    }

    // 保存通知数据
    if (response.notice) {
      if (response.notice.id) {
        noticeId.value = Number(response.notice.id)
      }

      // 构造符合NoticeItem格式的通知数据
      noticeData.value = {
        id: response.notice.id ? Number(response.notice.id) : null,
        title: response.notice.title || '系统通知',
        content: response.notice.content || tipMessage.value,
        department: response.notice.publisherDeptName || '系统',
        publishTime: response.notice.publishTime || new Date().toLocaleDateString(),
        type: response.notice.columnName || '通知公告',
        isImportant: false, // 默认不重要
        isUnread: true,
        // 添加noticeDetail.vue中需要的其他字段
        categoryColor: 'blue', // 默认使用蓝色
        iconBgColor: '#e0f2fe',
        iconColor: '#0284c7',
      }
    }

    // 设置倒计时
    if (response.jssj !== undefined) {
      if (typeof response.jssj === 'number' && response.jssj > 0) {
        startCountdown(response.jssj)
      } else {
        // 如果没有倒计时或倒计时为0或负数，则设置申报已截止
        isApplicationDeadlinePassed.value = true
        courseCountdown.value.isActive = false
      }
    } else {
      // 如果API没有返回倒计时信息，也设置为已截止，避免无限期开放申报
      isApplicationDeadlinePassed.value = true
      courseCountdown.value.isActive = false
    }
  } catch (error) {
    console.error('获取选课列表失败:', error)
    hasError.value = true
  } finally {
    isLoading.value = false
  }
}

// 更新通知信息
const updateNoticeInfo = (info: string) => {
  // 设置通知标题
  noticeInfo.value.title = '课程申报通知'

  // 设置时间为当前时间
  const now = new Date()
  const hours = now.getHours()
  const minutes = now.getMinutes()
  noticeInfo.value.time = `${hours}:${minutes < 10 ? '0' + minutes : minutes}`

  // 设置通知内容
  noticeInfo.value.content = info
}

// 获取本学期课程列表
const fetchCurrentSemesterCourses = async () => {
  currentCoursesLoading.value = true
  currentCoursesError.value = false

  try {
    const response = await getCurrentSemesterCourses({
      page: currentPage.value,
      pageSize: currentPageSize.value,
      semesters: [], // 不限定学年学期，显示全部课程
      deptAudit: '1', // 只获取已通过学院审核的课程
      academicAffairsApproval: '1', // 只获取已通过教务处审核的课程
    })

    currentCourses.value = response.items || []
    currentCoursesTotal.value = response.total || 0
    currentSemesterValue.value = response.xnxq || ''
  } catch (error) {
    console.error('获取本学期课程列表失败:', error)
    currentCoursesError.value = true
  } finally {
    currentCoursesLoading.value = false
  }
}

// 获取星期名称
const getWeekdayName = (day: string): string => {
  const weekdays = ['', '周一', '周二', '周三', '周四', '周五', '周六', '周日']
  const dayNumber = parseInt(day)
  return weekdays[dayNumber] || '未知'
}

// 处理页面大小变化
const handlePageSizeChange = (e: any) => {
  const index = e.detail.value
  currentPageSize.value = [10, 20, 50][index]
  currentPage.value = 1
}

// 处理申报课程页面大小变化
const handleApplicationPageSizeChange = (e: any) => {
  const index = e.detail.value
  applicationPageSize.value = [10, 20, 50][index]
  applicationPage.value = 1
}

// 更新倒计时
const updateCountdown = () => {
  if (courseCountdown.value.totalSeconds <= 0) {
    courseCountdown.value.isActive = false
    isApplicationDeadlinePassed.value = true
    return
  }
  courseCountdown.value.totalSeconds -= 1
  // 计算天、时、分、秒
  courseCountdown.value.days = Math.floor(courseCountdown.value.totalSeconds / (24 * 3600))
  const remainder = courseCountdown.value.totalSeconds % (24 * 3600)
  courseCountdown.value.hours = Math.floor(remainder / 3600)
  courseCountdown.value.minutes = Math.floor((remainder % 3600) / 60)
  courseCountdown.value.seconds = remainder % 60
}

// 启动倒计时
const startCountdown = (seconds: number) => {
  if (seconds <= 0) {
    isApplicationDeadlinePassed.value = true
    return
  }

  // 清除之前的计时器
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }

  courseCountdown.value.totalSeconds = seconds
  courseCountdown.value.isActive = true
  // 初次计算显示值
  updateCountdown()
  // 设置定时器
  countdownTimer.value = setInterval(() => {
    updateCountdown()
    if (!courseCountdown.value.isActive) {
      clearInterval(countdownTimer.value!)
      countdownTimer.value = null
    }
  }, 1000)
}

// 组件卸载时清除计时器
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
})

// 处理工作流跳转
const handleWorkflowDetail = async (courseId: number) => {
  try {
    console.log(1)

    // 显示加载提示
    uni.showLoading({
      title: '加载中...',
      mask: true,
    })

    // 调用API获取任务ID
    const response = await getWorkflowTask({
      id: courseId,
      code: 'jsxksbsq',
    })

    // 关闭加载提示
    uni.hideLoading()

    // 检查响应数据
    if (response) {
      // 跳转到工作流详情页面
      uni.navigateTo({
        url: `/pages/workflow/detail?id=${response.id}`,
        fail: (err) => {
          console.error('跳转工作流详情页失败:', err)
          uni.showToast({
            title: '跳转工作流详情页失败',
            icon: 'none',
          })
        },
      })
    } else {
      uni.showToast({
        title: '获取工作流信息失败',
        icon: 'none',
      })
    }
  } catch (error) {
    console.error('获取工作流任务失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: '获取工作流任务失败',
      icon: 'none',
    })
  }
}

// 处理申报课程
const handleApplyCourse = () => {
  if (isApplicationDeadlinePassed.value) {
    uni.showToast({
      title: '申报已截止，不允许申报新课程',
      icon: 'none',
      duration: 2000,
    })
    return
  }
  navigateTo('/pages/teacher/teaching-affairs/course-apply')
}
</script>

<template>
  <view class="select-course-container">
    <!-- 教师信息 -->
    <view class="user-profile flex items-center mb-3">
      <view class="user-avatar flex-center rounded-full bg-blue-100 mr-3">
        <template v-if="userStore.userInfo.avatar">
          <image :src="userStore.userInfo.avatar" class="avatar-image" mode="aspectFill"></image>
        </template>
        <template v-else>
          <wd-icon name="user" size="20px" color="#1989fa"></wd-icon>
        </template>
      </view>
      <view class="user-info">
        <view class="text-base font-semibold">{{ teacherInfo.name }}</view>
        <view class="text-xs text-gray-500">
          {{ teacherInfo.department }} • {{ teacherInfo.title }}
        </view>
      </view>
    </view>

    <!-- 通知卡片 -->
    <view
      class="notice-card p-2 rounded-lg bg-orange-50 border-l-3 border-l-solid border-warning mb-4"
    >
      <view class="text-sm">{{ noticeInfo.content }}</view>
    </view>

    <!-- 课程申报状态 -->
    <view class="statistics-overview bg-white px-2 py-2 pb-2 mb-3 rounded-lg shadow-sm">
      <view class="flex space-x-2">
        <view class="w-1/5 bg-blue-50 rounded-xl p-3 text-center">
          <view class="text-xs text-blue-700 mb-1">已申报</view>
          <view class="text-xl font-bold text-blue-800">{{ applyStatus[0].number }}</view>
          <view class="text-xs text-blue-600">门课</view>
        </view>
        <view class="w-1/5 bg-green-50 rounded-xl p-3 text-center">
          <view class="text-xs text-green-700 mb-1">已通过</view>
          <view class="text-xl font-bold text-green-800">{{ applyStatus[1].number }}</view>
          <view class="text-xs text-green-600">门课</view>
        </view>
        <view class="w-1/5 bg-yellow-50 rounded-xl p-3 text-center">
          <view class="text-xs text-yellow-700 mb-1">审核中</view>
          <view class="text-xl font-bold text-yellow-800">{{ applyStatus[2].number }}</view>
          <view class="text-xs text-yellow-600">门课</view>
        </view>
        <view class="w-1/5 bg-red-50 rounded-xl p-3 text-center">
          <view class="text-xs text-red-700 mb-1">未通过</view>
          <view class="text-xl font-bold text-red-800">{{ applyStatus[3].number }}</view>
          <view class="text-xs text-red-600">门课</view>
        </view>
        <view class="w-1/5 bg-purple-50 rounded-xl p-3 text-center">
          <view class="text-xs text-purple-700 mb-1">总门数</view>
          <view class="text-xl font-bold text-purple-800">{{ applyStatus[4].number }}</view>
          <view class="text-xs text-purple-600">门课</view>
        </view>
      </view>

      <!-- 详细统计信息 -->
      <view class="detailed-stats grid grid-cols-2 gap-2 mt-3">
        <view class="bg-gray-50 rounded-lg p-2.5">
          <view class="flex justify-between items-center">
            <view class="text-sm text-gray-700">通过课程详情</view>
            <wd-icon name="check-circle" size="14px" color="#07c160" class="ml-1"></wd-icon>
          </view>
          <view class="grid grid-cols-2 gap-2 mt-2">
            <view class="stat-item">
              <view class="text-xs text-gray-500">总学时</view>
              <view class="text-base font-semibold text-gray-800">
                {{ getStatValue('tgxx.zxs') }} 学时
              </view>
            </view>
            <view class="stat-item">
              <view class="text-xs text-gray-500">选课人数</view>
              <view class="text-base font-semibold text-gray-800">
                {{ getStatValue('tgxx.xkrs') }} 人
              </view>
            </view>
          </view>
        </view>

        <view class="bg-gray-50 rounded-lg p-2.5">
          <view class="flex justify-between items-center">
            <view class="text-sm text-gray-700">学期课程总览</view>
            <wd-icon name="menu" size="14px" color="#1989fa" class="ml-1"></wd-icon>
          </view>
          <view class="grid grid-cols-2 gap-2 mt-2">
            <view class="stat-item">
              <view class="text-xs text-gray-500">学期总学时</view>
              <view class="text-base font-semibold text-gray-800">
                {{ getStatValue('zmsxx.xqzxs') }} 学时
              </view>
            </view>
            <view class="stat-item">
              <view class="text-xs text-gray-500">总选课人数</view>
              <view class="text-base font-semibold text-gray-800">
                {{ getStatValue('zmsxx.zxkrs') }} 人
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 提示信息 -->
      <view
        v-if="tipMessage"
        class="tip-message mt-3 px-3 py-2 text-xs text-gray-600 bg-gray-50 rounded-lg"
        :class="{ 'clickable-card': noticeId !== null }"
        @click="noticeId !== null ? navigateToNoticeDetail() : null"
      >
        <view class="flex items-start justify-between">
          <view class="flex items-start flex-1">
            <wd-icon name="info-circle" size="14px" color="#1989fa" class="mr-1 mt-0.5"></wd-icon>
            <view>{{ tipMessage }}</view>
          </view>
        </view>
      </view>

      <!-- 倒计时 -->
      <view
        v-if="courseCountdown.isActive"
        class="countdown-container mt-3 p-3 rounded-lg bg-blue-50"
      >
        <view class="text-sm text-primary font-medium mb-2 flex items-center">
          <wd-icon name="time" size="16px" color="#1989fa" class="mr-1"></wd-icon>
          申报截止倒计时
        </view>
        <view class="countdown-timer flex justify-center">
          <view class="countdown-block bg-white px-2 py-1 rounded text-center mx-1">
            <view class="text-lg font-bold text-primary">{{ courseCountdown.days }}</view>
            <view class="text-xs text-gray-500">天</view>
          </view>
          <view class="countdown-separator text-primary font-bold self-center">:</view>
          <view class="countdown-block bg-white px-2 py-1 rounded text-center mx-1">
            <view class="text-lg font-bold text-primary">{{ courseCountdown.hours }}</view>
            <view class="text-xs text-gray-500">时</view>
          </view>
          <view class="countdown-separator text-primary font-bold self-center">:</view>
          <view class="countdown-block bg-white px-2 py-1 rounded text-center mx-1">
            <view class="text-lg font-bold text-primary">{{ courseCountdown.minutes }}</view>
            <view class="text-xs text-gray-500">分</view>
          </view>
          <view class="countdown-separator text-primary font-bold self-center">:</view>
          <view class="countdown-block bg-white px-2 py-1 rounded text-center mx-1">
            <view class="text-lg font-bold text-primary">{{ courseCountdown.seconds }}</view>
            <view class="text-xs text-gray-500">秒</view>
          </view>
        </view>
      </view>

      <!-- 申报截止提示 -->
      <view
        v-if="isApplicationDeadlinePassed"
        class="deadline-passed mt-3 p-3 rounded-lg bg-red-50 flex items-center"
      >
        <wd-icon name="warning" size="16px" color="#fa5151" class="mr-2"></wd-icon>
        <view class="text-sm text-danger font-medium">
          申报已截止，不可申报新课程或编辑现有课程
        </view>
      </view>
    </view>

    <!-- 快捷操作 -->
    <view class="rounded-lg bg-white shadow-sm mb-4">
      <!--      <view class="mb-2 text-base font-semibold">快捷操作</view>-->
      <view
        @click="handleApplyCourse"
        class="apply-course-btn flex items-center justify-center p-3 rounded-md"
        :class="{
          'bg-primary': !isApplicationDeadlinePassed,
          'bg-gray-400': isApplicationDeadlinePassed,
        }"
      >
        <wd-icon :name="quickActions[0].icon" size="20px" color="#fff" class="mr-2"></wd-icon>
        <text class="text-white">{{ quickActions[0].name }}</text>
        <text v-if="isApplicationDeadlinePassed" class="text-white text-xs ml-2">(已截止)</text>
      </view>
    </view>

    <!-- 课程管理 -->
    <view class="rounded-lg mb-4">
      <!-- 分段控制器 -->
      <view class="segment-control flex bg-gray-100 rounded-md p-1 mb-3">
        <view
          class="segment-button flex-1 text-center py-1.5 rounded-md text-sm transition-all"
          :class="{ 'bg-white shadow-sm font-medium': activeTab === 'application-tab' }"
          @click="changeTab('application-tab')"
        >
          申报课程
        </view>
        <view
          class="segment-button flex-1 text-center py-1.5 rounded-md text-sm transition-all"
          :class="{ 'bg-white shadow-sm font-medium': activeTab === 'current-tab' }"
          @click="changeTab('current-tab')"
        >
          开课课程
        </view>
      </view>

      <!-- 筛选栏 -->
      <!-- <view class="filter-bar flex overflow-x-auto py-2 mb-4 scrollbar-none">
        <view
          v-for="(filter, index) in filterOptions"
          :key="index"
          class="filter-button flex items-center whitespace-nowrap px-3 py-1.5 mr-2 rounded-full text-xs"
          :class="{
            'bg-primary text-white': filter.active,
            'bg-gray-100 text-gray-500': !filter.active,
          }"
          @click="toggleFilter(index)"
        >
          <wd-icon
            :name="filter.icon"
            size="12px"
            class="mr-1"
            :color="filter.active ? '#ffffff' : ''"
          ></wd-icon>
          {{ filter.name }}
        </view>
      </view> -->

      <!-- 分段控制器下的内容区域 -->
      <!-- 申报状态(现在作为第一个标签) -->
      <view v-if="activeTab === 'application-tab'" class="tab-content">
        <view v-if="isLoading" class="loading-state flex items-center justify-center py-10">
          <wd-icon name="loading" size="24px" color="#1989fa" class="loading-icon"></wd-icon>
          <text class="ml-2 text-gray-500">加载中...</text>
        </view>

        <view
          v-else-if="hasError"
          class="error-state flex flex-col items-center justify-center py-10 text-center"
        >
          <view class="error-icon flex-center w-16 h-16 rounded-full bg-red-50 text-danger mb-4">
            <wd-icon name="warning" size="24px"></wd-icon>
          </view>
          <view class="error-text text-base font-medium text-gray-700 mb-2">加载失败</view>
          <view class="error-subtext text-sm text-gray-500 mb-4 max-w-60">
            无法获取选课数据，请稍后重试
          </view>
          <view
            class="ios-button bg-primary text-white rounded-full px-4 py-2 text-sm flex items-center"
            @click="fetchSelectCourseList"
          >
            <wd-icon name="refresh" size="12px" class="mr-2"></wd-icon>
            重新加载
          </view>
        </view>

        <view v-else>
          <view class="course-list-header flex justify-between items-center mb-3">
            <view class="course-list-title text-base font-semibold">
              {{ currentSemester ? `${currentSemester.replace('|', '学期')}` : '当前学期' }}
            </view>
            <view class="course-list-action text-sm text-primary">
              共 {{ applicationTotal }} 门课程
            </view>
          </view>

          <template v-if="selectCourseList.length > 0">
            <view
              v-for="course in selectCourseList"
              :key="course.id"
              class="course-card bg-white rounded-lg overflow-hidden shadow-sm mb-4"
            >
              <view class="course-top flex justify-between p-4 border-b border-solid border-white">
                <view>
                  <view class="course-name text-base font-semibold mb-1">
                    {{ course.courseName }}
                  </view>
                  <view class="course-code text-xs text-gray-500">{{ course.courseCode }}</view>
                </view>
                <view
                  class="course-status flex items-center text-xs px-2 py-1 rounded"
                  :class="{
                    'bg-orange-50 text-warning': formatCourseStatus(course) === '审核中',
                    'bg-green-50 text-success': formatCourseStatus(course) === '已通过',
                    'bg-red-50 text-danger': formatCourseStatus(course) === '已驳回',
                  }"
                >
                  <wd-icon
                    :name="
                      formatCourseStatus(course) === '审核中'
                        ? 'clock'
                        : formatCourseStatus(course) === '已通过'
                          ? 'check-circle'
                          : 'close-circle'
                    "
                    size="12px"
                    class="mr-1"
                  ></wd-icon>
                  {{ formatCourseStatus(course) }}
                </view>
              </view>

              <view class="course-info flex flex-wrap gap-3 p-4">
                <view class="info-item flex items-center text-xs text-gray-500">
                  <wd-icon name="home" size="12px" class="mr-1 opacity-70"></wd-icon>
                  {{ course.deptName }}
                </view>
                <view class="info-item flex items-center text-xs text-gray-500">
                  <wd-icon name="layers" size="12px" class="mr-1 opacity-70"></wd-icon>
                  {{ course.courseType === '03' ? '专业选修' : '专业必修' }}
                </view>
                <view class="info-item flex items-center text-xs text-gray-500">
                  <wd-icon name="star" size="12px" class="mr-1 opacity-70"></wd-icon>
                  {{ course.creditHour }}学分
                </view>
                <view class="info-item flex items-center text-xs text-gray-500">
                  <wd-icon name="time" size="12px" class="mr-1 opacity-70"></wd-icon>
                  {{ course.courseTotalHours }}学时
                </view>
                <view class="info-item flex items-center text-xs text-gray-500">
                  <wd-icon name="calendar" size="12px" class="mr-1 opacity-70"></wd-icon>
                  申报日期：{{ formatApplicationDate(course.create_time) }}
                </view>
              </view>

              <view
                class="course-bottom flex justify-between items-center p-3 border-t border-solid border-white"
              >
                <view>
                  <text
                    class="text-xs"
                    :class="{
                      'text-gray-500': formatCourseStatus(course) === '审核中',
                      'text-success': formatCourseStatus(course) === '已通过',
                      'text-danger': formatCourseStatus(course) === '已驳回',
                    }"
                  >
                    {{ formatApplicationStatus(course) }}
                  </text>
                </view>
                <view class="course-actions flex gap-3">
                  <view
                    class="action-button flex-center w-8 h-8 rounded-full bg-gray-100 text-gray-500"
                    @click="handleWorkflowDetail(Number(course.id))"
                  >
                    <wd-icon name="info-circle" size="14px"></wd-icon>
                  </view>
                  <view
                    class="action-button flex-center w-8 h-8 rounded-full"
                    :class="{
                      'bg-blue-50 text-primary cursor-not-allowed opacity-50':
                        formatCourseStatus(course) === '已通过' || isApplicationDeadlinePassed,
                      'bg-gray-100 text-gray-500':
                        formatCourseStatus(course) !== '已通过' && !isApplicationDeadlinePassed,
                    }"
                    @click="navigateToEditCourse(Number(course.selectCourseId), course)"
                  >
                    <wd-icon
                      :name="formatCourseStatus(course) === '已通过' ? 'check' : 'edit'"
                      size="14px"
                    ></wd-icon>
                  </view>
                </view>
              </view>
            </view>

            <!-- 分页组件 -->
            <Pagination
              v-if="applicationTotal > 0"
              :total="applicationTotal"
              :page="applicationPage"
              :page-size="applicationPageSize"
              @update:page="applicationPage = $event"
            />
          </template>

          <template v-else>
            <!-- 空状态 -->
            <view class="empty-state flex flex-col items-center justify-center py-10 text-center">
              <view
                class="empty-icon flex-center w-16 h-16 rounded-full bg-gray-100 text-gray-400 mb-4"
              >
                <wd-icon name="empty" size="24px"></wd-icon>
              </view>
              <view class="empty-text text-base font-medium text-gray-700 mb-2">暂无申报课程</view>
              <view class="empty-subtext text-sm text-gray-500 mb-4 max-w-60">
                您可以点击右上角"+"按钮申报新课程
              </view>
              <view
                class="ios-button rounded-full px-4 py-2 flex items-center text-sm"
                :class="{
                  'bg-primary text-white': !isApplicationDeadlinePassed,
                  'bg-gray-400 text-white': isApplicationDeadlinePassed,
                }"
                @click="handleApplyCourse"
              >
                <wd-icon name="plus" size="12px" class="mr-2"></wd-icon>
                申报新课程
                <text v-if="isApplicationDeadlinePassed" class="text-white text-xs ml-2">
                  (已截止)
                </text>
              </view>
            </view>
          </template>
        </view>
      </view>

      <!-- 本学期课程(现在作为第二个标签) -->
      <view v-if="activeTab === 'current-tab'" class="tab-content">
        <view class="course-list-section">
          <view class="course-list-header flex justify-between items-center mb-3">
            <view class="course-list-title text-base font-semibold">开课课程</view>
            <view class="course-list-action flex items-center">
              <text class="text-sm text-primary">共 {{ currentCoursesTotal }} 门课程</text>
            </view>
          </view>

          <!-- 加载状态 -->
          <view
            v-if="currentCoursesLoading"
            class="loading-state flex items-center justify-center py-10"
          >
            <wd-icon name="loading" size="24px" color="#1989fa" class="loading-icon"></wd-icon>
            <text class="ml-2 text-gray-500">加载中...</text>
          </view>

          <!-- 错误状态 -->
          <view
            v-else-if="currentCoursesError"
            class="error-state flex flex-col items-center justify-center py-10 text-center"
          >
            <view class="error-icon flex-center w-16 h-16 rounded-full bg-red-50 text-danger mb-4">
              <wd-icon name="warning" size="24px"></wd-icon>
            </view>
            <view class="error-text text-base font-medium text-gray-700 mb-2">加载失败</view>
            <view class="error-subtext text-sm text-gray-500 mb-4 max-w-60">
              无法获取课程数据，请稍后重试
            </view>
            <view
              class="ios-button bg-primary text-white rounded-full px-4 py-2 text-sm flex items-center"
              @click="fetchCurrentSemesterCourses"
            >
              <wd-icon name="refresh" size="12px" class="mr-2"></wd-icon>
              重新加载
            </view>
          </view>

          <!-- 空状态 -->
          <view
            v-else-if="currentCourses.length === 0"
            class="empty-state flex flex-col items-center justify-center py-10 text-center"
          >
            <view
              class="empty-icon flex-center w-16 h-16 rounded-full bg-gray-100 text-gray-400 mb-4"
            >
              <wd-icon name="empty" size="24px"></wd-icon>
            </view>
            <view class="empty-text text-base font-medium text-gray-700 mb-2">暂无课程数据</view>
            <view class="empty-subtext text-sm text-gray-500 mb-4 max-w-60">
              本学期没有查询到您的授课信息
            </view>
            <view
              class="ios-button rounded-full px-4 py-2 flex items-center text-sm"
              :class="{
                'bg-primary text-white': !isApplicationDeadlinePassed,
                'bg-gray-400 text-white': isApplicationDeadlinePassed,
              }"
              @click="handleApplyCourse"
            >
              <wd-icon name="plus" size="12px" class="mr-2"></wd-icon>
              申报新课程
              <text v-if="isApplicationDeadlinePassed" class="text-white text-xs ml-2">
                (已截止)
              </text>
            </view>
          </view>

          <!-- 课程卡片 -->
          <template v-else>
            <view
              v-for="course in currentCourses"
              :key="course.id"
              class="course-card bg-white rounded-lg overflow-hidden shadow-sm mb-4"
            >
              <view class="course-top flex justify-between p-4 border-b border-solid border-white">
                <view>
                  <view class="course-name text-base font-semibold mb-1">
                    {{ course.courseName }}
                  </view>
                  <view class="course-code text-xs text-gray-500">{{ course.courseCode }}</view>
                </view>
                <view
                  class="course-status flex items-center text-xs px-2 py-1 rounded bg-blue-50 text-primary"
                >
                  <wd-icon name="check-circle" size="12px" class="mr-1"></wd-icon>
                  已审核
                </view>
              </view>

              <view class="course-info flex flex-wrap gap-3 p-4">
                <view class="info-item flex items-center text-xs text-gray-500">
                  <wd-icon name="home" size="12px" class="mr-1 opacity-70"></wd-icon>
                  {{ course.deptName }}
                </view>
                <view class="info-item flex items-center text-xs text-gray-500">
                  <wd-icon name="layers" size="12px" class="mr-1 opacity-70"></wd-icon>
                  {{ course.courseType === '03' ? '专业选修' : '专业必修' }}
                </view>
                <view class="info-item flex items-center text-xs text-gray-500">
                  <wd-icon name="star" size="12px" class="mr-1 opacity-70"></wd-icon>
                  {{ course.creditHour }}学分
                </view>
                <view class="info-item flex items-center text-xs text-gray-500">
                  <wd-icon name="time" size="12px" class="mr-1 opacity-70"></wd-icon>
                  {{ course.courseTotalHours }}学时
                </view>
              </view>

              <!-- <view class="course-schedule bg-gray-50 rounded-md m-4 p-3">
                <view
                  v-for="(schedule, scheduleIndex) in formatCourseSchedule(course.teachingInfo)"
                  :key="scheduleIndex"
                  class="schedule-row flex items-center mb-1.5"
                >
                  <view class="schedule-day w-8 font-medium text-gray-700">{{ schedule.day }}</view>
                  <view class="schedule-time text-xs text-gray-500">
                    {{ schedule.time }} {{ schedule.location }}
                  </view>
                </view>
              </view> -->

              <view
                class="course-bottom flex justify-between items-center p-3 border-t border-solid border-white"
              >
                <view class="course-students flex items-center">
                  <view class="student-avatars flex mr-2">
                    <view
                      class="student-avatar flex-center w-6 h-6 rounded-full bg-blue-50 text-xs text-primary border-2 border-solid border-white"
                      style="margin-left: -8px"
                    >
                      学
                    </view>
                    <view
                      class="student-avatar flex-center w-6 h-6 rounded-full bg-blue-50 text-xs text-primary border-2 border-solid border-white"
                      style="margin-left: -8px"
                    >
                      生
                    </view>
                  </view>
                  <view>
                    <text class="student-count text-sm font-medium">
                      {{ course.selectedCount || 0 }}
                    </text>
                    <text class="student-max text-xs text-gray-500">
                      / {{ course.limitCount || 0 }}
                    </text>
                  </view>
                </view>
                <view class="course-actions flex gap-3">
                  <!-- <view
                    class="action-button flex-center w-8 h-8 rounded-full bg-blue-50 text-primary"
                  >
                    <wd-icon name="user-group" size="14px"></wd-icon>
                  </view> -->
                  <view
                    class="action-button flex-center w-8 h-8 rounded-full bg-blue-50 text-primary"
                    @click="handleWorkflowDetail(Number(course.id))"
                  >
                    <wd-icon name="info-circle" size="14px"></wd-icon>
                  </view>
                  <!-- <view
                    class="action-button flex-center w-8 h-8 rounded-full bg-blue-50 text-primary"
                  >
                    <wd-icon name="arrow-right" size="14px"></wd-icon>
                  </view> -->
                </view>
              </view>
            </view>

            <!-- 分页组件 -->
            <Pagination
              v-if="currentCoursesTotal > 0"
              :total="currentCoursesTotal"
              :page="currentPage"
              :page-size="currentPageSize"
              @update:page="currentPage = $event"
            />
          </template>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.select-course-container {
  min-height: 100vh;
  padding: 20px;
  padding-top: 20rpx;
  background-color: #f7f8fa;
}

.user-avatar {
  width: 48px;
  height: 48px;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.section-title {
  margin: 20px 0 10px;
  color: #333;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-circle {
  width: 48px;
  height: 48px;
}

.course-icon {
  min-width: 36px;
}

// 分段控制器
.segment-control {
  padding: 2px;
  background-color: #f2f2f7;
  border-radius: 8px;
}

.segment-button {
  flex: 1;
  padding: 8px 0;
  font-size: 14px;
  text-align: center;
  border-radius: 6px;
  transition: all 0.3s;
}

// 筛选栏
.filter-bar {
  scrollbar-width: none;
  -webkit-overflow-scrolling: touch;
}

.filter-bar::-webkit-scrollbar {
  display: none;
}

.filter-button {
  padding: 6px 12px;
  margin-right: 8px;
  font-size: 13px;
  white-space: nowrap;
  background-color: #f2f2f7;
  border-radius: 16px;
}

// 课程卡片
.course-card {
  margin-bottom: 16px;
  overflow: hidden;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.course-name {
  margin-bottom: 4px;
  font-size: 16px;
  font-weight: 600;
}

.course-code {
  font-size: 13px;
  color: var(--gray-color, #999);
}

.course-status {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
}

.course-info {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 14px;
}

.info-item {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: var(--dark-gray, #666);
}

.course-schedule {
  padding: 10px 12px;
  margin-bottom: 14px;
  background-color: #f9fafb;
  border-radius: 8px;
}

.schedule-row {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.schedule-day {
  width: 32px;
  font-weight: 500;
  color: var(--dark-gray, #666);
}

.schedule-time {
  font-size: 13px;
  color: var(--gray-color, #999);
}

.course-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 10px;
  border-top: 1px solid #f3f4f6;
}

.student-count {
  margin-right: 6px;
  font-size: 14px;
  font-weight: 500;
}

.student-max {
  font-size: 13px;
  color: var(--gray-color, #999);
}

.student-avatars {
  display: flex;
  margin-right: 10px;
}

.student-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  font-size: 10px;
  color: var(--primary-color, #1989fa);
  background-color: #e6f7ff;
  border: 2px solid white;
  border-radius: 50%;
}

.student-avatar:first-child {
  margin-left: 0;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.empty-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
  font-size: 24px;
  color: #9ca3af;
  background-color: #f3f4f6;
  border-radius: 50%;
}

.empty-text {
  margin-bottom: 8px;
  font-size: 15px;
  font-weight: 500;
  color: var(--dark-gray, #666);
}

.empty-subtext {
  max-width: 240px;
  margin-bottom: 16px;
  font-size: 13px;
  color: var(--gray-color, #999);
}

.ios-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-size: 14px;
  color: white;
  background-color: var(--primary-color, #1989fa);
  border-radius: 20px;
}

.search-input {
  display: flex;
  align-items: center;
  height: 36px;
  padding: 0 12px;
  margin-bottom: 16px;
  background-color: #f2f2f7;
  border-radius: 10px;
}

.search-input input {
  flex: 1;
  font-size: 14px;
  color: var(--dark-gray, #666);
  background: transparent;
  border: none;
}

// UnoCSS 扩展
.bg-primary {
  background-color: #1989fa;
}

.bg-success {
  background-color: #07c160;
}

.bg-warning {
  background-color: #ff976a;
}

.bg-danger {
  background-color: #fa5151;
}

.bg-gray-400 {
  background-color: #9ca3af;
}

.text-primary {
  color: #1989fa;
}

.text-success {
  color: #07c160;
}

.text-warning {
  color: #ff976a;
}

.text-danger {
  color: #fa5151;
}

.border-warning {
  border-color: #ff976a;
}

.bg-blue-50 {
  background-color: rgba(25, 137, 250, 0.1);
}

.bg-green-50 {
  background-color: rgba(7, 193, 96, 0.1);
}

.bg-orange-50 {
  background-color: rgba(255, 151, 106, 0.1);
}

.bg-red-50 {
  background-color: rgba(250, 81, 81, 0.1);
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-700 {
  color: #374151;
}

.scrollbar-none::-webkit-scrollbar {
  display: none;
}

.scrollbar-none {
  scrollbar-width: none;
}

.transition-all {
  transition: all 0.3s;
}

// 加载图标动画
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-icon {
  animation: rotate 1.5s linear infinite;
}

.apply-course-btn {
  padding: 8px 0;
  font-size: 15px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(25, 137, 250, 0.2);
  transition: all 0.3s;
}

.apply-course-btn:active {
  opacity: 0.85;
  transform: scale(0.98);
}

// 分页控制器样式
.page-size-selector {
  min-width: 50px;
  text-align: center;
  transition: all 0.3s;
}

.page-size-selector:active {
  background-color: #e6f1fe;
}

.refresh-btn {
  transition: all 0.3s;
}

.refresh-btn:active {
  transform: rotate(30deg);
}

// 课程列表加载状态
.loading-state,
.error-state,
.empty-state {
  min-height: 200px;
}

// 新增紫色背景样式
.bg-purple-50 {
  background-color: rgba(139, 92, 246, 0.1);
}

// 新增文本颜色
.text-blue-600 {
  color: #2563eb;
}

.text-blue-700 {
  color: #1d4ed8;
}

.text-blue-800 {
  color: #1e40af;
}

.text-green-600 {
  color: #059669;
}

.text-green-700 {
  color: #047857;
}

.text-green-800 {
  color: #065f46;
}

.text-yellow-600 {
  color: #d97706;
}

.text-yellow-700 {
  color: #b45309;
}

.text-yellow-800 {
  color: #92400e;
}

.text-red-600 {
  color: #dc2626;
}

.text-red-700 {
  color: #b91c1c;
}

.text-red-800 {
  color: #991b1b;
}

.text-purple-600 {
  color: #7c3aed;
}

.text-purple-700 {
  color: #6d28d9;
}

.text-purple-800 {
  color: #5b21b6;
}

// 倒计时样式
.countdown-container {
  border: 1px dashed rgba(25, 137, 250, 0.3);
}

.countdown-block {
  min-width: 40px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.countdown-separator {
  margin: 0 2px;
  font-size: 16px;
}

// 提示信息样式
.tip-message {
  border-left: 3px solid #1989fa;
}

// 添加一个动画效果
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(25, 137, 250, 0.2);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(25, 137, 250, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(25, 137, 250, 0);
  }
}

// 可点击卡片样式
.clickable-card {
  position: relative;
  cursor: pointer;
  background-color: rgba(25, 137, 250, 0.05); // 轻微的背景色变化
  transition: all 0.2s ease;
  animation: pulse 2s infinite;
}

.clickable-card:active {
  opacity: 0.7;
  transform: scale(0.99);
}
</style>
