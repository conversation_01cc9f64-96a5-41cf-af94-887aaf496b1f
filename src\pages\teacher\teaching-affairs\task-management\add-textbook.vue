<route lang="json5">
{
  style: {
    navigationBarTitleText: '新增教材',
  },
}
</route>
<template>
  <FormWithApproval
    :id="storeCurrentTextbook?.id"
    :code="fromChangeList ? 'jcxybgsq' : 'jsxkjcsh'"
    @return="cancel"
  >
    <!-- 基本信息表单内容 -->
    <template #form-content>
      <!-- 表单容器 -->
      <view class="bg-white rounded-xl p-4 shadow-sm mb-4">
        <view class="text-lg font-semibold mb-4 text-gray-800">
          {{ getPageTitle() }}
          <text v-if="isDisabled" class="text-sm text-blue-500 ml-2">(查看模式)</text>
        </view>

        <!-- 选择任务 -->
        <view class="form-item">
          <view class="form-label">
            <text class="text-red-500 mr-1">*</text>
            选择任务
          </view>
          <!-- 当mode为'add'且source为'material'时，显示可点击的选择框 -->
          <view
            v-if="mode === 'add' && source === 'material' && !isDisabled"
            class="form-select rounded-lg p-3 flex justify-between items-center"
            @click="openTaskSelector"
          >
            <text
              v-if="currentTaskText !== '暂无任务信息'"
              class="flex-1 text-ellipsis overflow-hidden pr-1"
            >
              {{ currentTaskText }}
            </text>
            <text v-else class="text-[#bfbfbf] text-[14px]">点击选择教学任务</text>
            <wd-icon name="arrow-right" size="16px" class="text-[#bfbfbf] flex-shrink-0" />
          </view>
          <!-- 其他情况下显示不可点击的文本 -->
          <view v-else class="p-3 rounded-lg text-gray-800">
            {{ currentTaskText }}
          </view>
        </view>

        <!-- 当前选用教材 - 仅在编辑和变更模式下显示 -->
        <view class="form-item" v-if="mode === 'edit' || mode === 'change'">
          <view class="form-label">当前选用教材</view>
          <view class="p-3 rounded-lg text-gray-800">
            {{ storeCurrentTextbook?.jcmc || '暂无教材信息' }}
          </view>
        </view>

        <!-- 选用编号 - 仅在编辑和变更模式下显示 -->
        <view class="form-item" v-if="mode === 'edit' || mode === 'change'">
          <view class="form-label">选用编号</view>
          <view class="p-3 rounded-lg text-gray-800">
            {{ storeCurrentTextbook?.id || '暂无选用编号' }}
          </view>
        </view>

        <!-- 教材选用类别 -->
        <view class="form-item">
          <view class="form-label">
            <text class="text-red-500 mr-1">*</text>
            教材选用类别
          </view>
          <view class="selector-container">
            <view class="p-3 rounded-lg text-gray-800 border border-[#bfbfc3]">学生教材</view>
          </view>
        </view>

        <!-- 教材名称 -->
        <view class="form-item">
          <view class="form-label">
            <text class="text-red-500 mr-1">*</text>
            教材名称
          </view>
          <view
            v-if="!isDisabled"
            class="form-select rounded-lg p-3 flex justify-between items-center"
            @click="openTextbookSelector"
          >
            <text v-if="selectedTextbook" class="flex-1 text-ellipsis overflow-hidden pr-1">
              {{ selectedTextbook.name }}
            </text>
            <text v-else class="text-[#bfbfbf] text-[14px]">点击选择教材</text>
            <wd-icon name="arrow-right" size="16px" class="text-[#bfbfbf] flex-shrink-0" />
          </view>
          <view v-else class="p-3 rounded-lg text-gray-800">
            {{ selectedTextbook?.name || storeCurrentTextbook?.jcmc || '未选择教材' }}
          </view>
          <view class="text-xs text-gray-500 mt-1">
            说明：
            <br />
            1)学生使用的教材,系统统一配套给任课教师,故无需再进行教师教材的选择.
            <br />
            2)教师教材选择为教师参考书,原则上以1本为限.
          </view>
        </view>

        <!-- 是否首次使用 -->
        <view class="form-item">
          <view class="form-label">
            <text class="text-red-500 mr-1">*</text>
            是否首次使用
          </view>
          <view class="flex items-center">
            <wd-switch v-model="formData.isFirstTime" size="20px" :disabled="isDisabled" />
            <text class="ml-2 text-gray-700">{{ formData.isFirstTime ? '是' : '否' }}</text>
          </view>
        </view>

        <!-- 选用理由 -->
        <view class="form-item">
          <view class="form-label">
            <text class="text-red-500 mr-1">*</text>
            选用理由
          </view>
          <wd-textarea
            v-if="!isDisabled"
            v-model="formData.selectionReason"
            placeholder="请输入选用该教材的理由"
            class="form-textarea"
            :maxlength="200"
            show-count
          />
          <view v-else class="p-3 rounded-lg text-gray-800 border border-[#bfbfc3] min-h-[100px]">
            {{ formData.selectionReason }}
          </view>
        </view>

        <!-- 变更理由 -->
        <view class="form-item" v-if="mode === 'change'">
          <view class="form-label">
            <text class="text-red-500 mr-1">*</text>
            变更理由说明
          </view>
          <wd-textarea
            v-if="!isDisabled"
            v-model="formData.changeReason"
            placeholder="请输入变更理由"
            class="form-textarea"
            :maxlength="200"
            show-count
          />
          <view v-else class="p-3 rounded-lg text-gray-800 border border-[#bfbfc3] min-h-[100px]">
            {{ formData.changeReason }}
          </view>
        </view>
      </view>
    </template>

    <!-- 表单按钮 -->
    <template #form-buttons>
      <!-- 按钮组 -->
      <view class="flex space-x-4 mb-8" v-if="!isDisabled">
        <button class="btn-cancel flex-1" @click="cancel">取消</button>
        <button class="btn-primary flex-1" @click="submitForm">提交</button>
      </view>

      <!-- 查看模式下只显示返回按钮 -->
      <view class="flex mb-8" v-else>
        <button class="btn-cancel w-full" @click="cancel">返回</button>
      </view>
    </template>
  </FormWithApproval>

  <!-- 教材选择器弹窗 -->
  <wd-popup
    v-model="showTextbookSelector"
    position="bottom"
    :close-on-click-modal="true"
    closable
    custom-style="height: 80vh; border-radius: 16px 16px 0 0;"
    @close="closeTextbookSelector"
  >
    <view class="popup-header px-4 py-3 border-b border-gray-100">
      <view class="text-lg font-semibold">选择教材</view>
    </view>
    <view class="popup-content h-full">
      <TextbookSelector @select="handleTextbookSelect" />
    </view>
  </wd-popup>

  <!-- 教学任务选择器弹窗 -->
  <wd-popup
    v-model="showTaskSelector"
    position="bottom"
    :close-on-click-modal="true"
    closable
    custom-style="height: 80vh; border-radius: 16px 16px 0 0;"
    @close="closeTaskSelector"
  >
    <view class="popup-header px-4 py-3 border-b border-gray-100">
      <view class="text-lg font-semibold">选择教学任务</view>
    </view>
    <view class="popup-content h-full">
      <TeachingTaskSelector @select="handleTaskSelect" :semester="currentSemester" />
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import type {
  AddTextbookParams,
  TeachingTask,
  AddTeachingMaterialParams,
  UpdateTeachingMaterialParams,
  ChangeTeachingMaterialParams,
  UpdateChangeTeachingMaterialParams,
  TeachingTaskMaterialItem,
} from '@/types/textbook'
import {
  addTextbook,
  getTeachingTasks,
  addTeachingMaterial,
  updateTeachingMaterial,
  changeTeachingMaterial,
  deleteTextbook,
  updateChangeTeachingMaterial,
} from '@/service/textbook'
import { useTeachingTaskStore } from '@/store/teachingTask'
import { useTextbookStore } from '@/store/textbook'
import { loadDictData, getDictOptions } from '@/utils/dict'
import type { DictData } from '@/types/system'
import TextbookSelector from '@/components/TextbookSelector/index.vue'
import TeachingTaskSelector from '@/components/TeachingTaskSelector/index.vue'
import type { TeachingMaterial } from '@/types/teachingMaterial'
import type { TeachingTaskItem } from '@/types/teachingTask'
import FormWithApproval from '@/components/FormWithApproval/index.vue'

// 扩展 TeachingTaskMaterialItem 类型，添加可能缺少的属性
interface ExtendedTeachingTaskMaterialItem extends TeachingTaskMaterialItem {
  // 变更原因说明
  bgyysm?: string
  // 原教材选用ID
  yjcxyid?: number
}

// 从store获取当前教学任务信息
const teachingTaskStore = useTeachingTaskStore()
const textbookStore = useTextbookStore()
const storeCurrentTask = computed(() => teachingTaskStore.currentTask)
const storeCurrentTextbook = computed(
  () => textbookStore.currentTextbook as ExtendedTeachingTaskMaterialItem,
)

// 页面模式（新增/编辑/变更）
const mode = ref<'add' | 'edit' | 'change'>('add')
// 页面来源
const source = ref<string>('')
// 是否禁用编辑（查看模式）
const isDisabled = ref<boolean>(false)
// 是否来自变更列表
const fromChangeList = ref<boolean>(false)

// 教学任务数据
const teachingTasks = ref<TeachingTask[]>([])
const currentTask = ref<TeachingTask | null>(null)

// 字典数据
const textbookTypeDict = ref<DictData[]>([])
const dictLoading = ref(true)

// 选中的教材
const selectedTextbook = ref<TeachingMaterial | null>(null)

// 格式化当前任务文本
const currentTaskText = computed(() => {
  // 如果来源是教材选用页面，优先使用teachingTaskStore中的title
  if (source.value === 'material') {
    const title = teachingTaskStore.getCurrentTitle()
    if (title) {
      return title
    }
  }

  // 否则使用原来的逻辑
  if (storeCurrentTask.value) {
    return `${storeCurrentTask.value.studyYear}-${storeCurrentTask.value.studyTerm} 选修教学班${storeCurrentTask.value.className} 《${storeCurrentTask.value.courseName}》${storeCurrentTask.value.courseType} ${storeCurrentTask.value.leaderTeacherName}`
  }
  return '暂无任务信息'
})

// 表单数据 - 只保留需要的字段
const formData = reactive({
  name: '', // 教材名称
  usageType: '1', // 选用类别，默认设为"学生教材"的值
  isFirstTime: true, // 是否首次使用
  selectionReason: '', // 选用理由
  changeReason: '', // 变更理由
  taskId: 1, // 默认任务ID，将通过watch更新
})

// 监听当前任务变化，更新taskId
watch(
  storeCurrentTask,
  (newTask) => {
    if (newTask) {
      formData.taskId = newTask.id
    }
  },
  { immediate: true },
)

// 监听选中教材变化，更新表单教材名称
watch(
  selectedTextbook,
  (newTextbook) => {
    if (newTextbook) {
      formData.name = newTextbook.name
    } else {
      formData.name = ''
    }
  },
  { immediate: true },
)

// 提交状态
const submitting = ref(false)

// 选用类别选项 - 从字典中获取
const usageTypeOptions = computed(() => {
  return getDictOptions(textbookTypeDict.value)
})

// 加载字典数据
const loadDictionaries = async () => {
  try {
    dictLoading.value = true
    const dicts = await loadDictData(['DM_JCLXDM'])
    textbookTypeDict.value = dicts.DM_JCLXDM
    // 注意：虽然我们固定了选项，但仍然加载字典以备将来使用
  } catch (error) {
    console.error('加载字典数据失败:', error)
    uni.showToast({
      title: '加载字典数据失败',
      icon: 'none',
    })
  } finally {
    dictLoading.value = false
  }
}

// 获取教学任务列表
const fetchTeachingTasks = async () => {
  try {
    uni.showLoading({ title: '加载中...' })

    // 这里暂时使用模拟数据
    // const res = await getTeachingTasks()
    // teachingTasks.value = res

    // 模拟数据
    teachingTasks.value = [
      {
        id: 1,
        semester: '2024-2025(2)',
        classNo: '13885',
        courseName: '管理学基础',
        courseType: '专升本线上课',
        teacherName: '缪有贺',
        currentTextbook: '《职业汉语教程》',
        selectionNo: '63775',
      },
      {
        id: 2,
        semester: '2024-2025(2)',
        classNo: '13886',
        courseName: '大学英语',
        courseType: '专升本线上课',
        teacherName: '王明',
        currentTextbook: '《大学英语教程》',
        selectionNo: '63776',
      },
      {
        id: 3,
        semester: '2024-2025(2)',
        classNo: '13887',
        courseName: '高等数学',
        courseType: '专升本线上课',
        teacherName: '李华',
        currentTextbook: '',
        selectionNo: '',
      },
    ]

    // 设置当前任务为第一个任务
    currentTask.value = teachingTasks.value[0]

    uni.hideLoading()
  } catch (error) {
    console.error('获取教学任务失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: '获取教学任务失败',
      icon: 'none',
    })
  }
}

// 获取页面标题
const getPageTitle = () => {
  // 如果是查看模式（禁用状态），返回查看标题
  if (isDisabled.value) {
    return '查看教材信息'
  }

  // 否则根据模式返回对应标题
  switch (mode.value) {
    case 'edit':
      return '编辑教材选用信息'
    case 'change':
      return '变更教材选用申请'
    default:
      return '教材基本信息'
  }
}

// 表单验证
const validateForm = (): boolean => {
  // 教材选用类别已经固定为"学生教材"，不需要验证
  if (!formData.name || !selectedTextbook.value) {
    uni.showToast({ title: '请选择教材', icon: 'none' })
    return false
  }
  if (!formData.selectionReason) {
    uni.showToast({ title: '请输入选用理由', icon: 'none' })
    return false
  }
  // 在变更模式下验证变更理由
  if (mode.value === 'change' && !formData.changeReason) {
    uni.showToast({ title: '请输入变更理由说明', icon: 'none' })
    return false
  }
  return true
}

// 提交表单
const submitForm = async () => {
  // 表单验证
  if (!validateForm()) return

  // 防止重复提交
  if (submitting.value) return
  submitting.value = true

  try {
    uni.showLoading({ title: '提交中...' })

    // 如果来源是教材选用页面，优先使用teachingTaskStore中的jxrwid和title
    let taskId = formData.taskId
    let taskTitle = currentTaskText.value

    if (source.value === 'material') {
      const jxrwid = teachingTaskStore.getCurrentJxrwid()
      const title = teachingTaskStore.getCurrentTitle()

      if (jxrwid) {
        taskId = parseInt(jxrwid) || taskId
      }

      if (title) {
        taskTitle = title
      }
    }

    // 构建基本的提交数据
    const baseData: AddTeachingMaterialParams = {
      sfscxy: formData.isFirstTime ? '1' : '0', // 是否首次使用，转换为字符串
      ssjxrwid: String(taskId || storeCurrentTask.value?.id || ''), // 所属教学任务ID
      title: taskTitle, // 标题使用当前任务文本或从teachingTaskStore获取的title
      xylb: formData.usageType, // 选用类别
      jcid: selectedTextbook.value?.id || 0, // 教材ID
      xyly: formData.selectionReason, // 选用理由
      bgyysm: '', // 变更原因，默认为空字符串
    }

    // 如果是变更模式，添加变更理由
    if (mode.value === 'change' && formData.changeReason) {
      baseData.bgyysm = formData.changeReason
    }

    // 根据模式和来源使用不同的接口类型
    if (mode.value === 'edit') {
      // 如果是编辑模式，使用更新接口
      if (storeCurrentTextbook.value && storeCurrentTextbook.value.id) {
        const updateData: UpdateTeachingMaterialParams = {
          ...baseData,
          id: storeCurrentTextbook.value.id,
        }
        await updateTeachingMaterial(updateData)
      } else {
        throw new Error('缺少教材ID，无法更新')
      }
    } else if (mode.value === 'change') {
      // 如果是变更模式
      if (storeCurrentTextbook.value && storeCurrentTextbook.value.id) {
        if (fromChangeList.value) {
          // 如果来自变更列表，使用更新变更申请接口
          const updateChangeData: UpdateChangeTeachingMaterialParams = {
            sfscxy: formData.isFirstTime ? '1' : '0',
            id: storeCurrentTextbook.value.id,
            ssjxrwid: parseInt(String(taskId || storeCurrentTask.value?.id || '0')),
            title: taskTitle,
            jcmc: selectedTextbook.value?.name || '',
            yjcxyid: storeCurrentTextbook.value.yjcxyid || 0,
            xylb: formData.usageType,
            teachingMaterialInfo: {
              name: selectedTextbook.value?.name || '',
              id: selectedTextbook.value?.id || 0,
            },
            xyly: formData.selectionReason,
            bgyysm: formData.changeReason,
            jcid: selectedTextbook.value?.id || 0,
          }
          await updateChangeTeachingMaterial(updateChangeData)
        } else {
          // 否则使用变更申请接口
          const changeData: ChangeTeachingMaterialParams = {
            ...baseData,
            yjcxyid: storeCurrentTextbook.value.id,
            jcmc: selectedTextbook.value?.name || '',
            bgyysm: formData.changeReason,
          }
          await changeTeachingMaterial(changeData)
        }
      } else {
        throw new Error('缺少教材ID，无法提交变更申请')
      }
    } else {
      // 新增模式
      await addTeachingMaterial(baseData)
    }

    uni.hideLoading()
    uni.showToast({
      title: '提交成功',
      icon: 'success',
      duration: 2000,
      success: () => {
        // 清除store中的教材数据
        textbookStore.clearCurrentTextbook()

        // 如果来源是教材选用页面，清除teachingTaskStore中的数据
        if (source.value === 'material') {
          teachingTaskStore.clearCurrentMaterialTask()
        }

        // 延迟返回上一页
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      },
    })
  } catch (err) {
    const errMsg = err.msg || '提交失败，请重试'
    uni.hideLoading()
    uni.showToast({
      title: errMsg,
      icon: 'none',
    })
  } finally {
    submitting.value = false
  }
}

// 取消
const cancel = () => {
  // 清除store中的教材数据
  textbookStore.clearCurrentTextbook()

  // 如果来源是教材选用页面，清除teachingTaskStore中的数据
  if (source.value === 'material') {
    teachingTaskStore.clearCurrentMaterialTask()
  }

  uni.navigateBack()
}

// 教材选择器相关状态和函数
const showTextbookSelector = ref(false)

// 打开教材选择器
const openTextbookSelector = () => {
  showTextbookSelector.value = true
}

// 关闭教材选择器
const closeTextbookSelector = () => {
  showTextbookSelector.value = false
}

// 处理教材选择
const handleTextbookSelect = (textbook: TeachingMaterial) => {
  selectedTextbook.value = textbook
  closeTextbookSelector()
}

// 教学任务选择器相关状态和函数
const showTaskSelector = ref(false)
const currentSemester = ref('') // 添加当前学年学期变量

// 打开教学任务选择器
const openTaskSelector = () => {
  showTaskSelector.value = true
}

// 关闭教学任务选择器
const closeTaskSelector = () => {
  showTaskSelector.value = false
}

// 处理教学任务选择
const handleTaskSelect = (task: TeachingTaskItem) => {
  // 更新teachingTaskStore中的数据
  teachingTaskStore.setCurrentMaterialTask(
    String(task.id),
    `${task.studyYear}学年第${task.studyTerm}学期 ${task.className} 《${task.courseName}》 ${task.leaderTeacherName}`,
  )

  // 更新表单数据
  formData.taskId = task.id

  closeTaskSelector()
}

// 初始化编辑模式
const initEditMode = () => {
  if (storeCurrentTextbook.value) {
    // 从store中获取教材数据
    const textbook = storeCurrentTextbook.value

    // 创建一个符合TeachingMaterial接口的对象
    const teachingMaterial: TeachingMaterial = {
      id: textbook.jcxxid || 0,
      name: textbook.jcmc || '',
      mainEditor: textbook.zybz || '',
      publishingHouse: textbook.cbs || '',
      isbn: textbook.isbn || '',
      booksCode: '',
      booksType: null,
      booksTypeName: null,
      publicationTime: '',
      printingTime: '',
      category: textbook.jclb || '',
      categoryName: textbook.jclbmc || '',
      editorCode: '',
      pinyinQuickIndex: '',
      money: '',
      stockNumber: 0,
      remark: textbook.remark || '',
      create_time: 0,
      update_time: 0,
      deltag: 0,
      oprybh: null,
      type: textbook.jclx || '',
      typeName: textbook.jclxmc || '',
      isNationalStandard: 0,
      isNationalAward: 0,
      suitableLevel: '',
      suitableLevelName: '',
      correspondingDomain: '',
      correspondingDomainName: '',
      feature: '',
      featureName: '',
      editorNumber: 0,
      classNumber: '',
      isNationalPlan: 0,
      planBatch: '',
      textbookInfoId: 0,
    }

    // 设置选中的教材
    selectedTextbook.value = teachingMaterial

    // 设置表单数据
    formData.usageType = textbook.xylb || '1'
    formData.isFirstTime = textbook.sfscxy === '1'
    formData.selectionReason = textbook.xyly || ''

    // 在变更模式下，设置变更理由
    if (mode.value === 'change') {
      // 如果store中有变更理由，则使用它
      formData.changeReason = textbook.bgyysm || ''
      console.log('初始化变更理由:', formData.changeReason)
    }
  }
}

// 确认删除教材
const confirmDelete = (id: number) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除该教材吗？删除后无法恢复。',
    confirmText: '删除',
    confirmColor: '#ff3b30',
    success: (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '删除中...' })

          deleteTextbook(id)
            .then(() => {
              uni.hideLoading()
              uni.showToast({
                title: '删除成功',
                icon: 'success',
              })

              // 清除store中的教材数据
              textbookStore.clearCurrentTextbook()

              // 如果来源是教材选用页面，清除teachingTaskStore中的数据
              if (source.value === 'material') {
                teachingTaskStore.clearCurrentMaterialTask()
              }

              // 延迟返回上一页
              setTimeout(() => {
                uni.navigateBack()
              }, 1500)
            })
            .catch((error) => {
              console.error('删除失败:', error)
              uni.hideLoading()
              uni.showToast({
                title: '删除失败，请重试',
                icon: 'none',
              })
            })
        } catch (error) {
          console.error('删除失败:', error)
          uni.hideLoading()
          uni.showToast({
            title: '删除失败，请重试',
            icon: 'none',
          })
        }
      }
    },
  })
}

// 页面加载时获取教学任务列表和字典数据
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  // @ts-expect-error - 忽略类型检查，因为uni-app的类型定义不完整
  const query = currentPage?.options || {}

  // 根据传入的mode参数设置页面模式
  if (query.mode === 'edit') {
    mode.value = 'edit'
  } else if (query.mode === 'change') {
    mode.value = 'change'
  } else {
    mode.value = 'add'
  }

  // 获取页面来源
  source.value = query.source || ''

  // 获取disabled参数，设置是否禁用编辑
  isDisabled.value = query.disabled === 'true'

  // 获取fromChangeList参数，设置是否来自变更列表
  fromChangeList.value = query.fromChangeList === 'true'

  // 获取传递过来的学年学期参数
  if (query.semester) {
    currentSemester.value = decodeURIComponent(query.semester)
    console.log('获取到学年学期参数:', currentSemester.value)
  }

  // 如果来源是教材选用页面，使用teachingTaskStore中的数据
  if (source.value === 'material') {
    // 使用teachingTaskStore中的jxrwid和title
    const jxrwid = teachingTaskStore.getCurrentJxrwid()
    const title = teachingTaskStore.getCurrentTitle()
    console.log('来自教材选用页面，jxrwid:', jxrwid, 'title:', title)

    // 这里可以根据需要使用jxrwid和title
    if (jxrwid) {
      // 例如，可以根据jxrwid设置表单中的任务ID
      formData.taskId = parseInt(jxrwid) || 0
    }
  }

  // 加载字典数据
  loadDictionaries().then(() => {
    // 如果是编辑或变更模式，初始化编辑模式
    if (mode.value === 'edit' || mode.value === 'change') {
      initEditMode()
    }

    // 根据模式设置页面标题
    uni.setNavigationBarTitle({
      title: getPageTitle(),
    })

    fetchTeachingTasks()
  })
})
</script>

<style lang="scss">
.add-textbook {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.form-item {
  margin-bottom: 16px;
}

.form-label {
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.selector-container {
  width: 100%;
  overflow: hidden;
}

.form-input,
.form-picker,
.form-textarea,
.form-select {
  box-sizing: border-box;
  width: 100%;
  background-color: #ffffff;
  border: 1px solid #bfbfc3;
  border-radius: 8px;
}

.text-ellipsis {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 按钮样式
button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  padding: 0 16px;
  font-size: 15px;
  border-radius: 10px;
  transition: opacity 0.3s;

  &:active {
    opacity: 0.85;
  }
}

// 主要按钮样式 - 蓝色
.btn-primary {
  color: #fff;
  background-color: #007aff;
  border: 1px solid #007aff;
}

// 取消按钮样式 - 灰色
.btn-cancel {
  color: #374151;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
}

// 危险按钮样式 - 红色
.btn-danger {
  color: #fff;
  background-color: #ff3b30;
  border: 1px solid #ff3b30;
}

// 弹窗样式
.popup-header {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  border-bottom: 1px solid #f2f2f2;
}

.popup-content {
  height: calc(80vh - 60px);
}

// 覆盖wot-design-uni的picker样式，使其与自定义选择器一致
:deep(.wd-picker) {
  width: 100%;
  overflow: hidden;
  background-color: #ffffff !important;
  border-radius: 8px !important;
}
</style>
