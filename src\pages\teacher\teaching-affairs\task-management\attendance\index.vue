<route lang="json5">
{
  style: {
    navigationBarTitleText: '课程考勤',
  },
}
</route>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useTeachingTaskStore } from '@/store/teachingTask'
import { getTaskAttendance } from '@/service/teacher'
import type { TaskAttendanceItem, TaskAttendanceQuery } from '@/types/teacher'
import { loadDictData, getDictLabel, getDictClass, getDictOptions } from '@/utils/dict'
import type { DictData } from '@/types/system'

// 获取当前教学任务信息
const teachingTaskStore = useTeachingTaskStore()
const currentTask = teachingTaskStore.currentTask

// 字典数据
const dictData = reactive<{
  DM_XSKQZTDM: DictData[] // 学生考勤状态字典
  DM_KQKSZGZT: DictData[] // 考勤考试资格状态字典
}>({
  DM_XSKQZTDM: [],
  DM_KQKSZGZT: [],
})

// 考勤状态映射 (备用，如果字典加载失败时使用)
const ATTENDANCE_STATUS_MAP = {
  1: '请假',
  2: '迟到',
  3: '旷课',
  4: '正常',
} as const

// 考勤记录列表
const attendanceList = ref<TaskAttendanceItem[]>([])

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
})

// 加载中状态
const loading = ref(false)

// 搜索条件
const searchForm = reactive({
  keyword: '', // 学号/姓名关键词
  status: '', // 考勤状态
  date: '', // 日期
})

// 格式化考勤状态显示
const formatAttendanceStatus = (kqzt: number): string => {
  // 优先使用字典数据
  const dictLabel = getDictLabel(dictData.DM_XSKQZTDM, String(kqzt))
  if (dictLabel) {
    return dictLabel
  }
  // 备用映射
  return ATTENDANCE_STATUS_MAP[kqzt as keyof typeof ATTENDANCE_STATUS_MAP] || `未知状态(${kqzt})`
}

// 获取考勤状态的样式类
const getStatusClass = (kqzt: number): string => {
  // 优先使用字典样式
  const dictClass = getDictClass(dictData.DM_XSKQZTDM, String(kqzt))
  if (dictClass) {
    // 将字典样式类转换为Tailwind CSS类
    switch (dictClass) {
      case 'primary':
        return 'bg-blue-100 text-blue-600'
      case 'success':
        return 'bg-green-100 text-green-600'
      case 'warning':
        return 'bg-orange-100 text-orange-600'
      case 'danger':
        return 'bg-red-100 text-red-600'
      default:
        return 'bg-gray-100 text-gray-600'
    }
  }

  // 备用样式映射
  switch (kqzt) {
    case 4: // 正常
      return 'bg-green-100 text-green-600'
    case 2: // 迟到
      return 'bg-orange-100 text-orange-600'
    case 1: // 请假
      return 'bg-blue-100 text-blue-600'
    case 3: // 旷课
      return 'bg-red-100 text-red-600'
    default:
      return 'bg-gray-100 text-gray-600'
  }
}

// 重置搜索条件
const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.status = ''
  searchForm.date = ''
  pagination.page = 1
  getAttendanceRecords()
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getAttendanceRecords()
}

// 获取考勤记录
const getAttendanceRecords = async () => {
  if (!currentTask?.id) {
    uni.showToast({
      title: '未获取到教学任务信息',
      icon: 'none',
    })
    return
  }

  loading.value = true

  try {
    // 构建查询参数
    const queryParams: TaskAttendanceQuery = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      sortBy: 'id',
      sortOrder: 'desc',
      kqzt: searchForm.status || 'not0', // 根据选择的状态筛选，默认显示有考勤状态的记录
    }

    // 添加搜索条件
    if (searchForm.keyword) {
      // 判断是学号还是姓名
      if (/^\d+$/.test(searchForm.keyword)) {
        queryParams.xsxh = searchForm.keyword
      } else {
        queryParams.xsxm = searchForm.keyword
      }
    }

    if (searchForm.date) {
      queryParams.skrq = searchForm.date
    }

    // 调用API获取考勤数据
    const response = await getTaskAttendance(Number(currentTask.id), queryParams)

    if (pagination.page === 1) {
      // 第一页，直接替换数据
      attendanceList.value = response.items
    } else {
      // 后续页，追加数据
      attendanceList.value.push(...response.items)
    }
    pagination.total = response.total
  } catch (error) {
    console.error('获取考勤记录失败:', error)
    uni.showToast({
      title: '获取考勤记录失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 加载更多数据
const loadMore = () => {
  if (loading.value || attendanceList.value.length >= pagination.total) {
    return
  }
  pagination.page++
  getAttendanceRecords()
}

// 加载字典数据
const loadDictionaries = async () => {
  try {
    const dicts = await loadDictData(['DM_XSKQZTDM', 'DM_KQKSZGZT'])
    dictData.DM_XSKQZTDM = dicts.DM_XSKQZTDM || []
    dictData.DM_KQKSZGZT = dicts.DM_KQKSZGZT || []
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}

// 页面加载
onMounted(async () => {
  if (!currentTask) {
    uni.showToast({
      title: '未获取到课程信息',
      icon: 'none',
    })
  }

  // 并行加载字典数据和考勤记录
  await Promise.all([loadDictionaries(), getAttendanceRecords()])
})

// 考勤状态选项 (计算属性，基于字典数据)
const statusOptions = computed(() => {
  const options = ['全部']
  if (dictData.DM_XSKQZTDM.length > 0) {
    // 使用字典数据
    const dictOptions = getDictOptions(dictData.DM_XSKQZTDM)
    options.push(...dictOptions.map((item) => item.label))
  } else {
    // 备用选项
    options.push('正常', '请假', '迟到', '旷课')
  }
  return options
})

const statusValues = computed(() => {
  const values = ['']
  if (dictData.DM_XSKQZTDM.length > 0) {
    // 使用字典数据
    const dictOptions = getDictOptions(dictData.DM_XSKQZTDM)
    values.push(...dictOptions.map((item) => item.value))
  } else {
    // 备用值
    values.push('4', '1', '2', '3')
  }
  return values
})

// 处理考勤状态选择变化
const onStatusChange = (e: any) => {
  const index = e.detail.value
  searchForm.status = statusValues.value[index]
}

// 处理日期选择变化
const onDateChange = (e: any) => {
  searchForm.date = e.detail.value
}
</script>

<template>
  <view class="attendance-page p-3">
    <!-- 搜索和筛选区域 -->
    <view class="search-area bg-white rounded-lg p-3 mb-3 shadow-sm">
      <view class="flex mb-2">
        <view class="flex-1 mr-2">
          <wd-input v-model="searchForm.keyword" placeholder="学号" clearable />
        </view>
        <wd-button type="primary" size="small" @click="handleSearch">
          <wd-icon name="search" class="mr-1" />
          搜索
        </wd-button>
      </view>

      <view class="flex mb-2">
        <view class="flex-1 mr-2">
          <picker mode="selector" :range="statusOptions" @change="onStatusChange">
            <view class="picker-view border border-solid border-gray-200 rounded px-3 py-2 text-sm">
              {{ statusOptions[statusValues.indexOf(searchForm.status)] || '考勤状态' }}
            </view>
          </picker>
        </view>
        <view class="flex-1">
          <picker mode="date" @change="onDateChange">
            <view class="picker-view border border-solid border-gray-200 rounded px-3 py-2 text-sm">
              {{ searchForm.date || '日期' }}
            </view>
          </picker>
        </view>
      </view>

      <view class="flex justify-end">
        <wd-button plain size="small" @click="resetSearch">
          <wd-icon name="refresh" class="mr-1" />
          重置
        </wd-button>
      </view>
    </view>

    <!-- 考勤记录列表 -->
    <view class="attendance-list">
      <wd-loading v-if="loading" />

      <view
        v-for="item in attendanceList"
        :key="item.id"
        class="attendance-card bg-white rounded-lg p-3 mb-2 shadow-sm"
      >
        <view class="flex justify-between items-center mb-2">
          <view class="text-base font-bold">{{ item.xsxm }}</view>
          <view :class="['status-tag px-2 py-0.5 rounded text-xs', getStatusClass(item.kqzt)]">
            {{ formatAttendanceStatus(item.kqzt) }}
          </view>
        </view>

        <view class="grid grid-cols-2 gap-1 mb-2 text-sm">
          <view class="info-row flex">
            <view class="info-label w-16 text-gray-500">座位号：</view>
            <view class="info-value flex-1">{{ item.zwh }}</view>
          </view>

          <view class="info-row flex">
            <view class="info-label w-16 text-gray-500">班级：</view>
            <view class="info-value flex-1">{{ item.ssbjmc }}</view>
          </view>

          <view class="info-row flex">
            <view class="info-label w-16 text-gray-500">学号：</view>
            <view class="info-value flex-1">{{ item.xsxh }}</view>
          </view>

          <view class="info-row flex">
            <view class="info-label w-16 text-gray-500">课程：</view>
            <view class="info-value flex-1 truncate">{{ item.kcmc }}</view>
          </view>

          <view class="info-row flex">
            <view class="info-label w-16 text-gray-500">教师：</view>
            <view class="info-value flex-1">{{ item.skjsxm }}</view>
          </view>

          <view class="info-row flex">
            <view class="info-label w-16 text-gray-500">日期：</view>
            <view class="info-value flex-1">{{ item.skrq }}</view>
          </view>
        </view>

        <view class="info-section bg-gray-50 p-2 rounded-lg mb-2 text-sm">
          <view class="flex justify-between mb-1">
            <view class="info-row flex flex-1">
              <view class="info-label w-16 text-gray-500">节次：</view>
              <view class="info-value flex-1">{{ item.jcshow }}</view>
            </view>

            <view class="info-row flex flex-1">
              <view class="info-label w-16 text-gray-500">课时：</view>
              <view class="info-value flex-1">{{ item.ks }}</view>
            </view>
          </view>

          <view class="flex justify-between mb-1">
            <view class="info-row flex flex-1">
              <view class="info-label w-16 text-gray-500">操作人：</view>
              <view class="info-value flex-1">{{ item.kqtjryxm || '系统' }}</view>
            </view>

            <view class="info-row flex flex-1">
              <view class="info-label w-20 text-gray-500">考勤时间：</view>
              <view class="info-value flex-1">{{ item.kqsj }}</view>
            </view>
          </view>

          <view class="flex justify-between mb-1">
            <view class="info-row flex flex-1">
              <view class="info-label w-20 text-gray-500">上课内容：</view>
              <view class="info-value flex-1 truncate">{{ item.sknl }}</view>
            </view>
          </view>
          <view class="flex justify-between mb-1">
            <view class="info-row flex flex-1">
              <view class="info-label w-20 text-gray-500">上课地点：</view>
              <view class="info-value flex-1 truncate">{{ item.skcdmc }}</view>
            </view>
          </view>
        </view>

        <view class="info-row flex mb-1 text-sm" v-if="item.glqj > 0">
          <view class="info-label w-16 text-gray-500">请假：</view>
          <view class="info-value flex-1 text-blue-600">已请假</view>
        </view>

        <view class="info-row flex mb-1 text-sm" v-if="item.remark">
          <view class="info-label w-16 text-gray-500">备注：</view>
          <view class="info-value flex-1">{{ item.remark }}</view>
        </view>

        <!-- <view class="flex justify-end mt-2">
          <wd-button type="primary" size="small" class="mr-2 text-xs">
            <wd-icon name="edit" class="mr-1" size="12px" />
            编辑
          </wd-button>
          <wd-button type="error" size="small" class="text-xs">
            <wd-icon name="delete" class="mr-1" size="12px" />
            删除
          </wd-button>
        </view> -->
      </view>

      <view
        v-if="attendanceList.length === 0 && !loading"
        class="empty-tip text-center py-8 text-gray-400"
      >
        暂无考勤记录
      </view>

      <!-- 加载更多 -->
      <view
        v-if="attendanceList.length > 0 && attendanceList.length < pagination.total"
        class="load-more text-center py-4"
      >
        <wd-button type="primary" plain size="small" :loading="loading" @click="loadMore">
          {{ loading ? '加载中...' : '加载更多' }}
        </wd-button>
      </view>

      <!-- 分页信息 -->
      <view
        v-if="pagination.total > 0"
        class="pagination-info text-center py-2 text-sm text-gray-500"
      >
        共 {{ pagination.total }} 条记录，已显示 {{ attendanceList.length }} 条
      </view>
    </view>

    <!-- 悬浮添加按钮 -->
    <!-- <view class="fixed right-4 bottom-20">
      <wd-button type="primary" size="large" round>
        <wd-icon name="add" size="20px" />
      </wd-button>
    </view> -->
  </view>
</template>

<style lang="scss">
.attendance-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.attendance-card {
  transition: transform 0.2s;

  &:active {
    transform: scale(0.98);
  }
}

.status-tag {
  min-width: 48px;
  text-align: center;
}

.picker-view {
  height: 35px;
  overflow: hidden;
  line-height: 35px;
  color: #333;
  text-overflow: ellipsis;
  white-space: nowrap;

  &:empty::before {
    color: #999;
    content: attr(placeholder);
  }
}
</style>
