<route lang="json5">
{
  style: {
    navigationBarTitleText: '班级通知',
  },
}
</route>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useTeachingTaskStore } from '@/store/teachingTask'
import { getTeachingTaskNoticeList } from '@/service/teachingTask'
import type { TeachingTaskNoticeItem, TeachingTaskNoticeQuery } from '@/types/teachingTask'

// 获取当前教学任务信息
const teachingTaskStore = useTeachingTaskStore()
const currentTask = teachingTaskStore.currentTask

// 通知列表
const notificationList = ref<TeachingTaskNoticeItem[]>([])

// 加载中状态
const loading = ref(false)

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
})

// 搜索条件
const searchForm = reactive({
  keyword: '', // 通知主题关键词
  status: '', // 状态筛选
  date: '', // 发件日期
})

// 重置搜索条件
const resetSearch = () => {
  searchForm.keyword = ''
  searchForm.status = ''
  searchForm.date = ''
  pagination.page = 1
  getNotificationList()
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getNotificationList()
}

// 处理状态选择变化
const onStatusChange = (e: any) => {
  const statusOptions = ['全部', '已发送', '草稿']
  const index = e.detail.value
  searchForm.status = index === 0 ? '' : statusOptions[index]
}

// 处理日期选择变化
const onDateChange = (e: any) => {
  searchForm.date = e.detail.value
}

// 获取通知列表
const getNotificationList = async () => {
  if (!currentTask?.id) {
    uni.showToast({
      title: '未获取到课程信息',
      icon: 'none',
    })
    return
  }

  loading.value = true

  try {
    const params: TeachingTaskNoticeQuery = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      sortBy: 'id',
      sortOrder: 'desc',
    }

    // 添加搜索条件
    if (searchForm.keyword) {
      params.yjzt = searchForm.keyword
    }

    const response = await getTeachingTaskNoticeList(currentTask.id, params)

    notificationList.value = response.items
    pagination.total = response.total
  } catch (error) {
    console.error('获取通知列表失败:', error)
    uni.showToast({
      title: '获取通知列表失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 格式化发件时间
const formatSendTime = (fjsj: string) => {
  return fjsj || ''
}

// 格式化收件人显示
const formatRecipients = (sjr: string) => {
  if (!sjr) return ''

  // 解析收件人格式：学号|姓名,学号|姓名
  const recipients = sjr.split(',').map((item) => {
    const [code, name] = item.split('|')
    return name || code
  })

  // 如果收件人太多，只显示前几个
  if (recipients.length > 3) {
    return recipients.slice(0, 3).join('、') + ` 等${recipients.length}人`
  }

  return recipients.join('、')
}

// 解析附件信息
const parseAttachments = (wjlb: string) => {
  if (!wjlb) return []

  // 解析文件列表格式：文件名|URL
  const files = wjlb.split(',').map((item) => {
    const [name, url] = item.split('|')
    return { name, url }
  })

  return files
}

// 页面加载
onMounted(() => {
  getNotificationList()
})
</script>

<template>
  <view class="notification-page p-3">
    <!-- 搜索和筛选区域 -->
    <view class="search-area bg-white rounded-lg p-3 mb-3 shadow-sm">
      <view class="flex mb-2">
        <view class="flex-1 mr-2">
          <wd-input v-model="searchForm.keyword" placeholder="通知主题" clearable />
        </view>
        <wd-button type="primary" size="small" @click="handleSearch">
          <wd-icon name="search" class="mr-1" />
          搜索
        </wd-button>
      </view>

      <!-- <view class="flex mb-2">
        <view class="flex-1 mr-2">
          <picker mode="selector" :range="['全部', '已发送', '草稿']" @change="onStatusChange">
            <view class="picker-view border border-solid border-gray-200 rounded px-3 py-2 text-sm">
              {{ searchForm.status || '状态' }}
            </view>
          </picker>
        </view>
        <view class="flex-1">
          <picker mode="date" @change="onDateChange">
            <view class="picker-view border border-solid border-gray-200 rounded px-3 py-2 text-sm">
              {{ searchForm.date || '发件日期' }}
            </view>
          </picker>
        </view>
      </view> -->

      <!-- <view class="flex justify-end">
        <wd-button plain size="small" @click="resetSearch">
          <wd-icon name="refresh" class="mr-1" />
          重置
        </wd-button>
      </view> -->
    </view>

    <!-- 通知列表 -->
    <view class="notification-list">
      <wd-loading v-if="loading" />

      <view
        v-for="item in notificationList"
        :key="item.id"
        class="notification-card bg-white rounded-lg p-3 mb-2 shadow-sm"
      >
        <view class="flex justify-between items-center mb-2">
          <view class="text-base font-bold truncate pr-2 flex-1">{{ item.yjzt }}</view>
          <view class="status-tag px-2 py-0.5 rounded text-xs bg-green-100 text-green-600">
            已发送
          </view>
        </view>

        <view class="info-section mb-2 text-sm">
          <view class="info-row flex mb-1">
            <view class="info-label w-16 text-gray-500">发件人：</view>
            <view class="info-value flex-1">{{ item.fjrxm }}</view>
          </view>

          <view class="info-row flex mb-1">
            <view class="info-label w-16 text-gray-500">收件人：</view>
            <view class="info-value flex-1">{{ formatRecipients(item.sjr) }}</view>
          </view>

          <view class="info-row flex mb-1">
            <view class="info-label w-16 text-gray-500">发件时间：</view>
            <view class="info-value flex-1">{{ formatSendTime(item.fjsj) }}</view>
          </view>
        </view>

        <!-- 附件信息 -->
        <view v-if="item.wjlb" class="attachment-section mb-2 text-sm">
          <view class="text-gray-500 mb-1">附件：</view>
          <view
            v-for="(file, index) in parseAttachments(item.wjlb)"
            :key="index"
            class="attachment-item flex items-center bg-blue-50 p-2 rounded mb-1"
          >
            <wd-icon name="attachment" class="mr-2 text-blue-500" size="14px" />
            <view class="flex-1 text-blue-600 text-xs truncate">{{ file.name }}</view>
          </view>
        </view>

        <view class="content-preview bg-gray-50 p-2 rounded-lg mb-2 text-sm">
          <view class="text-gray-500">内容预览：</view>
          <view class="line-clamp-2 text-gray-700" v-html="item.yjnr"></view>
        </view>

        <!-- <view class="flex justify-end mt-2">
          <wd-button type="primary" size="small" class="mr-2 text-xs">
            <wd-icon name="view" class="mr-1" size="12px" />
            查看
          </wd-button>
          <wd-button type="primary" size="small" class="mr-2 text-xs">
            <wd-icon name="edit" class="mr-1" size="12px" />
            编辑
          </wd-button>
          <wd-button type="error" size="small" class="text-xs">
            <wd-icon name="delete" class="mr-1" size="12px" />
            删除
          </wd-button>
        </view> -->
      </view>

      <view
        v-if="notificationList.length === 0 && !loading"
        class="empty-tip text-center py-8 text-gray-400"
      >
        暂无通知记录
      </view>
    </view>

    <!-- 悬浮添加按钮 -->
    <!-- <view class="fixed right-4 bottom-20">
      <wd-button type="primary" size="large" round>
        <wd-icon name="add" size="20px" />
      </wd-button>
    </view> -->
  </view>
</template>

<style lang="scss">
.notification-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.notification-card {
  transition: transform 0.2s;

  &:active {
    transform: scale(0.98);
  }
}

.status-tag {
  min-width: 48px;
  text-align: center;
}

.picker-view {
  height: 35px;
  overflow: hidden;
  line-height: 35px;
  color: #333;
  text-overflow: ellipsis;
  white-space: nowrap;

  &:empty::before {
    color: #999;
    content: attr(placeholder);
  }
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.attachment-item {
  border: 1px solid #e3f2fd;
}

.attachment-item:hover {
  background-color: #e3f2fd;
}
</style>
