<route lang="json5">
{
  style: {
    navigationBarTitleText: '班级学生',
  },
}
</route>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useTeachingTaskStore } from '@/store/teachingTask'
import { getTeachingTaskStudentList } from '@/service/teachingTask'
import type { StudentInfo, StudentListQuery, StudentListResponse } from '@/types/teachingTask'
import { loadDictData, getDictLabel } from '@/utils/dict'
import type { DictData } from '@/types/system'

// 获取当前教学任务信息
const teachingTaskStore = useTeachingTaskStore()
const currentTask = teachingTaskStore.currentTask

// 字典数据
const dictData = ref<Record<string, DictData[]>>({})

// 学生列表数据
const studentList = ref<StudentInfo[]>([])
const total = ref(0)
const teachingTaskInfo = ref<any>(null)

// 加载中状态
const loading = ref(false)
const loadingMore = ref(false)

// 分页参数
const pagination = reactive({
  page: 1,
  pageSize: 10,
})

// 搜索条件
const searchForm = reactive({
  xsxh: '', // 学生学号
  xm: '', // 学生姓名
  zwh: '', // 座位号
  yddh: '', // 移动电话
})

// 加载字典数据
const loadDictionaries = async () => {
  try {
    const dictTypes = [
      'DM_XBDM', // 性别代码
      'DM_MZDM', // 民族代码
      'DM_ZZMMDM', // 政治面貌代码
      'DM_XSLBDM', // 学生类别代码
      'SYS_SCHOOL_STATUS', // 学校状态
      'DM_XJZTDM', // 学籍状态代码
    ]
    dictData.value = await loadDictData(dictTypes)
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}

// 性别显示转换 (保留作为备用方案)
const getGenderText = (xb: number) => {
  // 优先使用字典数据
  const genderLabel = getDictLabel(dictData.value.DM_XBDM, String(xb))
  if (genderLabel) {
    return genderLabel
  }
  // 备用方案
  return xb === 1 ? '男' : xb === 2 ? '女' : '未知'
}

// 学生状态显示样式
const getStatusClass = (xszt: string) => {
  switch (xszt) {
    case '在读':
      return 'bg-green-100 text-green-600'
    case '休学':
      return 'bg-orange-100 text-orange-600'
    case '退学':
      return 'bg-red-100 text-red-600'
    default:
      return 'bg-gray-100 text-gray-600'
  }
}

// 重置搜索条件
const resetSearch = () => {
  searchForm.xsxh = ''
  searchForm.xm = ''
  searchForm.zwh = ''
  searchForm.yddh = ''
  pagination.page = 1
  getStudentList()
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  getStudentList()
}

// 获取学生列表
const getStudentList = async (isLoadMore = false) => {
  if (!currentTask?.id) {
    uni.showToast({
      title: '未获取到教学任务信息',
      icon: 'none',
    })
    return
  }

  // 区分首次加载和加载更多的loading状态
  if (isLoadMore) {
    loadingMore.value = true
  } else {
    loading.value = true
  }

  try {
    const params: StudentListQuery = {
      page: pagination.page,
      pageSize: pagination.pageSize,
    }

    // 添加搜索条件
    if (searchForm.xsxh) params.xsxh = searchForm.xsxh
    if (searchForm.xm) params.xm = searchForm.xm
    if (searchForm.zwh) params.zwh = searchForm.zwh
    if (searchForm.yddh) params.yddh = searchForm.yddh

    const response: StudentListResponse = await getTeachingTaskStudentList(currentTask.id, params)

    // 如果是加载更多，追加数据；否则替换数据
    if (isLoadMore) {
      studentList.value = [...studentList.value, ...response.items]
    } else {
      studentList.value = response.items
    }

    total.value = response.total
    teachingTaskInfo.value = response.jxrwxx
  } catch (error) {
    console.error('获取学生列表失败:', error)
    uni.showToast({
      title: '获取学生列表失败',
      icon: 'none',
    })
  } finally {
    console.log(isLoadMore)

    if (isLoadMore) {
      loadingMore.value = false
    } else {
      loading.value = false
    }
    console.log(loading.value)
  }
}

// 加载更多数据
const loadMore = () => {
  if (studentList.value.length < total.value) {
    pagination.page++
    getStudentList(true)
  }
}

// 是否还有更多数据
const hasMore = computed(() => {
  return studentList.value.length < total.value
})

// 页面加载
onMounted(async () => {
  if (!currentTask) {
    uni.showToast({
      title: '未获取到课程信息',
      icon: 'none',
    })
    return
  }

  // 并行加载字典数据和学生列表
  await Promise.all([loadDictionaries(), getStudentList()])
})
</script>

<template>
  <view class="student-page p-3">
    <!-- 搜索和筛选区域 -->
    <view class="search-area bg-white rounded-lg p-3 mb-3 shadow-sm">
      <view class="flex mb-2">
        <view class="flex-1 mr-2">
          <wd-input v-model="searchForm.xsxh" placeholder="学生学号" clearable />
        </view>
        <view class="flex-1 mr-2">
          <wd-input v-model="searchForm.xm" placeholder="学生姓名" clearable />
        </view>
      </view>

      <view class="flex mb-2">
        <view class="flex-1 mr-2">
          <wd-input v-model="searchForm.zwh" placeholder="座位号" clearable />
        </view>
        <view class="flex-1">
          <wd-input v-model="searchForm.yddh" placeholder="移动电话" clearable />
        </view>
      </view>

      <view class="flex justify-between">
        <wd-button plain size="small" @click="resetSearch">
          <wd-icon name="refresh" class="mr-1" />
          重置
        </wd-button>
        <wd-button type="primary" size="small" @click="handleSearch">
          <wd-icon name="search" class="mr-1" />
          搜索
        </wd-button>
      </view>
    </view>

    <!-- 学生列表 -->
    <view class="student-list">
      <wd-loading v-if="loading" />

      <view
        v-for="item in studentList"
        :key="item.id"
        class="student-card bg-white rounded-lg p-3 mb-2 shadow-sm"
      >
        <view class="flex justify-between items-center mb-2">
          <view class="text-base font-bold">{{ item.xm }}</view>
          <view :class="['status-tag px-2 py-0.5 rounded text-xs', getStatusClass(item.xszt)]">
            {{ getDictLabel(dictData.SYS_SCHOOL_STATUS, item.xszt) || item.xszt }}
          </view>
        </view>

        <view class="grid grid-cols-2 gap-1 mb-2 text-sm">
          <view class="info-row flex">
            <view class="info-label w-16 text-gray-500">班级：</view>
            <view class="info-value flex-1">{{ item.ssbjmc }}</view>
          </view>

          <view class="info-row flex">
            <view class="info-label w-16 text-gray-500">学号：</view>
            <view class="info-value flex-1">{{ item.xsxh }}</view>
          </view>

          <view class="info-row flex">
            <view class="info-label w-16 text-gray-500">座号：</view>
            <view class="info-value flex-1">{{ item.zwh }}</view>
          </view>

          <view class="info-row flex">
            <view class="info-label w-16 text-gray-500">性别：</view>
            <view class="info-value flex-1">{{ getGenderText(item.xb) }}</view>
          </view>
        </view>

        <view class="info-section bg-gray-50 p-2 rounded-lg mb-2 text-sm">
          <view class="flex justify-between mb-1">
            <view class="info-row flex flex-1">
              <view class="info-label w-20 text-gray-500">出生年月：</view>
              <view class="info-value flex-1">{{ item.csny }}</view>
            </view>

            <view class="info-row flex flex-1">
              <view class="info-label w-20 text-gray-500">民族：</view>
              <view class="info-value flex-1">
                {{ getDictLabel(dictData.DM_MZDM, item.mzdm) || item.mzdm }}
              </view>
            </view>
          </view>

          <view class="flex justify-between mb-1">
            <view class="info-row flex flex-1">
              <view class="info-label w-20 text-gray-500">政治面貌：</view>
              <view class="info-value flex-1">
                {{ getDictLabel(dictData.DM_ZZMMDM, item.zzmmdm) || item.zzmmdm }}
              </view>
            </view>

            <view class="info-row flex flex-1">
              <view class="info-label w-20 text-gray-500">学生类别：</view>
              <view class="info-value flex-1">
                {{ getDictLabel(dictData.DM_XSLBDM, item.xslb) || item.xslb }}
              </view>
            </view>
          </view>
        </view>

        <view class="mb-2 text-sm">
          <view class="info-row flex mb-1">
            <view class="info-label w-20 text-gray-500">移动电话：</view>
            <view class="info-value flex-1">{{ item.yddh }}</view>
          </view>

          <view class="info-row flex mb-1">
            <view class="info-label w-20 text-gray-500">电子信箱：</view>
            <view class="info-value flex-1">{{ item.dzxx }}</view>
          </view>
        </view>

        <view class="grid grid-cols-2 gap-1 text-sm">
          <view class="info-row flex">
            <view class="info-label w-20 text-gray-500">学生状态：</view>
            <view class="info-value flex-1">
              {{ getDictLabel(dictData.SYS_SCHOOL_STATUS, item.xszt) || item.xszt }}
            </view>
          </view>

          <view class="info-row flex">
            <view class="info-label w-20 text-gray-500">学籍状态：</view>
            <view class="info-value flex-1">
              {{ getDictLabel(dictData.DM_XJZTDM, item.xjzt) || item.xjzt }}
            </view>
          </view>
        </view>

        <!-- <view class="flex justify-end mt-2">
          <wd-button type="primary" size="small" class="mr-2 text-xs">
            <wd-icon name="edit" class="mr-1" size="12px" />
            编辑
          </wd-button>
          <wd-button type="error" size="small" class="text-xs">
            <wd-icon name="delete" class="mr-1" size="12px" />
            删除
          </wd-button>
        </view> -->
      </view>

      <!-- 加载更多按钮 -->
      <view v-if="hasMore && studentList.length > 0" class="text-center py-4">
        <wd-button type="primary" plain size="small" @click="loadMore" :loading="loadingMore">
          加载更多
        </wd-button>
      </view>

      <!-- 没有更多数据提示 -->
      <view
        v-if="!hasMore && studentList.length > 0"
        class="text-center py-4 text-gray-400 text-sm"
      >
        已显示全部 {{ total }} 条记录
      </view>

      <view
        v-if="studentList.length === 0 && !loading"
        class="empty-tip text-center py-8 text-gray-400"
      >
        暂无学生记录
      </view>
    </view>

    <!-- 悬浮添加按钮 -->
    <!-- <view class="fixed right-4 bottom-20">
      <wd-button type="primary" size="large" round>
        <wd-icon name="add" size="20px" />
      </wd-button>
    </view> -->
  </view>
</template>

<style lang="scss">
.student-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.student-card {
  transition: transform 0.2s;

  &:active {
    transform: scale(0.98);
  }
}

.status-tag {
  min-width: 48px;
  text-align: center;
}

.picker-view {
  height: 35px;
  overflow: hidden;
  line-height: 35px;
  color: #333;
  text-overflow: ellipsis;
  white-space: nowrap;

  &:empty::before {
    color: #999;
    content: attr(placeholder);
  }
}
</style>
