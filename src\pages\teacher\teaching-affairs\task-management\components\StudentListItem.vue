<template>
  <view class="bg-white rounded-lg shadow-sm p-2">
    <view class="flex items-center">
      <!-- 序号 -->
      <view class="w-10 flex-shrink-0">
        <view
          :class="[
            'w-8 h-8 rounded-full flex items-center justify-center',
            hasScore ? 'bg-blue-100' : 'bg-gray-100',
          ]"
        >
          <text :class="['text-xs font-medium', hasScore ? 'text-blue-600' : 'text-gray-600']">
            {{ studentNumber < 10 ? `0${studentNumber}` : studentNumber }}
          </text>
        </view>
      </view>

      <!-- 学生姓名和学号 -->
      <view class="flex-1 ml-1">
        <view class="font-medium text-sm text-gray-800">{{ name }}</view>
        <view class="text-xs text-gray-500">学号: {{ studentId }}</view>
        <!-- 绩点、学分、是否通过信息 -->
        <view class="flex text-xs text-gray-500 mt-1">
          <view class="mr-3">绩点: {{ gpa || '-' }}</view>
          <view v-if="showCredit" class="mr-3">学分: {{ credit || '-' }}</view>
          <view :class="['', passedText.color]">
            {{ passedText.text }}
          </view>
        </view>
      </view>

      <!-- 成绩输入框 -->
      <view class="w-25 flex-shrink-0 flex justify-center">
        <input
          type="text"
          :value="displayScore"
          class="w-23 h-7 text-center text-sm border border-solid rounded-md transition-all"
          :class="[
            disabled
              ? 'border-gray-200 bg-gray-100 text-gray-500 cursor-not-allowed'
              : 'border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-200',
          ]"
          placeholder="分数"
          :max="scoreTypeInfo?.max"
          :disabled="disabled"
          @input="handleInput"
          @focus="handleFocus"
          @blur="handleBlur"
        />
      </view>

      <!-- 选择按钮 -->
      <view class="w-8 flex-shrink-0 flex justify-center">
        <view
          class="w-6 h-6 rounded-full flex items-center justify-center"
          :class="[
            disabled
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-blue-100 text-blue-600 cursor-pointer',
          ]"
          @click="!disabled && openScoreSelector()"
        >
          <wd-icon name="filter" size="10px" />
        </view>
      </view>
    </view>
  </view>

  <!-- 成绩选择弹窗 -->
  <wd-popup v-model="showScoreSelector" position="bottom" safe-area-inset-bottom>
    <view class="p-4 bg-white">
      <view class="text-center text-base font-medium mb-4">请选择成绩</view>

      <!-- 使用PickerView组件 -->
      <wd-picker-view
        v-model="selectedOption"
        :columns="[scoreOptions]"
        :key="selectorKey"
        label-key="label"
        value-key="value"
        loading-color="#3b82f6"
      ></wd-picker-view>

      <!-- 操作按钮 -->
      <view class="flex space-x-2 mt-4">
        <view
          class="flex-1 bg-gray-100 text-gray-700 py-2 rounded-md text-center"
          @click="showScoreSelector = false"
        >
          取消
        </view>
        <view
          class="flex-1 bg-blue-500 text-white py-2 rounded-md text-center"
          @click="confirmSelection"
        >
          确定
        </view>
      </view>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import { ref, watch, computed, onMounted } from 'vue'
import { loadDictData, getDictLabel, getDictDataFromStore } from '@/utils/dict'
import { debounce } from '@/utils'
import type { DictData } from '@/types/system'

const props = defineProps({
  studentNumber: {
    type: Number,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  studentId: {
    type: String,
    required: true,
  },
  score: {
    type: String,
    default: '',
  },
  hasScore: {
    type: Boolean,
    default: false,
  },
  scoreTypeInfo: {
    type: Object,
    default: null,
  },
  gpa: {
    type: [Number, String],
    default: '',
  },
  credit: {
    type: [Number, String],
    default: '',
  },
  isPassed: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  showCredit: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['update:score', 'update:hasScore'])

// 字典数据
const scoreDict = ref<DictData[]>([])
const passStatusDict = ref<DictData[]>([])

// 本地状态
const localScore = ref(props.score)
const showScoreSelector = ref(false)
const selectedOption = ref('')
const selectorKey = ref(0)
const inputting = ref(false)

// 初始化数据
onMounted(async () => {
  await loadDictionaries()
  initializeScoreOptions()
})

// 加载字典数据
const loadDictionaries = async () => {
  try {
    // 否则加载字典数据
    const dicts = await loadDictData(['DM_CJXXDM', 'SYS_PASS_NO'])
    scoreDict.value = dicts.DM_CJXXDM || []
    passStatusDict.value = dicts.SYS_PASS_NO || []
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}

// 初始化成绩选项
const initializeScoreOptions = () => {
  if (props.score && scoreOptions.value.length > 0) {
    const currentOption = scoreOptions.value.find((option) => option.value === props.score)
    if (currentOption) {
      selectedOption.value = currentOption.value
    }
  }
}

// 监听props变化更新本地状态
watch(
  () => props.score,
  (newVal) => {
    localScore.value = newVal
  },
)

// 监听成绩类型信息变化
watch(
  () => props.scoreTypeInfo,
  () => {
    updateScoreOptionsOnTypeChange()
  },
  { deep: true },
)

// 成绩类型变化时更新选项
const updateScoreOptionsOnTypeChange = () => {
  if (showScoreSelector.value && scoreOptions.value.length > 0) {
    const currentOption = scoreOptions.value.find((option) => option.value === localScore.value)
    selectedOption.value = currentOption ? currentOption.value : scoreOptions.value[0].value
  }
}

// 计算展示的分数值
const displayScore = computed(() => {
  if (localScore.value && Number(localScore.value) < 0) {
    // 使用字典获取负数成绩的标签
    const dictLabel = getDictLabel(scoreDict.value, localScore.value)
    return dictLabel || localScore.value
  }
  return localScore.value
})

// 成绩选项
const scoreOptions = computed(() => {
  if (props.scoreTypeInfo?.options?.length > 0) {
    return props.scoreTypeInfo.options.map((option) => ({
      label: option.title,
      value: option.value,
    }))
  }
  return []
})

// 每次选项变化时，增加key值以强制刷新选择器
watch(
  scoreOptions,
  () => {
    selectorKey.value += 1
  },
  { deep: true },
)

// 计算是否通过的显示文本和颜色
const passedText = computed(() => {
  const value = props.isPassed ? '1' : '0'
  const dictLabel = getDictLabel(passStatusDict.value, value)

  return {
    text: dictLabel || (props.isPassed ? '已通过' : '未通过'),
    color: props.isPassed ? 'text-green-500' : 'text-red-500',
  }
})

// 处理输入事件
const handleInput = (e: any) => {
  if (props.disabled) return

  const value = e.detail.value
  inputting.value = true

  // 检查最大值限制
  if (props.scoreTypeInfo?.max && Number(value) > props.scoreTypeInfo.max) {
    localScore.value = props.scoreTypeInfo.max.toString()
  } else {
    localScore.value = value
  }

  // 使用防抖函数延迟更新
  debouncedUpdateScore()
}

// 处理聚焦事件
const handleFocus = () => {
  if (props.disabled) return
  localScore.value = ''
}

// 处理失焦事件
const handleBlur = () => {
  if (props.disabled) return

  inputting.value = false

  // 如果输入框为空，回填原值
  if (!localScore.value.trim()) {
    localScore.value = props.score
  } else if (localScore.value !== props.score) {
    // 如果成绩发生变化，立即更新
    updateScore(localScore.value)
  }
}

// 创建防抖版本的更新分数函数
const debouncedUpdateScore = debounce(() => {
  if (inputting.value && localScore.value !== props.score) {
    updateScore(localScore.value)
    inputting.value = false
  }
}, 800)

// 打开成绩选择器
const openScoreSelector = () => {
  if (props.disabled) return

  if (scoreOptions.value.length > 0) {
    // 查找当前成绩在选项中的对应值
    const currentOption = scoreOptions.value.find((option) => option.value === localScore.value)
    // 如果找到对应值，则使用它；否则默认使用第一个选项
    selectedOption.value = currentOption ? currentOption.value : scoreOptions.value[0].value
    // 强制刷新选择器
    selectorKey.value += 1
    showScoreSelector.value = true
  }
}

// 确认选择
const confirmSelection = () => {
  if (props.disabled) return

  if (selectedOption.value) {
    // 检查是否有变化
    const hasChanged = localScore.value !== selectedOption.value
    localScore.value = selectedOption.value
    // 如果有变化，则更新成绩
    if (hasChanged || localScore.value !== props.score) {
      updateScore(localScore.value)
    }
  }
  showScoreSelector.value = false
}

// 更新分数并发射事件
const updateScore = (value: string) => {
  if (props.disabled) return

  emit('update:score', value)
  emit('update:hasScore', !!value)
}
</script>
