<template>
  <view class="mx-3 bg-white rounded-lg p-3 shadow-sm">
    <view class="flex justify-between mb-2">
      <view>
        <view class="font-medium text-sm text-gray-800">
          {{ courseName || '未知课程' }}
        </view>
        <view class="text-xs text-gray-500">{{ className || '未知班级' }}</view>
      </view>
      <view class="text-right">
        <view class="text-xs text-gray-500">学期</view>
        <view class="text-xs text-gray-800">{{ xn }}-{{ xq }}</view>
      </view>
    </view>

    <view class="bg-blue-50 rounded-md p-2">
      <view class="flex justify-between text-xs">
        <text class="text-gray-600">课程学分</text>
        <text class="text-blue-600 font-medium">{{ creditHour || '-' }}</text>
      </view>
      <view class="flex justify-between text-xs mt-1">
        <text class="text-gray-600">课程学时</text>
        <text class="text-blue-600 font-medium">{{ courseTotalHours || '-' }}</text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
defineProps({
  courseName: {
    type: String,
    default: '',
  },
  className: {
    type: String,
    default: '',
  },
  xn: {
    type: String,
    default: '',
  },
  xq: {
    type: String,
    default: '',
  },
  creditHour: {
    type: [Number, String],
    default: 0,
  },
  courseTotalHours: {
    type: [Number, String],
    default: 0,
  },
})
</script>
