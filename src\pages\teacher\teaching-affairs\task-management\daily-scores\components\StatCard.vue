<template>
  <view class="flex-1 bg-white px-4 py-2 rounded-lg card-shadow">
    <view
      :class="[
        'stat-value',
        type === 'success'
          ? 'success'
          : type === 'error'
            ? 'error'
            : type === 'warning'
              ? 'warning'
              : '',
      ]"
    >
      {{ value }}
    </view>
    <view class="text-xs text-gray-600 mt-2">{{ title }}</view>
  </view>
</template>

<script lang="ts" setup>
defineProps({
  title: {
    type: String,
    required: true,
  },
  value: {
    type: [Number, String],
    required: true,
  },
  type: {
    type: String,
    default: '',
    validator: (value: string) => ['success', 'error', 'warning', ''].includes(value),
  },
})
</script>

<style>
.card-shadow {
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #3a8eff;
}

.stat-value.warning {
  color: #faad14;
}

.stat-value.error {
  color: #ff3860;
}

.stat-value.success {
  color: #52c41a;
}
</style>
