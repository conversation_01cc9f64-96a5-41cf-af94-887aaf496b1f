<template>
  <view
    v-if="start && end"
    class="mx-3 mb-2 bg-blue-50 border border-blue-200 rounded-lg p-2 shadow-sm"
  >
    <view class="flex items-center">
      <wd-icon name="notification" size="16px" class="text-blue-500 mr-2" />
      <text class="text-sm text-blue-700 font-medium">{{ title }}</text>
    </view>
    <view class="text-xs text-blue-600 mt-1 pl-6">
      开放时间：{{ formatDateTime(start) }} 至 {{ formatDateTime(end) }}
    </view>
  </view>
</template>

<script lang="ts" setup>
defineProps({
  title: {
    type: String,
    default: '当前允许提交成绩',
  },
  start: {
    type: String,
    default: '',
  },
  end: {
    type: String,
    default: '',
  },
})

// 格式化日期时间
const formatDateTime = (dateTimeStr: string): string => {
  if (!dateTimeStr) return '-'
  return dateTimeStr
}
</script>
