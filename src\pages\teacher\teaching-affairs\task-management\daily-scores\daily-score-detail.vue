<route lang="json5">
{
  style: {
    navigationBarTitleText: '日常成绩登分操作',
  },
}
</route>
<template>
  <view class="min-h-screen bg-gray-100 pt-2">
    <!-- 课程信息卡片 -->
    <course-info-card
      :course-name="courseInfo.courseName"
      :class-name="courseInfo.className"
      :xn="courseInfo.xn"
      :xq="courseInfo.xq"
      :credit-hour="courseInfo.kcxf"
      :course-total-hours="courseInfo.kcss"
    />

    <!-- 统计信息 -->
    <view class="flex gap-3 mx-3 mt-2">
      <stat-card title="通过人数" :value="statsData.passCount" type="success" />
      <stat-card title="不通过人数" :value="statsData.failCount" type="error" />
      <stat-card title="0分人数" :value="statsData.zeroCount" type="warning" />
    </view>

    <!-- 快捷操作 -->
    <view class="mx-3 mt-2 bg-white rounded-lg p-3 shadow-sm">
      <view class="font-medium text-xs text-gray-800 mb-2">快捷操作</view>
      <!-- 总评生成、提交成绩：暂时隐藏、后续可能加入
        <view class="flex space-x-2">
        <action-button type="blue" icon-name="refresh" @click="handleGenerateTotalScore">
          总评生成
        </action-button>
        <action-button type="red" icon-name="check-bold" @click="handleSubmitScore">
          提交成绩
        </action-button>
      </view> -->

      <!-- 成绩状态显示 -->
      <action-button
        :type="getStatusButtonType"
        :icon-name="getStatusIconName"
        block
        @click="handleStatusClick"
      >
        {{ getStatusText }}
      </action-button>

      <!-- 成绩评定类型设置 -->
      <action-button
        class="mt-2"
        type="purple"
        icon-name="setting"
        block
        @click="showGradeTypePopup = true"
      >
        成绩评定类型：{{ currentGradeType }}
      </action-button>

      <!-- 教学平台同步按钮：暂时隐藏、后续可能加入-->
      <!-- <action-button
        class="mt-2"
        type="green"
        icon-name="cloud-download"
        block
        @click="handleSyncFromPlatform"
      >
        教学平台同步
      </action-button> -->
    </view>

    <!-- 学生成绩列表 -->
    <view class="p-3">
      <view class="flex justify-between mb-2">
        <view class="text-base font-medium text-gray-800">学生成绩</view>
        <!-- <view class="flex space-x-2">
          <view class="text-blue-500 text-xs flex items-center" @click="handleSort">
            <wd-icon name="sort" size="12px" class="mr-1" />
            排序
          </view>
          <view class="text-blue-500 text-xs flex items-center" @click="handleFilter">
            <wd-icon name="filter" size="12px" class="mr-1" />
            筛选
          </view>
        </view> -->
      </view>

      <!-- 成绩已提交提示 -->
      <view
        v-if="scoreTypeInfo?.submit"
        class="bg-yellow-50 border border-yellow-200 rounded-md p-2 mb-2"
      >
        <view class="flex items-center text-yellow-700 text-xs">
          <wd-icon name="warning" size="16px" class="mr-1" />
          <text>成绩已提交，不可修改。如需修改，请先将状态设置为"未提交"。</text>
        </view>
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="flex justify-center items-center py-6">
        <wd-loading color="#3b82f6" size="24px" />
        <text class="ml-2 text-xs text-gray-500">加载中...</text>
      </view>

      <!-- 无数据提示 -->
      <view
        v-else-if="students.length === 0"
        class="flex flex-col items-center justify-center py-6"
      >
        <wd-icon name="info-circle" size="36px" class="text-gray-400" />
        <text class="mt-1 text-xs text-gray-500">暂无学生成绩数据</text>
      </view>

      <!-- 学生列表 -->
      <view v-else class="space-y-2">
        <student-list-item
          v-for="(student, index) in students"
          :key="student.id"
          :student-number="student.zwh || index + 1"
          :name="student.name"
          :student-id="student.studentId"
          :score="student.score"
          :has-score="student.hasScore"
          :score-type-info="scoreTypeInfo"
          :gpa="student.gpa"
          :credit="student.credit"
          :is-passed="student.isPassed"
          :show-credit="false"
          :disabled="!!scoreTypeInfo?.submit"
          @update:score="updateStudentScore(index, $event)"
          @update:has-score="updateStudentHasScore(index, $event)"
        />
      </view>
    </view>

    <!-- 成绩评定类型选择弹窗 -->
    <wd-popup v-model="showGradeTypePopup" position="bottom" safe-area-inset-bottom>
      <view class="p-4 bg-white">
        <view class="text-center text-base font-medium mb-4">请选择成绩评定类型</view>

        <!-- 使用PickerView组件 -->
        <wd-picker-view
          v-model="selectedGradeType"
          :columns="[gradeTypeOptions]"
          label-key="label"
          value-key="value"
          loading-color="#3b82f6"
          @change="handleGradeTypeChange"
        ></wd-picker-view>

        <!-- 操作按钮 -->
        <view class="flex space-x-2 mt-4">
          <view
            class="flex-1 bg-gray-100 text-gray-700 py-2 rounded-md text-center"
            @click="showGradeTypePopup = false"
          >
            取消
          </view>
          <view
            class="flex-1 bg-blue-500 text-white py-2 rounded-md text-center"
            @click="confirmGradeType"
          >
            确定
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 成绩状态设置弹窗 -->
    <wd-popup v-model="showStatusPopup" position="bottom" safe-area-inset-bottom>
      <view class="p-4 bg-white">
        <view class="text-center text-base font-medium mb-4">
          请在下方选择需要设置的成绩提交状态及是否开放学生查询
        </view>

        <!-- 状态开关 -->
        <view class="space-y-4">
          <view class="flex justify-between items-center">
            <text class="text-sm">成绩是否提交</text>
            <wd-switch v-model="statusSettings.isSubmitted" size="20px" active-color="#3b82f6" />
          </view>
          <view class="flex justify-between items-center">
            <text class="text-sm">是否开放学生查询</text>
            <wd-switch v-model="statusSettings.isAllowed" size="20px" active-color="#3b82f6" />
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="flex space-x-2 mt-6">
          <view
            class="flex-1 bg-gray-100 text-gray-700 py-2 rounded-md text-center"
            @click="showStatusPopup = false"
          >
            取消
          </view>
          <view
            class="flex-1 bg-blue-500 text-white py-2 rounded-md text-center"
            @click="updateScoreStatus"
          >
            更新
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- Toast提示 -->
    <wd-toast />

    <!-- 结果对话框 -->
    <result-dialog
      v-model="resultDialog.show"
      :title="resultDialog.title"
      :message="resultDialog.message"
      :success="resultDialog.success"
      :show-error-icon="resultDialog.showErrorIcon"
      :centered="resultDialog.centered"
      :show-fail-list="resultDialog.showFailList"
      :fail-students="failStudents"
    />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, reactive } from 'vue'
import {
  getTeacherTotalScoreList,
  setScoreType,
  generateTotalScore,
  submitTotalScore,
  getDailyScoreList,
} from '@/service/score'
import {
  getScoreTypeInfo,
  syncTeachingTaskTotalScore,
  updateTeachingTaskTotalScore,
  getDailyScoreTypeInfo,
  editDailyScoreStatus,
  setDailyScoreType,
  updateDailyScore,
} from '@/service/teacher'
import type {
  TeacherTotalScoreItem,
  SetScoreTypeRequest,
  GenerateTotalScoreRequest,
  SubmitTotalScoreRequest,
  SubmitTotalScoreResponse,
  DailyScoreItem,
  DailyScoreQuery,
} from '@/types/score'
import type {
  ScoreTypeInfoQuery,
  ScoreTypeInfoResponse,
  TeachingTaskTotalScoreSyncRequest,
  TeachingTaskTotalScoreSyncResponse,
  DailyScoreTypeInfoQuery,
  DailyScoreTypeInfoResponse,
  ScoreTimeRange,
  DailyScoreStatusEditRequest,
  DailyScoreTypeSetRequest,
  DailyScoreUpdateRequest,
  DailyScoreUpdateItem,
} from '@/types/teacher'
import { useTeachingTaskStore } from '@/store/teachingTask'
import { loadDictData, getDictOptions, getDictLabel } from '@/utils/dict'
import type { DictData } from '@/types/system'
import { useToast } from 'wot-design-uni'

// 导入组件
import TimeBanner from './components/TimeBanner.vue'
import CourseInfoCard from './components/CourseInfoCard.vue'
import StatCard from './components/StatCard.vue'
import ActionButton from './components/ActionButton.vue'
import StudentListItem from '../components/StudentListItem.vue'
import ResultDialog from './components/ResultDialog.vue'

// 获取页面参数
const query = defineProps<{
  id?: string
  jxrwid?: string
}>()

// 获取教学任务信息
const teachingTaskStore = useTeachingTaskStore()
const currentTask = computed(() => teachingTaskStore.currentTask)

// 添加toast提示
const toast = useToast()

// API参数和状态
const jxrwid = computed(() => query.jxrwid || currentTask.value?.id?.toString() || '')
const dailyScoreId = computed(() => query.id || '')
const loading = ref(false)

// 统计数据
const statsData = ref({
  passCount: 0, // 通过人数
  failCount: 0, // 不通过人数
  zeroCount: 0, // 0分人数
})

// 成绩相关信息
const scoreTypeInfo = ref<DailyScoreTypeInfoResponse | null>(null)

// 课程信息
const courseInfo = ref({
  courseName: '',
  className: '',
  xn: '',
  xq: '',
  kcxf: 0,
  kcss: '',
})

// 成绩评定类型相关
const showGradeTypePopup = ref(false)
const selectedGradeType = ref('numeric')
const tempSelectedGradeType = ref('')
const gradeTypeDict = ref<DictData[]>([])
const gradeTypeOptions = computed(() => getDictOptions(gradeTypeDict.value))
const currentGradeType = computed(() => {
  return getDictLabel(gradeTypeDict.value, selectedGradeType.value) || '数字制'
})

// 学生数据
const students = ref<
  {
    id: number
    name: string
    studentId: string
    score: string
    hasScore: boolean
    zwh: number
    originalData: TeacherTotalScoreItem
    gpa: string
    credit: string
    isPassed: boolean
  }[]
>([])

// 结果对话框相关
const resultDialog = reactive({
  show: false,
  title: '',
  message: '',
  success: false,
  showErrorIcon: false,
  centered: false,
  showFailList: false,
})
const failStudents = ref<string[]>([])

// 计算属性
const totalStudents = computed(() => students.value.length)

// 状态按钮相关计算属性
const getStatusText = computed(() => {
  const submitStatus = scoreTypeInfo.value?.submit ? '成绩已提交' : '成绩未提交'
  const allowStatus = scoreTypeInfo.value?.allow ? '已开放学生查询' : '未开放学生查询'
  return `${submitStatus} | ${allowStatus}`
})

const getStatusButtonType = computed(() => {
  // 如果已提交且已开放查询，使用绿色
  if (scoreTypeInfo.value?.submit && scoreTypeInfo.value?.allow) {
    return 'green'
  }
  // 如果已提交但未开放查询，使用蓝色
  else if (scoreTypeInfo.value?.submit) {
    return 'blue'
  }
  // 如果未提交，使用橙色
  else {
    return 'orange'
  }
})

const getStatusIconName = computed(() => {
  // 如果已提交且已开放查询，使用check-bold图标
  if (scoreTypeInfo.value?.submit && scoreTypeInfo.value?.allow) {
    return 'check-bold'
  }
  // 如果已提交但未开放查询，使用info-circle图标
  else if (scoreTypeInfo.value?.submit) {
    return 'info-circle'
  }
  // 如果未提交，使用warning图标
  else {
    return 'warning'
  }
})

// 成绩状态设置相关
const showStatusPopup = ref(false)
const statusSettings = reactive({
  isSubmitted: false,
  isAllowed: false,
})

// 状态按钮点击事件
const handleStatusClick = () => {
  // 初始化状态设置
  statusSettings.isSubmitted = !!scoreTypeInfo.value?.submit
  statusSettings.isAllowed = !!scoreTypeInfo.value?.allow

  // 显示状态设置弹窗
  showStatusPopup.value = true
}

// 更新成绩状态
const updateScoreStatus = () => {
  // 关闭弹窗
  showStatusPopup.value = false

  // 保存原始状态用于对比
  const originalSubmitStatus = !!scoreTypeInfo.value?.submit

  try {
    // 调用API更新成绩状态
    const params: DailyScoreStatusEditRequest = {
      ids: [parseInt(dailyScoreId.value)],
      cjtjbz: statusSettings.isSubmitted ? 1 : 0,
      rccjcxzt: statusSettings.isAllowed ? 1 : 0,
    }

    toast.loading('正在更新成绩状态...')
    editDailyScoreStatus(params)
      .then((res) => {
        toast.close()
        // 更新本地状态
        if (scoreTypeInfo.value) {
          scoreTypeInfo.value.submit = statusSettings.isSubmitted ? 1 : 0
          scoreTypeInfo.value.allow = statusSettings.isAllowed ? 1 : 0
        }

        toast.show({
          msg: '成绩状态更新成功',
          iconName: 'success',
        })

        // 如果从提交状态变为未提交状态，显示提示
        if (originalSubmitStatus && !statusSettings.isSubmitted) {
          setTimeout(() => {
            toast.show({
              msg: '成绩已变为未提交状态，可以修改成绩了',
              iconName: 'info',
              duration: 2000,
            })
          }, 1000)
        }

        // 如果从未提交状态变为提交状态，显示提示
        if (!originalSubmitStatus && statusSettings.isSubmitted) {
          setTimeout(() => {
            toast.show({
              msg: '成绩已变为已提交状态，不能再修改成绩',
              iconName: 'info',
              duration: 2000,
            })
          }, 1000)
        }
      })
      .catch((error) => {
        toast.close()
        console.error('更新成绩状态失败', error)
      })
  } catch (error) {
    console.error('更新成绩状态失败', error)
    toast.show({
      msg: '更新成绩状态失败',
      iconName: 'error',
    })
  }
}

// ======= 数据加载相关函数 =======

// 初始化页面数据
const initPageData = async () => {
  // 检查教学任务信息
  if (!currentTask.value) {
    toast.show({ msg: '未获取到教学任务信息', iconName: 'error' })
  }

  try {
    // 并行请求数据
    await Promise.all([
      fetchStudentScoreList(),
      fetchScoreTypeInfo().then(() => loadGradeTypeDict()),
    ])
  } catch (error) {
    console.error('初始化页面数据失败', error)
    toast.show({ msg: '加载数据失败', iconName: 'error' })
  }
}

// 加载成绩评定类型字典
const loadGradeTypeDict = async () => {
  try {
    const dictData = await loadDictData(['DM_CJPDLX'])
    gradeTypeDict.value = dictData.DM_CJPDLX || []

    // 设置当前选中的成绩评定类型
    if (scoreTypeInfo.value?.type) {
      selectedGradeType.value = scoreTypeInfo.value.type
    } else if (gradeTypeDict.value.length > 0) {
      selectedGradeType.value = gradeTypeDict.value[0].dictValue
    }
  } catch (error) {
    console.error('加载成绩评定类型字典失败', error)
  }
}

// 获取成绩类型信息
const fetchScoreTypeInfo = async () => {
  if (!jxrwid.value) {
    toast.show({ msg: '未获取到教学任务信息', iconName: 'error' })
    return
  }

  try {
    // 如果没有日常成绩ID，先使用默认值
    const rccjdjid = dailyScoreId.value

    const params: DailyScoreTypeInfoQuery = {
      jxrwid: jxrwid.value,
      rccjdjid,
      type: 'dailyScore',
    }

    const res = await getDailyScoreTypeInfo(params)
    scoreTypeInfo.value = res

    // 如果返回了成绩评定类型，则更新selectedGradeType
    if (res.type) {
      selectedGradeType.value = res.type
    }
  } catch (error) {
    console.error('获取日常成绩类型信息失败', error)
    toast.show({ msg: '获取日常成绩类型信息失败', iconName: 'error' })
  }
}

// 获取学生成绩列表
const fetchStudentScoreList = async () => {
  loading.value = true

  if (!jxrwid.value) {
    toast.show({ msg: '未获取到教学任务信息', iconName: 'error' })
    loading.value = false
    return
  }

  try {
    // 如果有日常成绩ID，使用getDailyScoreList接口加载数据
    if (dailyScoreId.value) {
      const params: DailyScoreQuery = {
        page: 1,
        pageSize: 2000,
        jxrwid: parseInt(jxrwid.value),
        rccjdjid: parseInt(dailyScoreId.value),
      }

      const res = await getDailyScoreList(params)

      // 设置课程信息
      if (res.items.length > 0) {
        updateCourseInfo(res.items[0])
      } else {
        // 如果API没有返回数据，则使用store中的数据
        updateCourseInfo()
      }

      // 转换学生数据格式
      students.value = mapDailyStudentData(res.items)
    } else {
      // 如果没有日常成绩ID，继续使用总评成绩接口
      const params = {
        page: 1,
        pageSize: 1000,
        jxrwid: jxrwid.value,
      }

      const res = await getTeacherTotalScoreList(params)

      // 设置课程信息
      if (res.items.length > 0) {
        updateCourseInfo(res.items[0])
      } else {
        // 如果API没有返回数据，则使用store中的数据
        updateCourseInfo()
      }

      // 转换学生数据格式
      students.value = mapStudentData(res.items)
    }

    // 更新统计数据
    updateStats()
  } catch (error) {
    console.error('获取成绩列表失败', error)
    toast.show({ msg: '获取成绩列表失败', iconName: 'error' })
    // 如果API调用失败，仍然使用store中的数据更新课程信息
    updateCourseInfo()
  } finally {
    loading.value = false
  }
}

// 更新课程信息
const updateCourseInfo = (apiData?: any) => {
  if (apiData) {
    // 使用API返回的数据
    courseInfo.value = {
      courseName: apiData.kcmc,
      className: apiData.ssbjmc,
      xn: apiData.xn,
      xq: apiData.xq,
      kcxf: currentTask.value.creditHour || apiData.kcxf,
      kcss: currentTask.value.courseTotalHours || '0',
    }
  } else if (currentTask.value) {
    // 使用store中的数据
    courseInfo.value = {
      courseName: currentTask.value.courseName || '',
      className: currentTask.value.className || '',
      xn: currentTask.value.studyYear || '',
      xq: currentTask.value.studyTerm?.toString() || '',
      kcxf: currentTask.value.creditHour || 0,
      kcss: currentTask.value.courseTotalHours || '0',
    }
  }
}

// 将API返回的日常成绩数据映射为组件所需格式
const mapDailyStudentData = (items: DailyScoreItem[]) => {
  return items.map((item) => ({
    id: item.id,
    name: item.xm,
    studentId: item.xsxh,
    score: item.cj.toString(),
    hasScore: true, // 将所有学生视为已录入成绩，包括0分
    zwh: item.zwh,
    originalData: {
      ...item,
      // 确保xq是字符串类型
      xq: item.xq.toString(),
    },
    gpa: item.cjjd || '',
    credit: item.kcxf?.toString() || '',
    isPassed: item.tgbz === 1,
  }))
}

// 将API返回的学生数据映射为组件所需格式
const mapStudentData = (items: TeacherTotalScoreItem[]) => {
  return items.map((item) => ({
    id: item.id,
    name: item.xm,
    studentId: item.xsxh,
    score: item.cj.toString(),
    hasScore: true, // 将所有学生视为已录入成绩，包括0分
    zwh: item.zwh,
    originalData: item,
    gpa: item.cjjd || '',
    credit: item.kcxf?.toString() || '',
    isPassed: item.tgbz === 1,
  }))
}

// ======= 成绩处理相关函数 =======

// 更新学生分数
const updateStudentScore = async (index: number, value: string) => {
  // 如果成绩已提交，则阻止更新
  if (scoreTypeInfo.value?.submit) {
    toast.show({
      msg: '成绩已提交，不能修改。请先将状态改为"未提交"后再修改成绩。',
      iconName: 'warning',
      duration: 2000,
    })
    return
  }

  if (index >= 0 && index < students.value.length) {
    // 保存原始分数，用于检测变化
    const originalScore = students.value[index].score

    // 更新本地状态
    students.value[index].score = value
    students.value[index].hasScore = true // 始终将hasScore设为true，无论分数是多少

    // 如果分数有变化，则调用API更新成绩
    if (originalScore !== value) {
      await updateStudentScoreToServer(index)
    }

    // 更新统计数据
    updateStats()
  }
}

// 调用API更新成绩到服务器
const updateStudentScoreToServer = async (index: number) => {
  if (!jxrwid.value) {
    toast.show({ msg: '未获取到教学任务信息', iconName: 'error' })
    return
  }

  // 保存原始成绩，用于失败时恢复
  const student = students.value[index]
  const originalScore = student.originalData.cj.toString()
  const newScore = student.score

  try {
    // 如果有日常成绩ID，使用日常成绩更新API
    if (dailyScoreId.value) {
      const params = {
        jxrwid: jxrwid.value,
        rccjdjid: parseInt(dailyScoreId.value),
        rows: [{ id: student.id, cj: newScore }],
      }

      toast.loading('正在更新成绩...')
      const data = await updateDailyScore(params)
      toast.close()

      if (data && data.length > 0) {
        handleScoreUpdateSuccess(index, data[0])
      } else {
        handleScoreUpdateFailure(index, originalScore)
      }
    } else {
      // 否则使用总评成绩更新API
      const params = {
        jxrwid: jxrwid.value,
        rows: [{ id: student.id, cj: newScore }],
      }

      toast.loading('正在更新成绩...')
      const data = await updateTeachingTaskTotalScore(params)
      toast.close()

      if (data && data.length > 0) {
        handleScoreUpdateSuccess(index, data[0])
      } else {
        handleScoreUpdateFailure(index, originalScore)
      }
    }
  } catch (error) {
    handleScoreUpdateError(index, originalScore, error)
  }
}

// 处理成绩更新成功
const handleScoreUpdateSuccess = (index: number, updatedStudent: any) => {
  // 保留原始对象的引用，只更新属性
  Object.assign(students.value[index].originalData, updatedStudent)

  // 更新显示数据
  students.value[index].score = updatedStudent.cj.toString()
  students.value[index].hasScore = true // 始终为true，不考虑分数值

  // 更新绩点、学分和通过状态
  students.value[index].gpa = updatedStudent.cjjd + '' || ''
  // 确保转换为字符串
  if (updatedStudent.kcxf !== undefined) {
    students.value[index].credit = updatedStudent.kcxf.toString()
  } else {
    students.value[index].credit = ''
  }
  students.value[index].isPassed = updatedStudent.tgbz === 1

  toast.show({ msg: '成绩更新成功', iconName: 'success' })
}

// 处理成绩更新失败
const handleScoreUpdateFailure = (index: number, originalScore: string) => {
  // 服务器返回空数据：恢复原始成绩
  students.value[index].score = originalScore
  students.value[index].hasScore = true // 始终为true，不考虑分数值

  toast.show({ msg: '成绩更新失败', iconName: 'error' })

  // 更新统计数据
  updateStats()
}

// 处理成绩更新错误
const handleScoreUpdateError = (index: number, originalScore: string, error: any) => {
  toast.close()
  console.error('更新成绩失败', error)

  // 发生错误：恢复原始成绩
  students.value[index].score = originalScore
  students.value[index].hasScore = true // 始终为true，不考虑分数值

  // 更新统计数据
  updateStats()
}

// 更新学生分数状态
const updateStudentHasScore = (index: number, value: boolean) => {
  // 如果成绩已提交，则阻止更新
  if (scoreTypeInfo.value?.submit) {
    toast.show({
      msg: '成绩已提交，不能修改。请先将状态改为"未提交"后再修改成绩。',
      iconName: 'warning',
      duration: 2000,
    })
    return
  }

  if (index >= 0 && index < students.value.length) {
    students.value[index].hasScore = value
  }
}

// 更新统计数据
const updateStats = () => {
  const passCount = students.value.filter((s) => parseFloat(s.score) >= 60).length
  const zeroCount = students.value.filter((s) => parseFloat(s.score) === 0).length
  const failCount = students.value.filter(
    (s) => parseFloat(s.score) > 0 && parseFloat(s.score) < 60,
  ).length

  statsData.value = { passCount, failCount, zeroCount }
}

// ======= 成绩评定类型相关函数 =======

// 处理成绩评定类型变化
const handleGradeTypeChange = (event: any) => {
  if (event && event.value) {
    tempSelectedGradeType.value = event.value
  }
}

// 确认成绩评定类型
const confirmGradeType = async () => {
  // 如果有临时选择的值，则更新selectedGradeType
  if (tempSelectedGradeType.value) {
    selectedGradeType.value = tempSelectedGradeType.value
  }

  // 关闭弹窗
  showGradeTypePopup.value = false

  try {
    // 调用API设置成绩评定类型
    if (dailyScoreId.value) {
      // 如果有日常成绩ID，使用日常成绩评定类型设置API
      const params: DailyScoreTypeSetRequest = {
        jxrwid: jxrwid.value,
        rccjdjid: parseInt(dailyScoreId.value),
        cjptlx: selectedGradeType.value,
      }

      await setDailyScoreType(params)
    } else {
      // 否则使用总评成绩评定类型设置API
      const params: SetScoreTypeRequest = {
        jxrwid: jxrwid.value,
        cjptlx: selectedGradeType.value,
      }

      await setScoreType(params)
    }

    // 设置成功后，重新获取成绩类型信息
    await fetchScoreTypeInfo()

    uni.showToast({
      title: `已设置为${currentGradeType.value}`,
      icon: 'success',
    })
  } catch (error) {
    console.error('设置成绩评定类型失败', error)
  }
}

// ======= 成绩操作相关函数 =======

// 处理排序
const handleSort = () => {
  // 按照座位号排序
  students.value.sort((a, b) => a.zwh - b.zwh)
  toast.show({ msg: '已按座位号排序' })
}

// 处理筛选
const handleFilter = () => {
  // 改为筛选0分的学生
  const hasZeroScore = students.value.some((s) => parseFloat(s.score) === 0)

  if (hasZeroScore) {
    students.value = students.value.filter((s) => parseFloat(s.score) === 0)
    toast.show({ msg: '已筛选0分的学生' })
  } else {
    // 恢复所有学生
    fetchStudentScoreList()
    toast.show({ msg: '已恢复所有学生' })
  }
}

// 保存草稿
const handleSaveDraft = () => {
  toast.show({ msg: '已保存草稿', iconName: 'success' })
}

// 处理提交
const handleSubmit = () => {
  // 直接调用提交函数，不再检查是否所有学生都已录入成绩
  handleSubmitScore()
}

// 提交成绩
const handleSubmitScore = async () => {
  if (!jxrwid.value) {
    toast.show({ msg: '未获取到教学任务信息', iconName: 'error' })
    return
  }

  // 确认提交
  uni.showModal({
    title: '确认提交',
    content: '确认要提交本课程的总评成绩吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const params: SubmitTotalScoreRequest = {
            jxrwid: jxrwid.value,
          }

          toast.loading('正在提交总评成绩...')
          const res = await submitTotalScore(params)
          toast.close()

          // 显示提交结果
          showResultDialog({
            title: '提交结果',
            message: formatResultMessage(res.frontMsg),
            success: res.code === 1,
            showErrorIcon: false,
            centered: false,
            showFailList: true,
          })

          // 解析不通过学生名单
          parseFailStudents(res.frontMsg)

          // 重新获取数据
          fetchStudentScoreList()
        } catch (error) {
          toast.close()
          console.error('提交总评成绩失败', error)
        }
      }
    },
  })
}

// 生成总评成绩
const handleGenerateTotalScore = async () => {
  if (!jxrwid.value) {
    toast.show({ msg: '未获取到教学任务信息', iconName: 'error' })
    return
  }

  // 添加确认对话框
  uni.showModal({
    title: '确认生成总评',
    content:
      '确认要根据日常成绩生成总评成绩吗？（请提前配置好总评成绩比例）\n已登记的成绩将会被覆盖，请谨慎操作！',
    success: async (res) => {
      if (res.confirm) {
        try {
          const params: GenerateTotalScoreRequest = {
            jxrwid: jxrwid.value,
          }

          toast.loading('正在生成总评成绩...')
          const res = await generateTotalScore(params)
          toast.close()

          // 重新获取数据
          fetchStudentScoreList()
        } catch (error) {
          toast.close()
          console.error('生成总评成绩失败', error)
        }
      }
    },
  })
}

// 教学平台同步
const handleSyncFromPlatform = () => {
  // 显示确认对话框
  uni.showModal({
    title: '确认同步',
    content:
      '确认要从教学平台拉取总评成绩吗？（需要本课程已关联教学平台）\n已登记的成绩将会被覆盖，请谨慎操作！',
    success: async (res) => {
      if (res.confirm) {
        await syncScoreFromPlatform()
      }
    },
  })
}

// 从教学平台同步成绩
const syncScoreFromPlatform = async () => {
  try {
    if (!jxrwid.value) {
      toast.show({ msg: '未获取到教学任务信息', iconName: 'error' })
      return
    }

    const params: TeachingTaskTotalScoreSyncRequest = {
      jxrwid: jxrwid.value,
    }

    toast.loading('正在同步总评成绩...')
    const res = await syncTeachingTaskTotalScore(params)
    toast.close()

    // 显示同步结果
    showResultDialog({
      title: '同步结果',
      message: res.frontMsg,
      success: true,
      showErrorIcon: false,
      centered: true,
      showFailList: false,
    })

    // 同步成功后重新获取数据
    if (res.code === 1) {
      fetchStudentScoreList()
    }
  } catch (error) {
    toast.close()
    console.error('同步总评成绩失败', error)

    // 显示错误信息
    showResultDialog({
      title: '同步失败',
      message: error.msg || '同步失败',
      success: false,
      showErrorIcon: true,
      centered: true,
      showFailList: false,
    })
  }
}

// ======= 辅助函数 =======

// 显示结果对话框
const showResultDialog = (options: {
  title: string
  message: string
  success: boolean
  showErrorIcon: boolean
  centered: boolean
  showFailList: boolean
}) => {
  Object.assign(resultDialog, {
    ...options,
    show: true,
  })
}

// 解析不通过学生名单
const parseFailStudents = (message: string) => {
  failStudents.value = []

  // 检查消息中是否包含不通过学生信息
  if (message && message.includes('以下为不通过学生：')) {
    const match = message.match(/以下为不通过学生：(.+)/)
    if (match && match[1]) {
      // 分割学生名单
      failStudents.value = match[1].split('，').map((item) => item.trim())
    }
  }
}

// 格式化结果消息
const formatResultMessage = (message: string) => {
  if (!message) return ''

  // 提取主要信息，去除不通过学生名单
  if (message.includes('以下为不通过学生：')) {
    return message.split('以下为不通过学生：')[0].trim()
  }

  return message
}

// 页面加载时初始化数据
onMounted(() => {
  // 如果有传入的日常成绩ID，则优先使用
  if (dailyScoreId.value) {
    console.log('日常成绩ID:', dailyScoreId.value)
    console.log('教学任务ID:', jxrwid.value)
    // 后续可以根据这些参数加载对应的日常成绩详情
  }

  initPageData()
})
</script>
