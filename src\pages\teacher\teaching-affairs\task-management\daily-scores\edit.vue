<route lang="json5">
{
  style: {
    navigationBarTitleText: '日常成绩编辑',
  },
}
</route>
<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useTeachingTaskStore } from '@/store/teachingTask'
import { onLoad } from '@dcloudio/uni-app'
import { getDailyScoreMarkEdit, updateDailyScoreMark, addDailyScoreMark } from '@/service/teacher'
import type {
  DailyScoreMarkEditQuery,
  UpdateDailyScoreMarkRequest,
  AddDailyScoreMarkRequest,
} from '@/types/teacher'
import { loadDictData, getDictLabel, getDictOptions } from '@/utils/dict'
import type { DictData } from '@/types/system'

// 获取当前教学任务信息
const teachingTaskStore = useTeachingTaskStore()
const currentTask = computed(() => teachingTaskStore.currentTask)

// 课程信息
const courseName = computed(() => currentTask.value?.courseName || '未知课程')
const className = computed(() => currentTask.value?.className || '未知班级')

// 页面参数
const pageParams = ref<{
  id?: string | number
  jxrwid?: string | number
}>({})

// 页面标题
const pageTitle = computed(() => (pageParams.value.id ? '编辑日常成绩' : '新增日常成绩'))

// 表单数据
const formData = ref({
  scoreType: '', // 成绩类型
  scoreNumber: '', // 成绩编号
  registerDate: '', // 登记日期
  allowStudentQuery: true, // 开放学生查询
  description: '', // 说明
})

// 字典数据
const dictData = ref<{
  DM_RCCJLX: DictData[]
}>({
  DM_RCCJLX: [],
})

// 成绩类型选项
const scoreTypeOptions = computed(() => getDictOptions(dictData.value.DM_RCCJLX))

// 成绩类型标签
const getScoreTypeLabel = (value: string) => getDictLabel(dictData.value.DM_RCCJLX, value)

// 加载字典数据
const loadDictionaries = async () => {
  const dicts = await loadDictData(['DM_RCCJLX'])
  dictData.value.DM_RCCJLX = dicts.DM_RCCJLX
}

// 加载成绩数据
const loadScoreData = async (id: string | number) => {
  try {
    // 确保教学任务ID存在
    const jxrwid = pageParams.value.jxrwid || currentTask.value?.id

    if (!jxrwid) {
      uni.showToast({
        title: '缺少教学任务ID',
        icon: 'none',
      })
      return
    }

    // 构建请求参数
    const params: DailyScoreMarkEditQuery = {
      id: Number(id),
      jxrwid: Number(jxrwid),
    }

    // 获取成绩详情
    const res = await getDailyScoreMarkEdit(params)

    // 更新表单数据
    formData.value = {
      scoreType: res.rccjlxdm, // 成绩类型代码
      scoreNumber: String(res.rccjdjbh), // 成绩编号
      registerDate: res.rccjkssj, // 登记日期
      allowStudentQuery: res.rccjcxzt === 1, // 是否开放查询 (1-开放查询)
      description: res.rccjsm || '', // 说明
    }
  } catch (error) {
    console.error('获取成绩详情失败', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none',
    })
  }
}

// 表单验证
const validateForm = (): boolean => {
  if (!formData.value.scoreType) {
    uni.showToast({
      title: '请选择成绩类型',
      icon: 'none',
    })
    return false
  }

  // 确保教学任务ID存在
  if (!pageParams.value.jxrwid && !currentTask.value?.id) {
    uni.showToast({
      title: '缺少教学任务ID',
      icon: 'none',
    })
    return false
  }

  return true
}

// 保存表单
const saveForm = async () => {
  // 表单验证
  if (!validateForm()) return

  try {
    // 获取教学任务ID
    const jxrwid = Number(pageParams.value.jxrwid || currentTask.value?.id)

    // 构建基础参数
    const baseParams = {
      jxrwid,
      rccjlxdm: formData.value.scoreType,
      rccjdjbh: Number(formData.value.scoreNumber),
      rccjkssj: formData.value.registerDate,
      rccjcxzt: formData.value.allowStudentQuery ? 1 : 0,
      rccjsm: formData.value.description || '',
    }

    // 根据是否有ID判断是新增还是编辑
    const isEdit = !!pageParams.value.id
    let res

    if (isEdit) {
      // 编辑模式
      const updateParams: UpdateDailyScoreMarkRequest = {
        ...baseParams,
        id: Number(pageParams.value.id),
      }
      res = await updateDailyScoreMark(updateParams)
    } else {
      // 新增模式
      const addParams: AddDailyScoreMarkRequest = baseParams
      res = await addDailyScoreMark(addParams)
    }

    // 成功提示
    const actionText = isEdit ? '更新' : '创建'
    uni.showToast({
      title: `${actionText}成功`,
      icon: 'success',
    })

    // 返回列表页面
    setTimeout(() => {
      uni.redirectTo({
        url: '/pages/teacher/teaching-affairs/task-management/daily-scores/index',
      })
    }, 1500)
  } catch (error) {
    console.error('保存数据失败', error)
  }
}

// 取消编辑
const cancelEdit = () => {
  uni.navigateBack()
}

// 页面初始化
onLoad((options) => {
  // 加载字典数据
  loadDictionaries()

  // 获取页面参数
  if (options.id) {
    pageParams.value.id = options.id
  }

  if (options.jxrwid) {
    pageParams.value.jxrwid = options.jxrwid
  } else {
    pageParams.value.jxrwid = currentTask.value?.id
  }

  // 如果是编辑模式，加载成绩数据
  if (pageParams.value.id) {
    loadScoreData(pageParams.value.id)
  }

  // 设置页面标题
  uni.setNavigationBarTitle({
    title: pageTitle.value,
  })
})
</script>

<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 课程信息卡片 -->
    <view class="p-4">
      <view class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
        <view class="flex items-center justify-between">
          <view>
            <view class="text-lg font-semibold">{{ courseName }}</view>
            <view class="text-sm text-white/80">{{ className }}</view>
          </view>
          <view class="text-right">
            <view class="text-lg font-semibold">{{ pageTitle }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 表单内容 -->
    <view class="bg-white rounded-lg px-4 py-2 mx-4 mb-4">
      <view class="form-container">
        <!-- 成绩信息部分 -->
        <view class="form-section">
          <view class="section-title">成绩信息</view>

          <!-- 成绩类型 -->
          <view class="form-item">
            <text class="form-label required">成绩类型</text>
            <view class="form-content">
              <picker
                :range="scoreTypeOptions"
                range-key="label"
                @change="(e) => (formData.scoreType = scoreTypeOptions[e.detail.value].value)"
              >
                <view class="picker-display">
                  <text v-if="formData.scoreType">
                    {{ getScoreTypeLabel(formData.scoreType) }}
                  </text>
                  <text v-else class="placeholder">请选择成绩类型</text>
                  <wd-icon name="chevron-down" class="text-gray-500" size="16px" />
                </view>
              </picker>
            </view>
          </view>

          <!-- 成绩编号 -->
          <view class="form-item">
            <text class="form-label required">成绩编号</text>
            <view class="form-content">
              <input
                class="form-input"
                type="text"
                v-model="formData.scoreNumber"
                placeholder="请输入成绩编号"
              />
            </view>
          </view>

          <!-- 登记日期 -->
          <view class="form-item">
            <text class="form-label required">登记日期</text>
            <view class="form-content">
              <picker
                mode="date"
                :value="formData.registerDate"
                @change="(e) => (formData.registerDate = e.detail.value)"
              >
                <view class="picker-display">
                  <text>{{ formData.registerDate || '请选择日期' }}</text>
                  <wd-icon name="calendar" class="text-gray-500" size="16px" />
                </view>
              </picker>
            </view>
          </view>

          <!-- 开放学生查询 -->
          <view class="form-item">
            <view class="form-row-flex">
              <text class="form-label">开放学生查询</text>
              <view class="form-content flex justify-between items-center">
                <text class="text-sm text-gray-600">
                  {{ formData.allowStudentQuery ? '是' : '否' }}
                </text>
                <wd-switch v-model="formData.allowStudentQuery" size="20px" />
              </view>
            </view>
          </view>

          <!-- 说明 -->
          <view class="form-item">
            <text class="form-label">说明</text>
            <view class="form-content">
              <textarea
                class="form-textarea"
                v-model="formData.description"
                placeholder="请输入说明信息"
              />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="fixed-bottom-buttons pb-4">
      <view class="button-container safe-area-inset-bottom">
        <button class="btn-default" @click="cancelEdit">取消</button>
        <button class="btn-primary" @click="saveForm">{{ pageParams.id ? '更新' : '保存' }}</button>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
.form-container {
  width: 100%;
}

.form-section {
  box-sizing: border-box;
}

.section-title {
  padding-bottom: 16rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  border-bottom: 1px solid #f0f0f0;
}

.form-item {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 24rpx;
}

.form-row-flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.form-label {
  display: block;
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333333;

  &.required::before {
    margin-right: 4rpx;
    color: #f5222d;
    content: '*';
  }
}

.form-content {
  box-sizing: border-box;
  width: 100%;
}

.form-textarea {
  box-sizing: border-box;
  width: 100%;
  height: 180rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}

.form-input {
  box-sizing: border-box;
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}

.picker-display {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;

  .placeholder {
    color: #999999;
  }
}
/* 底部按钮样式 */
.fixed-bottom-buttons {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 100;
  background-color: #ffffff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.button-container {
  display: flex;
  gap: 32rpx;
  justify-content: center;
  padding: 24rpx 32rpx 40rpx;
}

button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45%;
  height: 80rpx;
  padding: 0 12px;
  font-size: 28rpx;
  border-radius: 6px;
  transition: opacity 0.3s;

  &:active {
    opacity: 0.85;
  }
}

.btn-primary {
  color: #fff;
  background-color: #007aff;
  border: 1px solid #007aff;
}

.btn-default {
  color: #666666;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
}

.safe-area-inset-bottom {
  padding-bottom: constant(safe-area-inset-bottom); /* iOS 11.0 */
  padding-bottom: env(safe-area-inset-bottom); /* iOS 11.2+ */
}
</style>
