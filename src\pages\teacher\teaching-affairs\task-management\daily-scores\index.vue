<route lang="json5">
{
  style: {
    navigationBarTitleText: '日常成绩',
  },
}
</route>
<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import * as echarts from 'echarts'
import { useTeachingTaskStore } from '@/store/teachingTask'
import { getDailyScoreMarkList, deleteDailyScoreMark } from '@/service/teacher'
import Pagination from '@/components/Pagination/index.vue'
import { loadDictData, getDictOptions } from '@/utils/dict'
import type {
  DailyScoreMarkListQuery,
  DailyScoreMarkItem,
  DailyScoreMarkListResponse,
  DeleteDailyScoreMarkRequest,
} from '@/types/teacher'
import type { DictData } from '@/types/system'

// 获取当前教学任务信息
const teachingTaskStore = useTeachingTaskStore()
const currentTask = computed(() => teachingTaskStore.currentTask)

// 分页参数
const pagination = ref({
  page: 1,
  pageSize: 100,
  total: 0,
})

// 查询参数
const queryParams = ref({
  teachingTaskId: computed(() => currentTask.value?.id || ''),
  page: computed(() => pagination.value.page),
  pageSize: computed(() => pagination.value.pageSize),
})

// 成绩列表数据
const scoreItems = ref<DailyScoreMarkItem[]>([])
// 存储所有原始数据，用于本地筛选
const allScoreItems = ref<DailyScoreMarkItem[]>([])
const loading = ref(false)

// 课程信息
const courseName = computed(() => currentTask.value?.courseName || '未知课程')
const className = computed(() => currentTask.value?.className || '未知班级')
// 由于 currentTask 中没有 studentCount 属性，暂时使用固定值
const studentCount = ref(32)

// 字典数据
const dictData = ref<Record<string, DictData[]>>({})
const dictLoading = ref(true)

// 过滤器选项
const filterOptions = ref<{ id: number; name: string; active: boolean; type: string }[]>([
  { id: 1, name: '全部', active: true, type: '' },
])

// 当前选中的过滤类型
const currentFilter = ref('')

// 存储实际存在的类型
const existingTypes = ref<Set<string>>(new Set())

// 加载字典数据
const loadDicts = async () => {
  try {
    dictData.value = await loadDictData(['DM_RCCJLX'])
  } catch (error) {
    console.error('加载字典数据失败', error)
  } finally {
    dictLoading.value = false
  }
}

// 更新筛选选项
const updateFilterOptions = () => {
  // 始终保留"全部"选项
  filterOptions.value = [{ id: 1, name: '全部', active: true, type: '' }]

  // 如果没有数据，则不显示其他筛选选项
  if (existingTypes.value.size === 0) return

  // 从字典中筛选出实际存在的类型
  const dictOptions = getDictOptions(dictData.value.DM_RCCJLX)
    .filter((item) => existingTypes.value.has(item.value))
    .map((item, index) => ({
      id: index + 2, // id从2开始，因为"全部"选项id为1
      name: item.label,
      active: false,
      type: item.value,
    }))

  // 合并"全部"选项和字典选项
  filterOptions.value = [{ id: 1, name: '全部', active: true, type: '' }, ...dictOptions]
}

// 应用筛选条件到本地数据
const applyFilter = () => {
  if (currentFilter.value) {
    // 根据选中的筛选条件过滤数据
    scoreItems.value = allScoreItems.value.filter((item) => item.rccjlxdm === currentFilter.value)
  } else {
    // 如果是"全部"选项，显示所有数据
    scoreItems.value = [...allScoreItems.value]
  }
}

// 切换过滤器
const toggleFilter = (index: number) => {
  filterOptions.value.forEach((item, idx) => {
    item.active = idx === index
  })
  // 设置当前过滤类型
  currentFilter.value = filterOptions.value[index].type
  // 直接在本地应用筛选，不再重新请求接口
  applyFilter()
  // 重置页码为 1
  pagination.value.page = 1
}

// 获取日常评分标记列表
const fetchDailyScoreMarkList = async () => {
  if (!currentTask.value?.id) return

  loading.value = true
  try {
    const params: DailyScoreMarkListQuery = {
      jxrwid: currentTask.value.id,
      page: pagination.value.page,
      pageSize: pagination.value.pageSize,
    }

    const res = await getDailyScoreMarkList(params)

    // 存储所有原始数据，用于本地筛选
    allScoreItems.value = res.items || []

    // 收集实际存在的类型
    existingTypes.value.clear()
    if (allScoreItems.value.length > 0) {
      allScoreItems.value.forEach((item) => {
        if (item.rccjlxdm) {
          existingTypes.value.add(item.rccjlxdm)
        }
      })
    }

    // 更新筛选选项
    updateFilterOptions()

    // 应用当前的筛选条件
    applyFilter()

    pagination.value.total = res.total || 0

    // 更新统计数据
    updateStats()
  } catch (error) {
    console.error('获取日常评分标记列表失败', error)
  } finally {
    loading.value = false
  }
}

// 更新统计数据
const updateStats = () => {
  // 使用当前筛选后的数据更新统计
  if (scoreItems.value.length > 0) {
    stats.value = {
      total: pagination.value.total,
      completed: scoreItems.value.filter((item) => item.cjtjbz === 1).length,
    }
  } else {
    // 如果没有数据，重置统计
    stats.value = {
      total: 0,
      completed: 0,
    }
  }
}

// 页码变化
const handlePageChange = (page: number) => {
  pagination.value.page = page
  // 分页后重新请求是必要的，因为我们需要获取对应页的数据
  fetchDailyScoreMarkList()
}

// 跳转到成绩编辑页面
const goToEditPage = () => {
  uni.navigateTo({
    url: './edit',
  })
}

// 查看成绩详情
const viewScoreDetail = (id: string | number) => {
  uni.navigateTo({
    url: `./edit?id=${id}`,
  })
}

// 查看总评成绩详情
const goToScoreDetail = (id: string | number) => {
  if (!currentTask.value?.id) return
  uni.navigateTo({
    url: `./daily-score-detail?id=${id}&jxrwid=${currentTask.value.id}`,
  })
}

// 删除成绩记录
const handleDelete = (id: number) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这条成绩记录吗？删除后无法恢复。',
    success: async (res) => {
      if (res.confirm) {
        try {
          const params: DeleteDailyScoreMarkRequest = {
            ids: [id],
          }
          await deleteDailyScoreMark(params)
          uni.showToast({
            title: '删除成功',
            icon: 'success',
          })
          // 刷新列表
          fetchDailyScoreMarkList()
        } catch (error) {
          console.error('删除成绩记录失败', error)
        }
      }
    },
  })
}

// 初始化加载数据
onMounted(async () => {
  await loadDicts()
  fetchDailyScoreMarkList()
})

// 模拟成绩数据（临时保留，后续删除）
const mockScoreItems = ref([])

// 统计数据
const stats = ref({
  total: 0,
  completed: 0,
})

// 计算录入进度百分比
const getProgressPercentage = (completed: number, total: number) => {
  if (total === 0) return 0
  return Math.round((completed / total) * 100)
}

// 计算当前进度
const currentProgress = computed(() => {
  // 如果API返回的数据为空，则使用模拟数据计算进度
  if (!scoreItems.value.length) {
    return getProgressPercentage(
      mockScoreItems.value.filter((item) => item.completed === item.total).length,
      mockScoreItems.value.length,
    )
  }

  // 使用API返回的数据计算进度
  const completedItems = scoreItems.value.filter((item) => item.cjtjbz === 1).length
  const totalItems = scoreItems.value.length

  return getProgressPercentage(completedItems, totalItems)
})
</script>

<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 课程信息和统计卡片 -->
    <view class="p-4">
      <!-- 课程信息卡片 -->
      <view class="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
        <view class="flex items-center justify-between">
          <view>
            <view class="text-lg font-semibold">{{ courseName }}</view>
            <view class="text-sm text-white/80">{{ className }} · {{ studentCount }}名学生</view>
          </view>
          <view class="text-right">
            <view class="text-2xl font-bold">{{ currentProgress }}%</view>
            <view class="text-xs text-white/80">录入进度</view>
          </view>
        </view>
        <view class="mt-3">
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: currentProgress + '%' }"></view>
          </view>
        </view>
      </view>

      <!-- 统计卡片 -->
      <view class="mt-2 rounded-xl bg-gradient-to-r from-blue-50 to-purple-50 p-4 shadow">
        <view class="mb-3 text-gray-800 font-semibold">本课程成绩统计</view>
        <view class="grid grid-cols-2 gap-4">
          <view class="text-center">
            <view class="text-2xl font-bold text-blue-600">{{ stats.total }}</view>
            <view class="text-sm text-gray-600">成绩项目</view>
          </view>
          <view class="text-center">
            <view class="text-2xl font-bold text-green-600">{{ stats.completed }}</view>
            <view class="text-sm text-gray-600">已完成</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 过滤器 -->
    <view class="bg-white px-4 py-3 mb-2">
      <scroll-view scroll-x class="whitespace-nowrap">
        <view class="flex space-x-3">
          <view
            v-for="(filter, index) in filterOptions"
            :key="filter.id"
            :class="['filter-chip', filter.active ? 'active' : '']"
            @click="toggleFilter(index)"
          >
            {{ filter.name }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 成绩列表区域 -->
    <view class="px-4 pb-24">
      <!-- 加载状态 -->
      <view v-if="loading" class="flex justify-center items-center py-8">
        <view class="text-center">
          <wd-loading color="#4299e1" />
          <view class="mt-2 text-gray-600">加载中...</view>
        </view>
      </view>

      <!-- 空数据状态 -->
      <view
        v-else-if="!loading && scoreItems.length === 0"
        class="flex flex-col items-center justify-center py-12"
      >
        <wd-icon name="note" size="48px" color="#a0aec0" />
        <text class="mt-3 text-gray-500">暂无评分记录</text>
        <view
          class="mt-4 px-4 py-2 bg-blue-500 text-white rounded-lg text-sm"
          @click="fetchDailyScoreMarkList"
        >
          刷新数据
        </view>
      </view>

      <!-- 成绩列表 -->
      <template v-else>
        <view
          v-for="item in scoreItems"
          :key="item.id"
          class="mb-3 rounded-lg bg-white p-3 shadow transition-all"
        >
          <!-- 成绩项目头部信息 -->
          <view class="flex items-center justify-between">
            <view class="flex flex-1 items-center overflow-hidden">
              <view class="mr-2 rounded-md px-2 py-1 text-xs font-medium bg-blue-100 text-blue-600">
                {{ item.rccjlxmc || '未分类' }}
              </view>
              <text class="text-xs text-gray-500 mr-2">编号: {{ item.rccjdjbh }}</text>
              <text class="text-xs text-gray-500">第{{ item.rccjkszc || 0 }}周</text>
            </view>

            <!-- 状态标签 -->
            <view class="flex items-center">
              <view
                class="mr-2 px-2 py-1 rounded-md text-xs"
                :class="
                  item.cjtjbz === 1 ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'
                "
              >
                {{ item.cjtjbz === 1 ? '已提交' : '未提交' }}
              </view>
              <view
                class="px-2 py-1 rounded-md text-xs"
                :class="
                  item.rccjcxzt === 1 ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
                "
              >
                {{ item.rccjcxzt === 1 ? '可查看' : '不可查看' }}
              </view>
            </view>
          </view>

          <!-- 说明与日期 -->
          <view class="mt-2 flex items-center justify-between">
            <view class="flex-1 overflow-hidden">
              <view class="text-sm text-gray-800 font-medium truncate">
                {{ item.rccjsm || '无说明' }}
              </view>
            </view>
            <view class="text-xs text-gray-500 ml-3">
              {{ item.rccjkssj }}
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="mt-2 flex justify-end">
            <view
              class="rounded-lg bg-blue-50 px-3 py-1 text-xs text-blue-600 mr-2"
              @click="viewScoreDetail(item.id)"
            >
              编辑
            </view>
            <view
              class="rounded-lg bg-green-50 px-3 py-1 text-xs text-green-600 mr-2"
              @click="goToScoreDetail(item.id)"
            >
              详情
            </view>
            <view
              class="rounded-lg bg-red-50 px-3 py-1 text-xs text-red-600"
              @click="handleDelete(item.id)"
            >
              删除
            </view>
          </view>
        </view>
      </template>

      <!-- 分页组件 -->
      <Pagination
        v-if="!loading && pagination.total > 0"
        :total="pagination.total"
        :page="pagination.page"
        :pageSize="pagination.pageSize"
        @update:page="handlePageChange"
        class="mt-4"
      />
    </view>

    <!-- 浮动添加按钮 -->
    <view
      class="fixed right-5 bottom-20 flex h-14 w-14 items-center justify-center rounded-full bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg transition-all active:scale-95"
      @click="goToEditPage"
    >
      <wd-icon name="add" size="24px" color="#ffffff" />
    </view>
  </view>
</template>

<style scoped>
.filter-chip {
  display: inline-block;
  padding: 8px 16px;
  font-size: 14px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 20px;
  transition: all 0.2s ease;
}

.filter-chip.active {
  color: white;
  background: #4299e1;
  border-color: #4299e1;
}

.progress-bar {
  height: 8px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 10px;
}

.progress-fill {
  height: 100%;
  background: white;
  transition: width 0.3s ease;
}
</style>
