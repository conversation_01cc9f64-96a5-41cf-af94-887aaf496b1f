<route lang="json5">
{
  style: {
    navigationBarTitleText: '无需征订教材',
  },
}
</route>
<template>
  <FormWithApproval :id="currentMaterial?.id" code="kcwxjcxysq" @return="goBack">
    <!-- 基本信息表单内容 -->
    <template #form-content>
      <view class="bg-white rounded-xl p-4 shadow-sm mb-4">
        <!-- 页面标题 -->
        <view class="text-lg font-semibold mb-4 text-gray-800">
          <text>{{ pageTitle }}</text>
        </view>

        <!-- 选择任务 - 添加模式下显示 -->
        <view class="form-item" v-if="mode === 'add' && from !== 'selection'">
          <view class="form-label">
            <text class="text-red-500 mr-1">*</text>
            选择任务
          </view>
          <view
            class="form-select rounded-lg p-3 flex justify-between items-center"
            @click="openTaskSelector"
          >
            <text v-if="currentMaterial" class="flex-1 text-ellipsis overflow-hidden pr-1">
              {{ currentTaskText }}
            </text>
            <text v-else class="text-[#bfbfbf] text-[14px]">点击选择教学任务</text>
            <wd-icon name="arrow-right" size="16px" class="text-[#bfbfbf] flex-shrink-0" />
          </view>
        </view>

        <!-- 课程信息卡片 -->
        <view class="bg-blue-50 rounded-lg p-3 mb-4">
          <view class="text-base font-semibold text-blue-800 mb-2 flex items-center">
            <wd-icon name="info-circle" color="#2563eb" size="16px" class="mr-1" />
            <text>教学任务信息</text>
          </view>
          <!-- 显示任务信息 -->
          <view class="flex flex-col gap-2 text-sm">
            <view class="text-blue-700">
              <text class="text-blue-500">学年学期：</text>
              {{ currentMaterial?.xn }}-{{ currentMaterial?.xq }}
            </view>
            <view class="text-blue-700">
              <text class="text-blue-500">课程名称：</text>
              {{ currentMaterial?.kcmc }}
            </view>
            <view class="text-blue-700">
              <text class="text-blue-500">班级：</text>
              {{ currentMaterial?.bjmc }}
            </view>
            <view class="text-blue-700">
              <text class="text-blue-500">授课教师：</text>
              {{ currentMaterial?.zdjsxm }}
            </view>
            <view class="text-blue-700">
              <text class="text-blue-500">总学时：</text>
              {{ currentMaterial?.xqzxs }}
            </view>
            <view class="text-blue-700">
              <text class="text-blue-500">学分：</text>
              {{ currentMaterial?.kcxf }}
            </view>
            <view class="text-blue-700">
              <text class="text-blue-500">周学时：</text>
              {{ currentMaterial?.zxs }}
            </view>
          </view>
        </view>

        <!-- 是否无需征订教材选择 -->
        <view class="form-item">
          <view class="form-label flex items-center">
            <wd-icon name="filter" color="#4b5563" size="16px" class="mr-1" />
            <text>是否无需征订教材</text>
            <text class="text-red-500 ml-1" v-if="!isDisabled">*</text>
          </view>
          <view class="flex gap-6 mt-2" v-if="!isDisabled">
            <view
              class="option-item flex items-center"
              :class="{ 'option-selected': reasonForm.isNoNeed === true }"
              @click="selectOption(true)"
            >
              <view class="radio-button mr-2">
                <view class="radio-inner" v-if="reasonForm.isNoNeed === true"></view>
              </view>
              <text>是</text>
            </view>
            <view
              class="option-item flex items-center"
              :class="{ 'option-selected': reasonForm.isNoNeed === false }"
              @click="selectOption(false)"
            >
              <view class="radio-button mr-2">
                <view class="radio-inner" v-if="reasonForm.isNoNeed === false"></view>
              </view>
              <text>否</text>
            </view>
          </view>
          <view v-else class="p-2 text-gray-700">
            {{ reasonForm.isNoNeed ? '是' : '否' }}
          </view>
        </view>

        <!-- 无需征订原因表单 - 无论是否都显示 -->
        <view class="form-item">
          <view class="form-label flex items-center">
            <wd-icon name="edit" color="#4b5563" size="16px" class="mr-1" />
            <text>{{ reasonForm.isNoNeed ? '无需征订教材理由' : '征订教材说明' }}</text>
            <text class="text-red-500 ml-1" v-if="!isDisabled">*</text>
          </view>
          <wd-textarea
            v-model="reasonForm.jcxysm"
            :placeholder="
              reasonForm.isNoNeed ? '请输入不需要征订教材的原因说明...' : '请输入征订教材的说明...'
            "
            class="form-textarea"
            :class="{ 'disabled-textarea': isDisabled }"
            :maxlength="200"
            show-count
            :disabled="isDisabled"
          />
        </view>

        <!-- 提示信息 -->
        <view
          v-if="!isDisabled && reasonForm.isNoNeed"
          class="text-xs text-gray-500 mb-6 px-3 py-2 bg-yellow-50 rounded-lg border border-yellow-200"
        >
          <view class="mb-1"><text>说明：</text></view>
          <view class="mb-1"><text>1) 申请无需征订教材会发起审批流。</text></view>
          <view><text>2) 审批通过后，已添加的选用教材都将全部清空。</text></view>
        </view>

        <!-- 审核状态提示 -->
        <view
          v-if="isDisabled"
          class="text-xs text-gray-500 mb-6 px-3 py-2 bg-green-50 rounded-lg border border-green-200"
        >
          <view class="flex items-center">
            <wd-icon name="check-circle" color="#16a34a" size="16px" class="mr-1" />
            <text class="text-green-700">该无需征订教材申请已审核通过，无法修改</text>
          </view>
        </view>
      </view>
    </template>

    <!-- 表单按钮 -->
    <template #form-buttons>
      <view class="flex space-x-4 mb-8">
        <button class="btn-cancel flex-1" @click="goBack">
          {{ isDisabled ? '返回' : '取消' }}
        </button>
        <button
          v-if="!isDisabled"
          class="btn-primary flex-1"
          @click="submitForm"
          :disabled="submitting"
        >
          提交
        </button>
      </view>
    </template>
  </FormWithApproval>

  <!-- 教学任务选择器弹窗 -->
  <wd-popup
    v-model="showTaskSelector"
    position="bottom"
    :close-on-click-modal="true"
    closable
    custom-style="height: 80vh; border-radius: 16px 16px 0 0;"
    @close="closeTaskSelector"
  >
    <view class="popup-header px-4 py-3 border-b border-gray-100">
      <view class="text-lg font-semibold">选择教学任务</view>
    </view>
    <view class="popup-content h-full">
      <TeachingTaskSelector @select="handleTaskSelect" :semester="currentSemester" />
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from 'vue'
import { useNoNeedMaterialStore } from '@/store/noNeedMaterial'
import { updateTeachingMaterialNoNeed } from '@/service/teachingMaterial'
import type {
  TeachingMaterialNoNeedItem,
  UpdateTeachingMaterialNoNeedParams,
} from '@/types/teachingMaterial'
import FormWithApproval from '@/components/FormWithApproval/index.vue'
import { onLoad } from '@dcloudio/uni-app'
import { useTeachingTaskStore } from '@/store/teachingTask'
import TeachingTaskSelector from '@/components/TeachingTaskSelector/index.vue'
import type { TeachingTaskItem } from '@/types/teachingTask'

// 获取无需征订教材任务数据
const noNeedMaterialStore = useNoNeedMaterialStore()
const currentMaterial = computed(() => noNeedMaterialStore.currentNoNeedMaterial)

// 格式化当前任务文本
const currentTaskText = computed(() => {
  if (currentMaterial.value) {
    return `${currentMaterial.value.xn}-${currentMaterial.value.xq} ${currentMaterial.value.kcmc} ${currentMaterial.value.bjmc} ${currentMaterial.value.zdjsxm}`
  }
  return '暂无任务信息'
})

// 获取URL参数
const from = ref('')
const isDisabled = ref(false)
const mode = ref('edit') // 默认为编辑模式，可以是 'add' 或 'edit'

// 表单数据
const reasonForm = ref({
  jcxysm: '',
  taskId: '', // 添加任务ID字段
  isNoNeed: true, // 默认为"是"无需征订教材
})

// 选择"是"或"否"选项
const selectOption = (value: boolean) => {
  reasonForm.value.isNoNeed = value
}

// 提交状态
const submitting = ref(false)

// 教学任务选择器状态
const showTaskSelector = ref(false)
const currentSemester = ref('')

// 页面加载时获取参数
onLoad((options) => {
  from.value = options.from || ''
  isDisabled.value = options.disabled === 'true'
  mode.value = options.mode || 'edit'

  // 获取传递过来的学年学期参数
  if (options.semester) {
    currentSemester.value = decodeURIComponent(options.semester)
  }
})

// 计算属性：页面标题
const pageTitle = computed(() => {
  if (isDisabled.value) return '查看无需征订教材'
  return mode.value === 'add' ? '添加无需征订教材' : '编辑无需征订教材'
})

// 初始化页面数据
onMounted(() => {
  // 检查是否有传入的无需征订教材任务数据
  if (currentMaterial.value) {
    // 从store中获取数据并初始化表单
    reasonForm.value.jcxysm = currentMaterial.value.jcxysm || ''
    reasonForm.value.taskId = currentMaterial.value.taskId?.toString() || ''
    // 如果有教材理由，则说明是"是"无需征订教材
    reasonForm.value.isNoNeed = Boolean(currentMaterial.value.jcxysm)

    // 如果是从selection页面跳转来的编辑模式（textbookUse为3的情况）
    if (from.value === 'selection' && mode.value === 'edit') {
      // 无需再次获取数据，因为已经在navigateToNoNeedMaterial函数中传递了申请理由
      // 只需确保表单数据正确设置
      if (currentMaterial.value.jcxysm) {
        reasonForm.value.isNoNeed = true
      }
    }
  } else if (mode.value === 'add') {
    // 如果是添加模式，初始化空表单
    reasonForm.value = {
      jcxysm: '',
      taskId: '',
      isNoNeed: true,
    }
  } else {
    // 如果是编辑模式但没有数据，返回上一页
    uni.showToast({
      title: '未找到无需征订教材任务数据',
      icon: 'none',
      duration: 2000,
    })
    setTimeout(() => {
      goBack()
    }, 2000)
  }

  // 如果是从selection页面跳转来的，检查是否有任务数据
  if (from.value === 'selection' && !currentMaterial.value) {
    uni.showToast({
      title: '未获取到课程信息，请返回重试',
      icon: 'none',
      duration: 2000,
    })
    setTimeout(() => {
      goBack()
    }, 2000)
  }
})

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 打开教学任务选择器
const openTaskSelector = () => {
  showTaskSelector.value = true
}

// 关闭教学任务选择器
const closeTaskSelector = () => {
  showTaskSelector.value = false
}

// 处理教学任务选择
const handleTaskSelect = (task: TeachingTaskItem) => {
  // 创建一个新的部分教材任务对象，只包含我们需要的字段
  // 使用 Partial 类型表示这是 TeachingMaterialNoNeedItem 的部分实现
  const materialTask: Partial<TeachingMaterialNoNeedItem> = {
    id: 0, // 新建的任务，ID为0
    taskId: task.id,
    xn: task.studyYear,
    xq: task.studyTerm,
    kcmc: task.courseName,
    kcdm: task.courseCode,
    bjmc: task.className,
    zdjsxm: task.leaderTeacherName,
    xqzxs: task.courseTotalHours,
    kcxf: task.creditHour,
    zxs: task.weekHours,
    jcxysm: '', // 无需征订教材理由
    // 添加视图显示需要的最小必要字段
    ssjh: null,
    ssxk: 0,
    sshb: null,
  }

  // 更新store中的数据，使用类型断言告诉TypeScript这是完整的类型
  noNeedMaterialStore.setCurrentNoNeedMaterial(materialTask as TeachingMaterialNoNeedItem)

  // 关闭选择器
  closeTaskSelector()
}

// 提交表单
const submitForm = async () => {
  // 如果是禁用状态，不允许提交
  if (isDisabled.value) return

  // 如果是添加模式，需要验证任务ID
  if (mode.value === 'add' && from.value !== 'selection' && !currentMaterial.value) {
    uni.showToast({
      title: '请选择教学任务',
      icon: 'none',
    })
    return
  }

  // 验证理由，无论选择"是"还是"否"都需要填写
  if (!reasonForm.value.jcxysm.trim()) {
    uni.showToast({
      title: reasonForm.value.isNoNeed ? '请输入无需征订理由' : '请输入征订教材说明',
      icon: 'none',
    })
    return
  }

  // 防止重复提交
  if (submitting.value) return
  submitting.value = true

  try {
    uni.showLoading({ title: '提交中...' })

    // 根据模式选择不同的提交逻辑
    if (mode.value === 'add') {
      // 添加无需征订教材
      await addNoNeedMaterial()
    } else {
      // 更新无需征订教材
      await updateNoNeedMaterial()
    }

    uni.hideLoading()
    uni.showToast({
      title: '提交成功',
      icon: 'success',
    })

    // 延迟返回上一页
    setTimeout(() => {
      goBack()
    }, 1500)
  } catch (error) {
    console.error('提交失败:', error)
    uni.hideLoading()
  } finally {
    submitting.value = false
  }
}

// 更新无需征订教材
const updateNoNeedMaterial = async () => {
  console.log(currentMaterial.value)

  // 构建基本参数
  const params: UpdateTeachingMaterialNoNeedParams = {
    status: reasonForm.value.isNoNeed ? '1' : '0', // 1表示"是"无需征订教材，0表示"否"
    reason: reasonForm.value.jcxysm, // 无论是否都提交理由
    task_id: currentMaterial.value!.id || 0,
  }

  // 如果是从selection页面跳转来的编辑模式（textbookUse为3的情况）
  if (from.value === 'selection' && mode.value === 'edit') {
    // 使用taskId而不是id
    params.task_id = currentMaterial.value!.taskId || 0
  }

  // 添加额外参数
  if (currentMaterial.value) {
    params.semesters = `${currentMaterial.value.xn}|${currentMaterial.value.xq}`
    params.className = currentMaterial.value.bjmc
    params.courseName = currentMaterial.value.kcmc
    params.courseTotalHours = currentMaterial.value.xqzxs
    params.creditHour = currentMaterial.value.kcxf
    params.weekHours = currentMaterial.value.zxs
  } else if (currentSemester.value) {
    // 如果没有 currentMaterial 但有 currentSemester，使用 currentSemester
    params.semesters = currentSemester.value
  }

  // 调用更新接口
  await updateTeachingMaterialNoNeed(params)
}

// 添加无需征订教材
const addNoNeedMaterial = async () => {
  // 构建基本参数
  const params: UpdateTeachingMaterialNoNeedParams = {
    status: reasonForm.value.isNoNeed ? '1' : '0', // 1表示"是"无需征订教材，0表示"否"
    reason: reasonForm.value.jcxysm, // 无论是否都提交理由
    task_id: currentMaterial.value?.taskId || 0,
  }

  // 如果是从selection页面跳转来的添加模式
  if (from.value === 'selection') {
    // 添加额外参数
    params.semesters = `${currentMaterial.value!.xn}|${currentMaterial.value!.xq}`
    params.className = currentMaterial.value!.bjmc
    params.courseName = currentMaterial.value!.kcmc
    params.courseTotalHours = currentMaterial.value!.xqzxs
    params.creditHour = currentMaterial.value!.kcxf
    params.weekHours = currentMaterial.value!.zxs
  } else if (currentSemester.value) {
    // 如果有学期参数，但不是从selection页面跳转来的，也需要添加学期参数
    params.semesters = currentSemester.value
  }

  // 调用更新接口
  await updateTeachingMaterialNoNeed(params)
}

// 获取审核状态图标
const getAuditStatusIcon = (status?: number) => {
  if (status === 2) {
    return 'check'
  } else if (status === 3) {
    return 'clock'
  } else {
    return 'help'
  }
}

// 获取审核状态文本
const getAuditStatusText = (status?: number) => {
  if (status === 2) {
    return '已通过'
  } else if (status === 3) {
    return '审批中'
  } else {
    return '未知状态'
  }
}

// 获取审核状态样式
const getAuditStatusStyle = (status?: number) => {
  if (status === 2) {
    return 'bg-green-100 text-green-600'
  } else if (status === 3) {
    return 'bg-orange-100 text-orange-600'
  } else {
    return 'bg-gray-100 text-gray-600'
  }
}
</script>

<style lang="scss">
.form-item {
  margin-bottom: 16px;
}

.form-label {
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.form-textarea {
  box-sizing: border-box;
  width: 100%;
  min-height: 120px;
  background-color: #ffffff;
  border: 1px solid #bfbfc3;
  border-radius: 8px;
}

.form-select {
  box-sizing: border-box;
  width: 100%;
  background-color: #ffffff;
  border: 1px solid #bfbfc3;
  border-radius: 8px;
}

.text-ellipsis {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 禁用状态的文本框样式
.disabled-textarea {
  color: #6b7280;
  cursor: not-allowed;
  background-color: #f3f4f6;
  border-color: #d1d5db;

  :deep(.wd-textarea) {
    background-color: #f3f4f6;
  }
  // 修改内部文本区域的背景色
  :deep(.wd-textarea__value) {
    background-color: #f3f4f6;
  }
}

// 按钮样式
button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
  padding: 0 16px;
  font-size: 15px;
  border-radius: 10px;
  transition: opacity 0.3s;

  &:active {
    opacity: 0.85;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

// 主要按钮样式 - 蓝色
.btn-primary {
  color: #fff;
  background-color: #007aff;
  border: 1px solid #007aff;
}

// 取消按钮样式 - 灰色
.btn-cancel {
  color: #4b5563;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
}

// 弹窗样式
.popup-header {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  border-bottom: 1px solid #f2f2f2;
}

.popup-content {
  height: calc(80vh - 60px);
}

// 单选按钮样式
.radio-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border: 1px solid #bfbfc3;
  border-radius: 50%;
}

.radio-inner {
  width: 10px;
  height: 10px;
  background-color: #007aff;
  border-radius: 50%;
}

.option-item {
  padding: 8px 12px;
  cursor: pointer;
  border-radius: 6px;
}

.option-selected {
  color: #007aff;

  .radio-button {
    border-color: #007aff;
  }
}
</style>
