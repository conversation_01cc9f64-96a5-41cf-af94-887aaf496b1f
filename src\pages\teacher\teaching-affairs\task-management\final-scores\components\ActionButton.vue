<template>
  <view
    :class="[
      'py-1 px-2 rounded-md text-xs font-medium text-center',
      bgColorClass,
      textColorClass,
      block ? '' : 'flex-1',
    ]"
    @click="$emit('click')"
  >
    <wd-icon v-if="iconName" :name="iconName" size="12px" class="mr-1" />
    <slot></slot>
  </view>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

const props = defineProps({
  iconName: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: 'blue',
    validator: (value: string) => ['blue', 'green', 'red', 'purple', 'gray'].includes(value),
  },
  block: {
    type: Boolean,
    default: false,
  },
})

defineEmits(['click'])

// 根据type计算背景色和文字色
const bgColorClass = computed(() => {
  const map: Record<string, string> = {
    blue: 'bg-blue-100',
    green: 'bg-green-100',
    red: 'bg-red-100',
    purple: 'bg-purple-100',
    gray: 'bg-gray-100',
  }
  return map[props.type] || 'bg-blue-100'
})

const textColorClass = computed(() => {
  const map: Record<string, string> = {
    blue: 'text-blue-800',
    green: 'text-green-800',
    red: 'text-red-800',
    purple: 'text-purple-800',
    gray: 'text-gray-700',
  }
  return map[props.type] || 'text-blue-800'
})
</script>
