<template>
  <wd-popup v-model="show" position="center">
    <view class="bg-white rounded-lg p-4 w-80">
      <view class="text-center text-lg font-medium mb-4 text-blue-600">
        {{ title }}
      </view>
      <view class="max-h-96 overflow-y-auto">
        <view v-if="success" class="flex items-center justify-center mb-4">
          <wd-icon name="check-circle-filled" size="40px" class="text-green-500" />
        </view>
        <view v-else-if="showErrorIcon" class="flex items-center justify-center mb-4">
          <wd-icon name="error-circle-filled" size="40px" class="text-red-500" />
        </view>
        <view :class="['text-sm text-gray-700 leading-relaxed', centered ? 'text-center' : '']">
          {{ message }}
        </view>
        <view v-if="showFailList && failStudents.length > 0" class="mt-4">
          <view class="text-sm font-medium text-red-600 mb-2">不通过学生名单：</view>
          <view class="bg-red-50 p-2 rounded-md max-h-40 overflow-y-auto">
            <view
              v-for="(student, index) in failStudents"
              :key="index"
              class="text-xs text-gray-700"
            >
              {{ student }}
            </view>
          </view>
        </view>
      </view>
      <view class="flex space-x-2 mt-4">
        <view
          class="flex-1 bg-blue-500 text-white py-2 rounded-md text-center"
          @click="handleClose"
        >
          确定
        </view>
      </view>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  message: {
    type: String,
    default: '',
  },
  success: {
    type: Boolean,
    default: false,
  },
  showErrorIcon: {
    type: Boolean,
    default: false,
  },
  centered: {
    type: Boolean,
    default: false,
  },
  showFailList: {
    type: Boolean,
    default: false,
  },
  failStudents: {
    type: Array as () => string[],
    default: () => [],
  },
})

const emit = defineEmits(['update:modelValue'])

// 弹窗显示状态
const show = ref(props.modelValue)

// 监听props变化
watch(
  () => props.modelValue,
  (newVal) => {
    show.value = newVal
  },
)

// 监听内部状态变化
watch(
  () => show.value,
  (newVal) => {
    emit('update:modelValue', newVal)
  },
)

// 处理关闭
const handleClose = () => {
  show.value = false
}
</script>
