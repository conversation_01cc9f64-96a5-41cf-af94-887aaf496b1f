<template>
  <view
    v-if="start && end"
    :class="[
      'mx-3 mb-2 border rounded-lg p-2 shadow-sm',
      isInTimeRange ? 'bg-blue-50 border-blue-200' : 'bg-orange-50 border-orange-200',
    ]"
  >
    <view class="flex items-center">
      <wd-icon
        :name="isInTimeRange ? 'notification' : 'warning'"
        size="16px"
        :class="isInTimeRange ? 'text-blue-500 mr-2' : 'text-orange-500 mr-2'"
      />
      <text
        :class="
          isInTimeRange
            ? 'text-sm text-blue-700 font-medium'
            : 'text-sm text-orange-700 font-medium'
        "
      >
        {{ isInTimeRange ? '当前允许提交成绩' : '当前不可提交成绩' }}
      </text>
    </view>
    <view
      :class="
        isInTimeRange ? 'text-xs text-blue-600 mt-1 pl-6' : 'text-xs text-orange-600 mt-1 pl-6'
      "
    >
      开放时间：{{ formatDateTime(start) }} 至 {{ formatDateTime(end) }}
    </view>
  </view>
</template>

<script lang="ts" setup>
defineProps({
  title: {
    type: String,
    default: '当前允许提交成绩',
  },
  start: {
    type: String,
    default: '',
  },
  end: {
    type: String,
    default: '',
  },
  isInTimeRange: {
    type: Boolean,
    default: true,
  },
})

// 格式化日期时间
const formatDateTime = (dateTimeStr: string): string => {
  if (!dateTimeStr) return '-'
  return dateTimeStr
}
</script>
