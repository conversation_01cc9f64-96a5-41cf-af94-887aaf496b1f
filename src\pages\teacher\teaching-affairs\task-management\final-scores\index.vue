<route lang="json5">
{
  style: {
    navigationBarTitleText: '总评成绩',
  },
}
</route>
<template>
  <view class="min-h-screen bg-gray-100 pt-2">
    <!-- 成绩提交时间横幅通知 -->
    <time-banner
      v-if="scoreTimeInfo.start && scoreTimeInfo.end && scoreTypeInfo?.submit !== 1"
      :start="scoreTimeInfo.start"
      :end="scoreTimeInfo.end"
      :is-in-time-range="isInSubmitTimeRange"
    />

    <!-- 课程信息卡片 -->
    <course-info-card
      :course-name="courseInfo.courseName"
      :class-name="courseInfo.className"
      :xn="courseInfo.xn"
      :xq="courseInfo.xq"
      :credit-hour="courseInfo.kcxf"
      :course-total-hours="courseInfo.kcss"
    />

    <!-- 成绩已提交通知 -->
    <submitted-notice v-if="scoreTypeInfo?.submit === 1" message="当前成绩已提交，不可修改成绩" />

    <!-- 成绩撤销申请按钮 - 仅在成绩已提交时显示 -->
    <view v-if="scoreTypeInfo?.submit === 1" class="mx-3 mt-2 bg-white rounded-lg p-3 shadow-sm">
      <view class="font-medium text-xs text-gray-800 mb-2">成绩操作</view>
      <view class="flex space-x-2">
        <action-button type="red" icon-name="rollback" @click="handleScoreRevokeRequest">
          成绩撤销申请
        </action-button>
      </view>
    </view>

    <!-- 成绩提交申请按钮 - 仅在不在提交时间范围内且成绩未提交时显示 -->
    <view
      v-if="!isInSubmitTimeRange && scoreTypeInfo?.submit !== 1"
      class="mx-3 mt-2 bg-white rounded-lg p-3 shadow-sm"
    >
      <view class="font-medium text-xs text-gray-800 mb-2">成绩操作</view>
      <view class="flex space-x-2">
        <action-button
          type="blue"
          icon-name="add-circle"
          @click="() => handleScoreRevokeRequest('cjtjsq')"
        >
          成绩提交申请
        </action-button>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="flex gap-3 mx-3 mt-2">
      <stat-card title="通过人数" :value="statsData.passCount" type="success" />
      <stat-card title="不通过人数" :value="statsData.failCount" type="error" />
      <stat-card title="0分人数" :value="statsData.zeroCount" type="warning" />
    </view>

    <!-- 快捷操作 -->
    <view
      v-if="!isDisabled && isInSubmitTimeRange"
      class="mx-3 mt-2 bg-white rounded-lg p-3 shadow-sm"
    >
      <view class="font-medium text-xs text-gray-800 mb-2">快捷操作</view>
      <view class="flex space-x-2">
        <action-button type="blue" icon-name="refresh" @click="handleGenerateTotalScore">
          总评生成
        </action-button>
        <action-button type="red" icon-name="check-bold" @click="handleSubmitScore">
          提交成绩
        </action-button>
      </view>

      <!-- 成绩评定类型设置 -->
      <action-button
        class="mt-2"
        type="purple"
        icon-name="setting"
        block
        @click="handleGradeTypeClick"
      >
        成绩评定类型：{{ currentGradeType }}
      </action-button>

      <!-- 教学平台同步按钮 -->
      <action-button
        class="mt-2"
        type="green"
        icon-name="cloud-download"
        block
        @click="handleSyncFromPlatform"
      >
        教学平台同步
      </action-button>
    </view>

    <!-- 学生成绩列表 -->
    <view class="p-3">
      <view class="flex justify-between mb-2">
        <view class="text-base font-medium text-gray-800">学生成绩</view>
        <!--        <view class="flex space-x-2">
          <view
            :class="[
              'text-xs flex items-center',
              isDisabled ? 'text-gray-400' : 'text-blue-500',
            ]"
            @click="handleSortClick"
          >
            <wd-icon name="sort" size="12px" class="mr-1" />
            排序
          </view>
          <view
            :class="[
              'text-xs flex items-center',
              isDisabled ? 'text-gray-400' : 'text-blue-500',
            ]"
            @click="handleFilterClick"
          >
            <wd-icon name="filter" size="12px" class="mr-1" />
            筛选
          </view>
        </view>-->
      </view>

      <!-- 加载状态 -->
      <view v-if="loading" class="flex justify-center items-center py-6">
        <wd-loading color="#3b82f6" size="24px" />
        <text class="ml-2 text-xs text-gray-500">加载中...</text>
      </view>

      <!-- 无数据提示 -->
      <view
        v-else-if="students.length === 0"
        class="flex flex-col items-center justify-center py-6"
      >
        <wd-icon name="info-circle" size="36px" class="text-gray-400" />
        <text class="mt-1 text-xs text-gray-500">暂无学生成绩数据</text>
      </view>

      <!-- 学生列表 -->
      <view v-else class="space-y-2">
        <student-list-item
          v-for="(student, index) in students"
          :key="student.id"
          :student-number="student.zwh || index + 1"
          :name="student.name"
          :student-id="student.studentId"
          :score="student.score"
          :has-score="student.hasScore"
          :score-type-info="scoreTypeInfo"
          :gpa="student.gpa"
          :credit="student.credit"
          :is-passed="student.isPassed"
          :show-credit="true"
          :disabled="isDisabled"
          @update:score="updateStudentScore(index, $event)"
          @update:has-score="updateStudentHasScore(index, $event)"
        />
      </view>
    </view>

    <!-- 成绩评定类型选择弹窗 -->
    <wd-popup v-model="showGradeTypePopup" position="bottom" safe-area-inset-bottom>
      <view class="p-4 bg-white">
        <view class="text-center text-base font-medium mb-4">请选择成绩评定类型</view>

        <!-- 使用PickerView组件 -->
        <wd-picker-view
          v-model="selectedGradeType"
          :columns="[gradeTypeOptions]"
          label-key="label"
          value-key="value"
          loading-color="#3b82f6"
          @change="handleGradeTypeChange"
        ></wd-picker-view>

        <!-- 操作按钮 -->
        <view class="flex space-x-2 mt-4">
          <view
            class="flex-1 bg-gray-100 text-gray-700 py-2 rounded-md text-center"
            @click="showGradeTypePopup = false"
          >
            取消
          </view>
          <view
            class="flex-1 bg-blue-500 text-white py-2 rounded-md text-center"
            @click="confirmGradeType"
          >
            确定
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- Toast提示 -->
    <wd-toast />

    <!-- 结果对话框 -->
    <result-dialog
      v-model="resultDialog.show"
      :title="resultDialog.title"
      :message="resultDialog.message"
      :success="resultDialog.success"
      :show-error-icon="resultDialog.showErrorIcon"
      :centered="resultDialog.centered"
      :show-fail-list="resultDialog.showFailList"
      :fail-students="failStudents"
    />
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, reactive } from 'vue'
import {
  getTeacherTotalScoreList,
  setScoreType,
  generateTotalScore,
  submitTotalScore,
} from '@/service/score'
import {
  getScoreTypeInfo,
  syncTeachingTaskTotalScore,
  updateTeachingTaskTotalScore,
} from '@/service/teacher'
import type {
  TeacherTotalScoreItem,
  SetScoreTypeRequest,
  GenerateTotalScoreRequest,
  SubmitTotalScoreRequest,
  SubmitTotalScoreResponse,
} from '@/types/score'
import type {
  ScoreTypeInfoQuery,
  ScoreTypeInfoResponse,
  TeachingTaskTotalScoreSyncRequest,
  TeachingTaskTotalScoreSyncResponse,
} from '@/types/teacher'
import { useTeachingTaskStore } from '@/store/teachingTask'
import { loadDictData, getDictOptions, getDictLabel } from '@/utils/dict'
import type { DictData } from '@/types/system'
import { useToast } from 'wot-design-uni'
import { parseDateTime } from '@/utils/index'

// 导入组件
import TimeBanner from './components/TimeBanner.vue'
import CourseInfoCard from './components/CourseInfoCard.vue'
import StatCard from './components/StatCard.vue'
import ActionButton from './components/ActionButton.vue'
import StudentListItem from '../components/StudentListItem.vue'
import ResultDialog from './components/ResultDialog.vue'
import SubmittedNotice from './components/SubmittedNotice.vue'

// 获取教学任务信息
const teachingTaskStore = useTeachingTaskStore()
const currentTask = computed(() => teachingTaskStore.currentTask)

// 添加toast提示
const toast = useToast()

// API参数和状态
const jxrwid = computed(() => currentTask.value?.id?.toString() || '')
const loading = ref(false)

// 统计数据
const statsData = ref({
  passCount: 0, // 通过人数
  failCount: 0, // 不通过人数
  zeroCount: 0, // 0分人数
})

// 成绩相关信息
const scoreTypeInfo = ref<ScoreTypeInfoResponse | null>(null)
const scoreTimeInfo = ref({
  start: '',
  end: '',
})

// 检查当前时间是否在允许提交的时间范围内
const isInSubmitTimeRange = computed(() => {
  if (!scoreTimeInfo.value.start || !scoreTimeInfo.value.end) {
    return true // 如果没有设置时间范围，则默认允许提交
  }

  const now = new Date()
  const startTime = parseDateTime(scoreTimeInfo.value.start)
  const endTime = parseDateTime(scoreTimeInfo.value.end)

  return now >= startTime && now <= endTime
})

// 是否禁用编辑和提交功能（成绩已提交时禁用，不在时间范围内但未提交时允许修改）
const isDisabled = computed(() => {
  return scoreTypeInfo.value?.submit === 1
})

// 课程信息
const courseInfo = ref({
  courseName: '',
  className: '',
  xn: '',
  xq: '',
  kcxf: 0,
  kcss: '',
})

// 成绩评定类型相关
const showGradeTypePopup = ref(false)
const selectedGradeType = ref('numeric')
const tempSelectedGradeType = ref('')
const gradeTypeDict = ref<DictData[]>([])
const gradeTypeOptions = computed(() => getDictOptions(gradeTypeDict.value))
const currentGradeType = computed(() => {
  return getDictLabel(gradeTypeDict.value, selectedGradeType.value) || '数字制'
})

// 学生数据
const students = ref<
  {
    id: number
    name: string
    studentId: string
    score: string
    hasScore: boolean
    zwh: number
    originalData: TeacherTotalScoreItem
    gpa: string
    credit: string
    isPassed: boolean
  }[]
>([])

// 结果对话框相关
const resultDialog = reactive({
  show: false,
  title: '',
  message: '',
  success: false,
  showErrorIcon: false,
  centered: false,
  showFailList: false,
})
const failStudents = ref<string[]>([])

// ======= 数据加载相关函数 =======

// 初始化页面数据
const initPageData = async () => {
  // 检查教学任务信息
  if (!currentTask.value) {
    toast.show({ msg: '未获取到教学任务信息', iconName: 'error' })
  }

  try {
    // 并行请求数据
    await Promise.all([
      fetchStudentScoreList(),
      fetchScoreTypeInfo().then(() => loadGradeTypeDict()),
    ])
  } catch (error) {
    console.error('初始化页面数据失败', error)
    toast.show({ msg: '加载数据失败', iconName: 'error' })
  }
}

// 加载成绩评定类型字典
const loadGradeTypeDict = async () => {
  try {
    const dictData = await loadDictData(['DM_CJPDLX'])
    gradeTypeDict.value = dictData.DM_CJPDLX || []

    // 设置当前选中的成绩评定类型
    if (scoreTypeInfo.value?.type) {
      selectedGradeType.value = scoreTypeInfo.value.type
    } else if (gradeTypeDict.value.length > 0) {
      selectedGradeType.value = gradeTypeDict.value[0].dictValue
    }
  } catch (error) {
    console.error('加载成绩评定类型字典失败', error)
  }
}

// 获取成绩类型信息
const fetchScoreTypeInfo = async () => {
  if (!jxrwid.value) {
    toast.show({ msg: '未获取到教学任务信息', iconName: 'error' })
    return
  }

  try {
    const params: ScoreTypeInfoQuery = {
      jxrwid: jxrwid.value,
      type: 'score',
    }

    const res = await getScoreTypeInfo(params)
    scoreTypeInfo.value = res

    // 设置成绩提交时间信息
    if (res.time) {
      scoreTimeInfo.value = {
        start: res.time.start || '',
        end: res.time.end || '',
      }
    }

    // 如果返回了成绩评定类型，则更新selectedGradeType
    if (res.type) {
      selectedGradeType.value = res.type
    }
  } catch (error) {
    console.error('获取成绩类型信息失败', error)
    toast.show({ msg: '获取成绩类型信息失败', iconName: 'error' })
  }
}

// 获取学生成绩列表
const fetchStudentScoreList = async () => {
  loading.value = true

  if (!jxrwid.value) {
    toast.show({ msg: '未获取到教学任务信息', iconName: 'error' })
    loading.value = false
    return
  }

  try {
    const params = {
      page: 1,
      pageSize: 1000,
      jxrwid: jxrwid.value,
    }

    const res = await getTeacherTotalScoreList(params)

    // 设置课程信息
    if (res.items.length > 0) {
      updateCourseInfo(res.items[0])
    } else {
      // 如果API没有返回数据，则使用store中的数据
      updateCourseInfo()
    }

    // 转换学生数据格式
    students.value = mapStudentData(res.items)

    // 更新统计数据
    updateStats()
  } catch (error) {
    console.error('获取成绩列表失败', error)
    toast.show({ msg: '获取成绩列表失败', iconName: 'error' })
    // 如果API调用失败，仍然使用store中的数据更新课程信息
    updateCourseInfo()
  } finally {
    loading.value = false
  }
}

// 更新课程信息
const updateCourseInfo = (apiData?: any) => {
  if (apiData) {
    // 使用API返回的数据
    courseInfo.value = {
      courseName: apiData.kcmc,
      className: apiData.ssbjmc,
      xn: apiData.xn,
      xq: apiData.xq,
      kcxf: currentTask.value.creditHour || 0,
      kcss: currentTask.value.courseTotalHours || '0',
    }
  } else if (currentTask.value) {
    // 使用store中的数据
    courseInfo.value = {
      courseName: currentTask.value.courseName || '',
      className: currentTask.value.className || '',
      xn: currentTask.value.studyYear || '',
      xq: currentTask.value.studyTerm?.toString() || '',
      kcxf: currentTask.value.creditHour || 0,
      kcss: currentTask.value.courseTotalHours || '0',
    }
  }
}

// 将API返回的学生数据映射为组件所需格式
const mapStudentData = (items: TeacherTotalScoreItem[]) => {
  return items.map((item) => ({
    id: item.id,
    name: item.xm,
    studentId: item.xsxh,
    score: item.cj.toString(),
    hasScore: true, // 成绩0分也是已录入，所有学生默认都已录入
    zwh: item.zwh,
    originalData: item,
    gpa: item.cjjd || '',
    credit: item.kcxf?.toString() || '',
    isPassed: item.tgbz === 1,
  }))
}

// ======= 成绩处理相关函数 =======

// 更新学生分数
const updateStudentScore = async (index: number, value: string) => {
  // 如果成绩已提交，则不允许更新
  if (scoreTypeInfo.value?.submit === 1) {
    toast.show({ msg: '成绩已提交，不可修改', iconName: 'error' })
    return
  }

  if (index >= 0 && index < students.value.length) {
    // 保存原始分数，用于检测变化
    const originalScore = students.value[index].score

    // 更新本地状态
    students.value[index].score = value
    // 不再需要更新hasScore，所有成绩都视为已录入

    // 如果分数有变化，则调用API更新成绩
    if (originalScore !== value) {
      await updateStudentScoreToServer(index)
    }

    // 更新统计数据
    updateStats()
  }
}

// 调用API更新成绩到服务器
const updateStudentScoreToServer = async (index: number) => {
  if (!jxrwid.value) {
    toast.show({ msg: '未获取到教学任务信息', iconName: 'error' })
    return
  }

  // 保存原始成绩，用于失败时恢复
  const student = students.value[index]
  const originalScore = student.originalData.cj.toString()
  const newScore = student.score

  try {
    const params = {
      jxrwid: jxrwid.value,
      rows: [{ id: student.id, cj: newScore }],
    }

    toast.loading('正在更新成绩...')
    const data = await updateTeachingTaskTotalScore(params)
    toast.close()

    if (data && data.length > 0) {
      handleScoreUpdateSuccess(index, data[0])
    } else {
      handleScoreUpdateFailure(index, originalScore)
    }
  } catch (error) {
    handleScoreUpdateError(index, originalScore, error)
  }
}

// 处理成绩更新成功
const handleScoreUpdateSuccess = (index: number, updatedStudent: any) => {
  // 保留原始对象的引用，只更新属性
  Object.assign(students.value[index].originalData, updatedStudent)

  // 更新显示数据
  students.value[index].score = updatedStudent.cj.toString()
  // 不再需要更新hasScore，所有成绩都视为已录入

  // 更新绩点、学分和通过状态
  students.value[index].gpa = updatedStudent.cjjd + '' || ''
  // 确保转换为字符串
  if (updatedStudent.kcxf !== undefined) {
    students.value[index].credit = updatedStudent.kcxf.toString()
  } else {
    students.value[index].credit = ''
  }
  students.value[index].isPassed = updatedStudent.tgbz === 1

  toast.show({ msg: '成绩更新成功', iconName: 'success' })
}

// 处理成绩更新失败
const handleScoreUpdateFailure = (index: number, originalScore: string) => {
  // 服务器返回空数据：恢复原始成绩
  students.value[index].score = originalScore
  // 不再需要更新hasScore，所有成绩都视为已录入

  toast.show({ msg: '成绩更新失败', iconName: 'error' })

  // 更新统计数据
  updateStats()
}

// 处理成绩更新错误
const handleScoreUpdateError = (index: number, originalScore: string, error: any) => {
  toast.close()
  console.error('更新成绩失败', error)

  // 发生错误：恢复原始成绩
  students.value[index].score = originalScore
  // 不再需要更新hasScore，所有成绩都视为已录入

  // 更新统计数据
  updateStats()
}

// 更新学生分数状态 - 该方法已不再需要，但保留函数签名以避免引用错误
const updateStudentHasScore = (index: number, value: boolean) => {
  // 成绩0分也是已录入，该方法已不再需要
  // 空函数实现，保留接口但不做任何操作
}

// 更新统计数据
const updateStats = () => {
  const passCount = students.value.filter((s) => parseFloat(s.score) >= 60).length
  const zeroCount = students.value.filter((s) => parseFloat(s.score) === 0).length
  const failCount = students.value.filter(
    (s) => parseFloat(s.score) > 0 && parseFloat(s.score) < 60,
  ).length

  statsData.value = { passCount, failCount, zeroCount }
}

// ======= 成绩评定类型相关函数 =======

// 处理成绩评定类型变化
const handleGradeTypeChange = (event: any) => {
  if (event && event.value) {
    tempSelectedGradeType.value = event.value
  }
}

// 确认成绩评定类型
const confirmGradeType = async () => {
  // 检查是否已提交成绩
  if (scoreTypeInfo.value?.submit === 1) {
    toast.show({ msg: '成绩已提交，不可操作', iconName: 'error' })
    showGradeTypePopup.value = false
    return
  }

  // 如果有临时选择的值，则更新selectedGradeType
  if (tempSelectedGradeType.value) {
    selectedGradeType.value = tempSelectedGradeType.value
  }

  // 关闭弹窗
  showGradeTypePopup.value = false

  try {
    // 调用API设置成绩评定类型
    const params: SetScoreTypeRequest = {
      jxrwid: jxrwid.value,
      cjptlx: selectedGradeType.value,
    }

    await setScoreType(params)

    // 设置成功后，重新获取成绩类型信息
    await fetchScoreTypeInfo()

    uni.showToast({
      title: `已设置为${currentGradeType.value}`,
      icon: 'success',
    })
  } catch (error) {
    console.error('设置成绩评定类型失败', error)
  }
}

// 成绩评定类型按钮点击处理
const handleGradeTypeClick = () => {
  if (scoreTypeInfo.value?.submit === 1) {
    toast.show({ msg: '成绩已提交，不可操作', iconName: 'error' })
    return
  }
  showGradeTypePopup.value = true
}

// ======= 成绩操作相关函数 =======

// 保存草稿
const handleSaveDraft = () => {
  toast.show({ msg: '已保存草稿', iconName: 'success' })
}

// 处理提交
const handleSubmit = () => {
  // 直接调用提交成绩的方法，不再检查是否所有学生都已录入成绩
  handleSubmitScore()
}

// 提交成绩
const handleSubmitScore = async () => {
  if (!jxrwid.value) {
    toast.show({ msg: '未获取到教学任务信息', iconName: 'error' })
    return
  }

  // 检查是否已提交成绩
  if (scoreTypeInfo.value?.submit === 1) {
    toast.show({ msg: '成绩已提交，不可重复提交', iconName: 'error' })
    return
  }

  // 检查是否在提交时间范围内
  if (!isInSubmitTimeRange.value) {
    toast.show({ msg: '不在成绩提交时间范围内，不可提交', iconName: 'error' })
    return
  }

  // 确认提交
  uni.showModal({
    title: '确认提交',
    content: '确认要提交本课程的总评成绩吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const params: SubmitTotalScoreRequest = {
            jxrwid: jxrwid.value,
          }

          toast.loading('正在提交总评成绩...')
          const res = await submitTotalScore(params)
          toast.close()

          // 显示提交结果
          showResultDialog({
            title: '提交结果',
            message: formatResultMessage(res.frontMsg),
            success: true,
            showErrorIcon: false,
            centered: false,
            showFailList: true,
          })

          // 解析不通过学生名单
          parseFailStudents(res.frontMsg)

          // 重新获取数据
          fetchStudentScoreList()
        } catch (error) {
          toast.close()
          console.error('提交总评成绩失败', error)
        }
      }
    },
  })
}

// 生成总评成绩
const handleGenerateTotalScore = async () => {
  if (!jxrwid.value) {
    toast.show({ msg: '未获取到教学任务信息', iconName: 'error' })
    return
  }

  // 检查是否已提交成绩
  if (scoreTypeInfo.value?.submit === 1) {
    toast.show({ msg: '成绩已提交，不可操作', iconName: 'error' })
    return
  }

  // 如果在提交时间范围内，直接允许操作
  // 如果不在提交时间范围内，添加提示但仍允许操作
  if (!isInSubmitTimeRange.value) {
    uni.showModal({
      title: '注意',
      content: '当前不在成绩提交时间范围内，生成的成绩将无法提交',
      showCancel: false,
      success: () => {
        showGenerateTotalScoreConfirm()
      },
    })
  } else {
    showGenerateTotalScoreConfirm()
  }
}

// 显示生成总评成绩确认对话框
const showGenerateTotalScoreConfirm = () => {
  // 添加确认对话框
  uni.showModal({
    title: '确认生成总评',
    content:
      '确认要根据日常成绩生成总评成绩吗？（请提前配置好总评成绩比例）\n已登记的成绩将会被覆盖，请谨慎操作！',
    success: async (res) => {
      if (res.confirm) {
        try {
          const params: GenerateTotalScoreRequest = {
            jxrwid: jxrwid.value,
          }

          toast.loading('正在生成总评成绩...')
          const res = await generateTotalScore(params)
          toast.close()

          // 重新获取数据
          fetchStudentScoreList()
        } catch (error) {
          toast.close()
          console.error('生成总评成绩失败', error)
        }
      }
    },
  })
}

// 教学平台同步
const handleSyncFromPlatform = () => {
  // 检查是否已提交成绩
  if (scoreTypeInfo.value?.submit === 1) {
    toast.show({ msg: '成绩已提交，不可操作', iconName: 'error' })
    return
  }

  // 如果在提交时间范围内，直接允许操作
  // 如果不在提交时间范围内，添加提示但仍允许操作
  if (!isInSubmitTimeRange.value) {
    uni.showModal({
      title: '注意',
      content: '当前不在成绩提交时间范围内，同步的成绩将无法提交',
      showCancel: false,
      success: () => {
        showSyncConfirmDialog()
      },
    })
  } else {
    showSyncConfirmDialog()
  }
}

// 显示同步确认对话框
const showSyncConfirmDialog = () => {
  // 显示确认对话框
  uni.showModal({
    title: '确认同步',
    content:
      '确认要从教学平台拉取总评成绩吗？（需要本课程已关联教学平台）\n已登记的成绩将会被覆盖，请谨慎操作！',
    success: async (res) => {
      if (res.confirm) {
        await syncScoreFromPlatform()
      }
    },
  })
}

// 从教学平台同步成绩
const syncScoreFromPlatform = async () => {
  try {
    if (!jxrwid.value) {
      toast.show({ msg: '未获取到教学任务信息', iconName: 'error' })
      return
    }

    const params: TeachingTaskTotalScoreSyncRequest = {
      jxrwid: jxrwid.value,
    }

    toast.loading('正在同步总评成绩...')
    const res = await syncTeachingTaskTotalScore(params)
    toast.close()

    // 显示同步结果
    showResultDialog({
      title: '同步结果',
      message: res.frontMsg,
      success: true,
      showErrorIcon: false,
      centered: true,
      showFailList: false,
    })
    fetchStudentScoreList()
  } catch (error) {
    toast.close()
    console.error('同步总评成绩失败', error)

    // 显示错误信息
    showResultDialog({
      title: '同步失败',
      message: error.msg || '同步失败',
      success: false,
      showErrorIcon: true,
      centered: true,
      showFailList: false,
    })
  }
}

// ======= 成绩撤销申请相关函数 =======

// 处理成绩撤销申请
const handleScoreRevokeRequest = (type?: string) => {
  // 跳转到成绩撤销申请页面
  const typeParam = type ? `&type=${type}` : ''
  uni.navigateTo({
    url: `/pages/teacher/teaching-affairs/task-management/final-scores/revoke-apply/index?jxrwid=${jxrwid.value}${typeParam}`,
  })
}

// ======= 辅助函数 =======

// 显示结果对话框
const showResultDialog = (options: {
  title: string
  message: string
  success: boolean
  showErrorIcon: boolean
  centered: boolean
  showFailList: boolean
}) => {
  Object.assign(resultDialog, {
    ...options,
    show: true,
  })
}

// 解析不通过学生名单
const parseFailStudents = (message: string) => {
  failStudents.value = []

  // 检查消息中是否包含不通过学生信息
  if (message && message.includes('以下为不通过学生：')) {
    const match = message.match(/以下为不通过学生：(.+)/)
    if (match && match[1]) {
      // 分割学生名单
      failStudents.value = match[1].split('，').map((item) => item.trim())
    }
  }
}

// 格式化结果消息
const formatResultMessage = (message: string) => {
  if (!message) return ''

  // 提取主要信息，去除不通过学生名单
  if (message.includes('以下为不通过学生：')) {
    return message.split('以下为不通过学生：')[0].trim()
  }

  return message
}

// 计算属性
const totalStudents = computed(() => students.value.length)
// 所有学生都视为已录入成绩，不再需要这些计算属性
// const recordedStudents = computed(() => students.value.filter((s) => s.hasScore).length)
// const pendingStudents = computed(() => totalStudents.value - recordedStudents.value)

// 页面加载时初始化数据
onMounted(() => {
  initPageData()
})
</script>
