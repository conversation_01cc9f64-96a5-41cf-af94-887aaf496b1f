<route lang="json5">
{
  style: {
    navigationBarTitleText: '成绩撤销申请',
  },
}
</route>
<template>
  <!-- 使用FormWithApproval组件，提供基本信息和审批流程两个标签页 -->
  <FormWithApproval
    :id="detail.id"
    :code="formCode"
    :show-workflow="true"
    v-model="activeTab"
    @return="handleBack"
  >
    <!-- 基本信息表单内容 -->
    <template #form-content>
      <view class="p-3 bg-white">
        <!-- 详情信息表格 -->
        <view class="info-table mb-4">
          <view class="info-row" v-for="(item, index) in infoItems" :key="index">
            <view class="info-label">{{ item.label }}</view>
            <view class="info-value">
              <template v-if="item.type === 'status'">
                <text :class="getDictClass(submitStatusDict, detail.cjtjbz.toString())">
                  {{ getDictLabel(submitStatusDict, detail.cjtjbz.toString()) }}
                </text>
              </template>
              <template v-else-if="item.type === 'course'">
                {{ detail.kcmc }}
                <view class="text-xs text-gray-500">
                  {{ getDictLabel(assessmentMethodDict, detail.khfs) }}
                </view>
              </template>
              <template v-else>
                {{ item.value }}
              </template>
            </view>
          </view>
        </view>

        <!-- 申请说明 -->
        <view class="mb-4">
          <view class="text-sm font-medium mb-1">
            申请说明
            <text class="text-red-500">*</text>
          </view>
          <textarea
            class="form-textarea"
            v-model="applyReason"
            placeholder="请输入申请说明（必填）"
          />
        </view>

        <!-- 使用文件上传组件 -->
        <FileUploader v-model="attachmentList" upload-type="scoreRevoke" title="附件" />
      </view>
    </template>

    <template #form-buttons>
      <view class="flex space-x-2 p-3 bg-white">
        <view
          class="flex-1 bg-gray-100 text-gray-700 py-2 rounded-md text-center"
          @click="handleBack"
        >
          返回
        </view>
        <view
          class="flex-1 bg-blue-500 text-white py-2 rounded-md text-center"
          @click="handleSubmitApply"
        >
          {{ isSubmitApply ? '提交申请' : '提交撤销申请' }}
        </view>
      </view>
    </template>
  </FormWithApproval>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue'
import { getTotalScoreRevokeDetail, submitTotalScoreRevokeApply } from '@/service/teacher'
import type { TotalScoreRevokeDetailResponse, TotalScoreRevokeApplyRequest } from '@/types/teacher'
import { useToast } from 'wot-design-uni'
import { loadDictData, getDictLabel, getDictClass } from '@/utils/dict'
import type { DictData } from '@/types/system'
import FileUploader from '@/components/common/FileUploader.vue'
import FormWithApproval from '@/components/FormWithApproval/index.vue'
import type { WorkflowTimelineResponse } from '@/types/workflow'

// 添加toast提示
const toast = useToast()

// 获取页面参数
const query = defineProps<{ jxrwid: string; type?: string }>()

// 根据type参数设置页面标题和表单代码
const isSubmitApply = computed(() => query.type === 'cjtjsq')
const pageTitle = computed(() => (isSubmitApply.value ? '成绩提交申请' : '成绩撤销申请'))
const formCode = computed(() => (isSubmitApply.value ? 'cjtjsq' : 'cjcxsq'))

// 设置页面标题
uni.setNavigationBarTitle({ title: pageTitle.value })

// ===== 数据定义 =====
const detail = ref<TotalScoreRevokeDetailResponse>({
  id: 0,
  xn: '',
  xq: 0,
  kcmc: '',
  ssxbmc: '',
  ssjysmc: '',
  bjmc: '',
  zdjsxm: '',
  cjtjbz: 0,
  xqzxs: '',
  zxs: '',
  zs: 0,
  khfs: '',
  ydsqbid: 0,
  sqyy: '',
  sqfjlb: '',
})
const applyReason = ref<string>('')
const attachmentList = ref<Array<{ url: string; name: string }>>([])

// 当前激活的标签页
const activeTab = ref(0)

// ===== 字典相关 =====
const assessmentMethodDict = ref<DictData[]>([])
const submitStatusDict = ref<DictData[]>([])

// ===== 计算属性 =====
// 信息项配置
const infoItems = computed(() => [
  { label: '学年学期', value: `${detail.value.xn} (${detail.value.xq})` },
  { label: '课程', type: 'course' },
  { label: '主导教师', value: detail.value.zdjsxm },
  { label: '班级', value: detail.value.bjmc },
  {
    label: '学时',
    value: `总学时：${detail.value.xqzxs} 周学时：${detail.value.zxs} 周数：${detail.value.zs}`,
  },
  { label: '成绩提交状态', type: 'status' },
])

// ===== 方法定义 =====
// 加载字典数据
const loadDicts = async () => {
  try {
    const dictData = await loadDictData(['SYS_ASSESSMENT_METHOD', 'SYS_SUBMIT_STATUS'])
    assessmentMethodDict.value = dictData.SYS_ASSESSMENT_METHOD || []
    submitStatusDict.value = dictData.SYS_SUBMIT_STATUS || []
  } catch (error) {
    console.error('加载字典数据失败', error)
  }
}

// 解析附件列表
const parseAttachments = (attachmentString: string) => {
  if (!attachmentString) return []

  try {
    return attachmentString.split(',').map((item: string) => {
      const [url, name] = item.split('|')
      return { url, name: name || url.split('/').pop() || '未命名文件' }
    })
  } catch (e) {
    console.error('解析附件列表失败:', e)
    return []
  }
}

// 加载详情数据
const loadDetail = async () => {
  if (!query.jxrwid) {
    toast.show({ msg: '缺少必要参数', iconName: 'error' })
    return
  }

  try {
    toast.loading('加载中...')
    await loadDicts()
    const res = await getTotalScoreRevokeDetail({
      jxrwid: query.jxrwid,
      sqlx: formCode.value,
    })

    detail.value = res
    applyReason.value = res.sqyy || ''
    attachmentList.value = parseAttachments(res.sqfjlb)

    toast.close()
  } catch (error) {
    toast.close()
    toast.show({ msg: '获取详情失败', iconName: 'error' })
    console.error(`获取${pageTitle.value}详情失败`, error)
  }
}

// 返回上一页
const handleBack = () => {
  uni.navigateBack()
}

// 提交撤销申请
const handleSubmitApply = async () => {
  // 验证申请说明是否填写
  if (!applyReason.value.trim()) {
    toast.show({ msg: '请填写申请说明', iconName: 'error' })
    return
  }

  // 准备提交数据
  const submitData: TotalScoreRevokeApplyRequest = {
    id: detail.value.id,
    sqlx: formCode.value,
    sqyy: applyReason.value,
    // 附件列表，格式为文件路径|文件名
    sqfjlb: attachmentList.value.map((file) => `${file.url}|${file.name}`).join(','),
  }

  try {
    toast.loading('提交中...')
    const res = await submitTotalScoreRevokeApply(submitData)
    toast.close()
    toast.show({ msg: res.frontMsg || '提交成功', iconName: 'success' })

    // 提交成功后返回上一页
    setTimeout(() => uni.navigateBack(), 1500)
  } catch (error) {
    toast.close()
    console.error(`提交${pageTitle.value}失败`, error)
  }
}

// 监听标签页切换
watch(activeTab, (newValue) => {
  // 当切换到审批流程标签页时，确保有ID
  if (newValue === 1 && detail.value.id) {
    // FormWithApproval组件会自动加载审批流程数据
    console.log('切换到审批流程标签页，ID:', detail.value.id)
  }
})

// 页面加载时获取数据
onMounted(() => loadDetail())
</script>

<style lang="scss" scoped>
.info-table {
  overflow: hidden;
  border: 1px solid #eee;
  border-radius: 4px;
}

.info-row {
  display: flex;
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  width: 120rpx;
  padding: 16rpx;
  font-size: 24rpx;
  color: #4b5563;
  background-color: #f9fafb;
  border-right: 1px solid #eee;
}

.info-value {
  flex: 1;
  padding: 16rpx;
  font-size: 24rpx;
  color: #1f2937;
}

.form-textarea {
  box-sizing: border-box;
  width: 100%;
  height: 180rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}
</style>
