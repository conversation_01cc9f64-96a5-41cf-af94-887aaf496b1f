<route lang="json5">
{
  style: {
    navigationBarTitleText: '教学任务管理',
  },
}
</route>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useTeachingTaskStore } from '@/store/teachingTask'

// 获取当前教学任务信息
const teachingTaskStore = useTeachingTaskStore()
const currentTask = computed(() => teachingTaskStore.currentTask)

// 当前课程信息
const currentCourse = reactive({
  name: computed(() => currentTask.value?.courseName || '未知课程'),
  code: computed(() => currentTask.value?.courseCode || '未知代码'),
})

// 功能卡片列表
const functionCards = reactive([
  {
    name: '授课要点',
    icon: 'i-carbon-pen',
    path: './teaching-points/index',
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-500',
  },
  {
    name: '教材选用',
    icon: 'i-carbon-book',
    path: './textbook-selection',
    bgColor: 'bg-amber-100',
    textColor: 'text-amber-500',
  },
  {
    name: '教学日程安排',
    icon: 'i-carbon-calendar',
    path: './teaching-daily-arrangement/index',
    bgColor: 'bg-green-100',
    textColor: 'text-green-500',
  },
  {
    name: '授课进度制定',
    icon: 'i-carbon-chart-line',
    path: './teaching-progress/index',
    bgColor: 'bg-purple-100',
    textColor: 'text-purple-500',
  },
  {
    name: '授课计划表',
    icon: 'i-carbon-notebook',
    path: './teaching-plan/index',
    bgColor: 'bg-cyan-100',
    textColor: 'text-cyan-500',
  },
  {
    name: '教学检查自查',
    icon: 'i-carbon-checkmark-filled',
    path: './teaching-inspection/index',
    bgColor: 'bg-rose-100',
    textColor: 'text-rose-500',
  },
  {
    name: '课程考勤',
    icon: 'i-carbon-user-profile',
    path: './attendance/index',
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-500',
  },
  {
    name: '班级学生',
    icon: 'i-carbon-group-presentation',
    path: './class-students/index',
    bgColor: 'bg-yellow-100',
    textColor: 'text-yellow-600',
  },
  {
    name: '班级通知',
    icon: 'i-carbon-notification',
    path: './class-notification/index',
    bgColor: 'bg-red-100',
    textColor: 'text-red-500',
  },
  {
    name: '日常成绩',
    icon: 'i-carbon-chart-bar',
    path: './daily-scores/index',
    bgColor: 'bg-lime-100',
    textColor: 'text-lime-600',
  },
  {
    name: '总评成绩',
    icon: 'i-carbon-star',
    path: './final-scores/index',
    bgColor: 'bg-indigo-100',
    textColor: 'text-indigo-500',
  },
  {
    name: '工作手册',
    icon: 'i-carbon-document',
    path: './work-manual',
    bgColor: 'bg-teal-100',
    textColor: 'text-teal-500',
  },
])

// 检查是否有课程信息
onMounted(() => {
  if (!currentTask.value) {
    uni.showToast({
      title: '未获取到课程信息',
      icon: 'none',
    })
  }
})

// 切换课程
const switchCourse = () => {
  uni.showToast({
    title: '暂未开放此功能',
    icon: 'none',
  })
}

// 点击功能卡片
const handleCardClick = (path: string, name: string) => {
  // 工作手册功能不开放，其他功能都开放
  if (name === '工作手册') {
    // 显示工作手册功能暂未开放提示
    uni.showToast({
      title: '该功能暂未开放',
      icon: 'none',
    })
  } else {
    // 检查是否有当前教学任务信息
    if (
      !currentTask.value &&
      (name === '总评成绩' ||
        name === '日常成绩' ||
        name === '授课要点' ||
        name === '教学日程安排' ||
        name === '授课进度制定' ||
        name === '授课计划表' ||
        name === '教学检查自查' ||
        name === '课程考勤' ||
        name === '班级学生' ||
        name === '班级通知')
    ) {
      uni.showToast({
        title: '请先选择教学任务',
        icon: 'none',
      })
      return
    }

    uni.navigateTo({
      url: path,
    })
  }
}
</script>

<template>
  <view class="container">
    <!-- 课程选择器 -->
    <view class="course-selector">
      <view class="course-info">
        <text class="course-label">当前课程</text>
        <text class="course-name">{{ currentCourse.name }}</text>
        <text v-if="currentTask" class="course-code">{{ currentTask.className || '' }}</text>
      </view>
      <view class="switch-button" @click="switchCourse">
        <text class="switch-text">切换课程</text>
        <view class="i-carbon-chevron-down text-xs"></view>
      </view>
    </view>

    <!-- 功能卡片网格 -->
    <view class="card-grid">
      <view
        v-for="(card, index) in functionCards"
        :key="index"
        :class="['card-item', card.name === '工作手册' ? 'card-disabled' : 'card-active']"
        @click="handleCardClick(card.path, card.name)"
      >
        <view class="icon-container" :class="card.bgColor">
          <view :class="[card.icon, card.textColor]"></view>
        </view>
        <text class="card-name">{{ card.name }}</text>
        <view v-if="card.name === '工作手册'" class="card-disabled-mark">未开放</view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 99vh;
  padding: 24rpx;
  background-color: #f5f5f7;
}

.course-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  margin-bottom: 24rpx;
  background-color: #ffffff;
  border-radius: 32rpx;
}

.course-info {
  display: flex;
  flex-direction: column;
}

.course-label {
  margin-bottom: 8rpx;
  font-size: 24rpx;
  color: #8e8e93;
}

.course-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.course-code {
  margin-top: 4rpx;
  font-size: 24rpx;
  color: #8e8e93;
}

.switch-button {
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  background-color: #e8f1ff;
  border-radius: 36rpx;
}

.switch-text {
  margin-right: 8rpx;
  font-size: 24rpx;
  font-weight: 500;
  color: #3a8eff;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 32rpx;
}

.card-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  cursor: pointer;
  border-radius: 24rpx;
  transition: all 0.3s;

  &:active {
    background-color: #f2f2f7;
    transform: scale(0.97);
  }
}

.card-active {
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-disabled {
  opacity: 0.6;
}

.card-disabled-mark {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  padding: 4rpx 8rpx;
  font-size: 20rpx;
  color: #ffffff;
  background-color: #999999;
  border-radius: 10rpx;
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 96rpx;
  height: 96rpx;
  margin-bottom: 16rpx;
  border-radius: 50%;
}

.card-name {
  font-size: 26rpx;
  color: #333333;
  text-align: center;
}
</style>
