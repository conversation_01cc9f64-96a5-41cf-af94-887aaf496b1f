/**
 * 教学任务管理相关页面的配置信息
 */

interface PageConfig {
  /** 页面名称 */
  name: string
  /** 页面路径 */
  path: string
  /** 图标名称 */
  icon: string
  /** 是否需要参数 */
  requireParams: boolean
}

/**
 * 教学任务管理页面配置
 */
export const taskManagementPages: PageConfig[] = [
  {
    name: '授课要点',
    path: '/pages/teacher/teaching-affairs/task-management/teaching-points',
    icon: 'note',
    requireParams: true,
  },
  {
    name: '教材选用',
    path: '/pages/teacher/teaching-affairs/task-management/textbook-selection',
    icon: 'books',
    requireParams: true,
  },
  {
    name: '教学日程安排',
    path: '/pages/teacher/teaching-affairs/task-management/teaching-schedule',
    icon: 'calendar',
    requireParams: true,
  },
  {
    name: '授课进度制定',
    path: '/pages/teacher/teaching-affairs/task-management/teaching-progress',
    icon: 'chart-pie',
    requireParams: true,
  },
  {
    name: '授课计划表',
    path: '/pages/teacher/teaching-affairs/task-management/teaching-plan',
    icon: 'clipboard-check',
    requireParams: true,
  },
  {
    name: '教学检查自查',
    path: '/pages/teacher/teaching-affairs/task-management/teaching-inspection',
    icon: 'check-circle',
    requireParams: true,
  },
  {
    name: '课程考勤',
    path: '/pages/teacher/teaching-affairs/task-management/course-attendance',
    icon: 'view-list',
    requireParams: true,
  },
  {
    name: '班级学生',
    path: '/pages/teacher/teaching-affairs/task-management/class-students',
    icon: 'usergroup',
    requireParams: true,
  },
  {
    name: '班级通知',
    path: '/pages/teacher/teaching-affairs/task-management/class-notification',
    icon: 'notification',
    requireParams: true,
  },
  {
    name: '日常成绩',
    path: '/pages/teacher/teaching-affairs/task-management/daily-grades',
    icon: 'star',
    requireParams: true,
  },
  {
    name: '总评成绩',
    path: '/pages/teacher/teaching-affairs/task-management/final-grades',
    icon: 'star-filled',
    requireParams: true,
  },
  {
    name: '工作手册',
    path: '/pages/teacher/teaching-affairs/task-management/work-manual',
    icon: 'note',
    requireParams: true,
  },
]

/**
 * 根据页面名称获取页面配置
 * @param name 页面名称
 * @returns 页面配置
 */
export function getPageByName(name: string): PageConfig | undefined {
  return taskManagementPages.find((page) => page.name === name)
}

/**
 * 根据路径获取页面配置
 * @param path 页面路径
 * @returns 页面配置
 */
export function getPageByPath(path: string): PageConfig | undefined {
  return taskManagementPages.find((page) => page.path === path)
}
