<script setup lang="ts">
// 定义组件属性
defineProps<{
  className: string
  courseName: string
  courseHours: number
  weeklyHours: number
  weeks: number
}>()
</script>

<template>
  <view class="course-info">
    <!-- 班级信息 -->
    <view class="form-item mb-32rpx">
      <view class="form-label mb-16rpx">班级</view>
      <view class="bg-gray-50 p-24rpx rounded-16rpx">
        <text class="text-26rpx text-gray-700">{{ className }}</text>
      </view>
    </view>

    <!-- 课程信息 -->
    <view class="form-item mb-32rpx">
      <view class="form-label mb-16rpx">课程</view>
      <view class="bg-gray-50 p-24rpx rounded-16rpx">
        <text class="text-26rpx text-gray-700">{{ courseName }}</text>
        <view class="mt-8rpx text-24rpx text-gray-500">
          本学期学时：{{ courseHours }} 周学时：{{ weeklyHours }}/周 周数：{{ weeks }}周
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
</style>
