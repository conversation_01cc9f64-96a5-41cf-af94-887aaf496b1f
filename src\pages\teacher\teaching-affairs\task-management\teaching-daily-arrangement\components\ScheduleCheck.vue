<script setup lang="ts">
interface CheckResult {
  xh: number
  rq: string
  zc: string
  xq: string
  jc: string
  result: string
  error: number
}

defineProps<{
  checkResults: CheckResult[]
}>()
</script>

<template>
  <view class="schedule-check">
    <view class="form-label mb-16rpx">授课安排设置结果检测</view>

    <!-- 卡片列表 -->
    <view class="card-list">
      <view
        v-for="item in checkResults"
        :key="item.xh"
        class="card-item bg-white border border-gray-200 rounded-16rpx mb-16rpx p-24rpx"
      >
        <view class="flex justify-between items-center mb-16rpx">
          <view class="flex items-center">
            <view class="card-badge mr-16rpx">{{ item.xh }}</view>
            <text class="text-30rpx font-500">{{ item.rq }}</text>
          </view>
          <view
            class="px-16rpx py-4rpx rounded-8rpx text-24rpx"
            :class="item.error === 0 ? 'bg-green-50 text-green-600' : 'bg-red-50 text-red-600'"
          >
            {{ item.error === 0 ? '正常' : '异常' }}
          </view>
        </view>

        <view class="flex flex-wrap">
          <view class="info-item">
            <text class="info-label">周次：</text>
            <text class="info-value">{{ item.zc }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">星期：</text>
            <text class="info-value">{{ item.xq }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">节次：</text>
            <text class="info-value">{{ item.jc }}</text>
          </view>
        </view>

        <view
          class="result-box mt-16rpx p-16rpx rounded-8rpx"
          :class="item.error === 0 ? 'bg-green-50' : 'bg-red-50'"
        >
          <!-- eslint-disable-next-line vue/no-v-text-v-html-on-component -->
          <text class="text-26rpx" v-html="item.result"></text>
        </view>
      </view>
    </view>

    <!-- 提示 -->
    <view class="bg-blue-50 p-24rpx rounded-16rpx mt-24rpx">
      <view class="flex items-center mb-8rpx">
        <wd-icon name="info-circle" color="#1989fa" size="36rpx" />
        <text class="text-28rpx font-600 text-blue-500 ml-8rpx">提示</text>
      </view>
      <view class="text-26rpx text-blue-600 leading-36rpx">
        如提示自己跟自己冲突，请返回日程安排第一步操作
        "日程安排清空"，合班任务多个班级仅需操作一次即可。
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.card-badge {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  font-size: 24rpx;
  color: #666666;
  background-color: #f2f3f5;
  border-radius: 50%;
}

.info-item {
  display: flex;
  margin-right: 32rpx;
  margin-bottom: 8rpx;
}

.info-label {
  font-size: 26rpx;
  color: #999999;
}

.info-value {
  font-size: 26rpx;
  color: #333333;
}

.result-box {
  word-break: break-all;
}
</style>
