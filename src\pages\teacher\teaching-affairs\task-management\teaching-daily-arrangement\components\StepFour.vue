<script setup lang="ts">
import { onMounted, ref } from 'vue'
import CourseInfo from './CourseInfo.vue'
import ScheduleCheck from './ScheduleCheck.vue'
import { useTeachingDailyArrangementStore } from '@/store/teachingDailyArrangement'
import { useTeachingTaskStore } from '@/store/teachingTask'
import { checkTeachingScheduleConflicts } from '@/service/teachingTask'

interface FormData {
  className: string
  courseName: string
  courseHours: number
  weeklyHours: number
  weeks: number
}

interface CheckResult {
  xh: number
  rq: string
  zc: string
  xq: string
  jc: string
  result: string
  error: number
}

const props = defineProps<{
  formData: FormData
  scheduleCheckResults: CheckResult[]
}>()

const emit = defineEmits<{
  (e: 'prev'): void
  (e: 'submit'): void
}>()

const dailyArrangementStore = useTeachingDailyArrangementStore()
const teachingTaskStore = useTeachingTaskStore()
const checkResults = ref<CheckResult[]>([])
const loading = ref<boolean>(true)

// 获取教学安排检查结果
const fetchScheduleCheckResults = async () => {
  loading.value = true
  uni.showLoading({
    title: '检测中...',
  })

  try {
    // 从store中获取教学安排检查参数
    const params = dailyArrangementStore.scheduleCheckParams

    if (!params) {
      console.error('教学安排检查参数为空')
      uni.showToast({
        title: '参数错误',
        icon: 'none',
      })
      loading.value = false
      uni.hideLoading()
      return
    }

    console.log('开始检查教学安排冲突，参数:', params)

    // 调用检查教学安排冲突接口
    const results = await checkTeachingScheduleConflicts(params)

    // 打印检查结果
    console.log('教学安排检查结果:', results)

    // 将结果存储到store中，直接修改状态而不是调用方法
    dailyArrangementStore.scheduleCheckResults = results

    // 直接使用接口返回的数据，不进行转换
    checkResults.value = results
  } catch (error) {
    console.error('检查教学安排冲突失败:', error)
    uni.showToast({
      title: '检查安排失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
    uni.hideLoading()
  }
}

onMounted(() => {
  fetchScheduleCheckResults()
})

// 提交表单
const submitForm = () => {
  /* uni.showLoading({
    title: '提交中...',
  })

  setTimeout(() => {
    uni.hideLoading()
    emit('submit')
    uni.showToast({
      title: '提交成功',
      icon: 'success',
    })
  }, 1500) */
  uni.redirectTo({ url: '/pages/teacher/teaching-affairs/task-management/teaching-progress/index' })
}

// 返回上一步
const handlePrevStep = () => {
  emit('prev')
}
</script>

<template>
  <view class="step-four">
    <!-- 班级和课程信息 -->
    <CourseInfo
      :class-name="dailyArrangementStore.formData.className"
      :course-name="dailyArrangementStore.formData.courseName"
      :course-hours="dailyArrangementStore.formData.courseHours"
      :weekly-hours="dailyArrangementStore.formData.weeklyHours"
      :weeks="dailyArrangementStore.formData.weeks"
    />

    <!-- 加载中状态 -->
    <view v-if="loading" class="loading-container">
      <view class="loading-icon mb-24rpx">
        <wd-icon name="refresh" size="48rpx" class="loading-spinner" />
      </view>
      <view class="loading-text">正在进行日程安排检测，请耐心等待...</view>
    </view>

    <!-- 授课安排设置结果检测 -->
    <ScheduleCheck v-else :check-results="checkResults" />

    <!-- 按钮组 -->
    <view class="flex flex-col mt-48rpx">
      <wd-button plain size="medium" @click="handlePrevStep">上一步</wd-button>
      <wd-button
        type="primary"
        size="medium"
        class="mt-24rpx"
        @click="submitForm"
        :disabled="loading"
      >
        安排完成，开始录入授课进度信息
      </wd-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
  margin: 24rpx 0;
  background-color: #f8f8f8;
  border-radius: 12rpx;
}

.loading-icon {
  display: flex;
  justify-content: center;
}

.loading-spinner {
  animation: spin 1.5s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
  text-align: center;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
