<script setup lang="ts">
import CourseInfo from './CourseInfo.vue'
import { onMounted, watch, ref, computed } from 'vue'
import { useTeachingTaskStore } from '@/store/teachingTask'
import { useTeachingDailyArrangementStore } from '@/store/teachingDailyArrangement'

// 初始化store
const teachingTaskStore = useTeachingTaskStore()
const dailyArrangementStore = useTeachingDailyArrangementStore()

// 获取当前教学任务ID
const taskId = ref('')

// 初始化获取taskId并加载数据
onMounted(() => {
  if (teachingTaskStore.currentTask?.id) {
    taskId.value = String(teachingTaskStore.currentTask.id)
    dailyArrangementStore.fetchTeachingScheduleArrangement(taskId.value)
  }
})

// 监听currentTask变化，确保在taskId变化时重新获取数据
watch(
  () => teachingTaskStore.currentTask?.id,
  (newTaskId) => {
    if (newTaskId) {
      taskId.value = String(newTaskId)
      dailyArrangementStore.fetchTeachingScheduleArrangement(taskId.value)
    }
  },
)

// 清空日常安排
const clearArrangement = () => {
  uni.showModal({
    title: '提示',
    content: '确定要清空日常安排吗？该操作会将未执行的补课/调课及停课数据清空。',
    success: (res) => {
      if (res.confirm) {
        dailyArrangementStore.clearArrangement()
      }
    },
  })
}

// 更新上课频率
const updateFrequency = (value: number) => {
  dailyArrangementStore.updateFrequency(value)
}

// 是否存在排课信息
const hasScheduleConfig = computed(
  () =>
    dailyArrangementStore.formData.scheduleConfig &&
    dailyArrangementStore.formData.scheduleConfig.length > 0,
)

const emit = defineEmits<{
  (e: 'next'): void
}>()
</script>

<template>
  <view class="step-one">
    <!-- 注意事项 -->
    <view class="bg-orange-50 p-24rpx rounded-16rpx mb-32rpx">
      <view class="flex items-center mb-8rpx">
        <wd-icon name="warning" color="#f59a23" size="36rpx" />
        <text class="text-28rpx font-600 text-orange-500 ml-8rpx">注意</text>
      </view>
      <text class="text-26rpx text-orange-600 leading-36rpx">
        日程安排操作按排课信息执行，该功能结果会将未执行的补课/调课及停课数据清空，请慎重操作，敬请悉知！
      </text>
    </view>

    <!-- 日程开放时间 -->
    <view class="form-item mb-32rpx">
      <view class="form-label mb-16rpx">日程开放时间</view>
      <view class="bg-gray-50 p-24rpx rounded-16rpx">
        <text class="text-26rpx text-gray-700">
          {{ dailyArrangementStore.scheduleTime.openTime }}
        </text>
      </view>
    </view>

    <!-- 班级和课程信息 -->
    <CourseInfo
      :class-name="dailyArrangementStore.formData.className"
      :course-name="dailyArrangementStore.formData.courseName"
      :course-hours="dailyArrangementStore.formData.courseHours"
      :weekly-hours="dailyArrangementStore.formData.weeklyHours"
      :weeks="dailyArrangementStore.formData.weeks"
    />

    <!-- 排课/进程 -->
    <view class="form-item mb-32rpx">
      <view class="form-label mb-16rpx">排课/进程</view>
      <view class="bg-gray-50 p-24rpx rounded-16rpx">
        <!-- 当没有排课信息时显示提示 -->
        <text v-if="!hasScheduleConfig" class="text-26rpx text-gray-700">
          系统未设置，请自行配置。
        </text>
        <!-- 有排课信息时一行显示一条 -->
        <view v-else>
          <view
            v-for="(item, index) in dailyArrangementStore.formData.scheduleConfig"
            :key="index"
            class="text-26rpx text-gray-700 mb-8rpx last:mb-0"
          >
            {{ item }}
          </view>
        </view>
      </view>
    </view>

    <!-- 上课频率 -->
    <view class="form-item mb-32rpx">
      <view class="form-label mb-16rpx">上课频率</view>
      <view class="flex items-center">
        <text class="text-26rpx text-gray-700 mr-16rpx">每周</text>

        <view class="custom-input-number" :class="{ disabled: hasScheduleConfig }">
          <view
            class="btn decrease"
            @click="
              !hasScheduleConfig &&
                updateFrequency(Number(dailyArrangementStore.formData.frequency) - 1)
            "
            :class="{ disabled: hasScheduleConfig }"
          >
            -
          </view>
          <input
            type="number"
            class="input"
            :value="String(dailyArrangementStore.formData.frequency)"
            @blur="(e) => !hasScheduleConfig && updateFrequency(Number(e.detail.value) || 1)"
            :min="1"
            :disabled="hasScheduleConfig"
          />
          <view
            class="btn increase"
            @click="
              !hasScheduleConfig &&
                updateFrequency(Number(dailyArrangementStore.formData.frequency) + 1)
            "
            :class="{ disabled: hasScheduleConfig }"
          >
            +
          </view>
        </view>

        <text class="text-26rpx text-gray-700 ml-16rpx">次课</text>
      </view>
    </view>

    <!-- 提示信息 -->
    <view class="bg-blue-50 p-24rpx rounded-16rpx mb-48rpx">
      <view class="flex items-center mb-8rpx">
        <wd-icon name="info-circle" color="#1989fa" size="36rpx" />
        <text class="text-28rpx font-600 text-blue-500 ml-8rpx">提示</text>
      </view>
      <view class="text-26rpx text-blue-600 leading-36rpx">
        <view>1）理论课，请选择每周上课次数(不是节数)，单双周也算一次；</view>
        <view>2）实践周，默认2节为一个单位，即每周上15次课（5天*3），如有不同，自行更改；</view>
        <view>3）如已有排课或进程信息，按默认设置即可。</view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="flex justify-between mt-48rpx">
      <wd-button type="warning" size="medium" @click="clearArrangement">清空日常安排</wd-button>
      <wd-button type="primary" size="medium" @click="$emit('next')">下一步</wd-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.custom-input-number {
  display: flex;
  align-items: center;
  overflow: hidden;
  border: 1px solid #e5e5e5;
  border-radius: 4px;

  &.disabled {
    background-color: #f5f5f5;
    opacity: 0.6;
  }

  .btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60rpx;
    height: 60rpx;
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    background-color: #f5f5f5;

    &.decrease {
      border-right: 1px solid #e5e5e5;
    }

    &.increase {
      border-left: 1px solid #e5e5e5;
    }

    &.disabled {
      color: #999;
      cursor: not-allowed;
    }
  }

  .input {
    width: 80rpx;
    height: 60rpx;
    font-size: 28rpx;
    color: #333;
    text-align: center;

    &:disabled {
      color: #999;
      background-color: #f5f5f5;
    }
  }
}
</style>
