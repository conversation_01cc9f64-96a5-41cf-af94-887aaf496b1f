<script setup lang="ts">
import { onMounted, ref, computed, watch, onActivated } from 'vue'
import CourseInfo from './CourseInfo.vue'
import TeachingSchedule from './TeachingSchedule.vue'
import { useTeachingDailyArrangementStore } from '@/store/teachingDailyArrangement'
import { useTeachingTaskStore } from '@/store/teachingTask'
import { parseDateTime } from '@/utils'
import { TeachingScheduleCheckRequest } from '@/types/teachingTask'

const dailyArrangementStore = useTeachingDailyArrangementStore()
const teachingTaskStore = useTeachingTaskStore()

// 存储所有周的日期数据
const allWeekDates = ref<{ week: number; dates: Record<string, string> }[]>([])

// 存储校历安排数据
const calendarArrangements = ref<Record<string, CalendarItem[]>>({})

interface CalendarItem {
  tkrq: string // 调课日期（不允许上课的日期）
  dhrq: string // 调换日期
  remark: string // 备注说明
}

interface FormData {
  className: string
  courseName: string
  courseHours: number
  weeklyHours: number
  weeks: number
}

interface DateItem {
  week: number
  date: string
  selected: boolean
  disabled?: boolean
  disabledReason?: string
  alternateDate?: string // 调课后的日期
}

interface ArrangementItem {
  id: number
  name: string
  detail: string
  dates: DateItem[]
}

interface TimeArrangement {
  id: number
  weekType: string
  weekDay: string
  period: string
}

/**
 * 根据开学时间和周数计算特定星期几的日期
 * @param startDate 开学时间
 * @param weekNumber 周数
 * @param weekDay 星期几 (1-7，1代表周一，7代表周日)
 * @returns 日期字符串，格式为 YYYY-MM-DD
 */
const calculateDateByWeek = (startDate: Date, weekNumber: number, weekDay: number): string => {
  // 确保weekDay在1-7范围内
  const targetWeekDay = Math.min(Math.max(1, weekDay), 7)

  // 获取开学日期是星期几 (0-6，0代表周日，1代表周一)
  const startWeekDay = startDate.getDay()
  // 转换为1-7表示法 (1代表周一，7代表周日)
  const adjustedStartWeekDay = startWeekDay === 0 ? 7 : startWeekDay

  // 计算开学日期所在周的周一日期
  const mondayOfFirstWeek = new Date(startDate)
  mondayOfFirstWeek.setDate(startDate.getDate() - (adjustedStartWeekDay - 1))

  // 计算目标周的周一日期
  const mondayOfTargetWeek = new Date(mondayOfFirstWeek)
  mondayOfTargetWeek.setDate(mondayOfFirstWeek.getDate() + (weekNumber - 1) * 7)

  // 计算目标周的目标星期几日期
  const targetDate = new Date(mondayOfTargetWeek)
  targetDate.setDate(mondayOfTargetWeek.getDate() + (targetWeekDay - 1))

  // 格式化日期为 YYYY-MM-DD
  const year = targetDate.getFullYear()
  const month = String(targetDate.getMonth() + 1).padStart(2, '0')
  const day = String(targetDate.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}

// 格式化节次信息
const formatPeriod = (period: string): string => {
  if (!period) return '未知节次'

  // 如果已经是格式化的形式，直接返回
  if (period.includes('节')) return period

  // 尝试从节次值中提取信息
  const matches = period.match(/(\d+)-(\d+)/)
  if (matches && matches.length === 3) {
    return `${matches[1]}~${matches[2]}节`
  }

  return `${period}节`
}
// 获取周类型显示文本
const getWeekTypeText = (weekType: string): string => {
  switch (weekType) {
    case '0':
      return '每周'
    case '1':
      return '单周'
    case '2':
      return '双周'
    default:
      return '每周'
  }
}

// 获取星期显示文本
const getWeekDayText = (weekDay: string): string => {
  switch (weekDay) {
    case '1':
      return '周一'
    case '2':
      return '周二'
    case '3':
      return '周三'
    case '4':
      return '周四'
    case '5':
      return '周五'
    case '6':
      return '周六'
    case '7':
      return '周日'
    default:
      return '未知'
  }
}

const props = defineProps<{
  formData: FormData
  teachingArrangements: ArrangementItem[]
  calendarInfo: string[]
  taskId?: string // 添加taskId作为可选prop
}>()

const emit = defineEmits<{
  (e: 'prev'): void
  (e: 'next'): void
  (e: 'update:teachingArrangements', value: ArrangementItem[]): void
}>()

// 计算属性：获取时间安排数据
const timeArrangements = computed(() => {
  console.log(dailyArrangementStore.timeArrangements)
  return dailyArrangementStore.timeArrangements
})

// 计算属性：获取周使用情况数据
const weekUsage = computed(() => dailyArrangementStore.weekUsage)

// 获取教学日程安排数据
const fetchScheduleData = () => {
  // 从props中获取教学任务ID
  return dailyArrangementStore
    .fetchTeachingScheduleData(teachingTaskStore.currentTask.id.toString())
    .then(() => {
      // 解析校历安排数据
      if (dailyArrangementStore.teachingScheduleData?.xlap) {
        calendarArrangements.value = dailyArrangementStore.teachingScheduleData.xlap
      }

      // 获取开学时间
      if (dailyArrangementStore.teachingScheduleData?.kxsj) {
        const kxsj = dailyArrangementStore.teachingScheduleData.kxsj
        const startDate = parseDateTime(kxsj)

        // 获取总周数
        const totalWeeks = dailyArrangementStore.formData.weeks || 20 // 默认20周

        // 生成所有周的日期数据
        allWeekDates.value = generateWeekDates(startDate, totalWeeks)
      }
    })
}

/**
 * 生成从第一周到最后一周的所有周的日期数据
 * @param startDate 开学时间
 * @param totalWeeks 总周数
 * @returns 周日期数据，格式为 { week: number, dates: { [weekDay: string]: string } }[]
 */
const generateWeekDates = (startDate: Date, totalWeeks: number) => {
  const weekDates = []

  for (let week = 1; week <= totalWeeks; week++) {
    const weekData = {
      week,
      dates: {} as Record<string, string>,
    }

    // 计算该周的周一到周日的日期
    for (let day = 1; day <= 7; day++) {
      const date = calculateDateByWeek(startDate, week, day)
      weekData.dates[day.toString()] = date
    }

    weekDates.push(weekData)
  }

  return weekDates
}

/**
 * 检查日期是否在校历安排中，并获取相关信息
 * @param date 日期字符串，格式为 YYYY-MM-DD
 * @returns 如果日期在校历安排中，返回对应的安排信息；否则返回null
 */
const getCalendarArrangement = (date: string): CalendarItem | null => {
  if (calendarArrangements.value && calendarArrangements.value[date]) {
    return calendarArrangements.value[date][0] // 取第一条记录
  }
  return null
}

/**
 * 生成教学安排检查请求参数
 * @returns 教学安排检查请求参数对象
 */
const generateTeachingScheduleCheckParams = (): TeachingScheduleCheckRequest => {
  // 获取教学任务ID
  const jxrwid = teachingTaskStore.currentTask.id.toString()

  // 步骤固定为4
  const step = 4

  // 生成jxzc字符串，表示每周是否有课
  // 获取总周数
  const totalWeeks = dailyArrangementStore.formData.weeks || 20

  // 初始化jxzc为全0字符串
  let jxzc = '0'.repeat(totalWeeks)

  // 根据weekUsage更新jxzc
  if (weekUsage.value && weekUsage.value.length > 0) {
    const jxzcArray = jxzc.split('')
    weekUsage.value.forEach((week) => {
      if (week.hasClass && week.week <= totalWeeks) {
        // 周次从1开始，数组索引从0开始，所以需要减1
        jxzcArray[week.week - 1] = '1'
      }
    })
    jxzc = jxzcArray.join('')
  }

  // 创建一个数组来收集skap项及其相关信息，用于排序
  interface SkapItem {
    value: string // skap项的值
    date: string // 日期
    timeArrangement: TimeArrangement // 时间安排
  }

  const skapItems: SkapItem[] = []

  // 遍历教学安排数据
  props.teachingArrangements.forEach((arrangement) => {
    // 获取时间安排信息
    const timeArrangement = timeArrangements.value.find((item) => item.id === arrangement.id)
    if (!timeArrangement) return

    // 遍历日期数据
    arrangement.dates.forEach((dateItem) => {
      // 只处理选中的日期
      if (dateItem.selected) {
        // 使用实际日期或调课后的日期
        const date = dateItem.alternateDate || dateItem.date

        // 构建skap项
        // 格式：周次_日期_星期几_节次_类型
        // 例如：2_2025-02-24_1_7-8|5-6_0
        const skapItem = `${dateItem.week}_${date}_${timeArrangement.weekDay}_${timeArrangement.period}_${timeArrangement.weekType}`

        // 将skap项及其相关信息添加到收集数组中
        skapItems.push({
          value: skapItem.replace('节', ''),
          date,
          timeArrangement,
        })
      }
    })
  })

  // 按照日期和时间安排的顺序对skap项进行排序
  skapItems.sort((a, b) => {
    // 首先按日期排序
    if (a.date !== b.date) {
      return a.date.localeCompare(b.date)
    }

    // 日期相同，按星期几排序
    if (a.timeArrangement.weekDay !== b.timeArrangement.weekDay) {
      return parseInt(a.timeArrangement.weekDay) - parseInt(b.timeArrangement.weekDay)
    }

    // 星期几相同，按节次排序
    // 提取节次的起始数字进行比较
    const getPeriodStart = (period: string) => {
      const match = period.match(/^(\d+)/)
      return match ? parseInt(match[1]) : 0
    }

    const aStart = getPeriodStart(a.timeArrangement.period)
    const bStart = getPeriodStart(b.timeArrangement.period)

    return aStart - bStart
  })

  // 从排序后的数组中提取skap值
  const skap: string[] = skapItems.map((item) => item.value)

  // 构建并返回请求参数对象
  const params: TeachingScheduleCheckRequest = {
    jxrwid,
    step,
    skap,
    jxzc,
  }

  return params
}

// 重新初始化教学安排数据
const reinitializeScheduleData = () => {
  fetchScheduleData().then(() => {
    // 强制触发watch监听器重新计算教学安排
    if (
      timeArrangements.value &&
      timeArrangements.value.length > 0 &&
      weekUsage.value &&
      weekUsage.value.length > 0 &&
      allWeekDates.value &&
      allWeekDates.value.length > 0
    ) {
      // 手动触发更新
      const newArrangements = generateTeachingArrangements(
        timeArrangements.value,
        weekUsage.value,
        allWeekDates.value,
      )
      emit('update:teachingArrangements', newArrangements)
    }
  })
}

/**
 * 生成教学安排数据
 * @param timeArrangements 时间安排数据
 * @param weekUsage 周使用情况数据
 * @param allWeekDates 所有周的日期数据
 * @returns 教学安排数据
 */
const generateTeachingArrangements = (
  timeArrangements: TimeArrangement[],
  weekUsage: { week: number; hasClass: boolean }[],
  allWeekDates: { week: number; dates: Record<string, string> }[],
): ArrangementItem[] => {
  // 获取开学时间
  const kxsj = dailyArrangementStore.teachingScheduleData?.kxsj || ''
  const startDate = kxsj ? parseDateTime(kxsj) : new Date()

  // 将开学时间设置为当天的0点0分0秒，便于比较
  startDate.setHours(0, 0, 0, 0)

  // 筛选出hasClass为true的周
  const activeWeeks = weekUsage.filter((item) => item.hasClass).map((item) => item.week)

  // 生成新的教学安排数据
  return timeArrangements.map((timeArrangement, index) => {
    // 获取周类型文本
    const weekTypeText = getWeekTypeText(timeArrangement.weekType)
    // 获取星期文本
    const weekDayText = getWeekDayText(timeArrangement.weekDay)
    // 格式化节次信息
    const periodText = formatPeriod(timeArrangement.period)

    // 为每个活跃的周生成日期项
    const dates: DateItem[] = activeWeeks.map((week) => {
      // 从allWeekDates中获取对应周次和星期几的日期
      const weekData = allWeekDates.find((data) => data.week === week)
      let date = ''

      if (weekData && weekData.dates[timeArrangement.weekDay]) {
        date = weekData.dates[timeArrangement.weekDay]
      } else {
        // 如果没有找到对应的日期数据，使用备用计算方法
        date = calculateDateByWeek(startDate, week, parseInt(timeArrangement.weekDay))
      }

      // 判断日期是否在开学时间之前
      const currentDate = new Date(date)
      // 将当前日期设置为当天的0点0分0秒，便于比较
      currentDate.setHours(0, 0, 0, 0)

      const isBeforeStartDate = currentDate < startDate

      // 判断周类型是否匹配
      const isWeekTypeMatch = (() => {
        // 如果是每周(0)，则所有周都匹配
        if (timeArrangement.weekType === '0') return true

        // 如果是单周(1)，则只有单数周匹配
        if (timeArrangement.weekType === '1') {
          // 单周判断：1, 3, 5, 7...
          const result = week % 2 === 1
          return result
        }

        // 如果是双周(2)，则只有双数周匹配
        if (timeArrangement.weekType === '2') {
          // 双周判断：2, 4, 6, 8...
          const result = week % 2 === 0
          return result
        }

        return true // 默认匹配
      })()

      // 检查日期是否在校历安排中
      const calendarItem = getCalendarArrangement(date)

      // 判断是否禁用
      const isDisabled = isBeforeStartDate || !isWeekTypeMatch || !!calendarItem

      // 获取禁用原因
      let disabledReason
      if (isBeforeStartDate) {
        disabledReason = '未开学'
      } else if (!isWeekTypeMatch) {
        disabledReason = ''
      } else if (calendarItem) {
        disabledReason = calendarItem.remark
      }

      // 创建日期项
      const dateItem: DateItem = {
        week,
        date,
        selected: !isDisabled, // 不禁用的日期默认选中
        disabled: isDisabled, // 设置禁用状态
        disabledReason, // 设置禁用原因
      }

      // 如果有调课安排，添加调课日期
      if (calendarItem && calendarItem.dhrq && calendarItem.dhrq !== '0000-00-00') {
        dateItem.alternateDate = calendarItem.dhrq
        // 即使有调课日期，也需要考虑周类型匹配和开学时间
        dateItem.selected = !isBeforeStartDate && isWeekTypeMatch
        dateItem.disabled = isBeforeStartDate || !isWeekTypeMatch
      }

      return dateItem
    })

    return {
      id: timeArrangement.id || index + 1,
      name: `${weekTypeText}${weekDayText}`,
      detail: periodText,
      dates,
    }
  })
}

// 监听时间安排数据变化，同步更新教学安排数据
watch(
  [() => timeArrangements.value, () => weekUsage.value, () => allWeekDates.value],
  ([newTimeArrangements, newWeekUsage, newAllWeekDates]) => {
    if (
      newTimeArrangements &&
      newTimeArrangements.length > 0 &&
      newWeekUsage &&
      newWeekUsage.length > 0 &&
      newAllWeekDates &&
      newAllWeekDates.length > 0
    ) {
      // 每次数据变化时都重新生成教学安排数据，不再检查缓存
      const newArrangements = generateTeachingArrangements(
        newTimeArrangements,
        newWeekUsage,
        newAllWeekDates,
      )
      emit('update:teachingArrangements', newArrangements)
    }
  },
  { immediate: true },
)

// 监听timeArrangements和weekUsage的变化
watch(
  [() => dailyArrangementStore.timeArrangements, () => dailyArrangementStore.weekUsage],
  () => {
    console.log('[StepThree] 检测到timeArrangements或weekUsage变化，重新初始化数据')
    reinitializeScheduleData()
  },
  { deep: true },
)

// 生成教学安排检查参数并进入下一步
const handleNextStep = () => {
  const params = generateTeachingScheduleCheckParams()
  // 直接修改store中的状态，而不是调用方法
  dailyArrangementStore.scheduleCheckParams = params
  emit('next')
}

onMounted(() => {
  reinitializeScheduleData()
})

// 当组件被激活时重新初始化数据（例如从其他步骤返回时）
onActivated(() => {
  console.log('[StepThree] 组件被激活，重新初始化数据')
  reinitializeScheduleData()
})
</script>

<template>
  <view class="step-three">
    <!-- 班级和课程信息 -->
    <CourseInfo
      :class-name="dailyArrangementStore.formData.className"
      :course-name="dailyArrangementStore.formData.courseName"
      :course-hours="dailyArrangementStore.formData.courseHours"
      :weekly-hours="dailyArrangementStore.formData.weeklyHours"
      :weeks="dailyArrangementStore.formData.weeks"
    />

    <!-- 授课安排 -->
    <TeachingSchedule
      :model-value="teachingArrangements"
      :calendar-info="calendarInfo"
      :time-arrangements="timeArrangements"
      :weeks="dailyArrangementStore.formData.weeks"
      :week-usage="weekUsage"
      @update:model-value="emit('update:teachingArrangements', $event)"
      class="mb-32rpx"
    />

    <!-- 按钮组 -->
    <view class="flex justify-between mt-48rpx">
      <wd-button plain size="medium" @click="$emit('prev')">上一步</wd-button>
      <wd-button type="primary" size="medium" @click="handleNextStep">下一步</wd-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
</style>
