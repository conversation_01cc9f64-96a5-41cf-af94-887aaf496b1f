<script setup lang="ts">
import { onMounted, watch, computed, ref } from 'vue'
import CourseInfo from './CourseInfo.vue'
import TimeArrangement from './TimeArrangement.vue'
import WeekUsage from './WeekUsage.vue'
import { useTeachingDailyArrangementStore } from '@/store/teachingDailyArrangement'
import { useTeachingTaskStore } from '@/store/teachingTask'
import { getTeachingTaskSessions } from '@/service/teachingTask'
import type { TeachingTaskSessionRequest } from '@/types/teachingTask'

// 获取教学任务日常安排store
const dailyArrangementStore = useTeachingDailyArrangementStore()
const teachingTaskStore = useTeachingTaskStore()

// 获取disabled状态
const isDisabled = computed(() => dailyArrangementStore.disabled)

// 节次选项
const periodOptions = ref<Array<{ value: string; label: string; combinedValue?: string }>>([])
const periodLoading = ref(false)

// 教学节次信息
const teachingSessionInfo = ref<
  Array<{ zc: number; kcmc: string; zdjsxm: string; zcshow: string }>
>([])

// 计算教学节次的周次数组
const teachingWeeks = computed(() => {
  return teachingSessionInfo.value.map((item) => item.zc)
})

const emit = defineEmits<{
  (e: 'prev'): void
  (e: 'next'): void
}>()

// 获取节次信息
const fetchPeriodOptions = async () => {
  if (!teachingTaskStore.currentTask.id) {
    console.error('taskId未找到')
    return
  }

  periodLoading.value = true
  try {
    const params: TeachingTaskSessionRequest = {
      jxrwid: teachingTaskStore.currentTask.id.toString(),
      step: 2,
    }

    const response = await getTeachingTaskSessions(params)

    // 处理节次信息
    periodOptions.value = response.jcxx.map((item: any) => ({
      value: item.value,
      label: item.label,
      combinedValue: `${item.value}|${item.label}`,
    }))

    // 处理教学节次信息
    teachingSessionInfo.value = response.jxjc || []
  } catch (error) {
    console.error('获取节次信息失败:', error)
    periodOptions.value = []
    teachingSessionInfo.value = []
  } finally {
    periodLoading.value = false
  }
}

// 更新周使用情况数据
const updateWeekUsage = (weeks: number) => {
  if (!weeks || weeks <= 0) return

  // 创建新的周使用情况数据
  const newWeekUsage = Array.from({ length: weeks }, (_, i) => {
    const weekNumber = i + 1

    // 如果是教学节次中的周次，强制设为不选中
    const isTeachingWeek = teachingWeeks.value.includes(weekNumber)
    if (isTeachingWeek) {
      return {
        week: weekNumber,
        hasClass: false,
      }
    }

    // 非教学节次周，保持原有状态或默认不选中
    return {
      week: weekNumber,
      hasClass: dailyArrangementStore.weekUsage[i]?.hasClass || false,
    }
  })

  // 如果处于disabled状态，确保第一周被选中（除非第一周是教学节次周）
  if (isDisabled.value && newWeekUsage.length > 0) {
    const isFirstWeekTeaching = teachingWeeks.value.includes(1)
    if (!isFirstWeekTeaching) {
      newWeekUsage[0].hasClass = true
    }
  }

  dailyArrangementStore.weekUsage = newWeekUsage
}

// 根据frequency生成默认的时间安排数据
const generateDefaultTimeArrangements = (frequency: number) => {
  // 如果已经有时间安排数据或者处于禁用状态，则不生成默认数据
  if (dailyArrangementStore.timeArrangements.length > 0 || isDisabled.value) {
    return
  }

  // 创建新的时间安排数据
  const newTimeArrangements = Array.from({ length: frequency }, (_, i) => {
    // 默认从周一到周五循环
    const weekDay = '1'

    // 如果有节次选项，使用第一个选项的combinedValue，否则设为空
    let period = ''
    if (periodOptions.value.length > 0) {
      // 为不同的课程安排不同的节次，循环使用前三个节次
      period = periodOptions.value[0]?.combinedValue || ''
    }

    return {
      id: i + 1,
      weekType: '0', // 默认为全部(每周)
      weekDay,
      period,
    }
  })

  // 更新时间安排数据
  dailyArrangementStore.timeArrangements = newTimeArrangements
}

// 检测时间安排是否存在重复配置
const checkDuplicateTimeArrangements = () => {
  const { timeArrangements } = dailyArrangementStore

  for (let i = 0; i < timeArrangements.length; i++) {
    for (let j = i + 1; j < timeArrangements.length; j++) {
      // 检查是否同一天且节次相同
      if (
        timeArrangements[i].weekDay === timeArrangements[j].weekDay &&
        timeArrangements[i].period === timeArrangements[j].period &&
        timeArrangements[i].weekType === timeArrangements[j].weekType
      ) {
        // 发现重复，返回重复的安排编号
        return {
          hasDuplicate: true,
          duplicateInfo: {
            first: i + 1,
            second: j + 1,
          },
        }
      }
    }
  }

  // 没有重复
  return { hasDuplicate: false }
}

// 处理时间安排数据变更
const handleTimeArrangementsChange = (newValue: any) => {
  console.log('[StepTwo] 时间安排数据已更新')
  dailyArrangementStore.timeArrangements = newValue
}

// 处理周使用情况数据变更
const handleWeekUsageChange = (newValue: any) => {
  console.log('[StepTwo] 周使用情况数据已更新')
  dailyArrangementStore.weekUsage = newValue
}

// 处理下一步按钮点击
const handleNextStep = () => {
  // 检查是否选择了至少一个课程使用周
  const hasSelectedWeek = dailyArrangementStore.weekUsage.some((week) => week.hasClass)
  if (!hasSelectedWeek) {
    uni.showToast({
      title: '请选择课程使用周',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 检查时间安排是否存在重复
  const checkResult = checkDuplicateTimeArrangements()

  if (checkResult.hasDuplicate) {
    // 如果存在重复，提示用户
    uni.showToast({
      title: `时间安排存在重复配置：安排#${checkResult.duplicateInfo.first} 和安排#${checkResult.duplicateInfo.second}`,
      icon: 'none',
      duration: 3000,
    })
    return
  }

  // 没有重复，继续下一步
  emit('next')
}

// 监听表单数据变化，确保周使用情况数据与总周数一致
watch(
  () => dailyArrangementStore.formData.weeks,
  (newWeeks) => {
    if (newWeeks && newWeeks > 0 && dailyArrangementStore.weekUsage.length !== newWeeks) {
      updateWeekUsage(newWeeks)
    }
  },
  { immediate: true },
)

// 监听frequency变化，生成默认的时间安排数据
watch(
  () => dailyArrangementStore.formData.frequency,
  (newFrequency) => {
    if (newFrequency && newFrequency > 0 && periodOptions.value.length > 0) {
      generateDefaultTimeArrangements(newFrequency)
    }
  },
)

// 监听periodOptions变化，当节次选项加载完成后生成默认数据
watch(
  () => periodOptions.value,
  (newPeriodOptions) => {
    if (newPeriodOptions.length > 0 && dailyArrangementStore.formData.frequency > 0) {
      generateDefaultTimeArrangements(dailyArrangementStore.formData.frequency)
    }
  },
)

// 监听disabled状态变化，确保在变为disabled时第一周被选中
watch(
  () => dailyArrangementStore.disabled,
  (newDisabled) => {
    if (newDisabled && dailyArrangementStore.weekUsage.length > 0) {
      const newWeekUsage = [...dailyArrangementStore.weekUsage]
      // 只有第一周不是教学节次周时才选中
      const isFirstWeekTeaching = teachingWeeks.value.includes(1)
      if (!isFirstWeekTeaching) {
        newWeekUsage[0].hasClass = true
      }
      dailyArrangementStore.weekUsage = newWeekUsage
    }
  },
)

// 监听教学节次信息变化，重新更新周使用情况
watch(
  () => teachingSessionInfo.value,
  (newTeachingSessionInfo) => {
    if (newTeachingSessionInfo.length > 0 && dailyArrangementStore.formData.weeks > 0) {
      updateWeekUsage(dailyArrangementStore.formData.weeks)
    }
  },
)

// 组件挂载时获取节次信息
onMounted(async () => {
  await fetchPeriodOptions()
})
</script>

<template>
  <view class="step-two">
    <!-- 班级和课程信息 -->
    <CourseInfo
      :class-name="dailyArrangementStore.formData.className"
      :course-name="dailyArrangementStore.formData.courseName"
      :course-hours="dailyArrangementStore.formData.courseHours"
      :weekly-hours="dailyArrangementStore.formData.weeklyHours"
      :weeks="dailyArrangementStore.formData.weeks"
    />

    <!-- 时间安排 -->
    <TimeArrangement
      :model-value="dailyArrangementStore.timeArrangements"
      @update:model-value="handleTimeArrangementsChange"
      class="mb-32rpx"
    />

    <!-- 课程使用周 -->
    <WeekUsage
      :model-value="dailyArrangementStore.weekUsage"
      :teaching-weeks="teachingWeeks"
      @update:model-value="handleWeekUsageChange"
      class="mb-32rpx"
    />

    <!-- 实习周安排 -->
    <view class="mb-32rpx">
      <view class="text-28rpx font-500 text-[#333] mb-16rpx">该班实习周安排</view>

      <!-- 教学节次信息卡片列表 -->
      <view v-if="teachingSessionInfo.length > 0" class="space-y-16rpx">
        <view
          v-for="(item, index) in teachingSessionInfo"
          :key="index"
          class="bg-white rounded-12rpx p-24rpx shadow-sm border border-[#f0f0f0]"
        >
          <view class="flex items-center justify-between mb-12rpx">
            <view class="text-26rpx font-500 text-[#333]">{{ item.kcmc }}</view>
            <view class="text-24rpx text-[#1890ff] bg-[#e6f7ff] px-12rpx py-4rpx rounded-6rpx">
              {{ item.zcshow }}
            </view>
          </view>
          <view class="text-24rpx text-[#666]">
            <text class="text-[#999]">主讲教师：</text>
            {{ item.zdjsxm }}
          </view>
        </view>
      </view>

      <!-- 空状态提示 -->
      <view v-else class="text-center py-48rpx">
        <view class="text-24rpx text-[#999]">暂无实习周安排信息</view>
      </view>
    </view>

    <!-- 按钮组 -->
    <view class="flex justify-between mt-48rpx">
      <wd-button plain size="medium" @click="$emit('prev')">上一步</wd-button>
      <wd-button type="primary" size="medium" @click="handleNextStep">下一步</wd-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
</style>
