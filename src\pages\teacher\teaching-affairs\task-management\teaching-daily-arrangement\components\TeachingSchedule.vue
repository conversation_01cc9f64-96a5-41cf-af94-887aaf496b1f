<script setup lang="ts">
import { ref, watch, computed } from 'vue'

interface DateItem {
  week: number
  date: string
  selected: boolean
  disabled?: boolean
  disabledReason?: string
  alternateDate?: string // 调课后的日期
}

interface ArrangementItem {
  id: number
  name: string
  detail: string
  dates: DateItem[]
}

interface TimeArrangement {
  id: number
  weekType: string
  weekDay: string
  period: string
}

interface WeekUsageItem {
  week: number
  hasClass: boolean
}

const props = defineProps<{
  modelValue: ArrangementItem[]
  calendarInfo: string[]
  timeArrangements?: TimeArrangement[]
  weeks?: number // 总周数
  weekUsage?: WeekUsageItem[] // 周使用情况
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: ArrangementItem[]): void
}>()

// 全选状态
const allSelected = ref(false)

// 全选/取消全选
const toggleSelectAll = () => {
  allSelected.value = !allSelected.value
  const newArrangements = props.modelValue.map((arrangement) => {
    return {
      ...arrangement,
      dates: arrangement.dates.map((date) => ({
        ...date,
        // 只有未禁用的日期才能被选中或取消选中
        selected: date.disabled ? date.selected : allSelected.value,
      })),
    }
  })
  emit('update:modelValue', newArrangements)
}

// 切换单个日期选择状态
const toggleDateSelection = (arrangementIndex: number, dateIndex: number) => {
  if (dateIndex === -1) return // 如果找不到对应的日期项，则不执行任何操作

  const newArrangements = [...props.modelValue]
  // 如果日期被禁用，则不允许切换状态
  if (newArrangements[arrangementIndex].dates[dateIndex].disabled) {
    return
  }

  newArrangements[arrangementIndex].dates[dateIndex].selected =
    !newArrangements[arrangementIndex].dates[dateIndex].selected
  emit('update:modelValue', newArrangements)
  checkAllSelected()
}

// 检查是否全部选中
const checkAllSelected = () => {
  let selected = true
  let hasSelectableDate = false

  props.modelValue.forEach((arrangement) => {
    arrangement.dates.forEach((date) => {
      // 只考虑未禁用的日期
      if (!date.disabled) {
        hasSelectableDate = true
        if (!date.selected) {
          selected = false
        }
      }
    })
  })

  // 只有在有可选择的日期时才设置全选状态
  allSelected.value = hasSelectableDate && selected
}

// 初始化时检查全选状态
watch(
  () => props.modelValue,
  () => {
    checkAllSelected()
  },
  { immediate: true },
)

// 获取周类型显示文本
const getWeekTypeText = (weekType: string): string => {
  switch (weekType) {
    case '0':
      return '每周'
    case '1':
      return '单周'
    case '2':
      return '双周'
    default:
      return '每周'
  }
}

// 获取星期显示文本
const getWeekDayText = (weekDay: string): string => {
  switch (weekDay) {
    case '1':
      return '周一'
    case '2':
      return '周二'
    case '3':
      return '周三'
    case '4':
      return '周四'
    case '5':
      return '周五'
    case '6':
      return '周六'
    case '7':
      return '周日'
    default:
      return '未知'
  }
}

// 格式化节次信息
const formatPeriod = (period: string): string => {
  if (!period) return '未知节次'

  // 处理"value|label"格式
  if (period.includes('|')) {
    // 从"value|label"格式中提取label部分
    const parts = period.split('|')
    if (parts.length === 2) {
      return parts[1] // 直接返回label部分
    }
  }

  // 如果已经是格式化的形式，直接返回
  if (period.includes('节')) return period

  // 尝试从节次值中提取信息
  const matches = period.match(/(\d+)-(\d+)/)
  if (matches && matches.length === 3) {
    return `${matches[1]}~${matches[2]}节`
  }

  return `${period}节`
}

/**
 * 格式化日期显示
 * @param date 日期字符串，格式为 YYYY-MM-DD
 * @returns 格式化后的日期字符串，格式为 MM-DD
 */
const formatDate = (date: string): string => {
  if (!date || !date.includes('-')) return '--'

  const parts = date.split('-')
  if (parts.length < 3) return date

  // 只显示月和日，格式为 MM-DD
  return `${parts[1]}-${parts[2]}`
}

// 计算表格列数
const columnCount = computed(() => {
  return props.timeArrangements && props.timeArrangements.length > 0
    ? props.timeArrangements.length + 1
    : 4
})

// 计算表格的网格模板列样式
const gridTemplateColumnsStyle = computed(() => {
  return {
    gridTemplateColumns: `repeat(${columnCount.value}, minmax(0, 1fr))`,
  }
})

// 计算要显示的周列表
const displayWeeks = computed(() => {
  // 如果没有提供weeks或weekUsage，则使用默认值[3, 6]
  if (!props.weeks || !props.weekUsage || props.weekUsage.length === 0) {
    return []
  }

  // 根据weekUsage筛选出hasClass为true的周
  return props.weekUsage
    .filter((item) => item.hasClass)
    .map((item) => item.week)
    .sort((a, b) => a - b) // 按周次升序排序
})
</script>

<template>
  <view class="teaching-schedule">
    <!-- 校历信息 -->
    <view class="form-item mb-32rpx">
      <view class="form-label mb-16rpx">校历信息</view>
      <view class="bg-gray-50 p-24rpx rounded-16rpx">
        <view
          v-for="(item, index) in calendarInfo"
          :key="index"
          class="text-26rpx text-gray-700 mb-8rpx last:mb-0"
        >
          {{ item }}
        </view>
      </view>
    </view>
    <view class="flex justify-between items-center mb-16rpx">
      <view class="form-label">授课安排</view>
      <view class="flex items-center">
        <wd-checkbox v-model="allSelected" @change="toggleSelectAll" shape="square" />
        <text class="text-26rpx text-gray-700 ml-8rpx">全选</text>
      </view>
    </view>

    <view class="text-24rpx text-gray-600 mb-16rpx">
      注：请根据校历信息去除停课的节次，即将不上课的节次√去除 |
      通常情况周三5-6节为各班班会课，不安排
    </view>
    <view class="bg-white border border-gray-200 rounded-16rpx overflow-hidden">
      <!-- 表头 -->
      <view class="grid bg-gray-100 text-center p-16rpx" :style="gridTemplateColumnsStyle">
        <text class="text-26rpx font-500 text-gray-700">周次/节次</text>
        <!-- 使用时间安排数据动态生成表头 -->
        <template v-if="timeArrangements && timeArrangements.length > 0">
          <text
            v-for="item in timeArrangements"
            :key="item.id"
            class="text-26rpx font-500 text-gray-700"
          >
            ({{ getWeekTypeText(item.weekType) }}){{ getWeekDayText(item.weekDay) }}
            <view class="text-22rpx text-gray-500">{{ formatPeriod(item.period) }}</view>
          </text>
        </template>
        <template v-else>
          <text v-for="item in modelValue" :key="item.id" class="text-26rpx font-500 text-gray-700">
            {{ item.name }}
            <view class="text-22rpx text-gray-500">{{ item.detail }}</view>
          </text>
        </template>
      </view>

      <!-- 表格内容 -->
      <view
        v-for="week in displayWeeks"
        :key="week"
        class="grid text-center p-16rpx border-t border-gray-200"
        :style="gridTemplateColumnsStyle"
      >
        <view class="text-26rpx text-gray-700">第{{ week }}周</view>

        <template v-if="timeArrangements && timeArrangements.length > 0">
          <view
            v-for="(timeArrangement, arrangementIndex) in timeArrangements"
            :key="arrangementIndex"
            class="flex flex-col items-center"
          >
            <!-- 查找modelValue中是否有对应周次的日期项 -->
            <template
              v-if="
                modelValue[arrangementIndex] &&
                modelValue[arrangementIndex].dates &&
                modelValue[arrangementIndex].dates.some((date) => date.week === week)
              "
            >
              <!-- 找到对应周次的日期项索引 -->
              <view
                v-for="(date, dateIndex) in modelValue[arrangementIndex].dates.filter(
                  (date) => date.week === week,
                )"
                :key="dateIndex"
                class="flex flex-col items-center"
              >
                <!-- 显示调课后的日期或原始日期 -->
                <view class="text-24rpx text-gray-500 mb-8rpx">
                  {{ date.alternateDate || date.date }}
                </view>
                <wd-checkbox
                  :model-value="date.selected"
                  @change="
                    () =>
                      toggleDateSelection(
                        arrangementIndex,
                        modelValue[arrangementIndex].dates.findIndex((d) => d.week === week),
                      )
                  "
                  shape="square"
                  :disabled="date.disabled"
                />
                <!-- 显示禁用原因或调课备注 -->
                <view
                  v-if="date.disabled && date.disabledReason"
                  class="text-22rpx text-red-500 mt-4rpx"
                >
                  {{ date.disabledReason }}
                </view>
                <!-- 如果有调课日期，显示调课备注 -->
                <view
                  v-else-if="date.alternateDate && date.disabledReason"
                  class="text-22rpx text-blue-500 mt-4rpx"
                >
                  {{ date.disabledReason }}
                </view>
              </view>
            </template>
            <!-- 否则显示默认内容 -->
            <template v-else>
              <view class="text-24rpx text-gray-500 mb-8rpx">--</view>
              <wd-checkbox :model-value="false" @change="() => {}" shape="square" disabled />
            </template>
          </view>
        </template>
        <template v-else>
          <view
            v-for="(arrangement, arrangementIndex) in modelValue"
            :key="arrangementIndex"
            class="flex flex-col items-center"
          >
            <!-- 查找是否有对应周次的日期项 -->
            <template v-if="arrangement.dates.some((date) => date.week === week)">
              <!-- 找到对应周次的日期项索引 -->
              <view
                v-for="(date, dateIndex) in arrangement.dates.filter((date) => date.week === week)"
                :key="dateIndex"
                class="flex flex-col items-center"
              >
                <!-- 显示调课后的日期或原始日期 -->
                <view class="text-24rpx text-gray-500 mb-8rpx">
                  {{ date.alternateDate || date.date }}
                </view>
                <wd-checkbox
                  :model-value="date.selected"
                  @change="
                    () =>
                      toggleDateSelection(
                        arrangementIndex,
                        arrangement.dates.findIndex((d) => d.week === week),
                      )
                  "
                  shape="square"
                  :disabled="date.disabled"
                />
                <!-- 显示禁用原因或调课备注 -->
                <view
                  v-if="date.disabled && date.disabledReason"
                  class="text-22rpx text-red-500 mt-4rpx"
                >
                  {{ date.disabledReason }}
                </view>
                <!-- 如果有调课日期，显示调课备注 -->
                <view
                  v-else-if="date.alternateDate && date.disabledReason"
                  class="text-22rpx text-blue-500 mt-4rpx"
                >
                  {{ date.disabledReason }}
                </view>
              </view>
            </template>
            <!-- 否则显示默认内容 -->
            <template v-else>
              <view class="text-24rpx text-gray-500 mb-8rpx">--</view>
              <wd-checkbox :model-value="false" @change="() => {}" shape="square" disabled />
            </template>
          </view>
        </template>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
</style>
