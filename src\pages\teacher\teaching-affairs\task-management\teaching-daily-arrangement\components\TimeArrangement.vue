<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useTeachingDailyArrangementStore } from '@/store/teachingDailyArrangement'
import { useTeachingTaskStore } from '@/store/teachingTask'
import { getTeachingTaskSessions } from '@/service/teachingTask'
import type { TeachingTaskSessionRequest } from '@/types/teachingTask'

interface TimeArrangement {
  id: number
  weekType: string
  weekDay: string
  period: string
}

const props = defineProps<{
  modelValue: TimeArrangement[]
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: TimeArrangement[]): void
}>()

// 获取store
const dailyArrangementStore = useTeachingDailyArrangementStore()
const teachingTaskStore = useTeachingTaskStore()

// 禁用状态
const isDisabled = computed(() => dailyArrangementStore.disabled)

// 选项定义
const weekTypeOptions = [
  { value: '0', label: '全部' },
  { value: '1', label: '单周' },
  { value: '2', label: '双周' },
]

const weekDayOptions = [
  { value: '1', label: '周一' },
  { value: '2', label: '周二' },
  { value: '3', label: '周三' },
  { value: '4', label: '周四' },
  { value: '5', label: '周五' },
  { value: '6', label: '周六' },
  { value: '7', label: '周日' },
]

// 节次选项，动态获取
const periodOptions = ref<Array<{ value: string; label: string; combinedValue?: string }>>([])
const loading = ref(false)

// 获取节次信息
const fetchPeriodOptions = async () => {
  if (!teachingTaskStore.currentTask.id) {
    console.error('taskId未找到')
    return
  }

  loading.value = true
  try {
    const params: TeachingTaskSessionRequest = {
      jxrwid: teachingTaskStore.currentTask.id.toString(),
      step: 2,
    }

    const response = await getTeachingTaskSessions(params)
    periodOptions.value = response.jcxx.map((item: any) => ({
      value: item.value,
      label: item.label,
      combinedValue: `${item.value}|${item.label}`,
    }))
  } catch (error) {
    console.error('获取节次信息失败:', error)
    periodOptions.value = []
  } finally {
    loading.value = false
  }
}

// 组件挂载时获取节次信息
onMounted(async () => {
  await fetchPeriodOptions()

  // 初始化时处理period字段，转换为combinedValue格式
  if (periodOptions.value.length > 0 && props.modelValue.length > 0) {
    const newArrangements = [...props.modelValue]

    newArrangements.forEach((arrangement, index) => {
      // 如果period不包含"|"，说明还没有转换为combinedValue格式
      if (!arrangement.period.includes('|')) {
        // 尝试找到匹配的选项
        const matchedOption = periodOptions.value.find(
          (option) => option.value === arrangement.period || option.label === arrangement.period,
        )

        if (matchedOption) {
          // 使用combinedValue格式
          newArrangements[index].period = matchedOption.combinedValue || arrangement.period
        }
      }
    })

    // 更新modelValue
    if (JSON.stringify(newArrangements) !== JSON.stringify(props.modelValue)) {
      emit('update:modelValue', newArrangements)
    }
  }
})

// 查找选项索引的通用函数
const findOptionIndex = (
  options: Array<{ value: string; label: string; combinedValue?: string }>,
  valueOrLabel: string,
): number => {
  // 先尝试按combinedValue匹配
  let index = options.findIndex((item) => item.combinedValue === valueOrLabel)

  // 如果没找到，再尝试按value匹配
  if (index < 0) {
    index = options.findIndex((item) => item.value === valueOrLabel)
  }

  // 如果还没找到且valueOrLabel包含"节"，则尝试按label匹配（针对节次）
  if (index < 0 && valueOrLabel.includes('节')) {
    index = options.findIndex((item) => item.label === valueOrLabel)
  }

  return index >= 0 ? index : 0
}

// 更新选项值
const updateValue = (
  index: number,
  field: keyof TimeArrangement,
  pickerIndex: number,
  options: Array<{ value: string; label: string; combinedValue?: string }>,
) => {
  // 如果禁用状态，不进行更新
  if (isDisabled.value) return

  const newArrangements = [...props.modelValue]
  if (field === 'weekType' || field === 'weekDay') {
    newArrangements[index][field] = options[pickerIndex]?.value || ''
  } else if (field === 'period') {
    // 对于period，保存combinedValue (value|label格式)
    newArrangements[index][field] =
      options[pickerIndex]?.combinedValue || options[pickerIndex]?.value || ''
  }

  emit('update:modelValue', newArrangements)
}
</script>

<template>
  <view class="time-arrangement">
    <view class="text-28rpx font-500 text-[#333] mb-16rpx">时间安排</view>

    <view class="flex flex-col">
      <view
        v-for="(item, index) in modelValue"
        :key="item.id"
        class="bg-white rounded-16rpx shadow-sm mb-16rpx p-20rpx border border-solid border-[#ebedf0]"
      >
        <view class="flex items-center justify-between mb-12rpx">
          <text class="text-26rpx font-bold text-gray-800">安排 #{{ item.id }}</text>
          <wd-icon name="time" class="text-primary text-32rpx" />
        </view>

        <!-- 第一行：单/双周和星期 -->
        <view class="flex gap-12rpx mb-12rpx">
          <!-- 单/双周选择 -->
          <view class="flex-1 flex flex-col">
            <text class="text-24rpx text-gray-600 mb-8rpx">单/双周</text>
            <picker
              mode="selector"
              :range="weekTypeOptions"
              range-key="label"
              :value="findOptionIndex(weekTypeOptions, item.weekType)"
              @change="(e) => updateValue(index, 'weekType', e.detail.value, weekTypeOptions)"
              :disabled="isDisabled"
              class="h-80rpx bg-white border border-solid border-[#e8e8e8] rounded-8rpx"
            >
              <view
                class="flex items-center justify-between h-80rpx px-20rpx"
                :class="{ 'text-[#999] bg-[#f5f5f5]': isDisabled }"
              >
                <text>
                  {{ weekTypeOptions[findOptionIndex(weekTypeOptions, item.weekType)].label }}
                </text>
                <wd-icon v-if="!isDisabled" name="chevron-down" class="text-24rpx text-[#999]" />
              </view>
            </picker>
          </view>

          <!-- 星期选择 -->
          <view class="flex-1 flex flex-col">
            <text class="text-24rpx text-gray-600 mb-8rpx">星期</text>
            <picker
              mode="selector"
              :range="weekDayOptions"
              range-key="label"
              :value="findOptionIndex(weekDayOptions, item.weekDay)"
              @change="(e) => updateValue(index, 'weekDay', e.detail.value, weekDayOptions)"
              :disabled="isDisabled"
              class="h-80rpx bg-white border border-solid border-[#e8e8e8] rounded-8rpx"
            >
              <view
                class="flex items-center justify-between h-80rpx px-20rpx"
                :class="{ 'text-[#999] bg-[#f5f5f5]': isDisabled }"
              >
                <text>
                  {{ weekDayOptions[findOptionIndex(weekDayOptions, item.weekDay)].label }}
                </text>
                <wd-icon v-if="!isDisabled" name="chevron-down" class="text-24rpx text-[#999]" />
              </view>
            </picker>
          </view>
        </view>

        <!-- 第二行：节次 -->
        <view class="flex flex-col">
          <text class="text-24rpx text-gray-600 mb-8rpx">节次</text>
          <picker
            mode="selector"
            :range="periodOptions"
            range-key="label"
            :value="findOptionIndex(periodOptions, item.period)"
            @change="(e) => updateValue(index, 'period', e.detail.value, periodOptions)"
            :disabled="isDisabled || loading"
            class="h-80rpx bg-white border border-solid border-[#e8e8e8] rounded-8rpx"
          >
            <view
              class="flex items-center justify-between h-80rpx px-20rpx"
              :class="{ 'text-[#999] bg-[#f5f5f5]': isDisabled || loading }"
            >
              <text v-if="loading">加载中...</text>
              <text v-else>
                {{ periodOptions[findOptionIndex(periodOptions, item.period)]?.label || '未选择' }}
              </text>
              <wd-icon
                v-if="!isDisabled && !loading"
                name="chevron-down"
                class="text-24rpx text-[#999]"
              />
              <wd-icon
                v-else-if="loading"
                name="refresh"
                class="text-24rpx text-[#999] animate-spin"
              />
            </view>
          </picker>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
