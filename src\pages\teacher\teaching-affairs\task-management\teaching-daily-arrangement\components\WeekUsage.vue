<script setup lang="ts">
import { computed } from 'vue'
import { useTeachingDailyArrangementStore } from '@/store/teachingDailyArrangement'

interface WeekItem {
  week: number
  hasClass: boolean
}

const props = defineProps<{
  modelValue: WeekItem[]
  /** 教学节次的周次信息 */
  teachingWeeks?: number[]
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: WeekItem[]): void
}>()

// 获取教学任务日常安排store
const dailyArrangementStore = useTeachingDailyArrangementStore()

// 获取disabled状态
const isDisabled = computed(() => dailyArrangementStore.disabled)

// 计算总周数
const totalWeeks = computed(() => dailyArrangementStore.formData.weeks || 0)

// 计算第一组周次（1-10）
const firstGroupWeeks = computed(() => {
  const weeks = Math.min(10, totalWeeks.value)
  return Array.from({ length: weeks }, (_, i) => i + 1)
})

// 计算第二组周次（11-20）
const secondGroupWeeks = computed(() => {
  if (totalWeeks.value <= 10) return []
  const remainingWeeks = Math.min(10, totalWeeks.value - 10)
  return Array.from({ length: remainingWeeks }, (_, i) => i + 11)
})

// 判断某周是否应该被禁用 - 只有当处于disabled状态且是第一周时才禁用
const isWeekDisabled = (weekIndex: number) => {
  return isDisabled.value && weekIndex === 0 // 第一周的索引为0
}

// 判断某周是否是教学节次周
const isTeachingWeek = (weekNumber: number) => {
  return props.teachingWeeks?.includes(weekNumber) || false
}

// 切换周次是否有课
const toggleWeekUsage = (index: number) => {
  // 如果是第一周且处于disabled状态，不允许切换
  if (isWeekDisabled(index)) return

  const newWeekUsage = [...props.modelValue]
  newWeekUsage[index].hasClass = !newWeekUsage[index].hasClass
  emit('update:modelValue', newWeekUsage)
}

// 全选/全不选
const toggleAllWeeks = (select: boolean) => {
  const newWeekUsage = props.modelValue.map((item, index) => {
    // 如果是第一周且处于disabled状态，保持其原有状态
    if (isWeekDisabled(index)) {
      return item
    }
    return {
      ...item,
      hasClass: select,
    }
  })
  emit('update:modelValue', newWeekUsage)
}
</script>

<template>
  <view class="week-usage">
    <view class="flex justify-between items-center mb-16rpx">
      <view class="form-label">课程使用周</view>
      <view class="flex gap-16rpx">
        <wd-button plain size="small" @click="toggleAllWeeks(true)">全选</wd-button>
        <wd-button plain size="small" @click="toggleAllWeeks(false)">全不选</wd-button>
      </view>
    </view>

    <view class="bg-white border border-gray-200 rounded-16rpx overflow-hidden">
      <!-- 前10周 -->
      <view v-if="firstGroupWeeks.length > 0">
        <view class="grid grid-cols-11 bg-gray-100 text-center p-16rpx">
          <text class="text-26rpx font-500 text-gray-700">周次</text>
          <view
            v-for="week in firstGroupWeeks"
            :key="`header-${week}`"
            class="flex flex-col items-center"
          >
            <text
              class="text-26rpx font-500"
              :class="isTeachingWeek(week) ? 'text-[#1890ff]' : 'text-gray-700'"
            >
              {{ week }}
            </text>
            <text v-if="isTeachingWeek(week)" class="text-20rpx text-[#1890ff]">实习</text>
          </view>
        </view>
        <view class="grid grid-cols-11 text-center p-16rpx border-t border-gray-200">
          <text class="text-26rpx font-500 text-gray-700">有课</text>
          <view
            v-for="week in firstGroupWeeks"
            :key="`checkbox-${week}`"
            class="flex justify-center items-center"
          >
            <wd-checkbox
              :model-value="modelValue[week - 1]?.hasClass"
              @change="() => toggleWeekUsage(week - 1)"
              shape="square"
              :disabled="isWeekDisabled(week - 1)"
            />
          </view>
        </view>
      </view>

      <!-- 后10周 -->
      <view v-if="secondGroupWeeks.length > 0">
        <view class="grid grid-cols-11 bg-gray-100 text-center p-16rpx border-t border-gray-200">
          <text class="text-26rpx font-500 text-gray-700">周次</text>
          <view
            v-for="week in secondGroupWeeks"
            :key="`header-${week}`"
            class="flex flex-col items-center"
          >
            <text
              class="text-26rpx font-500"
              :class="isTeachingWeek(week) ? 'text-[#1890ff]' : 'text-gray-700'"
            >
              {{ week }}
            </text>
            <text v-if="isTeachingWeek(week)" class="text-20rpx text-[#1890ff]">实习</text>
          </view>
        </view>
        <view class="grid grid-cols-11 text-center p-16rpx border-t border-gray-200">
          <text class="text-26rpx font-500 text-gray-700">有课</text>
          <view
            v-for="week in secondGroupWeeks"
            :key="`checkbox-${week}`"
            class="flex justify-center items-center"
          >
            <wd-checkbox
              :model-value="modelValue[week - 1]?.hasClass"
              @change="() => toggleWeekUsage(week - 1)"
              shape="square"
              :disabled="isWeekDisabled(week - 1)"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 注释 -->
    <view class="text-24rpx text-gray-600 mt-16rpx leading-36rpx">
      注：根据该班进程信息请在有课的周次上打√，一般情况下学期最后一周为考试周，不安排授课，如有特殊情况，请自行更改。
    </view>
  </view>
</template>

<style lang="scss" scoped>
.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}
</style>
