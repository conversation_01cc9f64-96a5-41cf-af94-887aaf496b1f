<route lang="json5">
{
  style: {
    navigationBarTitleText: '教学日程安排',
  },
}
</route>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import StepOne from './components/StepOne.vue'
import StepTwo from './components/StepTwo.vue'
import StepThree from './components/StepThree.vue'
import StepFour from './components/StepFour.vue'
import { useTeachingTaskStore } from '@/store/teachingTask'
import { useTeachingDailyArrangementStore } from '@/store/teachingDailyArrangement'

// 获取教学任务store
const teachingTaskStore = useTeachingTaskStore()
// 获取教学日常安排store
const teachingDailyArrangementStore = useTeachingDailyArrangementStore()

// 当前步骤
const currentStep = ref(0)
const totalSteps = ref(4)

// 教学任务ID - 优先使用store中的currentTask.id，如果没有则尝试从路由参数获取
const taskId = ref('')

// 获取页面参数
onMounted(() => {
  // 优先使用store中的教学任务ID
  if (teachingTaskStore.currentTask?.id) {
    taskId.value = String(teachingTaskStore.currentTask.id)
    console.log('从store获取教学任务ID:', taskId.value)
  }
})

// 表单数据
const formData = reactive({
  className: '',
  courseName: '',
  courseHours: 0.0,
  weeklyHours: 0.0,
  weeks: 0,
  scheduleConfig: [],
  frequency: 0,
})

// 日程开放时间
const scheduleTime = reactive({
  openTime: '', // 直接使用单个字符串字段
})

// 更新日程开放时间
const updateScheduleTime = (value: typeof scheduleTime) => {
  Object.assign(scheduleTime, value)
}

// 时间安排数据
const timeArrangements = reactive([
  { id: 1, weekType: '', weekDay: '', period: '' },
  { id: 2, weekType: '', weekDay: '', period: '' },
  { id: 3, weekType: '', weekDay: '', period: '' },
])

// 课程使用周数据
const weekUsage = reactive(
  Array.from({ length: 20 }, (_, i) => ({
    week: i + 1,
    hasClass: false,
  })),
)

// 实习周安排
const internshipArrangement = ref('')

// 更新实习周安排
const updateInternshipArrangement = (val: string) => {
  internshipArrangement.value = val
}

// 授课安排数据
const teachingArrangements = reactive([])

// 授课安排设置结果检测数据
const scheduleCheckResults = [
  {
    id: 1,
    date: '2025-03-03',
    week: '第3周',
    weekDay: '星期1',
    period: '1-2节',
    result:
      '该任务主导教师[温倩楠]本时段已安排 物流管理2481 汉语 课程授课，故本时段不可使用！请确认或咨询系教学秘书！',
    isAvailable: false,
  },
  {
    id: 2,
    date: '2025-03-04',
    week: '第3周',
    weekDay: '星期2',
    period: '3-4节',
    result: '本时段可使用！',
    isAvailable: true,
  },
  {
    id: 3,
    date: '2025-03-05',
    week: '第3周',
    weekDay: '星期3',
    period: '3-4节',
    result: '本时段可使用！',
    isAvailable: true,
  },
  {
    id: 4,
    date: '2025-03-24',
    week: '第6周',
    weekDay: '星期1',
    period: '1-2节',
    result:
      '该任务主导教师[温倩楠]本时段已安排 物流管理2481 汉语 课程授课，故本时段不可使用！请确认或咨询系教学秘书！',
    isAvailable: false,
  },
  {
    id: 5,
    date: '2025-03-25',
    week: '第6周',
    weekDay: '星期2',
    period: '3-4节',
    result: '本时段可使用！',
    isAvailable: true,
  },
  {
    id: 6,
    date: '2025-03-26',
    week: '第6周',
    weekDay: '星期3',
    period: '3-4节',
    result: '本时段可使用！',
    isAvailable: true,
  },
]

// 下一步
const nextStep = () => {
  if (currentStep.value < totalSteps.value) {
    currentStep.value++
  }
}

// 上一步
const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 清空日常安排
const clearArrangement = () => {
  // 清空操作逻辑
  uni.showToast({
    title: '已清空日常安排',
    icon: 'none',
  })
}

// 提交表单
const handleSubmit = () => {
  uni.showToast({
    title: '提交成功',
    icon: 'success',
  })
}
</script>

<template>
  <view class="container bg-white p-4">
    <view>
      <!-- 步骤指示器 -->
      <view class="mb-32rpx">
        <wd-steps :active="currentStep" :steps="totalSteps" align-center>
          <wd-step title="任务设置"></wd-step>
          <wd-step title="周次设置"></wd-step>
          <wd-step title="授课安排"></wd-step>
          <wd-step title="日程安排确认"></wd-step>
        </wd-steps>
      </view>

      <!-- 第一步表单 -->
      <StepOne
        v-if="currentStep === 0"
        :form-data="formData"
        :schedule-time="scheduleTime"
        @next="nextStep"
        @clear="clearArrangement"
        @update:form-data="(val) => Object.assign(formData, val)"
        @update:schedule-time="updateScheduleTime"
      />

      <!-- 第二步表单 -->
      <StepTwo
        v-if="currentStep === 1"
        :form-data="formData"
        :time-arrangements="timeArrangements"
        :week-usage="weekUsage"
        :internship-arrangement="internshipArrangement"
        @prev="prevStep"
        @next="nextStep"
        @update:time-arrangements="(val) => Object.assign(timeArrangements, val)"
        @update:week-usage="(val) => Object.assign(weekUsage, val)"
        @update:internship-arrangement="(val) => updateInternshipArrangement(val)"
      />

      <!-- 第三步表单 -->
      <StepThree
        v-if="currentStep === 2"
        :form-data="formData"
        :teaching-arrangements="teachingArrangements"
        :calendar-info="teachingDailyArrangementStore.calendarInfo"
        @prev="prevStep"
        @next="nextStep"
        @update:teaching-arrangements="(val) => Object.assign(teachingArrangements, val)"
      />

      <!-- 第四步表单 -->
      <StepFour
        v-if="currentStep === 3"
        :form-data="formData"
        :schedule-check-results="scheduleCheckResults"
        @prev="prevStep"
        @submit="handleSubmit"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 99vh;
}

.content {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border-radius: 32rpx;
}
</style>
