<script setup lang="ts">
/**
 * 双字段输入组件 - 用于并排显示两个相关字段
 */
const props = defineProps({
  // 主标签
  label: {
    type: String,
    required: true,
  },
  // 是否必填
  required: {
    type: Boolean,
    default: false,
  },
  // 第一个输入框的标签
  firstLabel: {
    type: String,
    default: '计划：',
  },
  // 第一个输入框的值
  firstValue: {
    type: [String, Number],
    default: '',
  },
  // 第一个输入框的占位文本
  firstPlaceholder: {
    type: String,
    default: '请输入',
  },
  // 第二个输入框的标签
  secondLabel: {
    type: String,
    default: '实际：',
  },
  // 第二个输入框的值
  secondValue: {
    type: [String, Number],
    default: '',
  },
  // 第二个输入框的占位文本
  secondPlaceholder: {
    type: String,
    default: '请输入',
  },
})

const emit = defineEmits(['update:firstValue', 'update:secondValue'])

// 更新第一个输入框的值
const updateFirstValue = (e: any) => {
  emit('update:firstValue', e.detail.value)
}

// 更新第二个输入框的值
const updateSecondValue = (e: any) => {
  emit('update:secondValue', e.detail.value)
}
</script>

<template>
  <view class="form-item">
    <view class="form-row-flex">
      <text :class="['form-label', required ? 'required' : '']">{{ label }}</text>
      <view class="form-content">
        <view class="dual-input-row">
          <view class="input-item">
            <text class="small-label">{{ firstLabel }}</text>
            <input
              class="form-input"
              :value="firstValue"
              :placeholder="firstPlaceholder"
              @input="updateFirstValue"
            />
          </view>
          <view class="input-item">
            <text class="small-label">{{ secondLabel }}</text>
            <input
              class="form-input"
              :value="secondValue"
              :placeholder="secondPlaceholder"
              @input="updateSecondValue"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.form-item {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 20rpx;
}

.form-row-flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.form-label {
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333333;

  &.required::before {
    margin-right: 4rpx;
    color: #f5222d;
    content: '*';
  }
}

.form-content {
  box-sizing: border-box;
  width: 100%;
}

.small-label {
  margin-bottom: 6rpx;
  font-size: 24rpx;
  color: #666;
}

.form-input {
  box-sizing: border-box;
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}

.dual-input-row {
  display: flex;
  flex-direction: row;
  gap: 20rpx;
  width: 100%;
}

.input-item {
  display: flex;
  flex: 1;
  flex-direction: column;
}
</style>
