<script setup lang="ts">
/**
 * 期末检查表单组件
 */
import { defineProps, defineEmits, computed } from 'vue'
import FormField from './FormField.vue'
import DualField from './DualField.vue'
import type { TeachingCheckData } from '@/types/teacher'

interface OptionItem {
  value: string | number | boolean
  label: string
}

interface TaskInfo {
  courseName?: string
  [key: string]: any
}

const props = defineProps({
  // 教学任务信息
  taskInfo: {
    type: Object as () => TaskInfo,
    default: () => ({ courseName: '' }),
  },
  // 表单数据
  formData: {
    type: Object as () => TeachingCheckData,
    required: true,
  },
  // 是否选项
  yesNoOptions: {
    type: Array as () => OptionItem[],
    default: () => [],
  },
})

// 定义事件
const emit = defineEmits(['update:formData'])

// 类型安全的表单数据
const typedFormData = computed<TeachingCheckData>(() => props.formData as TeachingCheckData)

// 更新表单数据
const updateFormData = (key: keyof TeachingCheckData, value: string) => {
  const newFormData: TeachingCheckData = {
    ...(props.formData as TeachingCheckData),
    [key]: value,
  }
  emit('update:formData', newFormData)
}
</script>

<template>
  <view class="inspection-form">
    <view class="form-section">
      <view class="section-title">基本信息</view>

      <view class="form-item">
        <view class="form-row-flex">
          <text class="form-label">教学任务信息</text>
          <view class="form-content">
            <view class="form-static-text">
              {{ taskInfo?.courseName || '未知课程' }}
            </view>
          </view>
        </view>
      </view>

      <DualField
        label="授课计划执行"
        required
        firstLabel="计划课时："
        :firstValue="typedFormData.qmjxjctxx0"
        firstPlaceholder="请输入计划课时"
        secondLabel="实际完成课时："
        :secondValue="typedFormData.qmjxjctxx1"
        secondPlaceholder="请输入实际完成课时"
        @update:firstValue="updateFormData('qmjxjctxx0', $event)"
        @update:secondValue="updateFormData('qmjxjctxx1', $event)"
      />

      <FormField
        label="是否本学期新增的双师教师"
        type="picker"
        :options="yesNoOptions"
        :model-value="typedFormData.qmjxjctxx8"
        @update:model-value="updateFormData('qmjxjctxx8', $event)"
      />
    </view>

    <view class="form-section">
      <view class="section-title">作业与教学</view>

      <DualField
        label="作业布置及批改"
        required
        firstLabel="计划次数："
        :firstValue="typedFormData.qmjxjctxx2"
        firstPlaceholder="请输入计划次数"
        secondLabel="实际布置次数："
        :secondValue="typedFormData.qmjxjctxx3"
        secondPlaceholder="请输入实际布置次数"
        @update:firstValue="updateFormData('qmjxjctxx2', $event)"
        @update:secondValue="updateFormData('qmjxjctxx3', $event)"
      />

      <FormField
        label="全批全改次数"
        required
        :model-value="typedFormData.qmjxjctxx4"
        placeholder="请输入全批全改次数"
        @update:model-value="updateFormData('qmjxjctxx4', $event)"
      />

      <FormField
        label="是否按规定完成教案编写"
        type="picker"
        :options="yesNoOptions"
        :model-value="typedFormData.qmjxjctxx5"
        @update:model-value="updateFormData('qmjxjctxx5', $event)"
      />

      <FormField
        label="听课完成次数"
        required
        :model-value="typedFormData.qmjxjctxx6"
        placeholder="请输入听课完成次数"
        @update:model-value="updateFormData('qmjxjctxx6', $event)"
      />
    </view>

    <view class="form-section">
      <view class="section-title">教研与发展</view>

      <FormField
        label="何时参加何种进修"
        :model-value="typedFormData.qmjxjctxx7"
        placeholder="请输入进修信息"
        @update:model-value="updateFormData('qmjxjctxx7', $event)"
      />

      <FormField
        label="参加何种教科研项目及完成情况"
        :model-value="typedFormData.qmjxjctxx9"
        placeholder="请输入教科研项目信息"
        @update:model-value="updateFormData('qmjxjctxx9', $event)"
      />

      <FormField
        label="参编何种高职教材及完成情况"
        :model-value="typedFormData.qmjxjctxx10"
        placeholder="请输入教材编写信息"
        @update:model-value="updateFormData('qmjxjctxx10', $event)"
      />

      <FormField
        label="开设讲座的主题、对象及时间"
        :model-value="typedFormData.qmjxjctxx11"
        placeholder="请输入讲座信息"
        @update:model-value="updateFormData('qmjxjctxx11', $event)"
      />
    </view>

    <view class="form-section">
      <view class="section-title">规范要求</view>

      <FormField
        label="是否按要求填《教师工作手册》"
        type="picker"
        :options="yesNoOptions"
        :model-value="typedFormData.qmjxjctxx12"
        @update:model-value="updateFormData('qmjxjctxx12', $event)"
      />

      <FormField
        label="是否按规定完成期末拟卷任务"
        type="picker"
        :options="yesNoOptions"
        :model-value="typedFormData.qmjxjctxx14"
        @update:model-value="updateFormData('qmjxjctxx14', $event)"
      />

      <FormField
        label="出勤情况"
        required
        :model-value="typedFormData.qmjxjctxx13"
        placeholder="请输入出勤情况"
        @update:model-value="updateFormData('qmjxjctxx13', $event)"
      />

      <FormField
        label="备注"
        type="textarea"
        layout="column"
        :model-value="typedFormData.qmjxjctxx15"
        placeholder="请输入备注信息"
        @update:model-value="updateFormData('qmjxjctxx15', $event)"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.inspection-form {
  width: 100%;
}

.form-section {
  box-sizing: border-box;
  padding-bottom: 24rpx;
  margin-bottom: 24rpx;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  padding-bottom: 16rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  border-bottom: 1px solid #f0f0f0;
}

.form-item {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 20rpx;
}

.form-row-flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.form-label {
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333333;

  &.required::before {
    margin-right: 4rpx;
    color: #f5222d;
    content: '*';
  }
}

.form-content {
  box-sizing: border-box;
  width: 100%;
}

.form-static-text {
  padding: 10rpx 0;
  font-size: 28rpx;
  color: #666;
}
</style>
