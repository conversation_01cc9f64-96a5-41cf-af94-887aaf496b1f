<script setup lang="ts">
/**
 * 通用表单字段组件
 */
import { computed } from 'vue'

interface OptionItem {
  value: string | number | boolean
  label: string
}

const props = defineProps({
  // 字段标签
  label: {
    type: String,
    required: true,
  },
  // 是否必填
  required: {
    type: Boolean,
    default: false,
  },
  // 字段类型：input, textarea, picker
  type: {
    type: String,
    default: 'input',
  },
  // 字段值
  modelValue: {
    type: [String, Number, Boolean],
    default: '',
  },
  // 占位文本
  placeholder: {
    type: String,
    default: '请输入',
  },
  // 选择器选项
  options: {
    type: Array as () => OptionItem[],
    default: () => [],
  },
  // 列布局：column 垂直排列，flex 水平排列
  layout: {
    type: String,
    default: 'flex',
  },
})

const emit = defineEmits(['update:modelValue', 'change'])

// 计算选择器显示值
const pickerDisplayValue = computed(() => {
  if (!Array.isArray(props.options) || props.options.length === 0) return '请选择'

  const selectedOption = props.options.find((item) => item.value === props.modelValue)
  return selectedOption ? selectedOption.label : '请选择'
})

// 更新输入值
const updateValue = (e: any) => {
  emit('update:modelValue', e.detail.value)
}

// 更新选择器值
const updatePickerValue = (e: any) => {
  const index = e.detail.value
  if (props.options && props.options[index]) {
    emit('update:modelValue', props.options[index].value)
    emit('change', props.options[index])
  }
}
</script>

<template>
  <view class="form-item">
    <view :class="['form-row', layout === 'column' ? 'form-row-column' : 'form-row-flex']">
      <text :class="['form-label', required ? 'required' : '']">{{ label }}</text>
      <view class="form-content">
        <!-- 输入框 -->
        <template v-if="type === 'input'">
          <input
            class="form-input"
            :value="modelValue as string"
            :placeholder="placeholder"
            @input="updateValue"
          />
        </template>

        <!-- 文本域 -->
        <template v-else-if="type === 'textarea'">
          <textarea
            class="form-textarea"
            :value="modelValue as string"
            :placeholder="placeholder"
            @input="updateValue"
          />
        </template>

        <!-- 选择器 -->
        <template v-else-if="type === 'picker'">
          <picker :range="options" range-key="label" @change="updatePickerValue">
            <view class="picker-display">
              <text>{{ pickerDisplayValue }}</text>
              <wd-icon name="chevron-down" class="text-gray-500" size="16px" />
            </view>
          </picker>
        </template>

        <slot></slot>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.form-item {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 20rpx;
}

.form-row {
  box-sizing: border-box;
  width: 100%;
}

.form-row-flex {
  display: flex;
  flex-direction: column;
}

.form-row-column {
  display: flex;
  flex-direction: column;
}

.form-label {
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333333;

  &.required::before {
    margin-right: 4rpx;
    color: #f5222d;
    content: '*';
  }
}

.form-content {
  box-sizing: border-box;
  width: 100%;
}

.form-input {
  box-sizing: border-box;
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;

  &:disabled {
    color: #999999;
    cursor: not-allowed;
    background-color: #f5f5f5;
  }
}

.form-textarea {
  box-sizing: border-box;
  width: 100%;
  height: 180rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;

  &:disabled {
    color: #999999;
    cursor: not-allowed;
    background-color: #f5f5f5;
  }
}

.picker-display {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}

.text-gray-500 {
  color: #888;
}
</style>
