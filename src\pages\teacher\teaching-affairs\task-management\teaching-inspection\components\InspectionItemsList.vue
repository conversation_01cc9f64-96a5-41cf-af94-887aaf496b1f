<script setup lang="ts">
/**
 * 自查项目列表组件
 */
interface InspectionItem {
  id: number
  title: string
  status: boolean
  description: string
}

const props = defineProps({
  // 自查项目列表
  items: {
    type: Array as () => InspectionItem[],
    required: true,
  },
})

const emit = defineEmits(['toggle-item'])

// 切换项目状态
const toggleItem = (id: number) => {
  emit('toggle-item', id)
}
</script>

<template>
  <view class="inspection-list">
    <view class="form-section">
      <view class="section-title">自查项目列表</view>
      <view class="instruction-text">请完成以下自查项目，点击项目可切换完成状态</view>

      <view
        v-for="item in items"
        :key="item.id"
        class="inspection-item"
        @click="toggleItem(item.id)"
      >
        <view class="item-header">
          <view class="item-title">{{ item.title }}</view>
          <wd-icon
            :name="item.status ? 'check-bold' : 'close-outline'"
            :color="item.status ? '#07c160' : '#999'"
            size="18"
          />
        </view>
        <view class="item-description">{{ item.description }}</view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.inspection-list {
  width: 100%;
}

.form-section {
  box-sizing: border-box;
  padding-bottom: 24rpx;
  margin-bottom: 24rpx;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  padding-bottom: 16rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  border-bottom: 1px solid #f0f0f0;
}

.instruction-text {
  padding: 16rpx;
  margin-bottom: 16rpx;
  font-size: 26rpx;
  line-height: 1.8;
  color: #666;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.inspection-item {
  padding: 20rpx;
  margin-bottom: 16rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  transition: all 0.3s;

  &:active {
    background-color: #eef5ff;
  }
}

.item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8rpx;
}

.item-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.item-description {
  font-size: 24rpx;
  color: #666;
}
</style>
