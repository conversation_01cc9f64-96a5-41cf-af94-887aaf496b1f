<script setup lang="ts">
/**
 * 期中检查表单组件
 */
import { defineProps, defineEmits, computed } from 'vue'
import FormField from './FormField.vue'
import DualField from './DualField.vue'
import type { TeachingCheckData } from '@/types/teacher'

interface OptionItem {
  value: string | number | boolean
  label: string
}

interface TaskInfo {
  courseName?: string
  [key: string]: any
}

const props = defineProps({
  // 教学任务信息
  taskInfo: {
    type: Object as () => TaskInfo,
    default: () => ({ courseName: '' }),
  },
  // 表单数据
  formData: {
    type: Object as () => TeachingCheckData,
    required: true,
  },
  // 是否选项
  yesNoOptions: {
    type: Array as () => OptionItem[],
    default: () => [],
  },
  // 接受情况选项
  acceptanceOptions: {
    type: Array as () => OptionItem[],
    default: () => [],
  },
})

// 定义事件
const emit = defineEmits(['update:formData'])

// 类型安全的表单数据
const typedFormData = computed<TeachingCheckData>(() => props.formData as TeachingCheckData)

// 更新表单数据
const updateFormData = (key: keyof TeachingCheckData, value: string) => {
  const newFormData: TeachingCheckData = {
    ...(props.formData as TeachingCheckData),
    [key]: value,
  }
  emit('update:formData', newFormData)
}
</script>

<template>
  <view class="inspection-form">
    <view class="form-section">
      <view class="section-title">基本信息</view>

      <view class="form-item">
        <view class="form-row-flex">
          <text class="form-label">教学任务信息</text>
          <view class="form-content">
            <view class="form-static-text">
              {{ taskInfo?.courseName || '未知课程' }}
            </view>
          </view>
        </view>
      </view>

      <DualField
        label="教学进度(课时)(截至第10周)"
        required
        firstLabel="计划："
        :firstValue="typedFormData.qzjxjctxx0"
        firstPlaceholder="请输入计划课时"
        secondLabel="超前或滞后："
        :secondValue="typedFormData.qzjxjctxx1"
        secondPlaceholder="请输入超前或滞后课时"
        @update:firstValue="updateFormData('qzjxjctxx0', $event)"
        @update:secondValue="updateFormData('qzjxjctxx1', $event)"
      />
    </view>

    <view class="form-section">
      <view class="section-title">作业与教学</view>

      <DualField
        label="作业批改"
        required
        firstLabel="计划："
        :firstValue="typedFormData.qzjxjctxx2"
        firstPlaceholder="请输入计划作业批改"
        secondLabel="实际："
        :secondValue="typedFormData.qzjxjctxx3"
        secondPlaceholder="请输入实际作业批改"
        @update:firstValue="updateFormData('qzjxjctxx2', $event)"
        @update:secondValue="updateFormData('qzjxjctxx3', $event)"
      />

      <FormField
        label="批改情况"
        required
        type="textarea"
        :model-value="typedFormData.qzjxjctxx4"
        placeholder="请输入批改情况"
        @update:model-value="updateFormData('qzjxjctxx4', $event)"
      />

      <DualField
        label="实验（次）"
        firstLabel="计划："
        :firstValue="typedFormData.qzjxjctxx5"
        firstPlaceholder="请输入计划实验次数"
        secondLabel="实际："
        :secondValue="typedFormData.qzjxjctxx6"
        secondPlaceholder="请输入实际实验次数"
        @update:firstValue="updateFormData('qzjxjctxx5', $event)"
        @update:secondValue="updateFormData('qzjxjctxx6', $event)"
      />

      <DualField
        label="辅导（次）"
        firstLabel="计划："
        :firstValue="typedFormData.qzjxjctxx7"
        firstPlaceholder="请输入计划辅导次数"
        secondLabel="实际："
        :secondValue="typedFormData.qzjxjctxx8"
        secondPlaceholder="请输入实际辅导次数"
        @update:firstValue="updateFormData('qzjxjctxx7', $event)"
        @update:secondValue="updateFormData('qzjxjctxx8', $event)"
      />
    </view>

    <view class="form-section">
      <view class="section-title">考核与评估</view>

      <DualField
        label="测验（次）"
        firstLabel="小测："
        :firstValue="typedFormData.qzjxjctxx9"
        firstPlaceholder="请输入小测次数"
        secondLabel="阶段考："
        :secondValue="typedFormData.qzjxjctxx10"
        secondPlaceholder="请输入阶段考次数"
        @update:firstValue="updateFormData('qzjxjctxx9', $event)"
        @update:secondValue="updateFormData('qzjxjctxx10', $event)"
      />

      <FormField
        label="学生接受情况"
        type="picker"
        :options="acceptanceOptions"
        :model-value="typedFormData.qzjxjctxx11"
        @update:model-value="updateFormData('qzjxjctxx11', $event)"
      />

      <FormField
        label="听课次数"
        :model-value="typedFormData.qzjxjctxx12"
        placeholder="请输入听课次数"
        @update:model-value="updateFormData('qzjxjctxx12', $event)"
      />

      <FormField
        label="教案编写情况"
        type="textarea"
        :model-value="typedFormData.qzjxjctxx13"
        placeholder="请输入教案编写情况"
        @update:model-value="updateFormData('qzjxjctxx13', $event)"
      />

      <FormField
        label="备注"
        type="textarea"
        layout="column"
        :model-value="typedFormData.qzjxjctxx14"
        placeholder="请输入备注信息"
        @update:model-value="updateFormData('qzjxjctxx14', $event)"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.inspection-form {
  width: 100%;
}

.form-section {
  box-sizing: border-box;
  padding-bottom: 24rpx;
  margin-bottom: 24rpx;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  padding-bottom: 16rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  border-bottom: 1px solid #f0f0f0;
}

.form-item {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 20rpx;
}

.form-row-flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.form-label {
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333333;

  &.required::before {
    margin-right: 4rpx;
    color: #f5222d;
    content: '*';
  }
}

.form-content {
  box-sizing: border-box;
  width: 100%;
}

.form-static-text {
  padding: 10rpx 0;
  font-size: 28rpx;
  color: #666;
}
</style>
