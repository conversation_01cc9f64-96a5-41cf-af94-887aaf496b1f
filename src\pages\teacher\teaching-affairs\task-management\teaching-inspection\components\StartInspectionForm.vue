<script setup lang="ts">
/**
 * 期初检查表单组件
 */
import { defineProps, defineEmits, computed } from 'vue'
import FormField from './FormField.vue'
import type { TeachingCheckData } from '@/types/teacher'

interface OptionItem {
  value: string | number | boolean
  label: string
}

interface TaskInfo {
  courseName?: string
  [key: string]: any
}

const props = defineProps({
  // 教学任务信息
  taskInfo: {
    type: Object as () => TaskInfo,
    default: () => ({ courseName: '' }),
  },
  // 表单数据
  formData: {
    type: Object as () => TeachingCheckData,
    required: true,
  },
  // 是否选项
  yesNoOptions: {
    type: Array as () => OptionItem[],
    default: () => [],
  },
})

// 定义事件
const emit = defineEmits(['update:formData'])

// 类型安全的表单数据
const typedFormData = computed<TeachingCheckData>(() => props.formData as TeachingCheckData)

// 更新表单数据
const updateFormData = (key: keyof TeachingCheckData, value: string) => {
  const newFormData: TeachingCheckData = {
    ...(props.formData as TeachingCheckData),
    [key]: value,
  }
  emit('update:formData', newFormData)
}
</script>

<template>
  <view class="inspection-form">
    <view class="form-section">
      <view class="section-title">基本信息</view>

      <view class="form-item">
        <view class="form-row-flex">
          <text class="form-label">教学任务信息</text>
          <view class="form-content">
            <view class="form-static-text">
              {{ taskInfo.courseName || '未知课程' }}
            </view>
          </view>
        </view>
      </view>

      <FormField
        label="备课周数"
        required
        :model-value="typedFormData.qcjxjctxx1"
        placeholder="请输入备课周数"
        @update:model-value="updateFormData('qcjxjctxx1', $event)"
      />
    </view>

    <view class="form-section">
      <view class="section-title">教案情况</view>

      <FormField
        label="授课计划格式是否规范"
        type="picker"
        :options="yesNoOptions"
        :model-value="typedFormData.qcjxjctxx0"
        @update:model-value="updateFormData('qcjxjctxx0', $event)"
      />

      <FormField
        label="是否新教案"
        type="picker"
        :options="yesNoOptions"
        :model-value="typedFormData.qcjxjctxx2"
        @update:model-value="updateFormData('qcjxjctxx2', $event)"
      />

      <FormField
        label="是否详案"
        type="picker"
        :options="yesNoOptions"
        :model-value="typedFormData.qcjxjctxx3"
        @update:model-value="updateFormData('qcjxjctxx3', $event)"
      />

      <FormField
        label="教案首页填写是否规范"
        type="picker"
        :options="yesNoOptions"
        :model-value="typedFormData.qcjxjctxx4"
        @update:model-value="updateFormData('qcjxjctxx4', $event)"
      />
    </view>

    <view class="form-section">
      <view class="section-title">教学内容</view>

      <FormField
        label="是否体现课程教学大纲的要求"
        type="picker"
        :options="yesNoOptions"
        :model-value="typedFormData.qcjxjctxx5"
        @update:model-value="updateFormData('qcjxjctxx5', $event)"
      />

      <FormField
        label="是否体现学科或生产的最新成果"
        type="picker"
        :options="yesNoOptions"
        :model-value="typedFormData.qcjxjctxx6"
        @update:model-value="updateFormData('qcjxjctxx6', $event)"
      />

      <FormField
        label="突出技术应用能力培养情况"
        required
        type="textarea"
        layout="column"
        :model-value="typedFormData.qcjxjctxx7"
        placeholder="请输入技术应用能力培养情况"
        @update:model-value="updateFormData('qcjxjctxx7', $event)"
      />

      <FormField
        label="备注"
        type="textarea"
        layout="column"
        :model-value="typedFormData.qcjxjctxx8"
        placeholder="请输入备注信息"
        @update:model-value="updateFormData('qcjxjctxx8', $event)"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.inspection-form {
  width: 100%;
}

.form-section {
  box-sizing: border-box;
  padding-bottom: 24rpx;
  margin-bottom: 24rpx;
  border-bottom: 1px solid #f0f0f0;
}

.section-title {
  padding-bottom: 16rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  border-bottom: 1px solid #f0f0f0;
}

.form-item {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 20rpx;
}

.form-row-flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.form-label {
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333333;

  &.required::before {
    margin-right: 4rpx;
    color: #f5222d;
    content: '*';
  }
}

.form-content {
  box-sizing: border-box;
  width: 100%;
}

.form-static-text {
  padding: 10rpx 0;
  font-size: 28rpx;
  color: #666;
}
</style>
