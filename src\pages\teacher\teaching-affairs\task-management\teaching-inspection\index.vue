<route lang="json5">
{
  style: {
    navigationBarTitleText: '教学检查自查',
  },
}
</route>

<script setup lang="ts">
import { ref, onMounted, reactive, computed } from 'vue'
import { useToast } from 'wot-design-uni'
import { useTeachingTaskStore } from '@/store/teachingTask'
import { getTeachingCheck, teachingCheckSave } from '@/service/teacher'
import type { TeachingCheckData } from '@/types/teacher'
import FormWithApproval from '@/components/FormWithApproval/index.vue'
import FormField from './components/FormField.vue'
import DualField from './components/DualField.vue'
import StartInspectionForm from './components/StartInspectionForm.vue'
import MiddleInspectionForm from './components/MiddleInspectionForm.vue'
import EndInspectionForm from './components/EndInspectionForm.vue'
import InspectionItemsList from './components/InspectionItemsList.vue'

// toast提示
const toast = useToast()

// 获取当前教学任务信息
const teachingTaskStore = useTeachingTaskStore()
const currentTask = computed(() => teachingTaskStore.currentTask)

// 加载状态
const loading = ref<boolean>(false)

// 检查阶段选择
const inspectionStage = ref<string>('start')
const stageOptions = [
  { value: 'start', label: '期初检查自查' },
  { value: 'middle', label: '期中检查自查' },
  { value: 'end', label: '期末检查自查' },
]

// 通用选项
const yesNoOptions = [
  { value: '是', label: '是' },
  { value: '否', label: '否' },
]

const acceptanceOptions = [
  { value: '好', label: '好' },
  { value: '中', label: '中' },
  { value: '差', label: '差' },
]

// 期初检查表单数据
const startInspectionForm = reactive<TeachingCheckData>({
  qcjxjctxx0: '', // 是否规范
  qcjxjctxx1: '', // 备课周数
  qcjxjctxx2: '', // 是否新教案
  qcjxjctxx3: '', // 是否详案
  qcjxjctxx4: '', // 教案首页填写是否规范
  qcjxjctxx5: '', // 是否体现课程教学大纲的要求
  qcjxjctxx6: '', // 是否体现学科或生产的最新成果
  qcjxjctxx7: '', // 突出技术应用能力培养情况
  qcjxjctxx8: '', // 备注
})

// 期中检查表单数据 - 字段名优化
const middleInspectionForm = reactive<TeachingCheckData>({
  qzjxjctxx0: '', // 计划课时
  qzjxjctxx1: '', // 超前或滞后课时
  qzjxjctxx2: '', // 计划作业批改
  qzjxjctxx3: '', // 实际作业批改
  qzjxjctxx4: '', // 批改情况
  qzjxjctxx5: '', // 计划实验次数
  qzjxjctxx6: '', // 实际实验次数
  qzjxjctxx7: '', // 计划辅导次数
  qzjxjctxx8: '', // 实际辅导次数
  qzjxjctxx9: '', // 小测次数
  qzjxjctxx10: '', // 阶段考次数
  qzjxjctxx11: '', // 学生接受情况
  qzjxjctxx12: '', // 听课次数
  qzjxjctxx13: '', // 教案编写情况
  qzjxjctxx14: '', // 备注
})

// 期末检查表单数据
const endInspectionForm = reactive<TeachingCheckData>({
  qmjxjctxx0: '', // 计划课时
  qmjxjctxx1: '', // 实际完成课时
  qmjxjctxx2: '', // 作业布置计划次数
  qmjxjctxx3: '', // 作业布置实际次数
  qmjxjctxx4: '', // 作业全批全改次数
  qmjxjctxx5: '', // 是否按规定完成教案编写
  qmjxjctxx6: '', // 听课完成次数
  qmjxjctxx7: '', // 何时参加何种进修
  qmjxjctxx8: '', // 是否本学期新增的"双师"教师
  qmjxjctxx9: '', // 参加何种教科研项目及完成情况
  qmjxjctxx10: '', // 参编何种高职教材及完成情况
  qmjxjctxx11: '', // 开设讲座的主题、对象及时间
  qmjxjctxx12: '', // 是否按要求填《教师工作手册》
  qmjxjctxx13: '', // 出勤情况
  qmjxjctxx14: '', // 是否按规定完成期末拟卷任务
  qmjxjctxx15: '', // 备注
})

// 检查数据记录ID
const checkRecordId = ref<string>('')

// 加载教学检查数据
const loadTeachingCheck = async () => {
  if (!currentTask.value?.id) {
    toast.error('未获取到课程信息')
    return
  }

  loading.value = true
  try {
    // 获取当前操作类型
    const opType = getCurrentOpType()
    // 获取教学检查数据
    const res = await getTeachingCheck({
      jxrwid: currentTask.value.id,
      opType,
    })

    // 输出接口返回的数据结构，帮助调试
    console.log('教学检查数据接口返回:', res)

    // 如果有数据，则填充表单
    if (res && res.data) {
      console.log('填充表单数据:', res.data)
      // 根据操作类型填充不同的表单
      switch (opType) {
        case 1: // 期初检查
          Object.assign(startInspectionForm, res.data)
          console.log('期初检查表单数据:', startInspectionForm)
          break
        case 2: // 期中检查
          Object.assign(middleInspectionForm, res.data)
          console.log('期中检查表单数据:', middleInspectionForm)
          break
        case 3: // 期末检查
          Object.assign(endInspectionForm, res.data)
          console.log('期末检查表单数据:', endInspectionForm)
          break
      }
      // 保存记录ID
      if (res.id) {
        checkRecordId.value = res.id
      }
    }
  } catch (error) {
    console.error('加载教学检查数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 根据当前阶段获取操作类型
const getCurrentOpType = (): number => {
  switch (inspectionStage.value) {
    case 'start':
      return 1 // 期初检查
    case 'middle':
      return 2 // 期中检查
    case 'end':
      return 3 // 期末检查
    default:
      return 1
  }
}

// 根据当前阶段获取表单数据
const getCurrentForm = (): TeachingCheckData => {
  switch (inspectionStage.value) {
    case 'start':
      return startInspectionForm
    case 'middle':
      return middleInspectionForm
    case 'end':
      return endInspectionForm
    default:
      return startInspectionForm
  }
}

// 表单验证
const validateForm = (): boolean => {
  const form = getCurrentForm()
  switch (inspectionStage.value) {
    case 'start':
      if (!form.qcjxjctxx1) {
        toast.error('请输入备课周数')
        return false
      }
      if (!form.qcjxjctxx7) {
        toast.error('请输入技术应用能力培养情况')
        return false
      }
      break
    case 'middle':
      if (!form.qzjxjctxx0) {
        toast.error('请输入计划课时')
        return false
      }
      break
    case 'end':
      if (!form.qmjxjctxx0) {
        toast.error('请输入计划课时')
        return false
      }
      if (!form.qmjxjctxx1) {
        toast.error('请输入实际完成课时')
        return false
      }
      break
  }
  return true
}

// 处理阶段切换
const handleStageChange = () => {
  // 重新加载检查数据
  loadTeachingCheck()
}

// 提交自查结果
const submitInspection = async () => {
  if (!currentTask.value?.id) {
    toast.error('未获取到课程信息')
    return
  }

  // 表单验证
  if (!validateForm()) {
    return
  }

  loading.value = true
  try {
    // 获取当前表单数据
    const formData = getCurrentForm()
    // 获取当前操作类型
    const opType = getCurrentOpType()
    // 获取当前检查项目数量
    let xxxnum = 0
    switch (opType) {
      case 1: // 期初检查
        xxxnum = 9
        break
      case 2: // 期中检查
        xxxnum = 15
        break
      case 3: // 期末检查
        xxxnum = 16
        break
      default:
        xxxnum = 9
    }

    // 提交当前表单数据
    await teachingCheckSave(currentTask.value.id, formData, opType, xxxnum)
    toast.success('提交成功')
  } catch (error) {
    console.error('提交教学检查数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 自查项目列表
const inspectionItems = reactive([
  {
    id: 1,
    title: '教学计划完成情况',
    status: false,
    description: '检查教学计划的制定和执行情况',
  },
  {
    id: 2,
    title: '教材使用情况',
    status: false,
    description: '检查教材的选用和使用情况',
  },
  {
    id: 3,
    title: '教学进度执行情况',
    status: false,
    description: '检查教学进度是否按计划执行',
  },
  {
    id: 4,
    title: '教学质量评估',
    status: false,
    description: '评估教学质量和学生学习效果',
  },
  {
    id: 5,
    title: '教学资源使用情况',
    status: false,
    description: '检查教学资源的准备和使用情况',
  },
  {
    id: 6,
    title: '学生出勤情况',
    status: false,
    description: '检查学生的出勤和课堂参与情况',
  },
])

// 切换自查项目状态
const toggleItemStatus = (id: number) => {
  const item = inspectionItems.find((item) => item.id === id)
  if (item) {
    item.status = !item.status
  }
}

// 页面加载
onMounted(() => {
  if (!currentTask.value) {
    toast.error('未获取到课程信息')
    return
  }

  // 加载检查数据
  loadTeachingCheck()
})
</script>

<template>
  <view class="container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <wd-loading size="30" />
      <text class="loading-text">加载中...</text>
    </view>

    <template v-else>
      <!-- 课程信息 -->
      <view class="course-info-card">
        <view class="course-title">{{ currentTask?.courseName || '未知课程' }}</view>
        <view class="course-meta">
          <text>{{ currentTask?.courseCode || '未知编码' }}</text>
          <text class="divider">|</text>
          <text>{{ currentTask?.className || '未知班级' }}</text>
        </view>
      </view>

      <!-- 检查阶段选择器 -->
      <view class="stage-selector-card">
        <wd-tabs v-model="inspectionStage" @change="handleStageChange">
          <wd-tab
            v-for="item in stageOptions"
            :key="item.value"
            :title="item.label"
            :name="item.value"
          />
        </wd-tabs>
      </view>

      <!-- 期初检查表单 -->
      <FormWithApproval
        v-if="inspectionStage === 'start'"
        :id="checkRecordId"
        code="qcjxjc"
        :show-workflow="true"
      >
        <template #form-content>
          <StartInspectionForm
            :task-info="currentTask"
            :form-data="startInspectionForm"
            :yes-no-options="yesNoOptions"
            @update:form-data="(newData) => Object.assign(startInspectionForm, newData)"
          />
        </template>

        <template #form-buttons>
          <view class="footer-actions">
            <wd-button
              type="primary"
              block
              custom-style="background-color: #3a8eff; border-color: #3a8eff; border-radius: 8rpx;"
              @click="submitInspection"
            >
              提交期初检查
            </wd-button>
          </view>
        </template>
      </FormWithApproval>

      <!-- 期中检查表单 -->
      <FormWithApproval
        v-else-if="inspectionStage === 'middle'"
        :id="checkRecordId"
        code="qzjxjc"
        :show-workflow="true"
      >
        <template #form-content>
          <MiddleInspectionForm
            :task-info="currentTask"
            :form-data="middleInspectionForm"
            :yes-no-options="yesNoOptions"
            :acceptance-options="acceptanceOptions"
            @update:form-data="(newData) => Object.assign(middleInspectionForm, newData)"
          />
        </template>

        <template #form-buttons>
          <view class="footer-actions">
            <wd-button
              type="primary"
              block
              custom-style="background-color: #3a8eff; border-color: #3a8eff; border-radius: 8rpx;"
              @click="submitInspection"
            >
              提交期中检查
            </wd-button>
          </view>
        </template>
      </FormWithApproval>

      <!-- 期末检查表单 -->
      <FormWithApproval
        v-else-if="inspectionStage === 'end'"
        :id="checkRecordId"
        code="qmjxjc"
        :show-workflow="true"
      >
        <template #form-content>
          <EndInspectionForm
            :task-info="currentTask"
            :form-data="endInspectionForm"
            :yes-no-options="yesNoOptions"
            @update:form-data="(newData) => Object.assign(endInspectionForm, newData)"
          />
        </template>

        <template #form-buttons>
          <view class="footer-actions">
            <wd-button
              type="primary"
              block
              custom-style="background-color: #3a8eff; border-color: #3a8eff; border-radius: 8rpx;"
              @click="submitInspection"
            >
              提交期末检查
            </wd-button>
          </view>
        </template>
      </FormWithApproval>

      <!-- 其他阶段的检查项目列表 -->
      <view v-else class="content-card">
        <view class="card-content">
          <InspectionItemsList :items="inspectionItems" @toggle-item="toggleItemStatus" />
        </view>

        <!-- 底部按钮 -->
        <view class="footer-actions">
          <wd-button
            type="primary"
            block
            custom-style="background-color: #3a8eff; border-color: #3a8eff; border-radius: 8rpx;"
            @click="submitInspection"
          >
            提交自查结果
          </wd-button>
        </view>
      </view>
    </template>

    <!-- Toast提示 -->
    <wd-toast />
  </view>
</template>

<style lang="scss" scoped>
.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  padding: 24rpx;
  background-color: #f7f8fc;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
}

.loading-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #999;
}

.course-info-card {
  box-sizing: border-box;
  padding: 24rpx;
  margin-bottom: 24rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.course-title {
  margin-bottom: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.course-meta {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.divider {
  margin: 0 16rpx;
  color: #ddd;
}

.stage-selector-card {
  box-sizing: border-box;
  padding: 16rpx;
  margin-bottom: 24rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.content-card {
  box-sizing: border-box;
  padding: 24rpx;
  margin-bottom: 24rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-content {
  margin-bottom: 24rpx;
}

.footer-actions {
  margin-top: 24rpx;
  margin-bottom: 24rpx;
}
</style>
