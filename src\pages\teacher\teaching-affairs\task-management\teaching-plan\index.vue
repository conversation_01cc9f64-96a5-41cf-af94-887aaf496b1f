<route lang="json5">
{
  style: {
    navigationBarTitleText: '授课计划表',
  },
}
</route>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, nextTick } from 'vue'
import { useTeachingTaskStore } from '@/store/teachingTask'
import { getTeachingPlanTable } from '@/service/teachingTask'
import FormWithApproval from '@/components/FormWithApproval/index.vue'

// 获取当前教学任务信息
const teachingTaskStore = useTeachingTaskStore()
const currentTask = computed(() => teachingTaskStore.currentTask)

// 授课计划状态
const planStatus = ref('未制定')
const isLoading = ref(false)
const planHtmlContent = ref('')

// 审批相关状态
const planId = ref<number | null>(null) // 授课计划ID，用于审批流程
const showWorkflow = ref(true) // 是否显示审批流程
const formWithApprovalRef = ref() // FormWithApproval组件引用

// iframe相关状态
const showIframe = ref(false) // 是否显示iframe
const iframeUrl = ref('') // iframe的URL
const showEmptyState = ref(false) // 是否显示空状态

// URL处理函数
function processUrl(inputUrl: string): string {
  if (inputUrl.includes('http')) {
    return inputUrl
  } else {
    // 在不同平台获取当前路径
    let result = inputUrl

    // #ifdef H5
    const currentLocation = document.location.origin

    // const currentLocation = 'https://jw-dev-minio.fjpit.com'
    result = `${currentLocation}${inputUrl.startsWith('/') ? '' : '/'}${inputUrl}`
    // #endif

    return result
  }
}

// 从timeline数据中提取文件URL
const extractFileUrl = (timelineData: any): string => {
  try {
    const formData = timelineData?.formData
    if (formData?.file) {
      // 用,分割取第一个，再用|分割取第一个
      const fileStr = formData.file.split(',')[0]
      const fileName = fileStr.split('|')[0]
      return processUrl(fileName)
    }
  } catch (error) {
    console.error('提取文件URL失败:', error)
  }
  // 默认返回测试URL
  return ''
}

// 生成iframe URL的方法
const generateIframeUrl = (fileUrl?: string) => {
  const pdfBaseUrl = import.meta.env.VITE_PDF_VIEWERURL || ''
  const targetUrl = fileUrl || ''
  if (!targetUrl) return ''
  return `${pdfBaseUrl}?url=${encodeURIComponent(window.btoa(targetUrl))}`
}

// 页面加载时检查是否有课程信息
onMounted(() => {
  if (!currentTask.value) {
    uni.showToast({
      title: '未获取到课程信息',
      icon: 'none',
    })
  } else {
    loadTeachingPlan()
  }
})

// 加载授课计划数据
const loadTeachingPlan = async () => {
  if (!currentTask.value?.id) return

  isLoading.value = true
  try {
    const res = await getTeachingPlanTable({ jxrwid: currentTask.value.id.toString() })
    console.log(res)

    planHtmlContent.value = res.content
    planStatus.value = res.skjhtj === 1 ? '已制定' : '未制定'
    planId.value = res.shid // 设置授课计划ID，用于审批流程

    // 如果获取到shid，直接调用fetchWorkflowTimeline获取文件URL
    if (res.shid && formWithApprovalRef.value) {
      // 等待下一个tick确保组件已经更新
      await nextTick()
      formWithApprovalRef.value.fetchWorkflowTimeline()
    } else {
      // 没有shid时显示空状态
      showEmptyState.value = true
    }
  } catch (error) {
    uni.showToast({
      title: '获取授课计划失败',
      icon: 'none',
    })
    console.error('获取授课计划失败:', error)
  } finally {
    isLoading.value = false
  }
}

// 处理timeline数据加载完成事件
const handleTimelineLoaded = (timelineData: any) => {
  console.log('Timeline数据已加载:', timelineData)

  // 从timeline数据中提取文件URL并更新iframe
  const fileUrl = extractFileUrl(timelineData)
  if (fileUrl) {
    const iframeUrlGenerated = generateIframeUrl(fileUrl)
    if (iframeUrlGenerated) {
      iframeUrl.value = iframeUrlGenerated
      showIframe.value = true
      showEmptyState.value = false
    } else {
      showEmptyState.value = true
    }
  } else {
    // 没有文件时显示空状态
    showEmptyState.value = true
  }
}

// 处理返回事件
const handleReturn = () => {
  uni.navigateBack()
}
</script>

<template>
  <FormWithApproval
    ref="formWithApprovalRef"
    :id="planId"
    code="skjhtjsq"
    :show-workflow="showWorkflow"
    @return="handleReturn"
    @timeline-loaded="handleTimelineLoaded"
  >
    <!-- 基本信息内容插槽 -->
    <template #form-content>
      <view class="container">
        <!-- 页面标题和状态 -->
        <view class="header">
          <view class="title-section">
            <view class="course-info">
              <text class="course-name">{{ currentTask?.courseName || '未知课程' }}</text>
              <text class="class-name">{{ currentTask?.className || '' }}</text>
            </view>
            <view
              class="status-tag"
              :class="planStatus === '已制定' ? 'status-done' : 'status-pending'"
            >
              {{ planStatus }}
            </view>
          </view>
          <view class="description">
            授课计划是教师教学工作的指导性文件，对教学内容和教学安排进行详细规划。
          </view>
        </view>

        <!-- 授课计划内容 -->
        <view class="plan-content-container" v-if="showIframe">
          <!-- 在H5环境下使用iframe显示 -->
          <!-- #ifdef H5 -->
          <view class="iframe-container">
            <iframe :src="iframeUrl" class="plan-iframe" frameborder="0" scrolling="auto"></iframe>
          </view>
          <!-- #endif -->

          <!-- 非H5环境下使用rich-text显示 -->
          <!-- #ifndef H5 -->
          <rich-text :nodes="planHtmlContent"></rich-text>
          <!-- #endif -->
        </view>

        <!-- 空状态 -->
        <view class="empty-state" v-else-if="!isLoading && showEmptyState">
          <wd-icon name="note" size="160rpx" color="#d9d9d9" />
          <text class="empty-text">暂无授课计划文件</text>
        </view>

        <!-- 加载状态 -->
        <view class="loading-state" v-if="isLoading">
          <wd-icon name="refresh" size="48rpx" color="#3a8eff" class="loading-icon" />
          <text class="loading-text">加载中...</text>
        </view>
      </view>
    </template>
  </FormWithApproval>
</template>

<style lang="scss" scoped>
.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.header {
  padding: 32rpx;
  margin-bottom: 24rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
}

.title-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.course-info {
  display: flex;
  flex-direction: column;
}

.course-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.class-name {
  margin-top: 4rpx;
  font-size: 24rpx;
  color: #8e8e93;
}

.status-tag {
  padding: 6rpx 16rpx;
  font-size: 24rpx;
  border-radius: 24rpx;
}

.status-done {
  color: #52c41a;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

.status-pending {
  color: #faad14;
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
}

.description {
  font-size: 26rpx;
  line-height: 1.5;
  color: #666666;
}

.plan-content-container {
  padding: 24rpx;
  margin-bottom: 24rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
}

.iframe-container {
  width: 100%;
  height: 600px; /* 设置固定高度 */
  overflow: hidden;
  border-radius: 8rpx;
}

.plan-iframe {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 8rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-text {
  margin-top: 32rpx;
  font-size: 28rpx;
  color: #999999;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.loading-icon {
  animation: rotate 1.5s linear infinite;
}

.loading-text {
  margin-top: 24rpx;
  font-size: 28rpx;
  color: #666666;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
