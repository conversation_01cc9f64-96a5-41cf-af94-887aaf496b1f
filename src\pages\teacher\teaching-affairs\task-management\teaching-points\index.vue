<route lang="json5">
{
  style: {
    navigationBarTitleText: '授课要点',
  },
}
</route>
<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import FormWithApproval from '@/components/FormWithApproval/index.vue'
import { useUploadAnyFile } from '@/hooks/useUpload'
import { getTeachingPointDetail, updateTeachingPoint } from '@/service/teacher'
import type { TeachingPointDetail, UpdateTeachingPointRequest } from '@/types/teacher'
import { useTeachingTaskStore } from '@/store/teachingTask'

// 获取教学任务Store
const teachingTaskStore = useTeachingTaskStore()

// 表单与审批组件相关
const formWithApprovalRef = ref() // FormWithApproval组件引用
const workflowId = ref<number | null>(null) // 工作流ID
const workflowCode = ref<string>('jxskyd') // 工作流代码

// 表单数据
const formData = reactive({
  courseName: '', // 课程名称
  className: '', // 班级名称
  prerequisiteKnowledge: '', // 学生学习本课程必备的知识和技能
  providedKnowledge: '', // 本课程为后续课程提供的知识
  providedSkills: '', // 本课程为后续课程提供的技能
  syllabusName: '', // 教学大纲名称/版本
  knowledgeGoals: '', // 知识目标
  abilityGoals: '', // 能力目标
  qualityGoals: '', // 素质目标
  assessmentMethod: '', // 本课程的考核办法
  attachments: [] as Array<{ url: string; name: string }>, // 附件列表
})

// 附件处理相关
const attachmentList = ref<Array<{ url: string; name: string }>>([])

// 文件上传相关
const {
  loading: uploadLoading,
  error: uploadError,
  data: uploadData,
  run: uploadFile,
} = useUploadAnyFile<any>({
  count: 5, // 允许一次选择最多5个文件
  extension: ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
  formData: {
    type: 'course', // 上传类型
  },
})

// 处理文件上传
const handleUpload = () => {
  uploadFile()
}

// 监听上传结果并处理
watch(uploadData, (newVal) => {
  if (!newVal) return

  try {
    const result = typeof newVal === 'string' ? JSON.parse(newVal) : newVal

    // 处理多文件上传结果
    if (Array.isArray(result.data?.fileList)) {
      result.data.fileList.forEach((file) => {
        addAttachment(file.url, file.name)
      })
      showSuccessToast('上传成功')
    }
    // 处理单文件上传结果
    else if (result.code === 1 && result.data?.file) {
      addAttachment(result.data.file.url, result.data.file.name)
      showSuccessToast('上传成功')
    } else {
      showErrorToast(result.msg || '上传失败')
    }
  } catch (e) {
    console.error('处理上传结果失败:', e)
    showErrorToast('上传失败')
  }
})

// 添加附件到列表
const addAttachment = (url: string, name: string) => {
  attachmentList.value.push({ url, name })
  formData.attachments.push({ url, name })
}

// 删除附件
const deleteAttachment = (index: number) => {
  attachmentList.value.splice(index, 1)
  formData.attachments.splice(index, 1)
}

// 预览附件
const previewAttachment = (file: { url: string; name: string }) => {
  const ext = file.name.substring(file.name.lastIndexOf('.') + 1).toLowerCase()

  // 图片文件直接预览
  if (['jpg', 'jpeg', 'png', 'gif'].includes(ext)) {
    uni.previewImage({
      urls: [file.url],
      current: file.url,
    })
    return
  }

  // 其他文件根据平台处理
  // #ifdef H5
  window.open(file.url, '_blank')
  // #endif

  // #ifdef APP-PLUS
  uni.downloadFile({
    url: file.url,
    success: (res) => {
      if (res.statusCode === 200) {
        uni.openDocument({
          filePath: res.tempFilePath,
          success: () => console.log('打开文档成功'),
          fail: (err) => {
            console.error('打开文档失败', err)
            showErrorToast('无法预览该类型文件')
          },
        })
      }
    },
    fail: () => showErrorToast('文件下载失败'),
  })
  // #endif

  // #ifdef MP
  showErrorToast('小程序暂不支持该类型文件预览')
  // #endif
}

// 辅助函数 - 显示成功提示
const showSuccessToast = (message: string) => {
  uni.showToast({
    title: message,
    icon: 'success',
  })
}

// 辅助函数 - 显示错误提示
const showErrorToast = (message: string) => {
  uni.showToast({
    title: message,
    icon: 'none',
  })
}

// 加载状态
const loading = ref(false)

// 是否为只读模式
const isDisabled = ref(false)

// 记录当前授课要点ID
const teachingPointId = ref<number | null>(null)

// 获取当前页面参数
const getPageOptions = () => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  // @ts-expect-error 当前页面对象可能没有$page属性
  return currentPage.$page?.options || {}
}

// 验证表单
const validateForm = () => {
  // 必填字段列表
  const requiredFields = [
    { field: formData.courseName, message: '请输入课程名称' },
    { field: formData.className, message: '请输入班级名称' },
    { field: formData.prerequisiteKnowledge, message: '请输入学生学习本课程必备的知识和技能' },
    { field: formData.providedKnowledge, message: '请输入本课程为后续课程提供的知识' },
    { field: formData.providedSkills, message: '请输入本课程为后续课程提供的技能' },
    { field: formData.syllabusName, message: '请输入教学大纲名称/版本' },
    { field: formData.knowledgeGoals, message: '请输入知识目标' },
    { field: formData.abilityGoals, message: '请输入能力目标' },
    { field: formData.qualityGoals, message: '请输入素质目标' },
    { field: formData.assessmentMethod, message: '请输入本课程的考核办法' },
  ]

  // 检查必填字段
  for (const item of requiredFields) {
    if (!item.field) {
      showErrorToast(item.message)
      return false
    }
  }

  return true
}

// 处理返回按钮点击
const handleBack = () => {
  uni.navigateBack()
}

// 处理提交按钮点击
const handleSubmit = async () => {
  // 如果是只读模式，则直接返回上一页
  if (isDisabled.value) {
    handleBack()
    return
  }

  if (!validateForm()) {
    return
  }

  try {
    uni.showLoading({
      title: '提交中...',
    })

    // 准备附件数据
    const attachments = formData.attachments.map((file) => {
      // 从URL中提取文件ID
      const fileId = file.url.substring(file.url.lastIndexOf('/') + 1)
      return {
        id: fileId,
        name: file.name,
        url: file.url,
      }
    })

    // 准备请求数据
    const requestData: UpdateTeachingPointRequest = {
      kcbzfj: attachments,
      bbzsjn: formData.prerequisiteKnowledge,
      hxkctgzs: formData.providedKnowledge,
      hxkctgjn: formData.providedSkills,
      kcjxdgxx: formData.syllabusName,
      kcjyzsmb: formData.knowledgeGoals,
      kcjynlmb: formData.abilityGoals,
      kcjyszmb: formData.qualityGoals,
      kckhbf: formData.assessmentMethod,
    }

    // 调用API保存数据
    const taskId = teachingTaskStore.currentTask.id
    if (!taskId) {
      throw new Error('未找到教学任务ID')
    }

    await updateTeachingPoint(taskId, requestData)

    uni.hideLoading()
    showSuccessToast('提交成功')

    setTimeout(() => {
      handleBack()
    }, 2000)
  } catch (error) {
    uni.hideLoading()
    console.error('提交授课要点失败:', error)
    showErrorToast('提交失败')
  }
}

// 处理审批流程页面的返回事件
const handleApprovalReturn = () => {
  console.log('从审批流程页面返回')
}

// 加载授课要点详情数据
const loadTeachingPointDetail = async (id: number) => {
  try {
    loading.value = true
    const res = await getTeachingPointDetail(id)

    // 填充表单数据
    formData.courseName = res.kcmc
    formData.className = res.bjmc
    formData.prerequisiteKnowledge = res.bbzsjn
    formData.providedKnowledge = res.hxkctgzs
    formData.providedSkills = res.hxkctgjn
    formData.syllabusName = res.kcjxdgxx
    formData.knowledgeGoals = res.kcjyzsmb
    formData.abilityGoals = res.kcjynlmb
    formData.qualityGoals = res.kcjyszmb
    formData.assessmentMethod = res.kckhbf

    // 处理附件
    if (Array.isArray(res.kcbzfj) && res.kcbzfj.length > 0) {
      // 清空之前的附件列表
      attachmentList.value = []
      formData.attachments = []

      // 处理附件列表显示
      res.kcbzfj.forEach((attachment) => {
        if (attachment && attachment.url) {
          // 使用接口返回的name和url
          addAttachment(attachment.url, attachment.name || '未命名文件')
        }
      })
    }
  } catch (error) {
    console.error('获取授课要点详情失败:', error)
  } finally {
    loading.value = false
  }
}

// 初始化页面数据
const initPageData = () => {
  // 获取页面参数
  const options = getPageOptions()

  // 检查是否为只读模式
  if (options.disable === 'true') {
    isDisabled.value = true
    console.log('进入只读模式')
  }

  // 使用teachingTaskStore获取教学任务ID
  if (teachingTaskStore.currentTask.id) {
    // 加载授课要点详情
    loadTeachingPointDetail(teachingTaskStore.currentTask.id)
  }
}

// 组件挂载时初始化数据
onMounted(() => {
  initPageData()
})
</script>

<template>
  <FormWithApproval
    ref="formWithApprovalRef"
    :id="workflowId"
    :code="workflowCode"
    :showWorkflow="false"
    @return="handleApprovalReturn"
  >
    <template #form-content>
      <view class="min-h-auto p-0 bg-transparent">
        <view class="form-container">
          <!-- 只读模式提示 -->
          <view v-if="isDisabled" class="readonly-hint">
            <wd-icon name="info-circle" size="16px" class="mr-1"></wd-icon>
            <text>当前为只读模式，不可编辑</text>
          </view>

          <!-- 基本信息 -->
          <view class="form-section">
            <view class="section-title">基本信息</view>

            <!-- 课程名称 -->
            <view class="form-item">
              <view class="form-row-flex">
                <text class="form-label required">课程名称</text>
                <view class="form-content">
                  <input
                    class="form-input bg-gray-100"
                    v-model="formData.courseName"
                    placeholder="请输入课程名称"
                    disabled
                  />
                </view>
              </view>
            </view>

            <!-- 班级名称 -->
            <view class="form-item">
              <view class="form-row-flex">
                <text class="form-label required">班级名称</text>
                <view class="form-content">
                  <input
                    class="form-input bg-gray-100"
                    v-model="formData.className"
                    placeholder="请输入班级名称"
                    disabled
                  />
                </view>
              </view>
            </view>

            <!-- 教学大纲名称/版本 -->
            <view class="form-item">
              <view class="form-row-flex">
                <text class="form-label required">教学大纲名称/版本</text>
                <view class="form-content">
                  <input
                    class="form-input"
                    v-model="formData.syllabusName"
                    placeholder="请输入教学大纲名称/版本"
                    :disabled="isDisabled"
                  />
                </view>
              </view>
            </view>
          </view>

          <!-- 课程要求 -->
          <view class="form-section">
            <view class="section-title">课程要求</view>

            <!-- 学生学习本课程必备的知识和技能 -->
            <view class="form-item">
              <view class="form-row-column">
                <text class="form-label required">学生学习本课程必备的知识和技能</text>
                <view class="form-content">
                  <textarea
                    class="form-textarea"
                    v-model="formData.prerequisiteKnowledge"
                    placeholder="请输入学生学习本课程必备的知识和技能"
                    :disabled="isDisabled"
                  />
                </view>
              </view>
            </view>

            <!-- 本课程为后续课程提供的知识 -->
            <view class="form-item">
              <view class="form-row-column">
                <text class="form-label required">本课程为后续课程提供的知识</text>
                <view class="form-content">
                  <textarea
                    class="form-textarea"
                    v-model="formData.providedKnowledge"
                    placeholder="请输入本课程为后续课程提供的知识"
                    :disabled="isDisabled"
                  />
                </view>
              </view>
            </view>

            <!-- 本课程为后续课程提供的技能 -->
            <view class="form-item">
              <view class="form-row-column">
                <text class="form-label required">本课程为后续课程提供的技能</text>
                <view class="form-content">
                  <textarea
                    class="form-textarea"
                    v-model="formData.providedSkills"
                    placeholder="请输入本课程为后续课程提供的技能"
                    :disabled="isDisabled"
                  />
                </view>
              </view>
            </view>
          </view>

          <!-- 教学目标 -->
          <view class="form-section">
            <view class="section-title">教学目标</view>

            <!-- 知识目标 -->
            <view class="form-item">
              <view class="form-row-column">
                <text class="form-label required">知识目标</text>
                <view class="form-content">
                  <textarea
                    class="form-textarea"
                    v-model="formData.knowledgeGoals"
                    placeholder="请输入知识目标"
                    :disabled="isDisabled"
                  />
                </view>
              </view>
            </view>

            <!-- 能力目标 -->
            <view class="form-item">
              <view class="form-row-column">
                <text class="form-label required">能力目标</text>
                <view class="form-content">
                  <textarea
                    class="form-textarea"
                    v-model="formData.abilityGoals"
                    placeholder="请输入能力目标"
                    :disabled="isDisabled"
                  />
                </view>
              </view>
            </view>

            <!-- 素质目标 -->
            <view class="form-item">
              <view class="form-row-column">
                <text class="form-label required">素质目标</text>
                <view class="form-content">
                  <textarea
                    class="form-textarea"
                    v-model="formData.qualityGoals"
                    placeholder="请输入素质目标"
                    :disabled="isDisabled"
                  />
                </view>
              </view>
            </view>
          </view>

          <!-- 考核方式 -->
          <view class="form-section">
            <view class="section-title">考核方式</view>

            <!-- 本课程的考核办法 -->
            <view class="form-item">
              <view class="form-row-column">
                <text class="form-label required">本课程的考核办法</text>
                <view class="form-content">
                  <textarea
                    class="form-textarea"
                    v-model="formData.assessmentMethod"
                    placeholder="请输入本课程的考核办法"
                    :disabled="isDisabled"
                  />
                </view>
              </view>
            </view>
          </view>

          <!-- 课程标准附件 -->
          <view class="form-section">
            <view class="section-title">课程标准附件</view>

            <!-- 附件上传 -->
            <view class="form-item">
              <view class="form-row-column">
                <text class="form-label">附件</text>
                <view class="form-content">
                  <view class="upload-area" v-if="!isDisabled">
                    <view class="upload-buttons">
                      <button class="upload-button" @click="handleUpload" :disabled="uploadLoading">
                        <wd-icon name="add" size="16px" class="mr-1"></wd-icon>
                        {{ uploadLoading ? '上传中...' : '上传附件' }}
                      </button>
                    </view>
                    <text class="upload-tip">
                      支持jpg、png、pdf、word、excel、ppt等常见文件格式
                    </text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 附件列表 -->
            <view class="attachment-list" v-if="attachmentList.length > 0">
              <view class="attachment-item" v-for="(file, index) in attachmentList" :key="index">
                <view class="attachment-info" @click="previewAttachment(file)">
                  <wd-icon name="file-icon" size="18px" class="mr-2"></wd-icon>
                  <text class="attachment-name">{{ file.name }}</text>
                </view>
                <view class="attachment-actions">
                  <view class="attachment-action" @click="previewAttachment(file)">
                    <wd-icon name="view" size="18px" color="#1890ff"></wd-icon>
                  </view>
                  <view
                    v-if="!isDisabled"
                    class="attachment-action"
                    @click="deleteAttachment(index)"
                  >
                    <wd-icon name="delete" size="18px" color="#ff4d4f"></wd-icon>
                  </view>
                </view>
              </view>
            </view>
            <view class="empty-tip" v-else>
              <text>暂无附件</text>
            </view>
          </view>
        </view>
      </view>
    </template>

    <template #form-buttons>
      <!-- 提交按钮 - 只读模式下只显示返回按钮 -->
      <view class="button-group">
        <button v-if="!isDisabled" class="submit-button" @click="handleSubmit">提交</button>
        <button class="cancel-button" @click="handleBack">
          {{ isDisabled ? '返回' : '取消' }}
        </button>
      </view>
    </template>
  </FormWithApproval>
</template>

<style lang="scss" scoped>
.form-container {
  width: 100%;
}

.form-section {
  box-sizing: border-box;
  padding: 24rpx;
  margin-bottom: 24rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  padding-bottom: 16rpx;
  margin-bottom: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  border-bottom: 1px solid #f0f0f0;
}

.form-item {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 20rpx;
}

.form-row-flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.form-row-column {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.form-label {
  margin-bottom: 12rpx;
  font-size: 28rpx;
  color: #333333;

  &.required::before {
    margin-right: 4rpx;
    color: #f5222d;
    content: '*';
  }
}

.form-content {
  box-sizing: border-box;
  width: 100%;
}

.form-input {
  box-sizing: border-box;
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}

.form-textarea {
  box-sizing: border-box;
  width: 100%;
  height: 180rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
  background-color: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 8rpx;
}

.button-group {
  display: flex;
  gap: 32rpx;
  justify-content: center;
  margin-top: 48rpx;
  margin-bottom: 80rpx;
}

.submit-button {
  width: 240rpx;
  height: 80rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  color: #ffffff;
  text-align: center;
  background-color: #1890ff;
  border-radius: 40rpx;
}

.cancel-button {
  width: 240rpx;
  height: 80rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  color: #666666;
  text-align: center;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 40rpx;
}

.upload-area {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.upload-buttons {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.upload-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 240rpx;
  height: 80rpx;
  font-size: 28rpx;
  color: #ffffff;
  background-color: #1890ff;
  border: none;
  border-radius: 40rpx;
}

.upload-tip {
  margin-top: 12rpx;
  font-size: 24rpx;
  color: #999;
}

.attachment-list {
  width: 100%;
  margin-top: 24rpx;
}

.attachment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  margin-bottom: 16rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  transition: all 0.3s;
}

.attachment-item:hover {
  background-color: #e6f7ff;
}

.attachment-info {
  display: flex;
  align-items: center;
  max-width: 80%;
  cursor: pointer;
}

.attachment-name {
  overflow: hidden;
  font-size: 28rpx;
  color: #333;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.attachment-actions {
  display: flex;
  gap: 16rpx;
}

.attachment-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  cursor: pointer;
  background-color: #fff;
  border-radius: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.attachment-action:hover {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  transform: translateY(-2rpx);
}

.empty-tip {
  display: flex;
  justify-content: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #999;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

// 添加禁用样式
.disabled {
  color: #999;
  cursor: not-allowed;
  background-color: #f5f5f5;
}

// 只读模式提示样式
.readonly-hint {
  display: flex;
  align-items: center;
  padding: 16rpx 24rpx;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #1890ff;
  background-color: #e6f7ff;
  border-radius: 8rpx;
}

// 禁用状态下的文本框样式
.form-textarea:disabled,
.form-input:disabled {
  color: #666;
  cursor: not-allowed;
  background-color: #f5f5f5;
}
</style>
