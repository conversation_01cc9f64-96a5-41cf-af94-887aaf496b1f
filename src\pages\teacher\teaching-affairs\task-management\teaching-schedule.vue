<route lang="json5">
{
  style: {
    navigationBarTitleText: '教学日程安排',
  },
}
</route>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useToast } from 'wot-design-uni'
import { getTeachingTaskDetail } from '@/service/teachingTask'
import type { TeachingTaskDetail } from '@/types/teachingTask'

// toast提示
const toast = useToast()

// 课程ID
const courseId = ref<number>(0)

// 课程详情
const courseDetail = ref<TeachingTaskDetail>({} as TeachingTaskDetail)

// 加载状态
const loading = ref<boolean>(false)

// 日程表
interface ScheduleItem {
  id: number
  week: number
  date: string
  content: string
  chapter: string
  location: string
  remark: string
}

// 日程列表
const scheduleList = ref<ScheduleItem[]>([])

// 编辑的日程
const editingSchedule = ref<ScheduleItem | null>(null)

// 弹窗显示控制
const showEditDialog = ref<boolean>(false)

// 加载课程详情
const loadCourseDetail = async () => {
  if (!courseId.value) {
    toast.error('课程ID不能为空')
    return
  }

  loading.value = true
  try {
    const res = await getTeachingTaskDetail(courseId.value)
    courseDetail.value = res

    // 假设这里加载日程数据
    // 这里仅作示例，实际应该从接口获取
    scheduleList.value = [
      {
        id: 1,
        week: 1,
        date: '2025-03-01',
        content: '课程介绍与概述',
        chapter: '第一章',
        location: '教学楼A101',
        remark: '',
      },
      {
        id: 2,
        week: 2,
        date: '2025-03-08',
        content: '基础理论',
        chapter: '第二章',
        location: '教学楼A101',
        remark: '',
      },
      {
        id: 3,
        week: 3,
        date: '2025-03-15',
        content: '核心概念',
        chapter: '第三章',
        location: '教学楼A101',
        remark: '带实验器材',
      },
    ]

    loading.value = false
  } catch (error) {
    console.error('获取课程详情失败:', error)
    toast.error('获取课程详情失败')
    loading.value = false
  }
}

// 添加日程
const addSchedule = () => {
  editingSchedule.value = {
    id: 0,
    week: scheduleList.value.length + 1,
    date: '',
    content: '',
    chapter: '',
    location: '',
    remark: '',
  }
  showEditDialog.value = true
}

// 编辑日程
const editSchedule = (item: ScheduleItem) => {
  editingSchedule.value = { ...item }
  showEditDialog.value = true
}

// 删除日程
const deleteSchedule = (id: number) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个日程吗？',
    success: (res) => {
      if (res.confirm) {
        scheduleList.value = scheduleList.value.filter((item) => item.id !== id)
        toast.success('删除成功')
      }
    },
  })
}

// 保存日程编辑
const saveScheduleEdit = () => {
  if (!editingSchedule.value) return

  if (!editingSchedule.value.content.trim()) {
    toast.error('请输入内容')
    return
  }

  if (!editingSchedule.value.date) {
    toast.error('请选择日期')
    return
  }

  if (editingSchedule.value.id === 0) {
    // 添加新日程
    const newId = Math.max(0, ...scheduleList.value.map((item) => item.id)) + 1
    scheduleList.value.push({
      ...editingSchedule.value,
      id: newId,
    })
  } else {
    // 更新现有日程
    const index = scheduleList.value.findIndex((item) => item.id === editingSchedule.value?.id)
    if (index !== -1) {
      scheduleList.value[index] = { ...editingSchedule.value }
    }
  }

  showEditDialog.value = false
  editingSchedule.value = null
  toast.success('保存成功')
}

// 取消编辑
const cancelEdit = () => {
  showEditDialog.value = false
  editingSchedule.value = null
}

// 保存全部日程
const saveAllSchedules = async () => {
  try {
    // 这里需要实现保存所有日程的API调用
    // 示例: await saveAllSchedulesAPI(courseId.value, scheduleList.value)
    toast.success('保存成功')
  } catch (error) {
    console.error('保存日程失败:', error)
    toast.error('保存日程失败')
  }
}

// 页面加载
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  // @ts-ignore
  const query = currentPage.$page?.options
  if (query && query.id) {
    courseId.value = Number(query.id)
    // 加载课程详情
    loadCourseDetail()
  } else {
    toast.error('课程ID不能为空')
  }
})
</script>

<template>
  <view class="container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <wd-loading size="30" />
      <text class="loading-text">加载中...</text>
    </view>

    <template v-else>
      <!-- 课程信息 -->
      <view class="course-info-card">
        <view class="course-title">{{ courseDetail.courseName || '未知课程' }}</view>
        <view class="course-meta">
          <text>{{ courseDetail.courseCode || '未知编码' }}</text>
          <text class="divider">|</text>
          <text>{{ courseDetail.className || '未知班级' }}</text>
        </view>
      </view>

      <!-- 日程列表 -->
      <view class="content-card">
        <view class="card-header">
          <view class="card-title">
            <wd-icon name="calendar" size="18" color="#3a8eff" />
            <text class="title-text">教学日程表</text>
          </view>
          <wd-button
            size="small"
            type="primary"
            custom-style="background-color: #3a8eff; border-color: #3a8eff; border-radius: 8rpx;"
            @click="addSchedule"
          >
            <wd-icon name="add" size="16" />
            添加
          </wd-button>
        </view>

        <!-- 日程表头 -->
        <view class="schedule-header">
          <view class="header-item week">周次</view>
          <view class="header-item date">日期</view>
          <view class="header-item content">内容</view>
          <view class="header-item chapter">章节</view>
          <view class="header-item location">地点</view>
          <view class="header-item action">操作</view>
        </view>

        <!-- 日程内容 -->
        <view v-if="scheduleList.length > 0">
          <view v-for="item in scheduleList" :key="item.id" class="schedule-item">
            <view class="item-cell week">{{ item.week }}</view>
            <view class="item-cell date">{{ item.date }}</view>
            <view class="item-cell content">{{ item.content }}</view>
            <view class="item-cell chapter">{{ item.chapter }}</view>
            <view class="item-cell location">{{ item.location }}</view>
            <view class="item-cell action">
              <view class="action-buttons">
                <wd-icon name="edit" size="16" color="#3a8eff" @click="editSchedule(item)" />
                <wd-icon name="delete" size="16" color="#ff4d4f" @click="deleteSchedule(item.id)" />
              </view>
            </view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-else class="empty-state">
          <wd-icon name="calendar" size="40" color="#d1d1d6" />
          <text class="empty-text">暂无教学日程，请点击添加</text>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="footer-actions">
        <wd-button
          type="primary"
          block
          custom-style="background-color: #3a8eff; border-color: #3a8eff; border-radius: 8rpx;"
          @click="saveAllSchedules"
        >
          保存所有日程
        </wd-button>
      </view>
    </template>

    <!-- 编辑对话框 -->
    <wd-popup v-model="showEditDialog" position="center" width="80%">
      <view class="edit-dialog">
        <view class="dialog-title">{{ editingSchedule?.id ? '编辑日程' : '添加日程' }}</view>
        <view class="dialog-content">
          <view class="form-item">
            <view class="form-label">周次</view>
            <input
              v-model="editingSchedule.week"
              type="number"
              class="form-input"
              placeholder="请输入周次"
            />
          </view>
          <view class="form-item">
            <view class="form-label">日期</view>
            <picker
              mode="date"
              :value="editingSchedule?.date || ''"
              @change="(e) => (editingSchedule.date = e.detail.value)"
              class="date-picker"
            >
              <view class="picker-text">{{ editingSchedule?.date || '请选择日期' }}</view>
            </picker>
          </view>
          <view class="form-item">
            <view class="form-label">内容</view>
            <input
              v-model="editingSchedule.content"
              class="form-input"
              placeholder="请输入教学内容"
            />
          </view>
          <view class="form-item">
            <view class="form-label">章节</view>
            <input
              v-model="editingSchedule.chapter"
              class="form-input"
              placeholder="请输入对应章节"
            />
          </view>
          <view class="form-item">
            <view class="form-label">地点</view>
            <input
              v-model="editingSchedule.location"
              class="form-input"
              placeholder="请输入教学地点"
            />
          </view>
          <view class="form-item">
            <view class="form-label">备注</view>
            <textarea
              v-model="editingSchedule.remark"
              class="form-textarea"
              placeholder="请输入备注信息"
            ></textarea>
          </view>
        </view>
        <view class="dialog-footer">
          <wd-button size="small" @click="cancelEdit" custom-style="margin-right: 16rpx;">
            取消
          </wd-button>
          <wd-button
            size="small"
            type="primary"
            custom-style="background-color: #3a8eff; border-color: #3a8eff;"
            @click="saveScheduleEdit"
          >
            确定
          </wd-button>
        </view>
      </view>
    </wd-popup>

    <!-- Toast提示 -->
    <wd-toast />
  </view>
</template>

<style lang="scss" scoped>
.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  padding: 24rpx;
  background-color: #f7f8fc;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  padding: 60rpx 20rpx;
  text-align: center;
  background-color: white;
  border-radius: 12rpx;
}

.loading-text {
  margin-top: 16rpx;
  font-size: 28rpx;
  color: #8e8e93;
}

.course-info-card {
  padding: 24rpx;
  margin-bottom: 24rpx;
  background-color: white;
  border-radius: 12rpx;
}

.course-title {
  margin-bottom: 8rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.course-meta {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
}

.divider {
  margin: 0 16rpx;
  color: #ccc;
}

.content-card {
  flex: 1;
  padding: 24rpx;
  margin-bottom: 24rpx;
  background-color: white;
  border-radius: 12rpx;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.card-title {
  display: flex;
  align-items: center;
}

.title-text {
  margin-left: 8rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.schedule-header {
  display: flex;
  padding: 16rpx 0;
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  background-color: #f2f2f7;
  border-radius: 8rpx 8rpx 0 0;
}

.header-item {
  text-align: center;
}

.week {
  width: 10%;
}

.date {
  width: 20%;
}

.content {
  width: 30%;
  padding-left: 8rpx;
  text-align: left;
}

.chapter {
  width: 15%;
}

.location {
  width: 15%;
}

.action {
  width: 10%;
}

.schedule-item {
  display: flex;
  padding: 16rpx 0;
  font-size: 26rpx;
  color: #333;
  border-bottom: 1rpx solid #f2f2f7;
}

.item-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  word-break: break-all;
}

.item-cell.content {
  justify-content: flex-start;
  padding-left: 8rpx;
  text-align: left;
}

.action-buttons {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.empty-text {
  margin-top: 16rpx;
  font-size: 28rpx;
  color: #8e8e93;
}

.footer-actions {
  padding: 24rpx 0;
}

// 编辑对话框样式
.edit-dialog {
  padding: 32rpx;
}

.dialog-title {
  margin-bottom: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  text-align: center;
}

.dialog-content {
  margin-bottom: 32rpx;
}

.form-item {
  margin-bottom: 20rpx;
}

.form-label {
  margin-bottom: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.form-input {
  width: 100%;
  height: 80rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  background-color: #f5f5f5;
  border: none;
  border-radius: 8rpx;
}

.form-textarea {
  width: 100%;
  height: 120rpx;
  padding: 16rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background-color: #f5f5f5;
  border: none;
  border-radius: 8rpx;
}

.date-picker {
  width: 100%;
  height: 80rpx;
  padding: 0 16rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  color: #333;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}

.picker-text {
  color: #333;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
