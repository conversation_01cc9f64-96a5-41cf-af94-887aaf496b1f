<route lang="json5">
{
  style: {
    navigationBarTitleText: '页面标题',
  },
}
</route>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useToast } from 'wot-design-uni'
import { getTeachingTaskDetail } from '@/service/teachingTask'
import type { TeachingTaskDetail } from '@/types/teachingTask'

// toast提示
const toast = useToast()

// 课程ID
const courseId = ref<number>(0)

// 课程详情
const courseDetail = ref<TeachingTaskDetail>({} as TeachingTaskDetail)

// 加载状态
const loading = ref<boolean>(false)

// 加载课程详情
const loadCourseDetail = async () => {
  if (!courseId.value) {
    toast.error('课程ID不能为空')
    return
  }

  loading.value = true
  try {
    const res = await getTeachingTaskDetail(courseId.value)
    courseDetail.value = res
    // 根据具体页面需要加载其他数据

    loading.value = false
  } catch (error) {
    console.error('获取课程详情失败:', error)
    toast.error('获取课程详情失败')
    loading.value = false
  }
}

// 保存数据
const saveData = async () => {
  try {
    // 这里需要实现保存数据的API调用
    // 示例: await saveDataAPI(courseId.value, formData)
    toast.success('保存成功')
  } catch (error) {
    console.error('保存数据失败:', error)
    toast.error('保存数据失败')
  }
}

// 页面加载
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  // @ts-expect-error uniapp类型定义问题
  const query = currentPage.$page?.options
  if (query && query.id) {
    courseId.value = Number(query.id)
    // 加载课程详情
    loadCourseDetail()
  } else {
    toast.error('课程ID不能为空')
  }
})
</script>

<template>
  <view class="container">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <wd-loading size="30" />
      <text class="loading-text">加载中...</text>
    </view>

    <template v-else>
      <!-- 课程信息 -->
      <view class="course-info-card">
        <view class="course-title">{{ courseDetail.courseName || '未知课程' }}</view>
        <view class="course-meta">
          <text>{{ courseDetail.courseCode || '未知编码' }}</text>
          <text class="divider">|</text>
          <text>{{ courseDetail.className || '未知班级' }}</text>
        </view>
      </view>

      <!-- 内容区域 -->
      <view class="content-card">
        <view class="card-title">
          <wd-icon name="note" size="18" color="#3a8eff" />
          <text class="title-text">内容标题</text>
        </view>
        <view class="card-content">
          <!-- 在这里添加具体功能的内容 -->
          <view class="placeholder-text">功能开发中...</view>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="footer-actions">
        <wd-button
          type="primary"
          block
          custom-style="background-color: #3a8eff; border-color: #3a8eff; border-radius: 8rpx;"
          @click="saveData"
        >
          保存
        </wd-button>
      </view>
    </template>

    <!-- Toast提示 -->
    <wd-toast />
  </view>
</template>

<style lang="scss" scoped>
.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  padding: 24rpx;
  background-color: #f7f8fc;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300rpx;
  padding: 60rpx 20rpx;
  text-align: center;
  background-color: white;
  border-radius: 12rpx;
}

.loading-text {
  margin-top: 16rpx;
  font-size: 28rpx;
  color: #8e8e93;
}

.course-info-card {
  padding: 24rpx;
  margin-bottom: 24rpx;
  background-color: white;
  border-radius: 12rpx;
}

.course-title {
  margin-bottom: 8rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.course-meta {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
}

.divider {
  margin: 0 16rpx;
  color: #ccc;
}

.content-card {
  flex: 1;
  padding: 24rpx;
  margin-bottom: 24rpx;
  background-color: white;
  border-radius: 12rpx;
}

.card-title {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.title-text {
  margin-left: 8rpx;
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

.card-content {
  width: 100%;
}

.placeholder-text {
  padding: 60rpx 0;
  font-size: 28rpx;
  color: #8e8e93;
  text-align: center;
}

.footer-actions {
  padding: 24rpx 0;
}
</style>
