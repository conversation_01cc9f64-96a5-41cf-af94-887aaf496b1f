<route lang="json5">
{
  style: {
    navigationBarTitleText: '教材选用',
  },
}
</route>
<template>
  <view class="textbook-selection bg-gray-50 pt-3">
    <!-- 当前课程信息 -->
    <view v-if="currentTask" class="bg-white p-3 mb-3 shadow-sm rounded-lg mx-2">
      <view class="flex items-center justify-between flex-wrap">
        <view class="mr-2 mb-2">
          <view class="text-lg font-semibold text-gray-900">{{ currentTask.courseName }}</view>
          <view class="text-sm text-gray-600">
            {{ currentTask.className }} | {{ currentTask.courseCode }}
          </view>
          <view class="flex flex-wrap gap-2 mt-2">
            <view class="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-xs text-center">
              {{ currentTask.credit }}学分
            </view>
            <view class="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-xs text-center">
              {{ currentTask.courseTotalHours }}学时
            </view>
          </view>
          <!-- 添加教材使用状态提示 -->
          <view v-if="currentTask.textbookUse" class="mt-1">
            <span
              v-if="currentTask.textbookUse === '2'"
              class="text-xs py-1 px-2 bg-yellow-50 text-yellow-600 rounded-md inline-flex items-center"
            >
              <wd-icon name="warning" size="12px" class="mr-1" />
              该教学任务已申请无需征订教材
            </span>
            <span
              v-if="currentTask.textbookUse === '3'"
              class="text-xs py-1 px-2 bg-blue-50 text-blue-600 rounded-md inline-flex items-center"
            >
              <wd-icon name="time" size="12px" class="mr-1" />
              该教学任务无需征订教材审批中
            </span>
            <span
              v-if="currentTask.textbookUse === '4'"
              class="text-xs py-1 px-2 bg-red-50 text-red-600 rounded-md inline-flex items-center"
            >
              <wd-icon name="close" size="12px" class="mr-1" />
              该教学任务无需征订教材审批不通过
            </span>
          </view>
        </view>
      </view>
    </view>

    <!-- 申报时间通知 -->
    <view
      v-if="notificationInfo"
      class="mt-3 py-2 px-3 rounded-lg bg-blue-50 border border-blue-200 flex items-center animate-pulse-light mx-2 mb-3"
    >
      <wd-icon name="notification" color="#2563eb" size="16px" class="mr-2 flex-shrink-0" />
      <text class="text-sm text-blue-700 flex-1 font-medium">{{ notificationInfo }}</text>
    </view>

    <!-- 倒计时组件 -->
    <view
      v-if="courseCountdown.isActive"
      class="mt-3 p-3 rounded-lg bg-blue-50 border border-blue-200 mx-2 mb-3"
    >
      <view class="text-sm text-blue-700 font-medium mb-2 flex items-center">
        <wd-icon name="time" size="16px" color="#2563eb" class="mr-1" />
        <text v-if="courseCountdown.totalSeconds > 0">距离申报结束时间还有</text>
        <text v-else>申报已结束</text>
      </view>
      <view class="flex justify-center">
        <view class="countdown-block bg-white px-2 py-1 rounded-md text-center mx-1 shadow-sm">
          <view class="text-lg font-bold text-blue-600">{{ courseCountdown.days }}</view>
          <view class="text-xs text-gray-500">天</view>
        </view>
        <view class="text-blue-600 font-bold self-center mx-1">:</view>
        <view class="countdown-block bg-white px-2 py-1 rounded-md text-center mx-1 shadow-sm">
          <view class="text-lg font-bold text-blue-600">{{ courseCountdown.hours }}</view>
          <view class="text-xs text-gray-500">时</view>
        </view>
        <view class="text-blue-600 font-bold self-center mx-1">:</view>
        <view class="countdown-block bg-white px-2 py-1 rounded-md text-center mx-1 shadow-sm">
          <view class="text-lg font-bold text-blue-600">{{ courseCountdown.minutes }}</view>
          <view class="text-xs text-gray-500">分</view>
        </view>
        <view class="text-blue-600 font-bold self-center mx-1">:</view>
        <view class="countdown-block bg-white px-2 py-1 rounded-md text-center mx-1 shadow-sm">
          <view class="text-lg font-bold text-blue-600">{{ courseCountdown.seconds }}</view>
          <view class="text-xs text-gray-500">秒</view>
        </view>
      </view>
    </view>

    <!-- 无需征订教材按钮 -->
    <view class="flex justify-end m-2 flex-1">
      <button
        class="btn-special w-full"
        @click="navigateToNoNeedMaterial"
        :class="{ 'opacity-50': !allowAddMaterial }"
      >
        <wd-icon name="filter" size="16px" class="mr-1" />
        <text>无需征订教材</text>
      </button>
    </view>

    <!-- 分段选择器 -->
    <view class="bg-white p-3 mb-3 shadow-sm rounded-lg mx-2">
      <view class="segment-selector">
        <view
          class="segment-item"
          :class="{ active: activeTab === 'selection' }"
          @click="switchTab('selection')"
        >
          教材选用列表
        </view>
        <view
          class="segment-item"
          :class="{ active: activeTab === 'change' }"
          @click="switchTab('change')"
        >
          选用变更申请列表
        </view>
      </view>
    </view>

    <!-- 搜索和筛选栏 -->
    <!-- <view class="bg-white p-3 shadow-sm" v-if="activeTab === 'selection'">
      <view class="flex space-x-2 mb-3">
        <view class="flex-1 relative">
          <wd-input
            v-model="searchText"
            :placeholder="activeTab === 'selection' ? '搜索教材、ISBN、编者...' : '搜索变更申请...'"
            class="w-full bg-gray-100 rounded-lg"
            clearable
            @change="fetchTextbookList"
          />
          <wd-icon
            name="search"
            class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"
          />
        </view>
        <button class="btn-primary">
          <wd-icon name="filter" />
        </button>
      </view>
      <view class="flex space-x-2 overflow-x-auto">
        <view
          class="px-3 py-1 rounded-full text-xs whitespace-nowrap cursor-pointer"
          :class="
            filterStatus === 'all' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
          "
          @click="filterTextbooks('all')"
        >
          全部({{ totalCount }})
        </view>
        <view
          class="px-3 py-1 rounded-full text-xs whitespace-nowrap cursor-pointer"
          :class="
            filterStatus === 'used' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
          "
          @click="filterTextbooks('used')"
        >
          已选用
        </view>
        <view
          class="px-3 py-1 rounded-full text-xs whitespace-nowrap cursor-pointer"
          :class="
            filterStatus === 'pending'
              ? 'bg-orange-100 text-orange-600'
              : 'bg-gray-100 text-gray-600'
          "
          @click="filterTextbooks('pending')"
        >
          待审批
        </view>
        <view
          class="px-3 py-1 rounded-full text-xs whitespace-nowrap cursor-pointer"
          :class="
            filterStatus === 'approved'
              ? 'bg-green-100 text-green-600'
              : 'bg-gray-100 text-gray-600'
          "
          @click="filterTextbooks('approved')"
        >
          已通过
        </view>
        <view
          class="px-3 py-1 rounded-full text-xs whitespace-nowrap cursor-pointer"
          :class="
            filterStatus === 'rejected' ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-600'
          "
          @click="filterTextbooks('rejected')"
        >
          未通过
        </view>
      </view>
    </view> -->

    <button
      class="btn-success mx-2"
      v-if="activeTab === 'selection'"
      :class="{ 'opacity-50': !allowAddMaterial || currentTask?.textbookUse === '3' }"
      @click="activeTab === 'selection' ? navigateToAddTextbook() : navigateToChangeRequest()"
    >
      <wd-icon name="add" class="mr-2" />
      新增教材选用
    </button>
    <!-- 加载中提示 -->
    <view v-if="loading" class="flex justify-center items-center py-10">
      <wd-loading color="#3b82f6" />
      <text class="ml-2 text-gray-600">加载中...</text>
    </view>

    <!-- 无数据提示 -->
    <view
      v-else-if="textbookList.length === 0"
      class="flex flex-col justify-center items-center py-10"
    >
      <wd-icon name="info-circle" size="48px" color="#9ca3af" />
      <text class="mt-3 text-gray-500">
        {{ activeTab === 'selection' ? '暂无教材数据' : '暂无变更申请数据' }}
      </text>
      <text class="mt-1 text-gray-400 text-xs">
        {{
          activeTab === 'selection' ? '点击右上角"+"按钮添加教材' : '点击右上角"+"按钮提交变更申请'
        }}
      </text>
    </view>

    <!-- 教材列表 -->
    <view v-else class="p-3 space-y-3">
      <!-- 教材项目 -->
      <view
        v-for="item in activeTab === 'selection' ? textbookList : changeRequestList"
        :key="item.id"
        class="bg-white rounded-xl p-3 shadow-sm border-l-4 relative"
        :class="activeTab === 'selection' ? getStatusBorderStyle(item) : 'border-orange-400'"
      >
        <!-- 删除按钮移除 - 将移到底部按钮区 -->

        <view v-if="activeTab === 'selection'" class="flex flex-col mb-3">
          <view class="font-semibold text-gray-900 text-sm mb-1 truncate">
            {{ item.jcmc }}
          </view>
          <view class="flex items-center space-x-2 mb-2">
            <view
              class="px-2 py-0.5 rounded text-xs"
              :class="[
                getCategoryStyle(item.jclbmc).bgClass,
                getCategoryStyle(item.jclbmc).textClass,
              ]"
            >
              {{ getMaterialCategoryLabel(item.jclb) || item.jclbmc }}
            </view>
            <view
              class="px-2 py-0.5 rounded text-xs font-medium"
              :class="[getStatusInfo(item).bgClass, getStatusInfo(item).textClass]"
            >
              <wd-icon :name="getStatusInfo(item).icon" class="mr-1" />
              {{ getStatusInfo(item).text }}
            </view>
          </view>
        </view>

        <!-- 教材变更申请内容 -->
        <view v-else class="flex flex-col mb-3">
          <!-- 学年学期、课程 -->
          <view class="font-semibold text-gray-800 text-sm mb-1.5 flex flex-wrap">
            <view
              class="inline-flex items-center bg-blue-50 rounded-md py-0.5 px-1.5 mr-2 text-blue-600 mb-1"
            >
              <wd-icon name="time" size="12px" class="mr-0.5" />
              <text>{{ item.xn }}-{{ item.xq }}</text>
            </view>
            <view
              class="inline-flex items-center bg-orange-50 rounded-md py-0.5 px-1.5 mr-2 text-orange-600 mb-1"
            >
              <wd-icon name="filter" size="12px" class="mr-0.5" />
              <text>{{ getSelectionTypeLabel(item.xylb) }}</text>
            </view>
          </view>

          <!-- 课程和班级 -->
          <view class="font-semibold text-gray-800 text-sm mb-1.5 flex items-center">
            <wd-icon name="books" size="14px" class="mr-1 text-blue-600" />
            <text>{{ item.kcmc }}</text>
          </view>
          <view class="text-sm text-gray-600 mb-2">
            <text>班级: {{ item.bjmc }}</text>
          </view>

          <!-- 原教材与新教材 -->
          <view class="bg-gray-50 p-2 rounded-lg mb-2">
            <view class="text-xs text-gray-500 mb-1">原教材</view>
            <view class="text-sm font-medium text-gray-800 mb-2">{{ item.yjcmc }}</view>
            <view class="flex items-center mb-1">
              <wd-icon name="arrow-down" size="16px" color="#3b82f6" class="mr-1" />
            </view>
            <view class="text-xs text-gray-500 mb-1">新教材</view>
            <view class="text-sm font-medium text-gray-800">{{ item.jcmc }}</view>
            <view class="flex flex-wrap mt-2">
              <view class="mr-2 mb-1 text-xs text-gray-500">
                类别:
                <text class="text-gray-700">{{ getMaterialCategoryLabel(item.jclb) }}</text>
              </view>
              <view class="mr-2 mb-1 text-xs text-gray-500">
                类型:
                <text class="text-gray-700">{{ getMaterialTypeLabel(item.jclx) }}</text>
              </view>
            </view>
          </view>
        </view>

        <view v-if="activeTab === 'selection'" class="info-grid mb-3">
          <view class="info-item">
            <text class="info-label">选用类别:</text>
            <text class="info-value">{{ getSelectionTypeLabel(item.xylb) }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">ISBN:</text>
            <text class="info-value">{{ item.isbn }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">主编:</text>
            <text class="info-value">{{ item.zybz }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">出版社:</text>
            <text class="info-value">{{ item.cbs }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">版次:</text>
            <text class="info-value">{{ item.bc || '无' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">教材类别:</text>
            <text class="info-value">{{ getMaterialCategoryLabel(item.jclb) }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">教材类型:</text>
            <text class="info-value">{{ getMaterialTypeLabel(item.jclx) }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">选用时间:</text>
            <text class="info-value">{{ item.create_time }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">操作人:</text>
            <text class="info-value">{{ item.zdjsxm || '未知' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">首次选用:</text>
            <text
              class="info-value"
              :class="item.sfscxy === '1' ? 'text-green-600' : 'text-red-600'"
            >
              <wd-icon :name="item.sfscxy === '1' ? 'check' : 'close'" />
              {{ item.sfscxy === '1' ? '是' : '否' }}
            </text>
          </view>
          <view class="info-item">
            <text class="info-label">审核状态:</text>
            <text class="info-value" :class="getStatusInfo(item).textClass">
              {{ getStatusInfo(item).text }}
            </text>
          </view>
          <view class="info-item">
            <text class="info-label">评价审阅:</text>
            <text class="info-value" :class="getEvaluationInfo(item).textClass">
              <wd-icon :name="getEvaluationInfo(item).icon" class="mr-1" />
              {{ getEvaluationInfo(item).text }}
            </text>
          </view>
        </view>

        <!-- 变更申请详细信息 -->
        <view v-else class="grid grid-cols-2 gap-2 text-xs mb-3">
          <view class="flex">
            <text class="text-gray-500 mr-1">申请时间:</text>
            <text class="text-gray-800">{{ item.sqsj }}</text>
          </view>
          <view class="flex">
            <text class="text-gray-500 mr-1">申请人:</text>
            <text class="text-gray-800">{{ item.sqrxm }}</text>
          </view>
          <view class="flex">
            <text class="text-gray-500 mr-1">变更原因:</text>
            <text class="text-orange-600 font-medium">{{ item.bgyysm }}</text>
          </view>
        </view>

        <!-- 审核状态 -->
        <view v-if="activeTab === 'change'" class="bg-gray-50 p-2 rounded-lg mb-3">
          <view class="text-xs font-medium text-gray-700 mb-2">审核状态</view>
          <view class="grid grid-cols-3 gap-2">
            <view class="flex flex-col items-center">
              <text class="text-xs text-gray-500 mb-1">教研室</text>
              <view class="flex items-center">
                <view
                  class="w-3 h-3 rounded-full mr-1"
                  :class="{
                    'bg-green-500': item.jyssh === 1,
                    'bg-red-500': item.jyssh === 2,
                    'bg-orange-500': item.jyssh === 0,
                  }"
                ></view>
                <text
                  class="text-xs"
                  :class="{
                    'text-green-600': item.jyssh === 1,
                    'text-red-600': item.jyssh === 2,
                    'text-orange-600': item.jyssh === 0,
                  }"
                >
                  {{ getApprovalStatusText(item.jyssh) }}
                </text>
              </view>
            </view>
            <view class="flex flex-col items-center">
              <text class="text-xs text-gray-500 mb-1">二级学院</text>
              <view class="flex items-center">
                <view
                  class="w-3 h-3 rounded-full mr-1"
                  :class="{
                    'bg-green-500': item.xbsh === 1,
                    'bg-red-500': item.xbsh === 2,
                    'bg-orange-500': item.xbsh === 0,
                  }"
                ></view>
                <text
                  class="text-xs"
                  :class="{
                    'text-green-600': item.xbsh === 1,
                    'text-red-600': item.xbsh === 2,
                    'text-orange-600': item.xbsh === 0,
                  }"
                >
                  {{ getApprovalStatusText(item.xbsh) }}
                </text>
              </view>
            </view>
            <view class="flex flex-col items-center">
              <text class="text-xs text-gray-500 mb-1">教务处</text>
              <view class="flex items-center">
                <view
                  class="w-3 h-3 rounded-full mr-1"
                  :class="{
                    'bg-green-500': item.jwsh === 1,
                    'bg-red-500': item.jwsh === 2,
                    'bg-orange-500': item.jwsh === 0,
                  }"
                ></view>
                <text
                  class="text-xs"
                  :class="{
                    'text-green-600': item.jwsh === 1,
                    'text-red-600': item.jwsh === 2,
                    'text-orange-600': item.jwsh === 0,
                  }"
                >
                  {{ getApprovalStatusText(item.jwsh) }}
                </text>
              </view>
            </view>
          </view>
        </view>

        <view class="flex space-x-2 pt-2 border-t border-gray-100">
          <!-- 教材选用列表的操作按钮 -->
          <template v-if="activeTab === 'selection'">
            <button class="btn-primary flex-1" @click="showApprovalHistory(item.id)">
              审批记录
            </button>
            <!-- 根据审核状态条件显示编辑或变更按钮 -->
            <button
              v-if="String(item.shzt) === '1'"
              class="btn-info"
              @click="handleChangeTextbook(item.id)"
            >
              变更
            </button>
            <button v-else class="btn-success" @click="handleEditTextbook(item.id)">编辑</button>
            <button class="btn-warning" @click="showEvaluation(item.id)">评价</button>
            <!-- 将删除按钮移到这里 -->
            <button class="btn-danger" @click="confirmDelete(item.id)">
              <wd-icon name="delete" size="16px" />
            </button>
          </template>

          <!-- 变更申请列表的操作按钮 -->
          <template v-else>
            <button class="btn-primary flex-1" @click="showApprovalHistory(item.id)">
              审批记录
            </button>
            <button class="btn-warning" @click="handleViewChangeDetail(item.id)">
              {{ !(item.jyssh === 1 && item.xbsh === 1 && item.jwsh === 1) ? '编辑' : '查看详情' }}
            </button>
            <button
              class="btn-danger"
              v-if="!(item.jyssh === 1 && item.xbsh === 1 && item.jwsh === 1)"
              @click="confirmCancelChange(item.id)"
            >
              申请撤销
            </button>
          </template>
        </view>
      </view>
    </view>

    <!-- 审批记录模态框 -->
    <wd-popup v-model="showApprovalModal" position="center" :close-on-click-modal="true">
      <view class="modal-content">
        <view class="p-4 border-b">
          <view class="flex justify-between items-center">
            <view class="text-lg font-semibold">审批记录</view>
            <wd-icon name="close" @click="showApprovalModal = false" />
          </view>
        </view>
        <view class="p-4">
          <!-- 加载中提示 -->
          <view v-if="loadingApproval" class="flex justify-center items-center py-10">
            <wd-loading color="#3b82f6" />
            <text class="ml-2 text-gray-600">加载审批记录中...</text>
          </view>

          <!-- 无数据提示 -->
          <view
            v-else-if="!workflowTimelineData || currentApprovalBranches.length === 0"
            class="flex flex-col justify-center items-center py-10"
          >
            <wd-icon name="info-circle" size="48px" color="#9ca3af" />
            <text class="mt-3 text-gray-500">暂无审批记录</text>
          </view>

          <template v-else>
            <!-- 多流程分支选择器 -->
            <view v-if="currentApprovalBranches.length > 1" class="mb-4">
              <view class="text-sm text-gray-500 mb-2">审批流程分支</view>
              <view class="flex space-x-2">
                <view
                  v-for="(branch, index) in currentApprovalBranches"
                  :key="index"
                  class="px-3 py-1 rounded-full text-xs cursor-pointer"
                  :class="[
                    currentBranchIndex === index
                      ? 'bg-blue-100 text-blue-600'
                      : 'bg-gray-100 text-gray-600',
                  ]"
                  @click="selectBranch(index)"
                >
                  {{ branch.name }}
                  <view
                    v-if="branch.status === 'rejected'"
                    class="inline-block ml-1 px-1 rounded bg-red-500 text-white text-xs"
                  >
                    已拒绝
                  </view>
                  <view
                    v-if="branch.status === 'success'"
                    class="inline-block ml-1 px-1 rounded bg-green-500 text-white text-xs"
                  >
                    已通过
                  </view>
                </view>
              </view>
            </view>

            <!-- 审批流程时间线 -->
            <view class="approval-timeline">
              <view class="relative py-2">
                <view
                  class="flex relative mb-4"
                  v-for="(step, index) in currentApprovalSteps"
                  :key="index"
                  :class="{ 'mb-0': index === currentApprovalSteps.length - 1 }"
                >
                  <view
                    class="w-4 h-4 rounded-full mr-4 flex-shrink-0 z-2"
                    :class="{
                      'bg-green-500': step.status === 'success',
                      'bg-blue-500': step.status === 'active',
                      'bg-red-500': step.status === 'rejected',
                      'bg-gray-400': step.status === 'waiting',
                    }"
                  ></view>
                  <view
                    v-if="index !== currentApprovalSteps.length - 1"
                    class="absolute left-2 top-4 w-0.5 h-full bg-gray-200 z-1"
                  ></view>
                  <view class="flex-1">
                    <view class="flex items-center justify-between">
                      <view class="flex-1">
                        <view class="text-sm font-medium text-gray-800 mb-1">{{ step.name }}</view>
                        <view class="text-xs text-gray-500 mb-1">{{ step.handler }}</view>
                        <view class="flex items-center">
                          <view class="text-xs text-gray-400 mb-1">{{ step.time }}</view>
                          <!-- 显示拒绝标记 -->
                          <view
                            v-if="step.status === 'rejected'"
                            class="ml-2 px-1.5 py-0.5 rounded text-xs text-white bg-red-500"
                          >
                            已拒绝
                          </view>
                        </view>
                      </view>
                      <view
                        v-if="step.status === 'success'"
                        class="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center"
                      >
                        <wd-icon name="check" size="14px" color="#22c55e"></wd-icon>
                      </view>
                      <view
                        v-else-if="step.status === 'rejected'"
                        class="w-6 h-6 rounded-full bg-red-100 flex items-center justify-center"
                      >
                        <wd-icon name="close" size="14px" color="#ef4444"></wd-icon>
                      </view>
                      <view
                        v-else-if="step.status === 'active'"
                        class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center"
                      >
                        <wd-icon name="time" size="14px" color="#3b82f6"></wd-icon>
                      </view>
                    </view>

                    <!-- 审批意见显示 - 只在有意见时显示 -->
                    <view
                      v-if="step.comment"
                      class="mt-2 py-2 px-3 rounded-lg border-l-2 text-sm text-gray-700"
                      :class="{
                        'bg-gray-50 border-blue-400': !step.isRejected,
                        'bg-red-50 border-red-400': step.isRejected,
                      }"
                    >
                      <view class="text-sm leading-relaxed break-all">{{ step.comment }}</view>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </template>
        </view>
      </view>
    </wd-popup>

    <!-- 评价模态框 -->
    <wd-popup v-model="showEvaluationModal" position="center" :close-on-click-modal="true">
      <view class="modal-content">
        <view class="p-4 border-b">
          <view class="flex justify-between items-center">
            <view class="text-lg font-semibold">教材评价</view>
            <wd-icon name="close" @click="showEvaluationModal = false" />
          </view>
        </view>
        <view class="p-4">
          <view class="space-y-4">
            <!-- 当前选择教材 -->
            <view class="space-y-1">
              <text class="text-sm text-gray-500">当前选择教材</text>
              <view class="font-medium text-gray-800">
                {{ currentTextbook?.jcmc || '未选择教材' }}
              </view>
            </view>

            <!-- 教学参考书 -->
            <view class="space-y-2">
              <view class="flex items-center">
                <text class="font-medium">教学参考书</text>
                <text class="text-red-500 ml-1">*</text>
              </view>
              <view class="flex items-center">
                <wd-switch v-model="evaluationForm.isReference" size="20px" />
                <text class="ml-2 text-gray-700">
                  {{ evaluationForm.isReference ? '是' : '否' }}
                </text>
              </view>
            </view>

            <!-- 实验指导书 -->
            <view class="space-y-2">
              <view class="flex items-center">
                <text class="font-medium">实验指导书</text>
                <text class="text-red-500 ml-1">*</text>
              </view>
              <view class="flex items-center">
                <wd-switch v-model="evaluationForm.isLabGuide" size="20px" />
                <text class="ml-2 text-gray-700">
                  {{ evaluationForm.isLabGuide ? '是' : '否' }}
                </text>
              </view>
            </view>

            <!-- 评价与意见 -->
            <view class="space-y-2">
              <text class="font-medium">评价与意见</text>
              <wd-textarea
                v-model="evaluationForm.comment"
                placeholder="请输入您对该教材的评价与意见..."
                class="form-textarea"
                :maxlength="200"
                show-count
              />
            </view>

            <!-- 提交按钮 -->
            <button class="btn-primary w-full" @click="submitEvaluation">提交评价</button>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch, onBeforeUnmount } from 'vue'
import {
  deleteTextbook,
  getTeachingTaskMaterialList,
  submitTextbookEvaluation,
} from '@/service/textbook'
import { getTeachingTaskMaterialChangeList } from '@/service/teacher'
import { getWorkflowTimeline } from '@/service/workflow'
import { useTeachingTaskStore } from '@/store/teachingTask'
import { useTextbookStore } from '@/store/textbook'
import { useNoNeedMaterialStore } from '@/store/noNeedMaterial'
import type {
  TeachingTaskMaterialItem,
  TeachingTaskMaterialQuery,
  TextbookEvaluationParams,
} from '@/types/textbook'
import type {
  TeachingMaterialChangeQuery,
  TeachingMaterialChangeResponse,
  TeachingMaterialNoNeedItem,
} from '@/types/teachingMaterial'
import type { WorkflowTimelineResponse, WorkflowProcessItem } from '@/types/workflow'
import { loadDictData, getDictLabel, getDictClass, getDictOptions } from '@/utils/dict'
import type { DictData } from '@/types/system'

// 获取当前教学任务信息
const teachingTaskStore = useTeachingTaskStore()
const currentTask = computed(() => teachingTaskStore.currentTask)

// 搜索关键词
const searchText = ref('')

// 分段选择器当前活动标签
const activeTab = ref('selection') // 默认显示教材选用列表

// 切换标签方法
const switchTab = (tab: string) => {
  activeTab.value = tab
  // 切换标签时重置筛选状态
  filterStatus.value = 'all'
  // 重新获取对应列表数据
  fetchTextbookList()
}

// 模态框状态
const showApprovalModal = ref(false)
const showEvaluationModal = ref(false)
const currentBookId = ref(0)
const rating = ref(0)
const loadingApproval = ref(false) // 添加加载审批记录的状态

// 当前选中的教材信息
const currentTextbook = ref<TeachingTaskMaterialItem | null>(null)

// 教材列表数据
const textbookList = ref<TeachingTaskMaterialItem[]>([])
const totalCount = ref(0)
const loading = ref(false)
const filterStatus = ref('all')

// 教材变更申请列表数据
const changeRequestList = ref<any[]>([])
const loadingChangeList = ref(false)

// 字典数据
const dictData = ref<Record<string, DictData[]>>({})

// 查询参数
const queryParams = ref<TeachingTaskMaterialQuery>({
  task_id: '',
})

// 评价表单数据
const evaluationForm = ref({
  isReference: false, // 是否为教学参考书
  isLabGuide: false, // 是否为实验指导书
  comment: '', // 评价与意见
})

// 添加一个ref来保存申请理由
const taskJcxysm = ref('')

// 申报时间信息
const textbookInfo = ref<string>('')

// 是否允许新增教材（基于jssj状态）
const allowAddMaterial = ref(true)

// 通知信息计算属性
const notificationInfo = computed(() => {
  return textbookInfo.value
})

// 课程申报倒计时
const courseCountdown = ref({
  days: 0,
  hours: 0,
  minutes: 0,
  seconds: 0,
  totalSeconds: 0,
  isActive: false,
})

// 存储计时器引用，方便清除
const countdownTimer = ref<number | null>(null)

// 更新倒计时
const updateCountdown = () => {
  if (courseCountdown.value.totalSeconds <= 0) {
    courseCountdown.value.isActive = false
    return
  }

  courseCountdown.value.totalSeconds -= 1

  // 计算天、时、分、秒
  courseCountdown.value.days = Math.floor(courseCountdown.value.totalSeconds / (24 * 3600))
  const remainder = courseCountdown.value.totalSeconds % (24 * 3600)
  courseCountdown.value.hours = Math.floor(remainder / 3600)
  courseCountdown.value.minutes = Math.floor((remainder % 3600) / 60)
  courseCountdown.value.seconds = remainder % 60
}

// 启动倒计时
const startCountdown = (seconds: number) => {
  if (seconds <= 0) return

  // 清除之前的计时器
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }

  courseCountdown.value.totalSeconds = seconds
  courseCountdown.value.isActive = true

  // 初次计算显示值
  updateCountdown()

  // 设置定时器
  countdownTimer.value = setInterval(() => {
    updateCountdown()
    if (!courseCountdown.value.isActive) {
      clearInterval(countdownTimer.value!)
      countdownTimer.value = null
    }
  }, 1000)
}

// 停止倒计时的方法
const stopCountdown = () => {
  // 清除计时器
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
  // 设置倒计时为非活动状态
  courseCountdown.value.isActive = false
}

// 获取教材列表
const fetchTextbookList = async () => {
  if (!currentTask.value?.id) {
    uni.showToast({
      title: '未获取到课程信息',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  try {
    loading.value = true
    queryParams.value.task_id = String(currentTask.value.id)

    // 设置列表类型，根据当前选中的标签
    queryParams.value.list_type = activeTab.value === 'change' ? 'change' : 'selection'

    // 如果有搜索关键词，添加到查询参数
    if (searchText.value) {
      queryParams.value.jcmc = searchText.value
      queryParams.value.isbn = searchText.value
      queryParams.value.zybz = searchText.value
    } else {
      delete queryParams.value.jcmc
      delete queryParams.value.isbn
      delete queryParams.value.zybz
    }

    if (activeTab.value === 'change') {
      // 调用教材变更列表API
      const changeParams: TeachingMaterialChangeQuery = {
        sortBy: 'id',
        sortOrder: 'desc',
        task_id: String(currentTask.value.id),
      }

      // 如果有搜索关键词，添加到查询参数
      if (searchText.value) {
        // 搜索关键词暂不添加到查询参数，因为TeachingMaterialChangeQuery类型中没有相关属性
      }

      const changeRes = await getTeachingTaskMaterialChangeList(changeParams)
      changeRequestList.value = changeRes.items || []
      totalCount.value = changeRes.total || 0

      // 如果没有数据，提示用户
      if (changeRequestList.value.length === 0) {
        uni.showToast({
          title: '暂无变更申请数据',
          icon: 'none',
          duration: 2000,
        })
      }
    } else {
      const res = await getTeachingTaskMaterialList(queryParams.value)
      textbookList.value = res.items || []
      totalCount.value = res.total || 0

      // 更新当前任务的教材使用状态
      if (res.taskInfo && res.taskInfo.textbookUse) {
        // 更新当前任务对象中的教材使用状态
        const teachingTaskStore = useTeachingTaskStore()
        if (currentTask.value) {
          teachingTaskStore.setCurrentTask({
            ...currentTask.value,
            textbookUse: String(res.taskInfo.textbookUse),
          })
          // 保存申请理由到ref，使用类型断言
          const taskInfo = res.taskInfo as any
          if (taskInfo.jcxysm) {
            taskJcxysm.value = taskInfo.jcxysm
          }
        }
      }

      // 保存info信息
      const responseAny = res as any
      if (responseAny.info) {
        textbookInfo.value = responseAny.info
      }

      // 设置倒计时
      if (responseAny.jssj !== undefined) {
        if (responseAny.jssj === 0) {
          // 如果jssj为0，停止倒计时，并设置不允许新增教材
          stopCountdown()
          allowAddMaterial.value = false
          // 仍然设置为活跃以显示倒计时UI
          courseCountdown.value = {
            days: 0,
            hours: 0,
            minutes: 0,
            seconds: 0,
            totalSeconds: 0,
            isActive: true, // 仍然设置为活跃以显示倒计时UI
          }
        } else if (typeof responseAny.jssj === 'number') {
          // 计算当前时间和结束时间的差值（秒）
          const now = Math.floor(Date.now() / 1000)
          const endTime = Math.floor(responseAny.jssj / 1000)
          const seconds = endTime - now

          if (seconds > 0) {
            startCountdown(seconds)
            allowAddMaterial.value = true
          } else {
            // 如果计算结果小于等于0，停止倒计时，并设置不允许新增教材
            stopCountdown()
            allowAddMaterial.value = false
            // 显示为全部0的倒计时
            courseCountdown.value = {
              days: 0,
              hours: 0,
              minutes: 0,
              seconds: 0,
              totalSeconds: 0,
              isActive: true,
            }
          }
        }
      }

      // 如果没有数据，提示用户
      if (textbookList.value.length === 0) {
        uni.showToast({
          title: '暂无教材数据',
          icon: 'none',
          duration: 2000,
        })
      }
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    loading.value = false
  }
}

// 根据状态筛选教材
const filterTextbooks = (status: string) => {
  filterStatus.value = status
  // 根据状态更新查询参数
  if (status === 'all') {
    delete queryParams.value.pjshzt
  } else if (status === 'pending') {
    queryParams.value.pjshzt = '0' // 待审批
  } else if (status === 'approved') {
    queryParams.value.pjshzt = '1' // 已通过
  } else if (status === 'rejected') {
    queryParams.value.pjshzt = '2' // 未通过
  } else if (status === 'used') {
    // 已选用的逻辑，可能需要根据实际接口调整
    queryParams.value.pjshzt = '3'
  }

  fetchTextbookList()
}

// 当前选中的分支索引
const currentBranchIndex = ref(0)

// 导航到新增教材页面
const navigateToAddTextbook = () => {
  // 如果不允许添加教材，则显示提示并返回
  if (!allowAddMaterial.value) {
    uni.showToast({
      title: '当前不在教材选用申报时间内',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 检查是否为无需征订教材审批中状态
  if (currentTask.value?.textbookUse === '3') {
    uni.showToast({
      title: '该教学任务已申请无需征订教材，不可新增选用',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 先清空store中的教材数据
  const textbookStore = useTextbookStore()
  textbookStore.clearCurrentTextbook()

  uni.navigateTo({
    url: '/pages/teacher/teaching-affairs/task-management/add-textbook?mode=add',
  })
}

// 导航到变更申请页面
const navigateToChangeRequest = () => {
  // 如果不允许添加教材，则显示提示并返回
  if (!allowAddMaterial.value) {
    uni.showToast({
      title: '当前不在教材选用申报时间内',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 检查是否为无需征订教材审批中状态
  if (currentTask.value?.textbookUse === '3') {
    uni.showToast({
      title: '该教学任务已申请无需征订教材，不可变更选用',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 先清空store中的教材数据
  const textbookStore = useTextbookStore()
  textbookStore.clearCurrentTextbook()

  uni.navigateTo({
    url: '/pages/teacher/teaching-affairs/task-management/add-textbook?mode=change',
  })
}

// 组件卸载时清除计时器
onBeforeUnmount(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
})

// 处理变更教材
const handleChangeTextbook = (id: number) => {
  // 检查是否为无需征订教材审批中状态
  if (currentTask.value?.textbookUse === '3') {
    uni.showToast({
      title: '该教学任务已申请无需征订教材，不可变更选用',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 根据ID找到当前教材
  const textbook = textbookList.value.find((item) => item.id === id)

  // 获取store实例
  const textbookStore = useTextbookStore()

  // 先清空store
  textbookStore.clearCurrentTextbook()

  if (textbook) {
    // 将选中的教材存入store
    textbookStore.setCurrentTextbook(textbook)

    // 导航到新增教材页面，传递变更模式参数
    uni.navigateTo({
      url: `/pages/teacher/teaching-affairs/task-management/add-textbook?mode=change`,
    })
  } else {
    uni.showToast({
      title: '未找到教材信息',
      icon: 'none',
    })
  }
}

// 处理编辑教材
const handleEditTextbook = (id: number) => {
  // 检查是否为无需征订教材审批中状态
  if (currentTask.value?.textbookUse === '3') {
    uni.showToast({
      title: '该教学任务已申请无需征订教材，不可编辑选用',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 根据ID找到当前教材
  const textbook = textbookList.value.find((item) => item.id === id)

  // 获取store实例
  const textbookStore = useTextbookStore()

  // 先清空store
  textbookStore.clearCurrentTextbook()

  if (textbook) {
    // 将选中的教材存入store
    textbookStore.setCurrentTextbook(textbook)

    // 导航到编辑教材页面，传递编辑模式参数
    uni.navigateTo({
      url: `/pages/teacher/teaching-affairs/task-management/add-textbook?mode=edit`,
    })
  } else {
    uni.showToast({
      title: '未找到教材信息',
      icon: 'none',
    })
  }
}

// 确认删除教材
const confirmDelete = (id: number) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除该教材吗？删除后无法恢复。',
    confirmText: '删除',
    confirmColor: '#ff3b30',
    success: (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '删除中...' })

          deleteTextbook(id)
            .then(() => {
              uni.hideLoading()
              uni.showToast({
                title: '删除成功',
                icon: 'success',
              })

              // 刷新列表数据
              fetchTextbookList()
            })
            .catch((error) => {
              console.error('删除失败:', error)
              uni.hideLoading()
              uni.showToast({
                title: '删除失败，请重试',
                icon: 'none',
              })
            })
        } catch (error) {
          console.error('删除失败:', error)
          uni.hideLoading()
          uni.showToast({
            title: '删除失败，请重试',
            icon: 'none',
          })
        }
      }
    },
  })
}

// 审批步骤数据结构
interface ApprovalStep {
  name: string
  handler: string
  time: string
  status: 'success' | 'rejected' | 'active' | 'waiting'
  comment?: string
  isRejected?: boolean
}

// 流程分支数据结构
interface ApprovalBranch {
  name: string
  status: 'success' | 'rejected' | 'active' | 'waiting'
  steps: ApprovalStep[]
}

// 工作流时间线数据
const workflowTimelineData = ref<WorkflowTimelineResponse | null>(null)

// 将工作流节点数据转换为审批步骤格式
const convertToApprovalSteps = (processItems: WorkflowProcessItem[]): ApprovalStep[] => {
  return processItems.map((item) => {
    // 确定步骤状态
    let status: 'success' | 'rejected' | 'active' | 'waiting' = 'waiting'

    if (String(item.status) === '1') {
      status = 'success'
    } else if (String(item.status) === '2') {
      status = 'rejected'
    } else if (String(item.status) === '0') {
      status = 'active'
    }

    // 格式化时间
    const time =
      typeof item.time === 'string'
        ? item.time
        : item.create_time
          ? new Date(item.create_time * 1000).toLocaleString()
          : status === 'active'
            ? '待处理'
            : '未到达'

    return {
      name: item.name,
      handler: item.user_name || '',
      time,
      status,
      comment: item.approval_opinion || '',
      isRejected: status === 'rejected',
    }
  })
}

// 当前教材的审批流程分支
const currentApprovalBranches = computed(() => {
  if (!workflowTimelineData.value) return []

  // 将API返回的流程数据转换为前端需要的分支格式
  return workflowTimelineData.value.process.map((branch, index) => {
    // 确定分支状态
    let branchStatus: 'success' | 'rejected' | 'active' | 'waiting' = 'waiting'

    // 检查分支中的节点状态来确定整个分支的状态
    const hasRejected = branch.some((item) => String(item.status) === '2')
    const allCompleted = branch.every((item) => String(item.status) === '1')
    const hasActive = branch.some((item) => String(item.status) === '0')

    if (hasRejected) {
      branchStatus = 'rejected'
    } else if (allCompleted) {
      branchStatus = 'success'
    } else if (hasActive) {
      branchStatus = 'active'
    }

    return {
      name: index === 0 ? '当前流程' : `历史流程${index}`,
      status: branchStatus,
      steps: convertToApprovalSteps(branch),
    }
  })
})

// 当前选中分支的审批步骤
const currentApprovalSteps = computed(() => {
  const branches = currentApprovalBranches.value
  if (branches.length === 0) return []

  // 安全地访问当前分支索引
  const safeIndex = Math.min(currentBranchIndex.value, branches.length - 1)
  return branches[safeIndex]?.steps || []
})

// 确保当前分支索引在有效范围内
const ensureValidBranchIndex = () => {
  const branches = currentApprovalBranches.value
  if (branches.length === 0) return

  // 如果当前分支索引超出范围，重置为0
  if (currentBranchIndex.value >= branches.length) {
    currentBranchIndex.value = 0
  }
}

// 选择分支
const selectBranch = (index: number) => {
  if (index >= 0 && index < currentApprovalBranches.value.length) {
    currentBranchIndex.value = index
  }
}

// 显示审批记录
const showApprovalHistory = async (id: number) => {
  currentBookId.value = id
  // 重置分支索引
  currentBranchIndex.value = 0

  try {
    loadingApproval.value = true
    showApprovalModal.value = true

    // 调用工作流时间线接口获取审批记录
    const res = await getWorkflowTimeline({
      id,
      code: 'jsxkjcsh', // 教师选课教材审核的代码
    })

    workflowTimelineData.value = res

    // 确保分支索引有效
    ensureValidBranchIndex()
  } catch (error) {
    console.error('获取审批记录失败:', error)
    uni.showToast({
      title: '获取审批记录失败',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    loadingApproval.value = false
  }
}

// 显示评价
const showEvaluation = (id: number) => {
  currentBookId.value = id
  // 获取当前选中的教材信息
  currentTextbook.value = textbookList.value.find((item) => item.id === id) || null

  // 如果找到了教材信息，从中获取已有的评价数据
  if (currentTextbook.value) {
    // 从教材项中获取评价数据
    evaluationForm.value = {
      // 将字符串"1"转换为布尔值true，其他值为false
      isReference: currentTextbook.value.sfjxcks === '1',
      isLabGuide: currentTextbook.value.sfsyzds === '1',
      comment: currentTextbook.value.jcpjyj || '',
    }
  } else {
    // 如果没有找到教材信息，重置评价表单
    evaluationForm.value = {
      isReference: false,
      isLabGuide: false,
      comment: '',
    }
  }

  showEvaluationModal.value = true
}

// 提交评价
const submitEvaluation = async () => {
  try {
    // 表单验证
    if (!evaluationForm.value.comment.trim()) {
      uni.showToast({
        title: '请输入评价与意见',
        icon: 'none',
      })
      return
    }

    uni.showLoading({ title: '提交中...' })

    // 构建评价参数
    const params: TextbookEvaluationParams = {
      id: currentBookId.value,
      sfjxcks: evaluationForm.value.isReference ? '1' : '0',
      sfsyzds: evaluationForm.value.isLabGuide ? '1' : '0',
      jcpjyj: evaluationForm.value.comment,
      jcmc: currentTextbook.value?.jcmc,
    }

    // 调用评价API
    await submitTextbookEvaluation(params)

    uni.hideLoading()
    uni.showToast({
      title: '评价提交成功',
      icon: 'success',
    })
    showEvaluationModal.value = false

    // 刷新教材列表
    fetchTextbookList()
  } catch (error) {
    console.error('提交评价失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'none',
    })
  }
}

// 加载字典数据
const loadDicts = async () => {
  try {
    uni.showLoading({ title: '加载字典数据...' })
    // 加载四种字典数据
    dictData.value = await loadDictData([
      'DM_SPZT', // 审核状态
      'SYS_SELECTION_TYPE', // 选用类别
      'DM_JCLBDM', // 教材类别
      'DM_JCLXDM', // 教材类型
    ])
    uni.hideLoading()
  } catch (error) {
    console.error('加载字典数据失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: '加载字典数据失败',
      icon: 'none',
      duration: 2000,
    })
  }
}

onShow(() => {
  fetchTextbookList()
})

// 检查是否有课程信息并加载数据
onMounted(() => {
  // 先加载字典数据
  loadDicts().then(() => {
    if (!currentTask.value) {
      uni.showToast({
        title: '未获取到课程信息',
        icon: 'none',
        duration: 2000,
      })
    } else {
      // 设置初始查询参数
      queryParams.value.list_type = 'selection'
      fetchTextbookList()
    }
  })
})

// 获取教材状态文本和样式
const getStatusInfo = (item: TeachingTaskMaterialItem) => {
  const pjshzt = String(item.shzt || '0')

  // 使用字典数据获取状态标签
  const statusLabel = getDictLabel(dictData.value.DM_SPZT, pjshzt)

  // 根据状态值设置样式和图标
  if (pjshzt === '0') {
    return {
      text: statusLabel || '待审批',
      bgClass: 'bg-orange-100',
      textClass: 'text-orange-600',
      icon: 'clock',
    }
  } else if (pjshzt === '1') {
    return {
      text: statusLabel || '已通过',
      bgClass: 'bg-green-100',
      textClass: 'text-green-600',
      icon: 'check',
    }
  } else if (pjshzt === '2') {
    return {
      text: statusLabel || '未通过',
      bgClass: 'bg-red-100',
      textClass: 'text-red-600',
      icon: 'close',
    }
  }

  return {
    text: statusLabel || '未知状态',
    bgClass: 'bg-gray-100',
    textClass: 'text-gray-600',
    icon: 'help',
  }
}

// 获取教材类别样式
const getCategoryStyle = (category: string) => {
  // 使用字典数据获取类别标签
  const categoryLabel = getDictLabel(dictData.value.DM_JCLBDM, category) || category

  if (categoryLabel.includes('国家规划')) {
    return {
      bgClass: 'bg-green-100',
      textClass: 'text-green-600',
    }
  } else if (categoryLabel.includes('省级优秀')) {
    return {
      bgClass: 'bg-purple-100',
      textClass: 'text-purple-600',
    }
  } else {
    return {
      bgClass: 'bg-gray-100',
      textClass: 'text-gray-600',
    }
  }
}

// 获取课程类型样式
const getCourseTypeStyle = (type: string) => {
  if (type === '必修') {
    return {
      bgClass: 'bg-blue-100',
      textClass: 'text-blue-600',
    }
  } else {
    return {
      bgClass: 'bg-blue-100',
      textClass: 'text-blue-600',
    }
  }
}

// 获取教材状态边框样式
const getStatusBorderStyle = (item: TeachingTaskMaterialItem) => {
  const pjshzt = String(item.pjshzt || '0')

  if (pjshzt === '0') {
    return 'border-orange-400'
  } else if (pjshzt === '1') {
    return 'border-green-400'
  } else if (pjshzt === '2') {
    return 'border-red-400'
  }

  return 'border-gray-400'
}

// 获取选用类别标签
const getSelectionTypeLabel = (value: string) => {
  return getDictLabel(dictData.value.SYS_SELECTION_TYPE, value) || value
}

// 获取教材类别标签
const getMaterialCategoryLabel = (value: string) => {
  return getDictLabel(dictData.value.DM_JCLBDM, value) || value
}

// 获取教材类型标签
const getMaterialTypeLabel = (value: string) => {
  return getDictLabel(dictData.value.DM_JCLXDM, value) || value
}

// 获取评价审阅文本和样式
const getEvaluationInfo = (item: TeachingTaskMaterialItem) => {
  // 使用DM_SPZT字典判断评价审阅的审核状态
  const pjshzt = String(item.pjshzt || '0')

  // 使用字典数据获取状态标签
  const statusLabel = getDictLabel(dictData.value.DM_SPZT, pjshzt)

  // 根据状态值设置样式和图标
  if (pjshzt === '0') {
    return {
      text: statusLabel || '待审核',
      bgClass: 'bg-orange-100',
      textClass: 'text-orange-600',
      icon: 'clock',
    }
  } else if (pjshzt === '1') {
    return {
      text: statusLabel || '已审核',
      bgClass: 'bg-green-100',
      textClass: 'text-green-600',
      icon: 'check',
    }
  } else if (pjshzt === '2') {
    return {
      text: statusLabel || '审核未通过',
      bgClass: 'bg-red-100',
      textClass: 'text-red-600',
      icon: 'close',
    }
  }

  return {
    text: statusLabel || '未知状态',
    bgClass: 'bg-gray-100',
    textClass: 'text-gray-400',
    icon: 'info-circle',
  }
}

// 获取审批状态文本
const getApprovalStatusText = (status: number) => {
  if (status === 1) {
    return '已通过'
  } else if (status === 2) {
    return '未通过'
  } else {
    return '待审核'
  }
}

// 获取教材变更申请状态文本
const getChangeRequestStatusText = (item: any) => {
  // 检查是否所有审批环节都已通过
  const allApproved = item.jyssh === 1 && item.xbsh === 1 && item.jwsh === 1
  // 检查是否有任何一个审批环节被拒绝
  const anyRejected = item.jyssh === 2 || item.xbsh === 2 || item.jwsh === 2

  if (anyRejected) {
    return '未通过'
  } else if (allApproved) {
    return '已通过'
  } else {
    return '审核中'
  }
}

// 获取教材变更申请状态图标
const getChangeRequestStatusIcon = (item: any) => {
  // 检查是否有任何一个审批环节被拒绝
  const anyRejected = item.jyssh === 2 || item.xbsh === 2 || item.jwsh === 2
  // 检查是否所有审批环节都已通过
  const allApproved = item.jyssh === 1 && item.xbsh === 1 && item.jwsh === 1

  if (anyRejected) {
    return 'close'
  } else if (allApproved) {
    return 'check'
  } else {
    return 'clock'
  }
}

// 获取教材变更申请状态样式
const getChangeRequestStatusStyle = (item: any) => {
  // 检查是否有任何一个审批环节被拒绝
  const anyRejected = item.jyssh === 2 || item.xbsh === 2 || item.jwsh === 2
  // 检查是否所有审批环节都已通过
  const allApproved = item.jyssh === 1 && item.xbsh === 1 && item.jwsh === 1

  if (anyRejected) {
    return 'bg-red-100 text-red-600'
  } else if (allApproved) {
    return 'bg-green-100 text-green-600'
  } else {
    return 'bg-orange-100 text-orange-600'
  }
}

// 处理查看变更详情
const handleViewChangeDetail = (id: number) => {
  // 根据ID找到当前教材变更记录
  const changeItem = changeRequestList.value.find((item) => item.id === id)

  if (changeItem) {
    // 检查是否所有审批环节都已通过
    const allApproved = changeItem.jyssh === 1 && changeItem.xbsh === 1 && changeItem.jwsh === 1

    // 获取store实例
    const textbookStore = useTextbookStore()

    // 先清空store
    textbookStore.clearCurrentTextbook()

    // 将变更记录转换为教材信息格式，添加必要的属性
    const textbookData: any = {
      id: changeItem.id,
      jcmc: changeItem.jcmc,
      isbn: changeItem.isbn || '',
      cbs: changeItem.cbs || '',
      zybz: changeItem.zybz || '',
      jclb: changeItem.jclb,
      jclbmc: changeItem.jclbmc || '',
      jclx: changeItem.jclx,
      jclxmc: changeItem.jclxmc || '',
      xylb: changeItem.xylb,
      xyly: changeItem.xyly || '',
      bgyysm: changeItem.bgyysm || '',
      sfscxy: changeItem.sfscxy ? String(changeItem.sfscxy) : '0',
      jxrwid: changeItem.ssjxrwid,
      title: `${changeItem.xn}-${changeItem.xq} ${changeItem.bjmc} ${changeItem.kcmc}`,
      // 添加必要的属性，使用默认值
      jcxxid: changeItem.jcid || 0,
      remark: '',
      deltag: 0,
      create_time: '',
      update_time: '',
      bc: '',
      shzt: allApproved ? '1' : '0', // 审核状态
      pjshzt: '0',
      jcpjyj: '',
      sfjxcks: '0',
      sfsyzds: '0',
      zdjsxm: changeItem.sqrxm || '',
      xn: changeItem.xn || '',
      xq: changeItem.xq || '',
      kcmc: changeItem.kcmc || '',
      bjmc: changeItem.bjmc || '',
      // 添加原教材选用ID，用于变更申请
      yjcxyid: changeItem.yjcxyid || 0,
      // 添加缺少的必要属性以解决类型错误
      oprybh: '',
      ssxk: 0,
      kcdm: '',
      ssxy: '',
      ssxb: '',
      ssjys: '',
      ssjysmc: '',
      ssbj: '',
      taskId: changeItem.ssjxrwid, // 添加任务ID
    }

    // 将转换后的数据存入store
    textbookStore.setCurrentTextbook(textbookData)

    if (allApproved) {
      // 如果已审核通过，跳转到add-textbook页面并传递disabled参数
      uni.navigateTo({
        url: `/pages/teacher/teaching-affairs/task-management/add-textbook?mode=change&disabled=true`,
      })
    } else {
      // 如果未审核通过，跳转到编辑页面
      uni.navigateTo({
        url: `/pages/teacher/teaching-affairs/task-management/add-textbook?mode=change&fromChangeList=true`,
      })
    }
  } else {
    uni.showToast({
      title: '未找到变更记录',
      icon: 'none',
    })
  }
}

// 确认撤销变更记录
const confirmCancelChange = (id: number) => {
  uni.showModal({
    title: '确认撤销',
    content: '确定要撤销该教材变更申请吗？撤销后无法恢复。',
    confirmText: '撤销',
    confirmColor: '#ff3b30',
    success: (res) => {
      if (res.confirm) {
        uni.showToast({
          title: '撤销功能待实现',
          icon: 'none',
        })
      }
    },
  })
}

// 导航到无需征订教材页面
const navigateToNoNeedMaterial = () => {
  // 如果不允许添加教材，则显示提示并返回
  if (!allowAddMaterial.value) {
    uni.showToast({
      title: '当前不在教材选用申报时间内',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  if (!currentTask.value) {
    uni.showToast({
      title: '未获取到课程信息',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 获取无需征订教材store
  const noNeedMaterialStore = useNoNeedMaterialStore()

  // 检查当前任务的textbookUse状态
  if (currentTask.value.textbookUse === '3') {
    // 如果是审批中状态，使用编辑模式
    // 创建一个新的部分教材任务对象，只包含我们需要的字段
    const materialTask: Partial<TeachingMaterialNoNeedItem> = {
      id: currentTask.value.id, // 使用当前任务ID
      taskId: currentTask.value.id,
      xn: currentTask.value.studyYear,
      xq: currentTask.value.studyTerm,
      kcmc: currentTask.value.courseName,
      kcdm: currentTask.value.courseCode,
      bjmc: currentTask.value.className,
      zdjsxm: currentTask.value.leaderTeacherName,
      xqzxs: currentTask.value.courseTotalHours,
      kcxf: currentTask.value.creditHour,
      zxs: currentTask.value.weekHours,
      jcxysm: taskJcxysm.value, // 使用保存的申请理由
      // 添加视图显示需要的最小必要字段
      ssjh: null,
      ssxk: 0,
      sshb: null,
    }

    // 更新store中的数据，使用类型断言告诉TypeScript这是完整的类型
    noNeedMaterialStore.setCurrentNoNeedMaterial(materialTask as TeachingMaterialNoNeedItem)

    // 导航到无需征订教材页面，使用edit模式
    uni.navigateTo({
      url: '/pages/teacher/teaching-affairs/task-management/edit-no-need-material?mode=edit&from=selection',
    })
    return
  }

  // 清空store中的数据（仅在非审批中状态时）
  noNeedMaterialStore.clearCurrentNoNeedMaterial()

  // 创建一个新的部分教材任务对象，只包含我们需要的字段
  const materialTask: Partial<TeachingMaterialNoNeedItem> = {
    id: 0, // 新建的任务，ID为0
    taskId: currentTask.value.id,
    xn: currentTask.value.studyYear,
    xq: currentTask.value.studyTerm,
    kcmc: currentTask.value.courseName,
    kcdm: currentTask.value.courseCode,
    bjmc: currentTask.value.className,
    zdjsxm: currentTask.value.leaderTeacherName,
    xqzxs: currentTask.value.courseTotalHours,
    kcxf: currentTask.value.creditHour,
    zxs: currentTask.value.weekHours,
    jcxysm: '', // 无需征订教材理由
    // 添加视图显示需要的最小必要字段
    ssjh: null,
    ssxk: 0,
    sshb: null,
  }

  // 更新store中的数据，使用类型断言告诉TypeScript这是完整的类型
  noNeedMaterialStore.setCurrentNoNeedMaterial(materialTask as TeachingMaterialNoNeedItem)

  // 导航到无需征订教材页面，添加from=selection参数，表明来源是selection页面
  uni.navigateTo({
    url: '/pages/teacher/teaching-affairs/task-management/edit-no-need-material?mode=add&from=selection',
  })
}
</script>

<style lang="scss">
.textbook-selection {
  min-height: 100vh;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  font-size: 11px;
}

.info-item {
  display: flex;
  align-items: flex-start;
}

.info-label {
  flex-shrink: 0;
  min-width: 44px;
  color: #6b7280;
}

.info-value {
  font-weight: 500;
  line-height: 1.3;
  color: #374151;
}

.modal-content {
  width: 320px;
  max-height: 80vh;
  overflow-y: auto;
  background: white;
  border-radius: 16px;
}

// 添加轻微的脉冲动画样式
.animate-pulse-light {
  animation: pulse-light 2s infinite;
}

@keyframes pulse-light {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.85;
  }
  100% {
    opacity: 1;
  }
}

// 倒计时样式
.countdown-block {
  min-width: 40px;
  transition: all 0.3s ease;
}

// 审批时间线样式
.approval-timeline {
  position: relative;

  .relative.py-2 {
    transition: all 0.3s ease;
  }

  .w-4.h-4.rounded-full {
    position: relative;
    z-index: 2;
  }

  .absolute.left-2.top-4.w-0\.5.h-full {
    position: absolute;
    z-index: 1;
  }
}

// 分支选择器样式
.flex.space-x-2 {
  padding-bottom: 8px;
  overflow-x: auto;
  scrollbar-width: thin;

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  .cursor-pointer {
    transition: all 0.2s ease;

    &:hover {
      opacity: 0.85;
    }
  }
}

// 评价表单样式
.space-y-1,
.space-y-2,
.space-y-4 {
  display: flex;
  flex-direction: column;
}

.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

// 单选按钮样式
.flex.items-center {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.w-5.h-5.rounded-full.border {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.border-blue-500 {
  border-color: #3b82f6;
}

.bg-blue-500 {
  background-color: #3b82f6;
}

.border-gray-300 {
  border-color: #d1d5db;
}

// 按钮基础样式
button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 0 12px;
  font-size: 13px;
  border-radius: 6px;
  transition: opacity 0.3s;

  &:active {
    opacity: 0.85;
  }
}

// 主要按钮样式 - 蓝色
.btn-primary {
  color: #fff;
  background-color: #007aff;
  border: 1px solid #007aff;
}

// 成功按钮样式 - 绿色
.btn-success {
  color: #fff;
  background-color: #4cd964;
  border: 1px solid #4cd964;
}

// 警告按钮样式 - 橙色
.btn-warning {
  color: #fff;
  background-color: #ff9500;
  border: 1px solid #ff9500;
}

// 危险按钮样式 - 红色
.btn-danger {
  color: #fff;
  background-color: #ff3b30;
  border: 1px solid #ff3b30;
}

// 信息按钮样式 - 青色
.btn-info {
  color: #fff;
  background-color: #5ac8fa;
  border: 1px solid #5ac8fa;
}

// 搜索栏按钮特殊样式
.flex-1 > .btn-primary,
.flex-1 > .btn-success {
  width: 40px;
  height: 36px;
}

// 表单样式
.form-textarea {
  box-sizing: border-box;
  width: 100%;
  background-color: #ffffff;
  border: 1px solid #bfbfc3;
  border-radius: 8px;
}

// 图标按钮样式
.btn-icon {
  width: 28px;
  height: 28px;
  padding: 0;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

// 分段选择器样式
.segment-selector {
  display: flex;
  width: 100%;
  height: 36px;
  overflow: hidden;
  background-color: #f1f1f1;
  border-radius: 8px;
}

.segment-item {
  position: relative;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #666;
  transition: all 0.3s;

  &.active {
    font-weight: 500;
    color: #007aff;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  &:not(.active) {
    cursor: pointer;
    &:active {
      opacity: 0.7;
    }
  }
}

// 教材变更卡片样式
.bg-gray-50.p-2.rounded-lg {
  transition: all 0.3s ease;
}

.bg-gray-50.p-2.rounded-lg:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

// 任务列表项样式
.grid.grid-cols-2.gap-2 {
  font-size: 12px;
  line-height: 1.5;
}

// 特殊按钮样式 - 无需征订教材
.btn-special {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 0 12px;
  font-size: 13px;
  color: #fff;
  background-color: #6366f1;
  border: 1px solid #6366f1;
  border-radius: 6px;
  transition: opacity 0.3s;

  &:active {
    opacity: 0.85;
  }
}
</style>
