<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的教材选用',
  },
}
</route>
<template>
  <view class="textbook-selection bg-gray-50 pt-3">
    <!-- 学年选择器 -->
    <view class="mb-3 ml-3">
      <school-year-picker
        size="large"
        v-model:yearValue="currentYearValue"
        :show-all-year="false"
        :show-toast="false"
        :default-type="'next'"
        @yearChange="handleYearChange"
      />
    </view>

    <!-- 统计信息卡片 -->
    <view class="mb-3 mx-3">
      <view class="bg-white rounded-xl p-3 shadow-sm">
        <view class="flex items-center justify-between mb-2">
          <view class="text-base font-semibold text-gray-800 flex items-center">
            <wd-icon name="chart" color="#3b82f6" class="mr-1" />
            <text>教材选用统计</text>
          </view>
          <view class="flex items-center">
            <text class="text-sm text-gray-500 mr-2">
              {{ statistics.xnxq || currentYearValue }}
            </text>
            <wd-icon
              name="refresh"
              color="#6b7280"
              size="16px"
              @click="fetchStatistics"
              class="cursor-pointer"
            />
          </view>
        </view>

        <view class="grid grid-cols-2 gap-3">
          <!-- 教学任务统计 -->
          <view class="bg-blue-50 rounded-lg p-3">
            <view class="text-sm text-gray-600 mb-1">教学任务</view>
            <view class="text-xl font-bold text-blue-600 mb-1">
              {{ statistics.tasksTotal || 0 }}
            </view>
            <view class="flex flex-wrap text-xs text-gray-500">
              <view class="mr-2 mb-1 flex items-center">
                <view class="w-2 h-2 rounded-full bg-yellow-500 mr-1"></view>
                <text>未选用: {{ statistics.tasksNoSelect || 0 }}</text>
              </view>
              <view class="mr-2 mb-1 flex items-center">
                <view class="w-2 h-2 rounded-full bg-green-500 mr-1"></view>
                <text>已选用: {{ statistics.tasksSelect || 0 }}</text>
              </view>
              <view class="flex items-center">
                <view class="w-2 h-2 rounded-full bg-gray-500 mr-1"></view>
                <text>无需选用: {{ statistics.tasksNoNeed || 0 }}</text>
              </view>
            </view>
          </view>

          <!-- 教材申报统计 -->
          <view class="bg-green-50 rounded-lg p-3">
            <view class="text-sm text-gray-600 mb-1">教材申报</view>
            <view class="text-xl font-bold text-green-600 mb-1">
              {{ statistics.materialTotal || 0 }}
            </view>
            <view class="flex flex-wrap text-xs text-gray-500">
              <view class="mr-2 mb-1 flex items-center">
                <view class="w-2 h-2 rounded-full bg-orange-500 mr-1"></view>
                <text>审核中: {{ statistics.materialApprovalTotal || 0 }}</text>
              </view>
              <view class="mr-2 mb-1 flex items-center">
                <view class="w-2 h-2 rounded-full bg-green-500 mr-1"></view>
                <text>已通过: {{ statistics.materialPassTotal || 0 }}</text>
              </view>
              <view class="flex items-center mr-2">
                <view class="w-2 h-2 rounded-full bg-red-500 mr-1"></view>
                <text>未通过: {{ statistics.materialNoPassTotal || 0 }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 申报时间通知 -->
        <view
          v-if="notificationInfo"
          class="mt-3 py-2 px-3 rounded-lg bg-blue-50 border border-blue-200 flex items-center animate-pulse-light"
        >
          <wd-icon name="notification" color="#2563eb" size="16px" class="mr-2 flex-shrink-0" />
          <text class="text-sm text-blue-700 flex-1 font-medium">{{ notificationInfo }}</text>
        </view>

        <!-- 倒计时组件 -->
        <view
          v-if="courseCountdown.isActive"
          class="mt-3 p-3 rounded-lg bg-blue-50 border border-blue-200"
        >
          <view class="text-sm text-blue-700 font-medium mb-2 flex items-center">
            <wd-icon name="time" size="16px" color="#2563eb" class="mr-1" />
            距离申报结束时间还有
          </view>
          <view class="flex justify-center">
            <view class="countdown-block bg-white px-2 py-1 rounded-md text-center mx-1 shadow-sm">
              <view class="text-lg font-bold text-blue-600">{{ courseCountdown.days }}</view>
              <view class="text-xs text-gray-500">天</view>
            </view>
            <view class="text-blue-600 font-bold self-center mx-1">:</view>
            <view class="countdown-block bg-white px-2 py-1 rounded-md text-center mx-1 shadow-sm">
              <view class="text-lg font-bold text-blue-600">{{ courseCountdown.hours }}</view>
              <view class="text-xs text-gray-500">时</view>
            </view>
            <view class="text-blue-600 font-bold self-center mx-1">:</view>
            <view class="countdown-block bg-white px-2 py-1 rounded-md text-center mx-1 shadow-sm">
              <view class="text-lg font-bold text-blue-600">{{ courseCountdown.minutes }}</view>
              <view class="text-xs text-gray-500">分</view>
            </view>
            <view class="text-blue-600 font-bold self-center mx-1">:</view>
            <view class="countdown-block bg-white px-2 py-1 rounded-md text-center mx-1 shadow-sm">
              <view class="text-lg font-bold text-blue-600">{{ courseCountdown.seconds }}</view>
              <view class="text-xs text-gray-500">秒</view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 分段选择器 -->
    <view class="mx-3 mb-3">
      <view class="bg-white rounded-xl p-1 shadow-sm">
        <wd-tabs v-model="activeTabIndex" :sticky="false" :animated="true" style="width: 100%">
          <wd-tab v-for="(tab, index) in tabs" :key="index" :title="tab.name" />
        </wd-tabs>
      </view>
    </view>

    <!-- 加载中提示 -->
    <view v-if="loading" class="flex justify-center items-center py-10">
      <wd-loading color="#3b82f6" />
      <text class="ml-2 text-gray-600">加载中...</text>
    </view>

    <!-- Tab内容区域 -->
    <view v-else>
      <!-- Tab 1: 教材选用列表 -->
      <view v-if="activeTabIndex === 0">
        <!-- 搜索和筛选栏 -->

        <button
          class="btn-success mx-3"
          @click="navigateToAddTextbook"
          :class="{ 'opacity-50': !allowAddMaterial }"
        >
          <wd-icon name="add" class="mr-2" />
          新增教材选用
        </button>
        <!-- <view class="bg-white p-3 shadow-sm">
          <view class="flex space-x-2 mb-3">
            <view class="flex-1 relative">
              <wd-input
                v-model="searchText"
                placeholder="搜索教材、ISBN、编者..."
                class="w-full bg-gray-100 rounded-lg"
                clearable
                @change="fetchTextbookList"
              />
              <wd-icon
                name="search"
                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
            </view>
            <button class="btn-primary">
              <wd-icon name="filter" />
            </button>
            <button class="btn-success" @click="navigateToAddTextbook">
              <wd-icon name="add" />
            </button>
          </view>

          <view class="flex space-x-2 overflow-x-auto">
            <view
              class="px-3 py-1 rounded-full text-xs whitespace-nowrap cursor-pointer"
              :class="
                filterStatus === 'all' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
              "
              @click="filterTextbooks('all')"
            >
              全部({{ totalCount }})
            </view>
            <view
              class="px-3 py-1 rounded-full text-xs whitespace-nowrap cursor-pointer"
              :class="
                filterStatus === 'used' ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
              "
              @click="filterTextbooks('used')"
            >
              已选用
            </view>
            <view
              class="px-3 py-1 rounded-full text-xs whitespace-nowrap cursor-pointer"
              :class="
                filterStatus === 'pending'
                  ? 'bg-orange-100 text-orange-600'
                  : 'bg-gray-100 text-gray-600'
              "
              @click="filterTextbooks('pending')"
            >
              待审批
            </view>
            <view
              class="px-3 py-1 rounded-full text-xs whitespace-nowrap cursor-pointer"
              :class="
                filterStatus === 'approved'
                  ? 'bg-green-100 text-green-600'
                  : 'bg-gray-100 text-gray-600'
              "
              @click="filterTextbooks('approved')"
            >
              已通过
            </view>
            <view
              class="px-3 py-1 rounded-full text-xs whitespace-nowrap cursor-pointer"
              :class="
                filterStatus === 'rejected'
                  ? 'bg-red-100 text-red-600'
                  : 'bg-gray-100 text-gray-600'
              "
              @click="filterTextbooks('rejected')"
            >
              未通过
            </view>
          </view>
        </view> -->

        <!-- 无数据提示 -->
        <view
          v-if="textbookList.length === 0"
          class="flex flex-col justify-center items-center py-10"
        >
          <wd-icon name="info-circle" size="48px" color="#9ca3af" />
          <text class="mt-3 text-gray-500">暂无教材数据</text>
        </view>

        <!-- 教材列表 -->
        <view v-else class="p-3 space-y-3">
          <!-- 教材项目 -->
          <view
            v-for="item in textbookList"
            :key="item.id"
            class="bg-white rounded-xl p-3 shadow-sm border-l-4 relative"
            :class="getStatusBorderStyle(item)"
          >
            <!-- 删除按钮移除 - 将移到底部按钮区 -->

            <!-- 审核状态徽章 - 右上角 -->
            <view
              class="absolute top-3 right-3 px-2 py-0.5 rounded-full text-xs font-medium z-2 flex items-center"
              :class="[getStatusInfo(item).bgClass, getStatusInfo(item).textClass]"
            >
              <wd-icon :name="getStatusInfo(item).icon" size="12px" class="mr-0.5" />
              <text>{{ getStatusInfo(item).text }}</text>
            </view>

            <view class="flex flex-col mb-3">
              <!-- 第一行：学年学期、班级 -->
              <view class="font-semibold text-gray-800 text-sm mb-1.5 flex flex-wrap">
                <view
                  class="inline-flex items-center bg-blue-50 rounded-md py-0.5 px-1.5 mr-2 text-blue-600 mb-1"
                >
                  <wd-icon name="time" size="12px" class="mr-0.5" />
                  <text>
                    {{ item.xn && item.xq ? `${item.xn}-${item.xq}` : currentYearValue }}
                  </text>
                </view>
                <view class="mr-3 mb-1">{{ item.bjmc || '未知班级' }}</view>
              </view>
              <!-- 课程单独一行显示 -->
              <view class="font-semibold text-gray-800 text-sm mb-1.5 flex items-center">
                <wd-icon name="books" size="14px" class="mr-1 text-blue-600" />
                <text>{{ item.kcmc || '未知课程' }}</text>
              </view>
              <view class="font-semibold text-gray-900 text-sm mb-2 leading-normal">
                <view
                  class="inline-flex items-center bg-orange-50 rounded-md py-0.5 px-1.5 mr-2 text-orange-600 mb-1"
                >
                  <wd-icon name="filter" size="12px" class="mr-0.5" />
                  <text>{{ getSelectionTypeLabel(item.xylb) }}</text>
                </view>
                <text class="break-all">{{ item.jcmc }}</text>
              </view>
            </view>

            <view class="info-grid mb-3">
              <view class="info-item">
                <text class="info-label">选用类别:</text>
                <text class="info-value">{{ getSelectionTypeLabel(item.xylb) }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">ISBN:</text>
                <text class="info-value">{{ item.isbn }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">主编:</text>
                <text class="info-value">{{ item.zybz }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">出版社:</text>
                <text class="info-value">{{ item.cbs }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">版次:</text>
                <text class="info-value">{{ item.bc || '无' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">教材类别:</text>
                <text class="info-value">{{ getMaterialCategoryLabel(item.jclb) }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">教材类型:</text>
                <text class="info-value">{{ getMaterialTypeLabel(item.jclx) }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">选用时间:</text>
                <text class="info-value">{{ item.create_time }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">操作人:</text>
                <text class="info-value">{{ item.zdjsxm || '未知' }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">首次选用:</text>
                <text
                  class="info-value"
                  :class="item.sfscxy === '1' ? 'text-green-600' : 'text-red-600'"
                >
                  <wd-icon :name="item.sfscxy === '1' ? 'check' : 'close'" />
                  {{ item.sfscxy === '1' ? '是' : '否' }}
                </text>
              </view>
              <view class="info-item">
                <text class="info-label">审核状态:</text>
                <text class="info-value" :class="getStatusInfo(item).textClass">
                  {{ getStatusInfo(item).text }}
                </text>
              </view>
              <view class="info-item">
                <text class="info-label">评价审阅:</text>
                <text class="info-value" :class="getEvaluationInfo(item).textClass">
                  <wd-icon :name="getEvaluationInfo(item).icon" class="mr-1" />
                  {{ getEvaluationInfo(item).text }}
                </text>
              </view>
            </view>

            <view class="flex space-x-2 pt-2 border-t border-gray-100">
              <button class="btn-primary flex-1" @click="showApprovalHistory(item.id)">
                审批记录
              </button>
              <button class="btn-info" @click="handlePerfectTextbook(item.jcxxid)">完善教材</button>
              <!-- 根据审核状态条件显示编辑或变更按钮 -->
              <button
                v-if="String(item.shzt) === '1'"
                class="btn-info"
                @click="handleChangeTextbook(item.id)"
              >
                变更
              </button>
              <button v-else class="btn-success" @click="handleEditTextbook(item.id)">编辑</button>
              <button class="btn-warning" @click="showEvaluation(item.id)">评价</button>
              <!-- 将删除按钮移到这里，并添加条件：只有未审核通过的教材才显示删除按钮 -->
              <button
                v-if="String(item.shzt) !== '1'"
                class="btn-danger"
                @click="confirmDelete(item.id)"
              >
                <wd-icon name="delete" size="16px" />
              </button>
            </view>
          </view>
        </view>
      </view>

      <!-- Tab 2: 无需征订教材任务列表 -->
      <view v-else-if="activeTabIndex === 1" class="p-3 space-y-3 pt-0">
        <!-- 添加"添加无需征订教材"按钮 -->
        <view class="flex justify-end mb-3">
          <button
            class="btn-success flex items-center flex-1"
            @click="handleAddNoNeedMaterial"
            :class="{ 'opacity-50': !allowAddMaterial }"
          >
            <wd-icon name="add" size="16px" class="mr-1" />
            <text>添加无需征订教材</text>
          </button>
        </view>
        <!-- 加载中提示 -->
        <view v-if="loadingNoNeedList" class="flex justify-center items-center py-10">
          <wd-loading color="#3b82f6" />
          <text class="ml-2 text-gray-600">加载中...</text>
        </view>
        <!-- 无数据提示 -->
        <view
          v-else-if="noOrderTaskList.length === 0"
          class="flex flex-col justify-center items-center py-10"
        >
          <wd-icon name="info-circle" size="48px" color="#9ca3af" />
          <text class="mt-3 text-gray-500">暂无无需征订教材任务数据</text>
        </view>

        <!-- 无需征订教材任务列表 -->
        <view v-else>
          <view
            v-for="(item, index) in noOrderTaskList"
            :key="item.id || index"
            class="bg-white rounded-xl p-3 shadow-sm border-l-4 border-gray-400 mb-3 relative"
          >
            <!-- 审核状态徽章 - 右上角 -->
            <view
              class="absolute top-3 right-3 px-2 py-0.5 rounded-full text-xs font-medium z-2 flex items-center"
              :class="[
                Number(item.jcxybz) === 2
                  ? 'bg-green-100 text-green-600'
                  : Number(item.jcxybz) === 3
                    ? 'bg-orange-100 text-orange-600'
                    : 'bg-gray-100 text-gray-600',
              ]"
            >
              <wd-icon :name="getAuditStatusIcon(Number(item.jcxybz))" size="12px" class="mr-0.5" />
              <text>{{ getAuditStatusText(Number(item.jcxybz)) }}</text>
            </view>

            <view class="flex flex-col mb-3">
              <!-- 学年学期、班级 -->
              <view class="font-semibold text-gray-800 text-sm mb-1.5 flex flex-wrap">
                <view
                  class="inline-flex items-center bg-blue-50 rounded-md py-0.5 px-1.5 mr-2 text-blue-600 mb-1"
                >
                  <wd-icon name="time" size="12px" class="mr-0.5" />
                  <text>{{ `${item.xn}-${item.xq}` || currentYearValue }}</text>
                </view>
                <view class="mr-3 mb-1">{{ item.bjmc || '未知班级' }}</view>
              </view>
              <view class="font-semibold text-gray-800 text-sm mb-1.5 flex items-center">
                <wd-icon name="books" size="14px" class="mr-1 text-blue-600" />
                <text>{{ item.kcmc }}</text>
                <text class="ml-2 text-xs text-gray-500">({{ item.kcdm }})</text>
              </view>
            </view>

            <view class="info-grid mb-3">
              <view class="info-item">
                <text class="info-label">考核方式:</text>
                <text class="info-value">
                  {{ getAssessmentMethodText(item.khfs) }}
                </text>
              </view>
              <view class="info-item">
                <text class="info-label">总学时:</text>
                <text class="info-value">{{ item.xqzxs }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">周学时:</text>
                <text class="info-value">{{ item.zxs }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">周数:</text>
                <text class="info-value">{{ item.zs }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">系数:</text>
                <text class="info-value">{{ item.gzlxs }}</text>
              </view>
              <view class="info-item">
                <text class="info-label">成绩:</text>
                <text class="info-value">
                  {{ getGradeTypeText(item.cjtjbz) }}
                </text>
              </view>
              <view class="info-item col-span-2 bg-gray-50 p-2 rounded-lg">
                <view class="flex items-center mb-1">
                  <wd-icon name="info-circle" size="14px" color="#10b981" class="mr-1" />
                  <text class="font-medium text-gray-700">无需原因</text>
                </view>
                <view class="pl-5">
                  <text class="info-value text-green-600 break-all">
                    {{ item.jcxysm || '未提供原因' }}
                  </text>
                </view>
              </view>
            </view>

            <view class="flex space-x-2 pt-2 border-t border-gray-100">
              <button class="btn-primary" @click="showApprovalHistory(item.id)">审批记录</button>
              <button class="btn-warning" @click="handleEditNoNeedMaterial(item)">编辑</button>
            </view>
          </view>
        </view>
      </view>

      <!-- Tab 3: 教材变更申请列表 -->
      <view v-else-if="activeTabIndex === 2" class="p-3 space-y-3">
        <!-- 加载中提示 -->
        <view v-if="loadingChangeList" class="flex justify-center items-center py-10">
          <wd-loading color="#3b82f6" />
          <text class="ml-2 text-gray-600">加载中...</text>
        </view>

        <!-- 无数据提示 -->
        <view
          v-else-if="changeRequestList.length === 0"
          class="flex flex-col justify-center items-center py-10"
        >
          <wd-icon name="info-circle" size="48px" color="#9ca3af" />
          <text class="mt-3 text-gray-500">暂无教材变更申请数据</text>
        </view>

        <!-- 教材变更申请列表 -->
        <view v-else>
          <view
            v-for="item in changeRequestList"
            :key="item.id"
            class="bg-white rounded-xl p-3 shadow-sm border-l-4 border-orange-400 mb-3 relative"
          >
            <!-- 状态徽章 -->
            <view
              class="absolute top-3 right-3 px-2 py-0.5 rounded-full text-xs font-medium z-2 flex items-center"
              :class="getChangeRequestStatusStyle(item)"
            >
              <wd-icon :name="getChangeRequestStatusIcon(item)" size="12px" class="mr-0.5" />
              <text>{{ getChangeRequestStatusText(item) }}</text>
            </view>

            <view class="flex flex-col mb-3">
              <!-- 学年学期、课程 -->
              <view class="font-semibold text-gray-800 text-sm mb-1.5 flex flex-wrap">
                <view
                  class="inline-flex items-center bg-blue-50 rounded-md py-0.5 px-1.5 mr-2 text-blue-600 mb-1"
                >
                  <wd-icon name="time" size="12px" class="mr-0.5" />
                  <text>{{ item.xn }}-{{ item.xq }}</text>
                </view>
                <view
                  class="inline-flex items-center bg-orange-50 rounded-md py-0.5 px-1.5 mr-2 text-orange-600 mb-1"
                >
                  <wd-icon name="filter" size="12px" class="mr-0.5" />
                  <text>{{ getSelectionTypeLabel(item.xylb) }}</text>
                </view>
              </view>

              <!-- 课程和班级 -->
              <view class="font-semibold text-gray-800 text-sm mb-1.5 flex items-center">
                <wd-icon name="books" size="14px" class="mr-1 text-blue-600" />
                <text>{{ item.kcmc }}</text>
              </view>
              <view class="text-sm text-gray-600 mb-2">
                <text>班级: {{ item.bjmc }}</text>
              </view>

              <!-- 原教材与新教材 -->
              <view class="bg-gray-50 p-2 rounded-lg mb-2">
                <view class="text-xs text-gray-500 mb-1">原教材</view>
                <view class="text-sm font-medium text-gray-800 mb-2">{{ item.yjcmc }}</view>
                <view class="flex items-center mb-1">
                  <wd-icon name="arrow-down" size="16px" color="#3b82f6" class="mr-1" />
                </view>
                <view class="text-xs text-gray-500 mb-1">新教材</view>
                <view class="text-sm font-medium text-gray-800">{{ item.jcmc }}</view>
                <view class="flex flex-wrap mt-2">
                  <view class="mr-2 mb-1 text-xs text-gray-500">
                    类别:
                    <text class="text-gray-700">{{ getMaterialCategoryLabel(item.jclb) }}</text>
                  </view>
                  <view class="mr-2 mb-1 text-xs text-gray-500">
                    类型:
                    <text class="text-gray-700">{{ getMaterialTypeLabel(item.jclx) }}</text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 详细信息 -->
            <view class="grid grid-cols-2 gap-2 text-xs mb-3">
              <view class="flex">
                <text class="text-gray-500 mr-1">申请时间:</text>
                <text class="text-gray-800">{{ item.sqsj }}</text>
              </view>
              <view class="flex">
                <text class="text-gray-500 mr-1">申请人:</text>
                <text class="text-gray-800">{{ item.sqrxm }}</text>
              </view>
              <view class="flex">
                <text class="text-gray-500 mr-1">变更原因:</text>
                <text class="text-orange-600 font-medium">{{ item.bgyysm }}</text>
              </view>
            </view>

            <!-- 审核状态 -->
            <view class="bg-gray-50 p-2 rounded-lg mb-3">
              <view class="text-xs font-medium text-gray-700 mb-2">审核状态</view>
              <view class="grid grid-cols-3 gap-2">
                <view class="flex flex-col items-center">
                  <text class="text-xs text-gray-500 mb-1">教研室</text>
                  <view class="flex items-center">
                    <view
                      class="w-3 h-3 rounded-full mr-1"
                      :class="{
                        'bg-green-500': item.jyssh === 1,
                        'bg-red-500': item.jyssh === 2,
                        'bg-orange-500': item.jyssh === 0,
                      }"
                    ></view>
                    <text
                      class="text-xs"
                      :class="{
                        'text-green-600': item.jyssh === 1,
                        'text-red-600': item.jyssh === 2,
                        'text-orange-600': item.jyssh === 0,
                      }"
                    >
                      {{ getApprovalStatusText(item.jyssh) }}
                    </text>
                  </view>
                </view>
                <view class="flex flex-col items-center">
                  <text class="text-xs text-gray-500 mb-1">二级学院</text>
                  <view class="flex items-center">
                    <view
                      class="w-3 h-3 rounded-full mr-1"
                      :class="{
                        'bg-green-500': item.xbsh === 1,
                        'bg-red-500': item.xbsh === 2,
                        'bg-orange-500': item.xbsh === 0,
                      }"
                    ></view>
                    <text
                      class="text-xs"
                      :class="{
                        'text-green-600': item.xbsh === 1,
                        'text-red-600': item.xbsh === 2,
                        'text-orange-600': item.xbsh === 0,
                      }"
                    >
                      {{ getApprovalStatusText(item.xbsh) }}
                    </text>
                  </view>
                </view>
                <view class="flex flex-col items-center">
                  <text class="text-xs text-gray-500 mb-1">教务处</text>
                  <view class="flex items-center">
                    <view
                      class="w-3 h-3 rounded-full mr-1"
                      :class="{
                        'bg-green-500': item.jwsh === 1,
                        'bg-red-500': item.jwsh === 2,
                        'bg-orange-500': item.jwsh === 0,
                      }"
                    ></view>
                    <text
                      class="text-xs"
                      :class="{
                        'text-green-600': item.jwsh === 1,
                        'text-red-600': item.jwsh === 2,
                        'text-orange-600': item.jwsh === 0,
                      }"
                    >
                      {{ getApprovalStatusText(item.jwsh) }}
                    </text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 操作按钮 -->
            <view class="flex space-x-2 pt-2 border-t border-gray-100">
              <button class="btn-primary flex-1" @click="showApprovalHistory(item.id)">
                审批记录
              </button>
              <button class="btn-warning" @click="handleViewChangeDetail(item.id)">
                {{
                  !(item.jyssh === 1 && item.xbsh === 1 && item.jwsh === 1) ? '编辑' : '查看详情'
                }}
              </button>
              <button
                class="btn-danger"
                v-if="item.shzt === 0"
                @click="confirmCancelChange(item.id)"
              >
                申请撤销
              </button>

              <button
                class="btn-danger"
                v-if="!(item.jyssh === 1 && item.xbsh === 1 && item.jwsh === 1)"
                @click="confirmCancelChange(item.id)"
              >
                申请撤销
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 审批记录模态框 -->
    <wd-popup v-model="showApprovalModal" position="center" :close-on-click-modal="true">
      <view class="modal-content">
        <view class="p-4 border-b">
          <view class="flex justify-between items-center">
            <view class="text-lg font-semibold">审批记录</view>
            <wd-icon name="close" @click="showApprovalModal = false" />
          </view>
        </view>
        <view class="p-4">
          <!-- 加载中提示 -->
          <view v-if="loadingApproval" class="flex justify-center items-center py-10">
            <wd-loading color="#3b82f6" />
            <text class="ml-2 text-gray-600">加载审批记录中...</text>
          </view>

          <!-- 无数据提示 -->
          <view
            v-else-if="!workflowTimelineData || flowSteps.length === 0"
            class="flex flex-col justify-center items-center py-10"
          >
            <wd-icon name="info-circle" size="48px" color="#9ca3af" />
            <text class="mt-3 text-gray-500">暂无审批记录</text>
          </view>

          <!-- 多个审批分支 -->
          <view v-else v-for="(branch, branchIndex) in flowSteps" :key="branchIndex" class="mb-6">
            <!-- 分支标题和折叠按钮 -->
            <view class="flex justify-between items-center mb-3">
              <!-- 分支标题 -->
              <view
                v-if="flowSteps.length > 1"
                class="text-sm font-medium flex items-center"
                :class="{
                  'text-blue-600': !isBranchRejected(branch),
                  'text-red-500': isBranchRejected(branch),
                }"
              >
                <wd-icon
                  :name="isBranchRejected(branch) ? 'close-circle' : 'flag'"
                  size="14px"
                  :color="isBranchRejected(branch) ? '#ef4444' : '#2563eb'"
                  class="mr-1"
                ></wd-icon>
                {{ branchIndex === 0 ? '审批流程 1' : '历史流程 ' + (branchIndex + 1) }}
                <view
                  v-if="isBranchRejected(branch)"
                  class="ml-2 px-1.5 py-0.5 rounded-sm text-xs text-white bg-red-500"
                >
                  已拒绝
                </view>
              </view>

              <!-- 折叠/展开按钮 -->
              <view
                v-if="flowSteps.length > 1"
                class="flex items-center cursor-pointer text-xs text-gray-500 px-2 py-1 rounded hover:bg-gray-100"
                @tap="toggleBranchExpand(branchIndex)"
              >
                {{ expandedBranches[branchIndex] ? '收起' : '展开' }}
                <wd-icon
                  :name="expandedBranches[branchIndex] ? 'chevron-up' : 'chevron-down'"
                  size="14px"
                  color="#666"
                  class="ml-1"
                ></wd-icon>
              </view>
            </view>

            <!-- 流程节点内容 - 根据折叠状态显示或隐藏 -->
            <view class="relative py-2" v-if="expandedBranches[branchIndex]">
              <view
                class="flex relative mb-4"
                v-for="(step, index) in branch"
                :key="step.id"
                :class="{ 'mb-0': index === branch.length - 1 }"
              >
                <view
                  class="w-4 h-4 rounded-full mr-4 flex-shrink-0 z-2"
                  :class="{
                    'bg-green-500': step.status === 'success',
                    'bg-blue-500': step.status === 'active',
                    'bg-red-500': step.status === 'rejected',
                    'bg-gray-400': step.status === 'waiting',
                  }"
                ></view>
                <view
                  v-if="index !== branch.length - 1"
                  class="absolute left-2 top-4 w-0.5 h-full bg-gray-200 z-1"
                ></view>
                <view class="flex-1">
                  <view class="flex items-center justify-between">
                    <view class="flex-1">
                      <view class="text-sm font-medium text-gray-800 mb-1">{{ step.name }}</view>
                      <view class="text-xs text-gray-500 mb-1">{{ step.handler }}</view>
                      <view class="flex items-center">
                        <view class="text-xs text-gray-400 mb-1">{{ step.time }}</view>
                        <!-- 显示拒绝标记 -->
                        <view
                          v-if="step.status === 'rejected'"
                          class="ml-2 px-1.5 py-0.5 rounded text-xs text-white bg-red-500"
                        >
                          已拒绝
                        </view>
                      </view>
                    </view>
                    <view
                      v-if="step.status === 'success'"
                      class="w-6 h-6 rounded-full bg-green-100 flex items-center justify-center"
                    >
                      <wd-icon name="check" size="14px" color="#22c55e"></wd-icon>
                    </view>
                    <view
                      v-else-if="step.status === 'rejected'"
                      class="w-6 h-6 rounded-full bg-red-100 flex items-center justify-center"
                    >
                      <wd-icon name="close" size="14px" color="#ef4444"></wd-icon>
                    </view>
                    <view
                      v-else-if="step.status === 'active'"
                      class="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center"
                    >
                      <wd-icon name="time" size="14px" color="#3b82f6"></wd-icon>
                    </view>
                  </view>

                  <!-- 审批意见显示 - 只在有意见时显示 -->
                  <view
                    v-if="step.comment"
                    class="mt-2 py-2 px-3 rounded-lg border-l-2 text-sm text-gray-700"
                    :class="{
                      'bg-gray-50 border-blue-400': !step.isRejected,
                      'bg-red-50 border-red-400': step.isRejected,
                    }"
                  >
                    <view class="text-sm leading-relaxed break-all">{{ step.comment }}</view>
                  </view>
                </view>
              </view>
            </view>

            <!-- 折叠状态展示 -->
            <view
              v-if="!expandedBranches[branchIndex]"
              class="flex items-center py-2 px-3 rounded-md text-sm"
              :class="branchStatusSummaries[branchIndex].bgColor"
            >
              <wd-icon
                :name="branchStatusSummaries[branchIndex].icon"
                size="14px"
                :color="branchStatusSummaries[branchIndex].color"
                class="mr-2"
              ></wd-icon>
              <view class="flex-1" :class="branchStatusSummaries[branchIndex].textColor">
                {{ branchStatusSummaries[branchIndex].message }}
              </view>
              <view class="ml-2 text-xs text-gray-400">点击"展开"查看详情</view>
            </view>

            <!-- 分支分隔符 -->
            <view
              v-if="branchIndex < flowSteps.length - 1"
              class="border-t border-dashed border-gray-300 my-4"
            ></view>
          </view>
        </view>
      </view>
    </wd-popup>

    <!-- 评价模态框 -->
    <wd-popup v-model="showEvaluationModal" position="center" :close-on-click-modal="true">
      <view class="modal-content">
        <view class="p-4 border-b">
          <view class="flex justify-between items-center">
            <view class="text-lg font-semibold">教材评价</view>
            <wd-icon name="close" @click="showEvaluationModal = false" />
          </view>
        </view>
        <view class="p-4">
          <view class="space-y-4">
            <!-- 当前选择教材 -->
            <view class="space-y-1">
              <text class="text-sm text-gray-500">当前选择教材</text>
              <view class="font-medium text-gray-800">
                {{ currentTextbook?.jcmc || '未选择教材' }}
              </view>
            </view>

            <!-- 教学参考书 -->
            <view class="space-y-2">
              <view class="flex items-center">
                <text class="font-medium">教学参考书</text>
                <text class="text-red-500 ml-1">*</text>
              </view>
              <view class="flex items-center">
                <wd-switch v-model="evaluationForm.isReference" size="20px" />
                <text class="ml-2 text-gray-700">
                  {{ evaluationForm.isReference ? '是' : '否' }}
                </text>
              </view>
            </view>

            <!-- 实验指导书 -->
            <view class="space-y-2">
              <view class="flex items-center">
                <text class="font-medium">实验指导书</text>
                <text class="text-red-500 ml-1">*</text>
              </view>
              <view class="flex items-center">
                <wd-switch v-model="evaluationForm.isLabGuide" size="20px" />
                <text class="ml-2 text-gray-700">
                  {{ evaluationForm.isLabGuide ? '是' : '否' }}
                </text>
              </view>
            </view>

            <!-- 评价与意见 -->
            <view class="space-y-2">
              <text class="font-medium">评价与意见</text>
              <wd-textarea
                v-model="evaluationForm.comment"
                placeholder="请输入您对该教材的评价与意见..."
                class="form-textarea"
                :maxlength="200"
                show-count
              />
            </view>

            <!-- 提交按钮 -->
            <button class="btn-primary w-full" @click="submitEvaluation">提交评价</button>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch, onBeforeUnmount } from 'vue'
import { deleteTextbook, submitTextbookEvaluation } from '@/service/textbook'
import {
  getTeachingMaterialSelectionList,
  getTeachingMaterialStatistics,
  getTeachingMaterialChangeList,
  cancelTeachingMaterial,
  getTeachingMaterialNoNeedList,
} from '@/service/teachingMaterial'
import { getWorkflowTimeline } from '@/service/workflow'
import { useTeachingTaskStore } from '@/store/teachingTask'
import { useTextbookStore } from '@/store/textbook'
import { useNoNeedMaterialStore } from '@/store/noNeedMaterial'
import type { TextbookEvaluationParams } from '@/types/textbook'
import type { WorkflowTimelineResponse, WorkflowProcessItem } from '@/types/workflow'
import { loadDictData, getDictLabel, getDictClass, getDictOptions } from '@/utils/dict'
import type { DictData } from '@/types/system'
import type {
  TeachingMaterialSelectionQuery,
  TeachingMaterialSelectionItem,
  TeachingMaterialStatisticsResponse,
  TeachingMaterialChangeItem,
  TeachingMaterialChangeQuery,
  TeachingMaterialNoNeedItem,
  TeachingMaterialNoNeedQuery,
} from '@/types/teachingMaterial'
import SchoolYearPicker from '@/components/SchoolYearPicker/index.vue'

// 获取当前教学任务信息
const teachingTaskStore = useTeachingTaskStore()

// 搜索关键词
const searchText = ref('')

// 模态框状态
const showApprovalModal = ref(false)
const showEvaluationModal = ref(false)
const currentBookId = ref(0)
const rating = ref(0)
const loadingApproval = ref(false) // 添加加载审批记录的状态

// 当前选中的教材信息
const currentTextbook = ref<TeachingMaterialSelectionItem | null>(null)

// 教材列表数据
const textbookList = ref<TeachingMaterialSelectionItem[]>([])
const totalCount = ref(0)
const loading = ref(false)
const filterStatus = ref('all')

// 标签页数据
const tabs = ref([
  { name: '教材选用', key: 'textbooks' },
  { name: '无需征订', key: 'noOrderTasks' },
  { name: '变更申请', key: 'changeRequests' },
])
const activeTabIndex = ref(0)

// 无需征订教材任务列表数据
const noOrderTaskList = ref<TeachingMaterialNoNeedItem[]>([])
const loadingNoNeedList = ref(false)

// 教材变更申请列表数据
const changeRequestList = ref<TeachingMaterialChangeItem[]>([])
const loadingChangeList = ref(false)

// 统计数据
const statistics = ref<TeachingMaterialStatisticsResponse>({
  access: '',
  tasksTotal: 0,
  tasksNoSelect: 0,
  tasksSelect: 0,
  tasksNoNeed: 0,
  materialTotal: 0,
  materialApprovalTotal: 0,
  materialPassTotal: 0,
  materialNoPassTotal: 0,
  xnxq: '',
  info: '',
})

// 是否允许新增教材（基于jssj状态）
const allowAddMaterial = ref(true)

// 申报时间信息
const textbookInfo = ref<string>('')

// 课程申报倒计时
const courseCountdown = ref({
  days: 0,
  hours: 0,
  minutes: 0,
  seconds: 0,
  totalSeconds: 0,
  isActive: false,
})

// 存储计时器引用，方便清除
const countdownTimer = ref<number | null>(null)

// 字典数据
const dictData = ref<Record<string, DictData[]>>({})

// 学期周次选择器的值
const currentYearValue = ref('')

// 查询参数
const queryParams = ref<TeachingMaterialSelectionQuery>({
  page: 1,
  pageSize: 20,
  sortBy: 'create_time',
  sortOrder: 'desc',
  semesters: '', // 学年学期字符串
})

// 评价表单数据
const evaluationForm = ref({
  isReference: false, // 是否为教学参考书
  isLabGuide: false, // 是否为实验指导书
  comment: '', // 评价与意见
})

// 学期周次选择器变化事件处理
const handleYearChange = (data: { label: string; value: string }) => {
  // 更新查询参数中的学期值
  queryParams.value.semesters = data.value

  // 重新获取数据
  fetchTextbookList()

  // 重新获取统计数据
  fetchStatistics()

  // 如果当前是无需征订标签页，重新获取无需征订列表
  if (activeTabIndex.value === 1) {
    fetchNoNeedList()
  }
  // 如果当前是变更申请标签页，重新获取变更申请列表
  else if (activeTabIndex.value === 2) {
    fetchChangeRequestList()
  }
}

// 获取教材列表
const fetchTextbookList = async () => {
  try {
    loading.value = true

    // 使用学期选择器的值，如果没有则使用默认值
    if (!queryParams.value.semesters) {
      queryParams.value.semesters = currentYearValue.value || ''
    }

    // 如果有搜索关键词，添加到查询参数
    if (searchText.value) {
      queryParams.value.jcmc = searchText.value
      queryParams.value.isbn = searchText.value
      queryParams.value.zybz = searchText.value
    } else {
      delete queryParams.value.jcmc
      delete queryParams.value.isbn
      delete queryParams.value.zybz
    }

    const res = await getTeachingMaterialSelectionList(queryParams.value)
    textbookList.value = res.items || []
    totalCount.value = res.total || 0

    // 保存info信息
    if (res.info) {
      textbookInfo.value = res.info
    }

    // 设置倒计时
    if (res.jssj === 0) {
      // 如果jssj为0，停止倒计时，并设置不允许新增教材
      stopCountdown()
      allowAddMaterial.value = false
    } else if (res.jssj && typeof res.jssj === 'number') {
      // 计算当前时间和结束时间的差值（秒）
      const now = Math.floor(Date.now() / 1000)
      const endTime = Math.floor(res.jssj / 1000)
      const seconds = endTime - now

      if (seconds > 0) {
        startCountdown(seconds)
        allowAddMaterial.value = true
      } else {
        // 如果时间差小于等于0，停止倒计时，并设置不允许新增教材
        stopCountdown()
        allowAddMaterial.value = false
      }
    }

    // 如果没有数据，提示用户
    if (textbookList.value.length === 0) {
      /* uni.showToast({
        title: '暂无教材数据',
        icon: 'none',
        duration: 2000,
      }) */
    }
  } catch (error) {
    console.error('获取教材列表失败:', error)
    uni.showToast({
      title: '获取教材列表失败',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    loading.value = false
  }
}

// 监听学期值变化
watch(
  () => currentYearValue.value,
  (newVal) => {
    if (newVal) {
      queryParams.value.semesters = newVal
    }
  },
)

// 根据状态筛选教材
const filterTextbooks = (status: string) => {
  filterStatus.value = status
  // 根据状态更新查询参数
  if (status === 'all') {
    delete queryParams.value.shzt
    delete queryParams.value.pjshzt
  } else if (status === 'pending') {
    queryParams.value.shzt = '0' // 待审批
    delete queryParams.value.pjshzt
  } else if (status === 'approved') {
    queryParams.value.shzt = '1' // 已通过
    delete queryParams.value.pjshzt
  } else if (status === 'rejected') {
    queryParams.value.shzt = '2' // 未通过
    delete queryParams.value.pjshzt
  } else if (status === 'used') {
    // 已选用的逻辑，可能需要根据实际接口调整
    delete queryParams.value.shzt
    delete queryParams.value.pjshzt
  }

  fetchTextbookList()
}

// 当前选中的分支索引
const currentBranchIndex = ref(0)

// 导航到新增教材页面
const navigateToAddTextbook = () => {
  // 如果不允许添加教材，则显示提示并返回
  if (!allowAddMaterial.value) {
    uni.showToast({
      title: '当前不在教材选用申报时间内',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 先清空store中的教材数据
  const textbookStore = useTextbookStore()
  textbookStore.clearCurrentTextbook()

  // 清除教学任务ID和标题
  teachingTaskStore.clearCurrentMaterialTask()

  // 将当前选中的学年学期值作为参数传递
  const semesterParam = currentYearValue.value
    ? `&semester=${encodeURIComponent(currentYearValue.value)}`
    : ''

  uni.navigateTo({
    url: `/pages/teacher/teaching-affairs/task-management/add-textbook?mode=add&source=material${semesterParam}`,
  })
}

// 处理变更教材
const handleChangeTextbook = (id: number) => {
  // 根据ID找到当前教材
  const textbook = textbookList.value.find((item) => item.id === id)

  // 获取store实例
  const textbookStore = useTextbookStore()

  // 先清空store
  textbookStore.clearCurrentTextbook()

  if (textbook) {
    // 将选中的教材存入store
    textbookStore.setCurrentTextbook(textbook)

    // 将教学任务ID和标题存入teachingTaskStore
    const jxrwid = textbook.jxrwid ? String(textbook.jxrwid) : ''
    const title = textbook.title || ''
    teachingTaskStore.setCurrentMaterialTask(jxrwid, title)

    // 导航到新增教材页面，传递变更模式参数
    uni.navigateTo({
      url: `/pages/teacher/teaching-affairs/task-management/add-textbook?mode=change&source=material`,
    })
  } else {
    uni.showToast({
      title: '未找到教材信息',
      icon: 'none',
    })
  }
}

// 处理编辑教材
const handleEditTextbook = (id: number) => {
  // 根据ID找到当前教材
  const textbook = textbookList.value.find((item) => item.id === id)

  // 获取store实例
  const textbookStore = useTextbookStore()

  // 先清空store
  textbookStore.clearCurrentTextbook()

  if (textbook) {
    // 将选中的教材存入store
    textbookStore.setCurrentTextbook(textbook)

    // 将教学任务ID和标题存入teachingTaskStore
    const jxrwid = textbook.jxrwid ? String(textbook.jxrwid) : ''
    const title = textbook.title || ''
    teachingTaskStore.setCurrentMaterialTask(jxrwid, title)

    // 导航到编辑教材页面，传递编辑模式参数
    uni.navigateTo({
      url: `/pages/teacher/teaching-affairs/task-management/add-textbook?mode=edit&source=material`,
    })
  } else {
    uni.showToast({
      title: '未找到教材信息',
      icon: 'none',
    })
  }
}

// 确认删除教材
const confirmDelete = (id: number) => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除该教材吗？删除后无法恢复。',
    confirmText: '删除',
    confirmColor: '#ff3b30',
    success: (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '删除中...' })

          deleteTextbook(id)
            .then(() => {
              uni.hideLoading()
              uni.showToast({
                title: '删除成功',
                icon: 'success',
              })

              // 刷新列表数据
              fetchTextbookList()
            })
            .catch((error) => {
              console.error('删除失败:', error)
              uni.hideLoading()
              uni.showToast({
                title: '删除失败，请重试',
                icon: 'none',
              })
            })
        } catch (error) {
          console.error('删除失败:', error)
          uni.hideLoading()
          uni.showToast({
            title: '删除失败，请重试',
            icon: 'none',
          })
        }
      }
    },
  })
}

// 加载工作流程数据
const workflowTimelineData = ref<WorkflowTimelineResponse | null>(null)

// 转换后的流程步骤数据，二维数组表示多个审批流程分支
const flowSteps = ref<FlowStepItem[][]>([])

// 控制审批流程折叠状态
const expandedBranches = ref<Record<number, boolean>>({})

// 审批步骤数据结构
interface FlowStepItem {
  id: number | string
  name: string
  handler: string
  time: string
  status: 'success' | 'rejected' | 'active' | 'waiting'
  comment: string
  commentInfo: {
    user: string
    time: string
    content: string
    status: number
  } | null
  isRejected: boolean
}

// 分支状态摘要接口
interface BranchStatusSummary {
  status: 'rejected' | 'approved' | 'active' | 'pending'
  message: string
  icon: string
  color: string
  bgColor: string
  textColor: string
}

// 将工作流节点数据转换为审批步骤格式
const convertToApprovalSteps = (processItems: WorkflowProcessItem[]): FlowStepItem[] => {
  return processItems.map((item) => {
    // 确定步骤状态
    let status: 'success' | 'rejected' | 'active' | 'waiting' = 'waiting'

    if (String(item.status) === '1') {
      status = 'success'
    } else if (String(item.status) === '2') {
      status = 'rejected'
    } else if (String(item.status) === '0') {
      status = 'active'
    }

    // 格式化时间
    const time =
      typeof item.time === 'string'
        ? item.time
        : item.create_time
          ? new Date(item.create_time * 1000).toLocaleString()
          : status === 'active'
            ? '待处理'
            : '未到达'

    return {
      id: item.id || Math.random().toString(36).substr(2, 9),
      name: item.name,
      handler: item.user_name || '',
      time,
      status,
      comment: item.approval_opinion || '',
      commentInfo: item.approval_opinion
        ? {
            user: item.user_name || '',
            time,
            content: item.approval_opinion,
            status: Number(item.status),
          }
        : null,
      isRejected: status === 'rejected',
    }
  })
}

// 判断分支是否被拒绝
const isBranchRejected = (branch: FlowStepItem[]): boolean => {
  return branch.some((step) => step.status === 'rejected')
}

// 判断分支是否已全部通过
const isBranchApproved = (branch: FlowStepItem[]): boolean => {
  // 检查是否所有节点都是成功状态
  return branch.every((step) => step.status === 'success')
}

// 获取分支审批状态摘要
const getBranchStatusSummary = (branch: FlowStepItem[]): BranchStatusSummary => {
  // 如果分支被拒绝，返回拒绝信息
  if (isBranchRejected(branch)) {
    // 查找被拒绝的节点和审批信息
    const rejectedStep = branch.find((step) => step.status === 'rejected')
    if (rejectedStep && rejectedStep.commentInfo) {
      return {
        status: 'rejected',
        message: `${rejectedStep.commentInfo.user} 拒绝：${rejectedStep.commentInfo.content}`,
        icon: 'close-circle',
        color: '#ef4444',
        bgColor: 'bg-red-50',
        textColor: 'text-red-600',
      }
    }
    return {
      status: 'rejected',
      message: '审批已被拒绝',
      icon: 'close-circle',
      color: '#ef4444',
      bgColor: 'bg-red-50',
      textColor: 'text-red-600',
    }
  }

  // 如果分支全部通过，返回通过信息
  if (isBranchApproved(branch)) {
    // 找到最后一个通过的节点
    const lastApprovedStep = [...branch].reverse().find((step) => step.status === 'success')
    if (lastApprovedStep && lastApprovedStep.commentInfo) {
      return {
        status: 'approved',
        message: `${lastApprovedStep.commentInfo.user} 已通过`,
        icon: 'check-circle',
        color: '#22c55e',
        bgColor: 'bg-green-50',
        textColor: 'text-green-600',
      }
    }
    return {
      status: 'approved',
      message: '审批已通过',
      icon: 'check-circle',
      color: '#22c55e',
      bgColor: 'bg-green-50',
      textColor: 'text-green-600',
    }
  }

  // 如果分支正在审批中
  const activeStep = branch.find((step) => step.status === 'active')
  if (activeStep) {
    return {
      status: 'active',
      message: `等待 ${activeStep.handler} 审批`,
      icon: 'time',
      color: '#3b82f6',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-600',
    }
  }

  // 默认状态
  return {
    status: 'pending',
    message: '等待审批',
    icon: 'info-circle',
    color: '#6b7280',
    bgColor: 'bg-gray-50',
    textColor: 'text-gray-500',
  }
}

// 获取每个分支的状态摘要的计算属性
const branchStatusSummaries = computed(() => {
  const summaries: Record<number, BranchStatusSummary> = {}

  if (flowSteps.value) {
    flowSteps.value.forEach((branch, index) => {
      summaries[index] = getBranchStatusSummary(branch)
    })
  }

  return summaries
})

// 切换分支的展开/折叠状态
const toggleBranchExpand = (branchIndex: number) => {
  expandedBranches.value[branchIndex] = !expandedBranches.value[branchIndex]
}

// 初始化各个审批分支的展开/折叠状态
const initBranchExpandState = () => {
  if (!flowSteps.value || flowSteps.value.length === 0) return

  // 查找是否有已通过的流程
  const approvedIndex = flowSteps.value.findIndex((branch) => isBranchApproved(branch))
  // 查找是否有待处理的流程
  const pendingIndex = flowSteps.value.findIndex((branch) => {
    // 存在活跃节点但没有拒绝节点的分支视为待处理
    return (
      branch.some((step) => step.status === 'active') &&
      !branch.some((step) => step.status === 'rejected')
    )
  })

  // 遍历所有分支
  flowSteps.value.forEach((branch, index) => {
    // 如果只有一个分支，则展开
    if (flowSteps.value.length === 1) {
      expandedBranches.value[index] = true
      return
    }

    // 优先展开已通过的流程
    if (approvedIndex !== -1) {
      expandedBranches.value[index] = index === approvedIndex
    }
    // 如果没有已通过的流程，则展开待处理的流程
    else if (pendingIndex !== -1) {
      expandedBranches.value[index] = index === pendingIndex
    }
    // 如果既没有已通过的也没有待处理的，则展开最后一个流程（即使是被拒绝的）
    else {
      expandedBranches.value[index] = index === flowSteps.value.length - 1
    }
  })
}

// 更新倒计时
const updateCountdown = () => {
  if (courseCountdown.value.totalSeconds <= 0) {
    courseCountdown.value.isActive = false
    return
  }

  courseCountdown.value.totalSeconds -= 1

  // 计算天、时、分、秒
  courseCountdown.value.days = Math.floor(courseCountdown.value.totalSeconds / (24 * 3600))
  const remainder = courseCountdown.value.totalSeconds % (24 * 3600)
  courseCountdown.value.hours = Math.floor(remainder / 3600)
  courseCountdown.value.minutes = Math.floor((remainder % 3600) / 60)
  courseCountdown.value.seconds = remainder % 60
}

// 启动倒计时
const startCountdown = (seconds: number) => {
  if (seconds <= 0) return

  // 清除之前的计时器
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }

  courseCountdown.value.totalSeconds = seconds
  courseCountdown.value.isActive = true

  // 初次计算显示值
  updateCountdown()

  // 设置定时器
  countdownTimer.value = setInterval(() => {
    updateCountdown()
    if (!courseCountdown.value.isActive) {
      clearInterval(countdownTimer.value!)
      countdownTimer.value = null
    }
  }, 1000)
}

// 停止倒计时的方法
const stopCountdown = () => {
  // 清除计时器
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
  // 设置倒计时为非活动状态
  courseCountdown.value.isActive = false
}

// 显示通知的计算属性
const notificationInfo = computed(() => {
  // 优先使用statistics中的info
  if (statistics.value.info) {
    return statistics.value.info
  }
  // 其次使用textbookInfo
  return textbookInfo.value
})

// 显示审批记录
const showApprovalHistory = async (id: number) => {
  currentBookId.value = id
  // 重置分支索引
  currentBranchIndex.value = 0

  try {
    loadingApproval.value = true
    showApprovalModal.value = true

    // 根据当前激活的标签页确定使用哪个工作流代码
    const workflowCode =
      activeTabIndex.value === 2
        ? 'jcxybgsq'
        : activeTabIndex.value === 1
          ? 'kcwxjcxysq' // 为无需征订教材任务使用对应的工作流代码
          : 'jsxkjcsh'

    // 调用工作流时间线接口获取审批记录
    const res = await getWorkflowTimeline({
      id,
      code: workflowCode, // 根据标签页使用不同的工作流代码
    })

    workflowTimelineData.value = res

    // 将API返回的流程数据转换为前端需要的格式
    if (res.process && res.process.length > 0) {
      flowSteps.value = res.process.map((branch) => convertToApprovalSteps(branch))

      // 初始化展开状态
      initBranchExpandState()
    } else {
      flowSteps.value = []
    }
  } catch (error) {
    console.error('获取审批记录失败:', error)
    uni.showToast({
      title: '获取审批记录失败',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    loadingApproval.value = false
  }
}

// 显示评价
const showEvaluation = (id: number) => {
  currentBookId.value = id
  // 获取当前选中的教材信息
  currentTextbook.value = textbookList.value.find((item) => item.id === id) || null

  // 如果找到了教材信息，从中获取已有的评价数据
  if (currentTextbook.value) {
    // 从教材项中获取评价数据
    evaluationForm.value = {
      // 将字符串"1"转换为布尔值true，其他值为false
      isReference: currentTextbook.value.sfjxcks === '1',
      isLabGuide: currentTextbook.value.sfsyzds === '1',
      comment: currentTextbook.value.jcpjyj || '',
    }
  } else {
    // 如果没有找到教材信息，重置评价表单
    evaluationForm.value = {
      isReference: false,
      isLabGuide: false,
      comment: '',
    }
  }

  showEvaluationModal.value = true
}

// 提交评价
const submitEvaluation = async () => {
  try {
    // 表单验证
    if (!evaluationForm.value.comment.trim()) {
      uni.showToast({
        title: '请输入评价与意见',
        icon: 'none',
      })
      return
    }

    uni.showLoading({ title: '提交中...' })

    // 构建评价参数
    const params: TextbookEvaluationParams = {
      id: currentBookId.value,
      sfjxcks: evaluationForm.value.isReference ? '1' : '0',
      sfsyzds: evaluationForm.value.isLabGuide ? '1' : '0',
      jcpjyj: evaluationForm.value.comment,
      jcmc: currentTextbook.value?.jcmc,
    }

    // 调用评价API
    await submitTextbookEvaluation(params)

    uni.hideLoading()
    uni.showToast({
      title: '评价提交成功',
      icon: 'success',
    })
    showEvaluationModal.value = false

    // 刷新教材列表
    fetchTextbookList()
  } catch (error) {
    console.error('提交评价失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: '提交失败，请重试',
      icon: 'none',
    })
  }
}

// 加载字典数据
const loadDicts = async () => {
  try {
    uni.showLoading({ title: '加载字典数据...' })
    // 加载四种字典数据
    dictData.value = await loadDictData([
      'DM_SPZT', // 审核状态
      'SYS_SELECTION_TYPE', // 选用类别
      'DM_JCLBDM', // 教材类别
      'DM_JCLXDM', // 教材类型
      'SYS_JW_JCXY', // 教材选用审核状态
      'SYS_SUBMIT_STATUS', // 提交状态(成绩类型)
      'SYS_ASSESSMENT_METHOD', // 考核方式
    ])
    uni.hideLoading()
  } catch (error) {
    console.error('加载字典数据失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: '加载字典数据失败',
      icon: 'none',
      duration: 2000,
    })
  }
}

// 获取教材统计数据
const fetchStatistics = async () => {
  try {
    const res = await getTeachingMaterialStatistics()
    statistics.value = res

    // 检查是否有info信息
    if (res.info) {
      textbookInfo.value = res.info
    }

    // 如果统计接口也返回了结束时间，设置倒计时
    if (res.jssj === 0) {
      // 如果jssj为0，停止倒计时，并设置不允许新增教材
      stopCountdown()
      allowAddMaterial.value = false
    } else if (res.jssj && typeof res.jssj === 'number') {
      // 计算当前时间和结束时间的差值（秒）
      const now = Math.floor(Date.now() / 1000)
      const endTime = Math.floor(res.jssj / 1000)
      const seconds = endTime - now

      if (seconds > 0) {
        startCountdown(seconds)
        allowAddMaterial.value = true
      } else {
        // 如果时间差小于等于0，停止倒计时，并设置不允许新增教材
        stopCountdown()
        allowAddMaterial.value = false
      }
    }
  } catch (error) {
    console.error('获取教材统计数据失败:', error)
    uni.showToast({
      title: '获取统计数据失败',
      icon: 'none',
    })
  }
}

// 获取教材变更申请列表
const fetchChangeRequestList = async () => {
  try {
    loadingChangeList.value = true

    // 使用学期选择器的值，如果没有则使用默认值
    const params: TeachingMaterialChangeQuery = {
      page: 1,
      pageSize: 20,
      sortBy: 'id',
      sortOrder: 'desc',
      semesters: currentYearValue.value || '',
    }

    const res = await getTeachingMaterialChangeList(params)
    changeRequestList.value = res.items || []

    // 如果没有数据，提示用户
    if (changeRequestList.value.length === 0 && activeTabIndex.value === 2) {
      uni.showToast({
        title: '暂无教材变更申请数据',
        icon: 'none',
        duration: 2000,
      })
    }
  } catch (error) {
    console.error('获取教材变更申请列表失败:', error)
    uni.showToast({
      title: '获取教材变更申请列表失败',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    loadingChangeList.value = false
  }
}

// 获取无需征订教材任务列表
const fetchNoNeedList = async () => {
  try {
    loadingNoNeedList.value = true

    // 使用学期选择器的值，如果没有则使用默认值
    const params: TeachingMaterialNoNeedQuery = {
      page: 1,
      pageSize: 10,
      semesters: currentYearValue.value || '',
    }

    const res = await getTeachingMaterialNoNeedList(params)
    noOrderTaskList.value = res.items || []

    // 如果没有数据，提示用户
    if (noOrderTaskList.value.length === 0 && activeTabIndex.value === 1) {
      uni.showToast({
        title: '暂无无需征订教材任务数据',
        icon: 'none',
        duration: 2000,
      })
    }
  } catch (error) {
    console.error('获取无需征订教材任务列表失败:', error)
    uni.showToast({
      title: '获取无需征订教材任务列表失败',
      icon: 'none',
      duration: 2000,
    })
  } finally {
    loadingNoNeedList.value = false
  }
}
const isFirst = ref(true)
onShow(() => {
  if (!isFirst.value) {
    fetchTextbookList()
  }

  // 如果当前是变更申请标签页，获取变更申请列表
  if (activeTabIndex.value === 2) {
    fetchChangeRequestList()
  }
  // 如果当前是无需征订标签页，获取无需征订列表
  else if (activeTabIndex.value === 1) {
    fetchNoNeedList()
  }
})
// 检查是否有课程信息并加载数据
onMounted(() => {
  // 检查并清空 teachingTaskStore 中的值
  if (teachingTaskStore.currentTask) {
    teachingTaskStore.clearCurrentTask()
  }

  // 先加载字典数据
  loadDicts().then(() => {
    isFirst.value = false
    // 获取统计数据
    fetchStatistics()

    // 等待选择器初始化后再获取数据
    setTimeout(() => {
      fetchTextbookList()
      // 如果当前是变更申请标签页，获取变更申请列表
      if (activeTabIndex.value === 2) {
        fetchChangeRequestList()
      }
      // 如果当前是无需征订标签页，获取无需征订列表
      else if (activeTabIndex.value === 1) {
        fetchNoNeedList()
      }
    }, 500)
  })

  // 初始化审批流程分支的展开/折叠状态
  initBranchExpandState()
})

// 获取教材状态文本和样式
const getStatusInfo = (item: TeachingMaterialSelectionItem) => {
  const pjshzt = String(item.shzt || '0')

  // 使用字典数据获取状态标签
  const statusLabel = getDictLabel(dictData.value.DM_SPZT, pjshzt)

  // 根据状态值设置样式和图标
  if (pjshzt === '0') {
    return {
      text: statusLabel || '待审批',
      bgClass: 'bg-orange-100',
      textClass: 'text-orange-600',
      icon: 'clock',
    }
  } else if (pjshzt === '1') {
    return {
      text: statusLabel || '已通过',
      bgClass: 'bg-green-100',
      textClass: 'text-green-600',
      icon: 'check',
    }
  } else if (pjshzt === '2') {
    return {
      text: statusLabel || '未通过',
      bgClass: 'bg-red-100',
      textClass: 'text-red-600',
      icon: 'close',
    }
  }

  return {
    text: statusLabel || '未知状态',
    bgClass: 'bg-gray-100',
    textClass: 'text-gray-600',
    icon: 'help',
  }
}

// 获取教材类别样式
const getCategoryStyle = (category: string) => {
  // 使用字典数据获取类别标签
  const categoryLabel = getDictLabel(dictData.value.DM_JCLBDM, category) || category

  if (categoryLabel.includes('国家规划')) {
    return {
      bgClass: 'bg-green-100',
      textClass: 'text-green-600',
    }
  } else if (categoryLabel.includes('省级优秀')) {
    return {
      bgClass: 'bg-purple-100',
      textClass: 'text-purple-600',
    }
  } else {
    return {
      bgClass: 'bg-gray-100',
      textClass: 'text-gray-600',
    }
  }
}

// 获取课程类型样式
const getCourseTypeStyle = (type: string) => {
  if (type === '必修') {
    return {
      bgClass: 'bg-blue-100',
      textClass: 'text-blue-600',
    }
  } else {
    return {
      bgClass: 'bg-blue-100',
      textClass: 'text-blue-600',
    }
  }
}

// 获取教材状态边框样式
const getStatusBorderStyle = (item: TeachingMaterialSelectionItem) => {
  const pjshzt = String(item.pjshzt || '0')

  if (pjshzt === '0') {
    return 'border-orange-400'
  } else if (pjshzt === '1') {
    return 'border-green-400'
  } else if (pjshzt === '2') {
    return 'border-red-400'
  }

  return 'border-gray-400'
}

// 获取选用类别标签
const getSelectionTypeLabel = (value: string) => {
  return getDictLabel(dictData.value.SYS_SELECTION_TYPE, value) || value
}

// 获取教材类别标签
const getMaterialCategoryLabel = (value: string) => {
  return getDictLabel(dictData.value.DM_JCLBDM, value) || value
}

// 获取教材类型标签
const getMaterialTypeLabel = (value: string) => {
  return getDictLabel(dictData.value.DM_JCLXDM, value) || value
}

// 获取评价审阅文本和样式
const getEvaluationInfo = (item: TeachingMaterialSelectionItem) => {
  // 使用DM_SPZT字典判断评价审阅的审核状态
  const pjshzt = String(item.pjshzt || '0')

  // 使用字典数据获取状态标签
  const statusLabel = getDictLabel(dictData.value.DM_SPZT, pjshzt)

  // 根据状态值设置样式和图标
  if (pjshzt === '0') {
    return {
      text: statusLabel || '待审核',
      bgClass: 'bg-orange-100',
      textClass: 'text-orange-600',
      icon: 'clock',
    }
  } else if (pjshzt === '1') {
    return {
      text: statusLabel || '已审核',
      bgClass: 'bg-green-100',
      textClass: 'text-green-600',
      icon: 'check',
    }
  } else if (pjshzt === '2') {
    return {
      text: statusLabel || '审核未通过',
      bgClass: 'bg-red-100',
      textClass: 'text-red-600',
      icon: 'close',
    }
  }

  return {
    text: statusLabel || '未知状态',
    bgClass: 'bg-gray-100',
    textClass: 'text-gray-400',
    icon: 'info-circle',
  }
}

// 选项卡切换
const switchTab = (index: number) => {
  activeTabIndex.value = index
}

// 监听activeTabIndex变化
watch(
  () => activeTabIndex.value,
  (newIndex) => {
    // 可以在这里添加切换标签页时的额外逻辑
    console.log('切换到标签页:', tabs.value[newIndex].name)

    // 当切换到变更申请标签页时，获取变更申请列表
    if (newIndex === 2) {
      fetchChangeRequestList()
    }
    // 当切换到无需征订标签页时，获取无需征订列表
    else if (newIndex === 1) {
      fetchNoNeedList()
    }
  },
)

// 获取教材变更申请状态文本
const getChangeRequestStatusText = (item: TeachingMaterialChangeItem) => {
  // 检查是否所有审批环节都已通过
  const allApproved = item.jyssh === 1 && item.xbsh === 1 && item.jwsh === 1
  // 检查是否有任何一个审批环节被拒绝
  const anyRejected = item.jyssh === 2 || item.xbsh === 2 || item.jwsh === 2

  if (anyRejected) {
    return '未通过'
  } else if (allApproved) {
    return '已通过'
  } else {
    return '审核中'
  }
}

// 获取教材变更申请状态图标
const getChangeRequestStatusIcon = (item: TeachingMaterialChangeItem) => {
  // 检查是否有任何一个审批环节被拒绝
  const anyRejected = item.jyssh === 2 || item.xbsh === 2 || item.jwsh === 2
  // 检查是否所有审批环节都已通过
  const allApproved = item.jyssh === 1 && item.xbsh === 1 && item.jwsh === 1

  if (anyRejected) {
    return 'close'
  } else if (allApproved) {
    return 'check'
  } else {
    return 'clock'
  }
}

// 获取教材变更申请状态样式
const getChangeRequestStatusStyle = (item: TeachingMaterialChangeItem) => {
  // 检查是否有任何一个审批环节被拒绝
  const anyRejected = item.jyssh === 2 || item.xbsh === 2 || item.jwsh === 2
  // 检查是否所有审批环节都已通过
  const allApproved = item.jyssh === 1 && item.xbsh === 1 && item.jwsh === 1

  if (anyRejected) {
    return 'bg-red-100 text-red-600'
  } else if (allApproved) {
    return 'bg-green-100 text-green-600'
  } else {
    return 'bg-orange-100 text-orange-600'
  }
}

// 组件卸载时清除计时器
onBeforeUnmount(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
  }
})

// 处理查看变更详情
const handleViewChangeDetail = (id: number) => {
  // 根据ID找到当前教材变更记录
  const changeItem = changeRequestList.value.find((item) => item.id === id)

  if (changeItem) {
    // 检查是否所有审批环节都已通过
    const allApproved = changeItem.jyssh === 1 && changeItem.xbsh === 1 && changeItem.jwsh === 1

    // 获取store实例
    const textbookStore = useTextbookStore()

    // 先清空store
    textbookStore.clearCurrentTextbook()

    // 将变更记录转换为教材信息格式，添加必要的属性
    const textbookData: any = {
      id: changeItem.id,
      jcmc: changeItem.jcmc,
      isbn: changeItem.isbn || '',
      cbs: changeItem.cbs || '',
      zybz: changeItem.zybz || '',
      jclb: changeItem.jclb,
      jclbmc: changeItem.jclbmc || '',
      jclx: changeItem.jclx,
      jclxmc: changeItem.jclxmc || '',
      xylb: changeItem.xylb,
      xyly: changeItem.xyly || '',
      bgyysm: changeItem.bgyysm || '', // 确保变更理由说明字段正确传递
      sfscxy: changeItem.sfscxy ? String(changeItem.sfscxy) : '0',
      jxrwid: changeItem.ssjxrwid,
      title: `${changeItem.xn}-${changeItem.xq} ${changeItem.bjmc} ${changeItem.kcmc}`,
      // 添加必要的属性，使用默认值
      jcxxid: changeItem.jcid || 0,
      remark: '',
      deltag: 0,
      create_time: '',
      update_time: '',
      bc: '',
      shzt: allApproved ? '1' : '0', // 审核状态
      pjshzt: '0',
      jcpjyj: '',
      sfjxcks: '0',
      sfsyzds: '0',
      zdjsxm: changeItem.sqrxm || '',
      xn: changeItem.xn || '',
      xq: changeItem.xq || '',
      kcmc: changeItem.kcmc || '',
      bjmc: changeItem.bjmc || '',
      // 添加原教材选用ID，用于变更申请
      yjcxyid: changeItem.yjcxyid || 0,
      // 添加缺少的必要属性以解决类型错误
      oprybh: '',
      ssxk: 0,
      kcdm: '',
      ssxy: '',
      ssxb: '',
      ssjys: '',
      ssjysmc: '',
      ssbj: '',
      taskId: changeItem.ssjxrwid, // 添加任务ID
    }

    // 将转换后的数据存入store
    textbookStore.setCurrentTextbook(textbookData)

    // 设置教学任务信息
    teachingTaskStore.setCurrentMaterialTask(
      String(changeItem.ssjxrwid),
      `${changeItem.xn}-${changeItem.xq} ${changeItem.bjmc} ${changeItem.kcmc}`,
    )

    if (allApproved) {
      // 如果已审核通过，跳转到add-textbook页面并传递disabled参数
      uni.navigateTo({
        url: `/pages/teacher/teaching-affairs/task-management/add-textbook?mode=change&source=material&disabled=true`,
      })
    } else {
      // 如果未审核通过，跳转到编辑页面
      uni.navigateTo({
        url: `/pages/teacher/teaching-affairs/task-management/add-textbook?mode=change&source=material&fromChangeList=true`,
      })
    }
  } else {
    uni.showToast({
      title: '未找到变更记录',
      icon: 'none',
    })
  }
}

// 获取审批状态文本
const getApprovalStatusText = (status: number) => {
  if (status === 1) {
    return '已通过'
  } else if (status === 2) {
    return '未通过'
  } else {
    return '待审核'
  }
}

// 确认撤销变更记录
const confirmCancelChange = (id: number) => {
  uni.showModal({
    title: '确认撤销',
    content: '确定要撤销该教材变更申请吗？撤销后无法恢复。',
    confirmText: '撤销',
    confirmColor: '#ff3b30',
    success: (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '撤销中...' })

          // 调用取消教材选用API
          cancelTeachingMaterial({ id })
            .then(() => {
              uni.hideLoading()
              uni.showToast({
                title: '撤销成功',
                icon: 'success',
              })

              // 重新获取变更申请列表
              fetchChangeRequestList()
            })
            .catch((error) => {
              console.error('撤销失败:', error)
              uni.hideLoading()
              uni.showToast({
                title: '撤销失败，请重试',
                icon: 'none',
              })
            })
        } catch (error) {
          console.error('撤销失败:', error)
          uni.hideLoading()
          uni.showToast({
            title: '撤销失败，请重试',
            icon: 'none',
          })
        }
      }
    },
  })
}

// 获取教材状态文本
const getAuditStatusText = (status: number) => {
  // 使用SYS_JW_JCXY字典获取审核状态文本
  const statusText = getDictLabel(dictData.value.SYS_JW_JCXY, String(status))
  if (statusText) return statusText

  // 如果字典中没有对应值，使用默认显示
  if (status === 2) {
    return '已通过'
  } else if (status === 3) {
    return '审批中'
  } else {
    return '未知状态'
  }
}

// 获取教材状态类
const getAuditStatusClass = (status: number) => {
  // 可以基于字典中的数据决定样式类
  if (status === 2) {
    return 'text-green-600'
  } else if (status === 3) {
    return 'text-orange-600'
  } else {
    return 'text-gray-600'
  }
}

// 确保当前分支索引在有效范围内
const ensureValidBranchIndex = () => {
  const branches = flowSteps.value
  if (branches.length === 0) return

  // 如果当前分支索引超出范围，重置为0
  if (currentBranchIndex.value >= branches.length) {
    currentBranchIndex.value = 0
  }
}

// 选择分支
const selectBranch = (index: number) => {
  if (index >= 0 && index < flowSteps.value.length) {
    currentBranchIndex.value = index
  }
}

// 获取审核状态图标
const getAuditStatusIcon = (status: number) => {
  // 根据状态值返回对应的图标
  if (status === 2) {
    return 'check'
  } else if (status === 3) {
    return 'clock'
  } else {
    return 'help'
  }
}

// 获取考核方式文本
const getAssessmentMethodText = (khfs: string) => {
  // 使用SYS_ASSESSMENT_METHOD字典获取考核方式文本
  const methodText = getDictLabel(dictData.value.SYS_ASSESSMENT_METHOD, khfs)
  if (methodText) return methodText

  // 如果字典中没有对应值，使用默认显示
  if (khfs === '1') {
    return '考试'
  } else if (khfs === '2') {
    return '考查'
  } else {
    return khfs
  }
}

// 获取成绩提交状态文本
const getGradeSubmitStatusText = (status: string) => {
  // 使用SYS_SUBMIT_STATUS字典获取成绩提交状态文本
  const statusText = getDictLabel(dictData.value.SYS_SUBMIT_STATUS, status)
  if (statusText) return statusText

  // 如果字典中没有对应值，返回原始值
  return status
}

// 获取成绩类型文本
const getGradeTypeText = (cjptlx: string) => {
  // 尝试从字典中获取
  const typeText = getDictLabel(dictData.value.SYS_SUBMIT_STATUS, cjptlx)
  if (typeText) return typeText

  // 如果以上都没有匹配，返回原始值
  return '未知'
}

// 处理编辑无需征订教材任务
const handleEditNoNeedMaterial = (item: TeachingMaterialNoNeedItem) => {
  // 获取无需征订教材store
  const noNeedMaterialStore = useNoNeedMaterialStore()

  // 清空并存入选中的任务数据
  noNeedMaterialStore.clearCurrentNoNeedMaterial()
  noNeedMaterialStore.setCurrentNoNeedMaterial(item)

  // 判断是否已通过审核，如果已通过则禁用编辑
  const isDisabled = Number(item.jcxybz) === 2 // 2表示已通过审核

  // 导航到编辑页面，传递来源和禁用状态参数
  uni.navigateTo({
    url: `/pages/teacher/teaching-affairs/task-management/edit-no-need-material?from=material&disabled=${isDisabled}`,
  })
}

// 处理添加无需征订教材
const handleAddNoNeedMaterial = () => {
  // 如果不允许添加教材，则显示提示并返回
  if (!allowAddMaterial.value) {
    uni.showToast({
      title: '当前不在教材选用申报时间内',
      icon: 'none',
      duration: 2000,
    })
    return
  }

  // 清空无需征订教材store
  const noNeedMaterialStore = useNoNeedMaterialStore()
  noNeedMaterialStore.clearCurrentNoNeedMaterial()

  // 将当前选中的学年学期值作为参数传递
  const semesterParam = currentYearValue.value
    ? `&semester=${encodeURIComponent(currentYearValue.value)}`
    : ''

  // 导航到编辑页面，传递来源参数和学期参数
  uni.navigateTo({
    url: `/pages/teacher/teaching-affairs/task-management/edit-no-need-material?from=material&mode=add${semesterParam}`,
  })
}

// 处理完善教材
const handlePerfectTextbook = (jcxxid: number) => {
  if (!jcxxid) {
    uni.showToast({
      title: '教材信息ID不存在',
      icon: 'none',
    })
    return
  }

  // 跳转到指定页面，携带jcxxid参数
  uni.navigateTo({
    url: `/pages/teachingMaterial/add/index?jcxxid=${jcxxid}`,
  })
}
</script>

<style lang="scss">
.textbook-selection {
  min-height: 100vh;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  font-size: 11px;
}

.info-item {
  display: flex;
  align-items: flex-start;
}

.info-item.col-span-2 {
  grid-column: span 2;
}

.info-label {
  flex-shrink: 0;
  min-width: 44px;
  color: #6b7280;
}

.info-value {
  font-weight: 500;
  line-height: 1.3;
  color: #374151;
}

.modal-content {
  width: 320px;
  max-height: 80vh;
  overflow-y: auto;
  background: white;
  border-radius: 16px;
}

// 添加轻微的脉冲动画样式
.animate-pulse-light {
  animation: pulse-light 2s infinite;
}

@keyframes pulse-light {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.85;
  }
  100% {
    opacity: 1;
  }
}

// 审批时间线样式
.approval-timeline {
  position: relative;

  .relative.py-2 {
    transition: all 0.3s ease;
  }

  .w-4.h-4.rounded-full {
    position: relative;
    z-index: 2;
  }

  .absolute.left-2.top-4.w-0\.5.h-full {
    position: absolute;
    z-index: 1;
  }
}

// 分支选择器样式
.flex.space-x-2 {
  padding-bottom: 8px;
  overflow-x: auto;
  scrollbar-width: thin;

  &::-webkit-scrollbar {
    height: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
  }

  .cursor-pointer {
    transition: all 0.2s ease;

    &:hover {
      opacity: 0.85;
    }
  }
}

// 评价表单样式
.space-y-1,
.space-y-2,
.space-y-4 {
  display: flex;
  flex-direction: column;
}

.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-x-4 > * + * {
  margin-left: 1rem;
}

// 单选按钮样式
.flex.items-center {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.w-5.h-5.rounded-full.border {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.border-blue-500 {
  border-color: #3b82f6;
}

.bg-blue-500 {
  background-color: #3b82f6;
}

.border-gray-300 {
  border-color: #d1d5db;
}

// 按钮基础样式
button {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 0 12px;
  font-size: 13px;
  border-radius: 6px;
  transition: opacity 0.3s;

  &:active {
    opacity: 0.85;
  }
}

// 主要按钮样式 - 蓝色
.btn-primary {
  color: #fff;
  background-color: #007aff;
  border: 1px solid #007aff;
}

// 成功按钮样式 - 绿色
.btn-success {
  color: #fff;
  background-color: #4cd964;
  border: 1px solid #4cd964;
}

// 警告按钮样式 - 橙色
.btn-warning {
  color: #fff;
  background-color: #ff9500;
  border: 1px solid #ff9500;
}

// 危险按钮样式 - 红色
.btn-danger {
  color: #fff;
  background-color: #ff3b30;
  border: 1px solid #ff3b30;
}

// 信息按钮样式 - 青色
.btn-info {
  color: #fff;
  background-color: #5ac8fa;
  border: 1px solid #5ac8fa;
}

// 搜索栏按钮特殊样式
.flex-1 > .btn-primary,
.flex-1 > .btn-success {
  width: 40px;
  height: 36px;
}

// 表单样式
.form-textarea {
  box-sizing: border-box;
  width: 100%;
  background-color: #ffffff;
  border: 1px solid #bfbfc3;
  border-radius: 8px;
}

// 图标按钮样式
.btn-icon {
  width: 28px;
  height: 28px;
  padding: 0;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

// 倒计时样式
.countdown-block {
  min-width: 40px;
  transition: all 0.3s ease;
}

// Tab样式
.relative.py-2.px-4 {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
}

.relative.py-2.px-4:hover {
  color: #3b82f6;
}

.absolute.bottom-0.left-0.w-full.h-0\.5 {
  transition: all 0.3s ease-in-out;
}

// 教材变更卡片样式
.bg-gray-50.p-2.rounded-lg {
  transition: all 0.3s ease;
}

.bg-gray-50.p-2.rounded-lg:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

// 任务列表项样式
.grid.grid-cols-2.gap-2 {
  font-size: 12px;
  line-height: 1.5;
}

// 分段选择器样式
:deep(.wd-tabs) {
  --tabs-nav-background: transparent;
  --tabs-nav-height: 36px;
}

:deep(.wd-tab) {
  padding: 0 8px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

:deep(.wd-tab--active) {
  font-weight: 600;
  color: #3b82f6;
}

:deep(.wd-tabs__line) {
  bottom: 1px;
  height: 3px;
  background-color: #3b82f6;
  border-radius: 3px;
}
</style>
