<route lang="json5">
{
  style: {
    navigationBarTitleText: '教学日志',
  },
}
</route>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useToast } from 'wot-design-uni'
import type { TeacherScheduleItem, TeacherScheduleQuery } from '@/types/teacher'
import { getTeacherScheduleList, getTeacherScheduleStatistics } from '@/service/teacher'
import { getSemesterList } from '@/service/semester'
import type { SemesterOption } from '@/types/semester'
import Pagination from '@/components/Pagination/index.vue'
import SemesterWeekPicker from '@/components/SemesterWeekPicker/index.vue'
import StatsCards, { StatItem } from '@/components/StatsCards/index.vue'

// toast提示
const toast = useToast()

// 学期选择
const currentSemesterValue = ref<string>('') // 存储学期的值，用于API调用

// 周数选择
const currentWeekValue = ref<number | null>(null) // 存储周数值，用于API调用

// 统计数据 - 默认值设为0
const statistics = ref({
  totalCount: 0,
  unconfirmedCount: 0,
  confirmRate: 0,
  notStartedCount: 0,
})

// 统计卡片数据
const statsCardsData = ref<StatItem[]>([
  {
    value: 0,
    title: '本学期节数',
    type: 'default',
    clickable: false,
  },
  {
    value: 0,
    title: '未确认节数',
    type: 'error',
    clickable: true,
    active: false,
  },
  {
    value: '0%',
    title: '确认率',
    type: 'success',
    clickable: false,
  },
  {
    value: 0,
    title: '未开始节数',
    type: 'warning',
    clickable: false,
  },
])

// 当前选中的课程
const selectedCourse = ref('')

// 添加过滤状态变量
const filterState = ref({
  unconfirmedOnly: false, // 是否只显示未确认节次
})

// 列表数据
const loading = ref(false)
const list = ref<TeacherScheduleItem[]>([])
const total = ref(0)

// 分页参数
const page = ref(1)
const pageSize = ref(10)

// 清空统计数据
const resetStatistics = () => {
  statistics.value = {
    totalCount: 0,
    unconfirmedCount: 0,
    confirmRate: 0,
    notStartedCount: 0,
  }

  // 同时更新统计卡片数据
  updateStatsCardsData()
}

// 更新统计卡片数据
const updateStatsCardsData = () => {
  statsCardsData.value = [
    {
      value: statistics.value.totalCount,
      title: '本学期节数',
      type: 'default',
      clickable: false,
    },
    {
      value: statistics.value.unconfirmedCount,
      title: '未确认节数',
      type: 'error',
      clickable: true,
      active: filterState.value.unconfirmedOnly,
    },
    {
      value: `${statistics.value.confirmRate}%`,
      title: '确认率',
      type: 'success',
      clickable: false,
    },
    {
      value: statistics.value.notStartedCount,
      title: '未开始节数',
      type: 'warning',
      clickable: false,
    },
  ]
}

// 获取统计数据
const getStatistics = () => {
  getTeacherScheduleStatistics()
    .then((res) => {
      // 更新统计数据
      statistics.value = {
        totalCount: res.zjc, // 总节次
        unconfirmedCount: res.jswqrjc, // 教师未确认节次
        confirmRate: res.zxl, // 执行率
        notStartedCount: res.wzxjc, // 未执行节次
      }

      // 更新统计卡片数据
      updateStatsCardsData()
    })
    .catch((error) => {
      console.error('获取统计数据失败:', error)
      // 出错时重置统计数据
      resetStatistics()
    })
}

// 学期周次组合变化处理函数
const handlePickerChange = (data: {
  semester: { label: string; value: string }
  week: { label: string; value: number | null }
}) => {
  // 重置统计数据
  resetStatistics()
  // 重置分页
  page.value = 1
  // 重新获取数据
  getList()
  // 获取统计数据
  getStatistics()
}

// 获取列表数据
const getList = () => {
  if (!currentSemesterValue.value) return

  loading.value = true

  // 创建请求参数
  const params: TeacherScheduleQuery = {
    page: page.value,
    pageSize: pageSize.value,
    kcmc: selectedCourse.value,
    semesters: '',
  }

  // 处理学期值格式：2024-2025-1 -> 2024-2025|1
  if (currentSemesterValue.value) {
    const semesterValue = currentSemesterValue.value
    const lastDashIndex = semesterValue.lastIndexOf('-')
    const formattedSemester =
      lastDashIndex !== -1
        ? `${semesterValue.substring(0, lastDashIndex)}|${semesterValue.substring(lastDashIndex + 1)}`
        : semesterValue

    // 添加到请求参数中
    params.semesters = formattedSemester
  }

  // 添加周数参数
  if (currentWeekValue.value !== null) {
    params.zc = currentWeekValue.value
  }

  // 添加未确认节次过滤参数
  if (filterState.value.unconfirmedOnly) {
    params.skjhjsqrzt = 0 // 0表示未确认
  }

  getTeacherScheduleList(params)
    .then((res) => {
      list.value = res.items
      total.value = res.total
    })
    .catch((error) => {
      console.error('获取教师课程表列表失败:', error)
      uni.showToast({
        title: '获取数据失败',
        icon: 'error',
      })
    })
    .finally(() => {
      loading.value = false
    })
}

// 处理统计卡片点击事件
const handleStatsCardClick = (index: number, item: StatItem) => {
  if (index === 1) {
    // 未确认节次卡片
    // 重置分页
    page.value = 1

    // 切换未确认过滤状态
    filterState.value.unconfirmedOnly = !filterState.value.unconfirmedOnly

    // 更新卡片激活状态
    statsCardsData.value[index].active = filterState.value.unconfirmedOnly

    // 如果开启了未确认过滤，将周数设置为null以查询全部周数
    if (filterState.value.unconfirmedOnly) {
      currentWeekValue.value = null
      toast.show({
        msg: '已筛选全部周数的未确认节次',
        duration: 2000,
      })
    } else {
      toast.show({
        msg: '已取消筛选',
        duration: 2000,
      })
    }

    // 重新获取数据
    getList()
  }
}

// 分页变化
const handlePageChange = (newPage: number) => {
  page.value = newPage
  getList()
}

// 跳转到详情页
const goToDetail = (id: number) => {
  uni.navigateTo({
    url: `/pages/teacher/teaching-affairs/teaching-journal-detail?id=${id}`,
  })
}

// 跳转到编辑页
const goToEdit = (id: number) => {
  uni.navigateTo({
    url: `/pages/teacher/teaching-affairs/teaching-journal-edit?id=${id}`,
  })
}

// 格式化日期 2024-09-08 15:50:00 -> 09-08 15:50
const formatDate = (dateString: string): string => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${month}-${day} ${hours}:${minutes}`
}

// 根据日期获取星期几
const getWeekdayName = (dateString: string): string => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  return weekdays[date.getDay()]
}

// 根据ID生成随机颜色
const getRandomColor = (id: number): string => {
  const colors = ['#3a8eff', '#22c55e', '#a855f7', '#f59e0b', '#ef4444', '#06b6d4']
  return colors[id % colors.length]
}

// 检查URL参数，设置未确认节次筛选
const checkUrlParams = () => {
  // 获取当前页面路由参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  // @ts-expect-error - 获取页面参数
  const options = currentPage.$page?.options || {}

  // 如果URL参数中有unconfirm=1，则自动选中未确认节次
  if (options.unconfirm === '1') {
    // 设置未确认过滤状态为true
    filterState.value.unconfirmedOnly = true
    // 将周数设置为null以查询全部周数
    currentWeekValue.value = null

    // 更新卡片激活状态
    if (statsCardsData.value[1]) {
      statsCardsData.value[1].active = true
    }

    // 显示提示
    toast.show({
      msg: '已自动筛选未确认节次',
      duration: 2000,
    })
  }
}

// 处理StatCard的点击
const handleStatCardClick = (type: string) => {
  // 已被替换为 handleStatsCardClick
}

onShow(() => {
  // 获取统计数据
  getStatistics()

  // 检查URL参数
  checkUrlParams()
  // 获取数据列表
  getList()
})
// 初始化
/* onMounted(() => {
  // 获取统计数据
  getStatistics()

  // 检查URL参数
  checkUrlParams()

  // 获取数据列表
  getList()

  // 不需要调用获取学年学期列表，因为SemesterWeekPicker组件内部会自动获取
  // 这里不需要做什么初始化工作，组件会处理一切
}) */
</script>

<template>
  <view class="container">
    <!-- 顶部操作栏 -->
    <view class="top-action-bar">
      <!-- 使用综合选择器组件 -->
      <SemesterWeekPicker
        class="mb-1"
        v-model:weekValue="currentWeekValue"
        v-model:semesterValue="currentSemesterValue"
        @change="handlePickerChange"
        :showToast="true"
        :defaultCurrentWeek="true"
      />
    </view>

    <!-- 统计卡片 - 使用新组件 -->
    <StatsCards :stats="statsCardsData" @click="handleStatsCardClick" />

    <!-- 加载中提示 -->
    <view v-if="loading" class="loading-container">
      <wd-loading size="36px" color="#3a8eff" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 课程表列表 -->
    <view v-else class="course-list">
      <!-- 筛选指示器 -->
      <view v-if="filterState.unconfirmedOnly" class="filter-indicator mt-2">
        <view class="filter-tag">
          <wd-icon name="filter" size="28rpx" color="#ff3860"></wd-icon>
          <text>未确认节次</text>
        </view>
        <view class="filter-clear" @click="handleStatsCardClick(1, statsCardsData[1])">
          <wd-icon name="close" size="24rpx" color="#666"></wd-icon>
          <text>清除筛选</text>
        </view>
      </view>

      <!-- 标题 -->

      <!-- 无数据提示 -->
      <view v-if="list.length === 0" class="empty-data">
        <wd-icon name="search" size="64rpx" color="#c0c4cc"></wd-icon>
        <text class="empty-text">暂无课程表数据</text>
      </view>

      <!-- 课程列表 -->
      <view v-else>
        <view
          v-for="item in list"
          :key="item.id"
          class="course-item my-2"
          @click="goToEdit(item.id)"
        >
          <view class="course-header">
            <view class="course-icon" :style="{ backgroundColor: getRandomColor(item.id) }">
              <wd-icon name="code" color="#ffffff" size="32rpx"></wd-icon>
            </view>
            <view class="course-info">
              <view class="course-type" v-if="item.skfsmc">{{ item.skfsmc }}</view>
              <view class="course-name">{{ item.kcmc }}</view>
              <view class="course-time">
                {{ item.skkssj ? item.skkssj.substring(11, 16) : '--' }}-{{
                  item.skjssj ? item.skjssj.substring(11, 16) : '--'
                }}
                | {{ item.skbjmc }}
              </view>
            </view>
            <view class="course-tag">
              <view class="tag-date">{{ item.skrq }}</view>
              <view class="tag-weekday">{{ getWeekdayName(item.skrq) }}</view>
              <view class="tag-section">第{{ item.jcshow }}节</view>
            </view>
          </view>
          <view class="course-footer">
            <view class="course-meta">
              <view class="meta-item">
                <wd-icon name="usergroup" size="24rpx" color="#86909c"></wd-icon>
                {{ item.skcdmc || '未分配教室' }}
              </view>
              <!-- <view class="meta-item">
                <wd-icon name="usergroup-add" size="24rpx" color="#86909c"></wd-icon>
                {{ item.syrs }}人
              </view>
              <view class="meta-item">
                <wd-icon name="calendar" size="24rpx" color="#86909c"></wd-icon>
                第{{ item.zc }}周
              </view> -->
            </view>
          </view>

          <!-- 课程详细信息 -->
          <view class="journal-details">
            <view class="detail-row">
              <view class="detail-item">
                <text class="detail-label">日志编号:</text>
                <text class="detail-value">{{ item.id }}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">周次:</text>
                <text class="detail-value">第{{ item.zc }}周</text>
              </view>
            </view>

            <!-- <view class="detail-row" v-if="false">
              <view class="detail-item">
                <text class="detail-label">星期:</text>
                <text class="detail-value">{{ getWeekdayName(item.skrq) }}</text>
              </view>
              <view class="detail-item">
                <text class="detail-label">日期:</text>
                <text class="detail-value">{{ item.skrq }}</text>
              </view>
            </view>

            <view class="detail-row" v-if="false">
              <view class="detail-item">
                <text class="detail-label">上课方式:</text>
                <text class="detail-value">{{ item.skfsmc || '未设置' }}</text>
              </view>
            </view> -->

            <view class="detail-item full-width">
              <text class="detail-label">上课内容:</text>
              <text class="detail-value content-text">{{ item.sknl || '暂无内容' }}</text>
            </view>

            <!-- <view class="detail-row">
              <view class="detail-item">
                <text class="detail-label">执行状态:</text>
                <text :class="['detail-value', item.skjhjsqrzt === 1 ? 'success' : 'warning']">
                  {{ item.skjhjsqrzt === 1 ? '已执行' : '未执行' }}
                </text>
              </view>
              <view class="detail-item">
                <text class="detail-label">审批状态:</text>
                <text :class="['detail-value', item.spzt === 1 ? 'success' : 'warning']">
                  {{ item.spzt === 1 ? '已审批' : '未审批' }}
                </text>
              </view>
            </view> -->
          </view>

          <!-- 统计数据 -->
          <view
            class="class-stats"
            :class="
              item.skjhjsqrzt !== 1 ? 'mb-3 border-b border-b-solid border-[#f0f0f0] py-2' : 'pt-2'
            "
          >
            <view class="class-stat-item">
              <view class="class-stat-value">统计中</view>
              <view class="class-stat-label">出勤率</view>
            </view>
            <view class="class-stat-item">
              <view :class="['class-stat-value', item.skjhjsqrzt === 1 ? 'success' : 'warning']">
                {{ item.skjhjsqrzt === 1 ? '是' : '否' }}
              </view>
              <view class="class-stat-label">教师确认</view>
              <view v-if="item.skjhjsqrzt === 1" class="class-stat-time">
                {{ formatDate(item.skjhjsqrsj) }}
              </view>
            </view>
            <view class="class-stat-item">
              <view :class="['class-stat-value', item.skjhxsqrzt === 1 ? 'success' : 'warning']">
                {{ item.skjhxsqrzt === 1 ? '是' : '否' }}
              </view>
              <view class="class-stat-label">学生确认</view>
              <view v-if="item.skjhxsqrzt === 1" class="class-stat-time">
                {{ formatDate(item.skjhxsqrsj) }}
              </view>
            </view>
            <view class="class-stat-item">
              <view :class="['class-stat-value', item.skjhgzlrdzt === 1 ? 'success' : 'warning']">
                {{ item.skjhgzlrdzt === 1 ? '是' : '否' }}
              </view>
              <view class="class-stat-label">认定状态</view>
              <!-- <view v-if="item.skjhzxzt === 1" class="class-stat-time">
                {{
                  formatDate(
                    item.update_time ? new Date(item.update_time * 1000).toISOString() : '',
                  )
                }}
              </view> -->
            </view>
          </view>

          <!-- 操作按钮 -->
          <view class="class-actions" v-if="item.skjhjsqrzt !== 1">
            <view class="class-action-btn" @click.stop="goToEdit(item.id)">
              <wd-icon name="check" size="28rpx" color="#3a8eff"></wd-icon>
              <text>{{ item.skjhjsqrzt === 1 ? '查看详情' : '授课确认' }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 分页组件 -->
      <Pagination
        v-if="list.length > 0"
        :total="total"
        :page="page"
        :pageSize="pageSize"
        @update:page="handlePageChange"
      />
    </view>

    <!-- Toast提示 -->
    <wd-toast />
  </view>
</template>

<style lang="scss" scoped>
// 顶部操作栏
.top-action-bar {
  box-sizing: border-box;
  display: flex;
  gap: 16rpx;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  padding: 4rpx 0;
  margin-bottom: 20rpx;
}

.course-select .label {
  font-weight: 500;
}

.course-select .value {
  display: flex;
  align-items: center;
  color: #3a8eff;

  text {
    margin-right: 8rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .i-down {
    font-size: 28rpx;
  }
}

.container {
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
  padding: 24rpx;
  padding-top: 12rpx;
  overflow-x: hidden;
  background-color: #f7f8fc;

  > view {
    width: 100%;
    max-width: 686rpx;
    margin: 0 auto;
  }
}

.module-header {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 32rpx;

  .icon {
    width: 48rpx;
    height: 48rpx;
    margin-right: 16rpx;
    font-size: 48rpx;
    color: #3a8eff;
  }
}

.card {
  box-sizing: border-box;
  width: 100%;
  padding: 32rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 12rpx rgba(31, 35, 41, 0.05);

  &:last-child {
    margin-bottom: 0;
  }
}

.statistics {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin-bottom: 40rpx;

  .stat-row {
    display: flex;
    gap: 24rpx;

    .stat-item {
      flex: 1;
      min-width: 0;
      padding: 32rpx;
      text-align: center;
      background-color: #f7f8fa;
      border-radius: 16rpx;

      .value {
        margin-bottom: 12rpx;
        overflow: hidden;
        font-size: 40rpx;
        font-weight: 600;
        color: #3a8eff;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.warning {
          color: #faad14;
        }

        &.success {
          color: #52c41a;
        }
      }

      .label {
        overflow: hidden;
        font-size: 24rpx;
        color: #86909c;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    &:last-child {
      .stat-item {
        flex: 2;
      }
    }
  }
}

.action-center {
  display: flex;
  justify-content: center;

  .btn {
    width: 100%;
    max-width: 400rpx;
    padding: 20rpx 48rpx;
    font-size: 28rpx;
    color: #ffffff;
    background-color: #3a8eff;
    border: none;
    border-radius: 8rpx;
  }
}

.course-select {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8rpx;
  /* stylelint-disable-next-line no-descending-specificity */
  .label {
    font-weight: 500;
  }
  /* stylelint-disable-next-line no-descending-specificity */
  .value {
    display: flex;
    align-items: center;
    color: #3a8eff;

    text {
      margin-right: 8rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .i-down {
      font-size: 28rpx;
    }
  }
}

.card-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8rpx;
  margin-bottom: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2329;

  .more {
    font-size: 28rpx;
    font-weight: normal;
    color: #86909c;
  }
}

.list {
  box-sizing: border-box;
  width: 100%;
  overflow: hidden;

  .list-item {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    width: 100%;
    padding: 32rpx 8rpx;
    overflow: hidden;
    border-bottom: 1px solid #f2f3f5;

    &:last-child {
      border-bottom: none;
    }

    .icon {
      margin-right: 24rpx;
      font-size: 40rpx;

      &.warning {
        color: #faad14;
      }
    }

    .icon-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80rpx;
      height: 80rpx;
      margin-right: 24rpx;
      font-size: 40rpx;
      color: #3a8eff;
      background-color: rgba(58, 142, 255, 0.1);
      border-radius: 12rpx;
    }

    .content {
      flex: 1;
      min-width: 0;
      overflow: hidden;

      .title {
        margin-bottom: 12rpx;
        overflow: hidden;
        font-size: 28rpx;
        color: #1f2329;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .desc {
        overflow: hidden;
        font-size: 24rpx;
        color: #86909c;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .arrow {
      .btn {
        padding: 8rpx 16rpx;
        font-size: 24rpx;
        color: #3a8eff;
        background-color: rgba(58, 142, 255, 0.1);
        border: none;
        border-radius: 8rpx;
      }

      .i-right {
        font-size: 32rpx;
        color: #86909c;
      }
    }
  }
}

// 移除已经迁移到StatsCards组件的样式
// .stats-cards 样式已移除

.course-list {
  display: flex;
  flex-direction: column;
  width: 100%;

  .course-item {
    display: flex;
    flex-direction: column;
    padding: 24rpx;
    background-color: white;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);

    &.active {
      background-color: white;
      border-left: 6rpx solid #3a8eff;
    }

    .course-header {
      display: flex;
      align-items: center;

      .course-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80rpx;
        height: 80rpx;
        margin-right: 16rpx;
        border-radius: 12rpx;
      }

      .course-info {
        flex: 1;
        min-width: 0;
        overflow: hidden;

        .course-type {
          display: inline-block;
          padding: 2rpx 8rpx;
          margin-bottom: 4rpx;
          font-size: 22rpx;
          color: #3a8eff;
          background-color: rgba(58, 142, 255, 0.1);
          border-radius: 4rpx;
        }

        .course-name {
          margin-bottom: 8rpx;
          overflow: hidden;
          font-size: 32rpx;
          font-weight: 500;
          color: #1f2329;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .course-time {
          overflow: hidden;
          font-size: 24rpx;
          color: #86909c;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .course-tag {
        display: flex;
        flex-direction: column;
        gap: 4rpx;
        align-items: center;
        padding: 12rpx 16rpx;
        font-size: 22rpx;
        color: #3a8eff;
        background-color: rgba(58, 142, 255, 0.1);
        border-radius: 12rpx;

        .tag-section {
          font-weight: 500;
        }

        .tag-date,
        .tag-weekday,
        .tag-time {
          font-size: 20rpx;
          color: #5a9eff;
        }
      }
    }

    .course-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .course-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 24rpx;

        .meta-item {
          display: flex;
          display: -webkit-box;
          gap: 8rpx;
          align-items: flex-start;
          max-height: 2.6em;
          overflow: hidden;
          font-size: 24rpx;
          line-height: 1.3;
          color: #86909c;
          text-overflow: ellipsis;
          word-break: break-all;
          white-space: normal;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
    }
  }
}

// 将所有的.wd-icon选择器放在一起，从简单到复杂排序
.wd-icon {
  flex-shrink: 0;
  width: 24rpx;
  height: 24rpx;
  margin-top: 2rpx;
}

.filter-tag .wd-icon {
  flex-shrink: 0;
  width: 28rpx;
  height: 28rpx;
}

.filter-clear .wd-icon {
  flex-shrink: 0;
  width: 24rpx;
  height: 24rpx;
}

.course-list .course-item .course-footer .course-meta .meta-item .wd-icon {
  flex-shrink: 0;
  width: 24rpx;
  height: 24rpx;
  margin-top: 2rpx;
}

.section-title {
  margin: 24rpx 0 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2329;
}

.class-stats {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #f0f0f0;

  .class-stat-item {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;

    .class-stat-value {
      margin-bottom: 4rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: #3a8eff;

      &.success {
        color: #52c41a;
      }

      &.warning {
        color: #ff3860;
      }
    }

    .class-stat-label {
      font-size: 24rpx;
      color: #86909c;
    }

    .class-stat-time {
      margin-top: 4rpx;
      font-size: 20rpx;
      color: #999;
    }
  }
}

.class-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  justify-content: space-between;

  .class-action-btn {
    display: flex;
    flex: 1;
    gap: 8rpx;
    align-items: center;
    justify-content: center;
    height: 80rpx;
    background-color: #f7f8fa;
    border-radius: 8rpx;

    text {
      font-size: 26rpx;
      color: #3a8eff;
    }

    &:active {
      opacity: 0.8;
    }
  }
}

// 教学日志详细信息样式
.journal-details {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding: 20rpx 0;
  margin-top: 16rpx;
  border-top: 1px dashed #eaeaea;
}

.detail-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  width: 100%;
}

.detail-item {
  display: flex;
  flex: 1;
  align-items: center;
  min-width: 280rpx;
}

.detail-label {
  margin-right: 8rpx;
  font-size: 24rpx;
  color: #86909c;
}

.detail-value {
  font-size: 24rpx;
  font-weight: 500;
  color: #333;
}
/* 先定义复合选择器 */
.detail-item.full-width {
  flex: 0 0 100%;
  flex-direction: column;
  align-items: flex-start;
}

.detail-item.full-width .detail-value {
  margin-top: 8rpx;
}
/* 然后定义简单的修饰符类 */
.detail-value.success {
  color: #52c41a;
}

.detail-value.warning {
  color: #faad14;
}

.detail-value.content-text {
  line-height: 1.5;
}

// 加载和空数据状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;

  .loading-text {
    margin-top: 20rpx;
    font-size: 26rpx;
    color: #86909c;
  }
}

.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;

  .empty-text {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #86909c;
  }
}

.filter-indicator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 16rpx;
  background-color: rgba(255, 56, 96, 0.1);
  border-radius: 16rpx;

  .filter-tag {
    display: flex;
    gap: 8rpx;
    align-items: center;

    text {
      font-size: 26rpx;
      font-weight: 500;
      color: #ff3860;
    }
  }

  .filter-clear {
    display: flex;
    gap: 8rpx;
    align-items: center;
    cursor: pointer;

    text {
      font-size: 24rpx;
      font-weight: 500;
      color: #666;
    }
  }
}
</style>
