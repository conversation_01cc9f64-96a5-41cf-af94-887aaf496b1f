<route lang="json5">
{
  style: {
    navigationBarTitleText: '教学任务',
  },
}
</route>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useToast } from 'wot-design-uni'
import { getTeachingTaskListBySemester } from '@/service/teachingTask'
import type { TeachingTaskItem } from '@/types/teachingTask'
import { useUserStore } from '@/store/user'
import { loadDictData, getDictLabel } from '@/utils/dict'
import type { DictData } from '@/types/system'
import SemesterWeekPicker from '@/components/SemesterWeekPicker/index.vue'
import { useTeachingTaskStore } from '@/store/teachingTask'
import Pagination from '@/components/Pagination/index.vue'
import StatsCards, { StatItem } from '@/components/StatsCards/index.vue'

// toast实例
const toast = useToast()

// 用户信息
const userStore = useUserStore()
const userName = computed(() => userStore.userInfo.realname || userStore.userInfo.nickname || '我')

// 提交状态筛选
const submitStatus = ref<string | null>(null)
// 教材状态筛选
const textbookStatus = ref<string | null>(null)

// 处理提交状态选择
const handleSubmitStatusChange = (status: string | null) => {
  if (submitStatus.value === status) {
    submitStatus.value = null
    textbookStatus.value = null
  } else {
    submitStatus.value = status
    textbookStatus.value = null // 重置教材筛选
  }
  page.value = 1 // 重置页码
  loadCourseData()
}

// 处理教材筛选
const handleTextbookFilter = (status: string | null) => {
  if (textbookStatus.value === status) {
    textbookStatus.value = null
    submitStatus.value = null
  } else {
    textbookStatus.value = status
    submitStatus.value = null // 重置成绩筛选
  }
  page.value = 1 // 重置页码
  loadCourseData()
}

// 更新导航栏标题
const updateNavbarTitle = () => {
  uni.setNavigationBarTitle({
    title: `${userName.value}的教学任务`,
  })
}

// 监听用户名变化，更新标题
watch(userName, updateNavbarTitle)

// 字典数据
const dictData = reactive<{
  SYS_ASSESSMENT_METHOD: DictData[]
  SYS_JW_JCXY: DictData[]
  SYS_JW_CJTJ: DictData[]
  SYS_SUBMIT_STATUS: DictData[]
}>({
  SYS_ASSESSMENT_METHOD: [],
  SYS_JW_JCXY: [],
  SYS_JW_CJTJ: [],
  SYS_SUBMIT_STATUS: [],
})

// 加载字典数据
const loadDicts = () => {
  loadDictData(['SYS_ASSESSMENT_METHOD', 'SYS_JW_JCXY', 'SYS_JW_CJTJ', 'SYS_SUBMIT_STATUS'])
    .then((dicts) => {
      dictData.SYS_ASSESSMENT_METHOD = dicts.SYS_ASSESSMENT_METHOD || []
      dictData.SYS_JW_JCXY = dicts.SYS_JW_JCXY || []
      dictData.SYS_JW_CJTJ = dicts.SYS_JW_CJTJ || []
      dictData.SYS_SUBMIT_STATUS = dicts.SYS_SUBMIT_STATUS || []
    })
    .catch((error) => {
      console.error('加载字典数据失败:', error)
    })
}

// 获取字典项对应的颜色
const getDictItemColor = (dictData: DictData[], value: string, key: string): string => {
  if (!dictData?.length || !value) return '#ff9500' // 默认橙色

  const dict = dictData.find((item) => {
    return key === 'submitStatus' ? item.dictLabel === value : item.dictValue === value
  })

  if (!dict) return '#ff9500'

  // 根据字典项的listClass属性返回对应的颜色
  const colorMap: Record<string, string> = {
    success: '#4cd964', // 绿色
    warning: '#ff9500', // 橙色
    danger: '#ff3b30', // 红色
    info: '#8e8e93', // 灰色
    primary: '#007aff', // 蓝色
  }

  return colorMap[dict.listClass] || '#ff9500'
}

// 获取更柔和的颜色
const getCalmerColor = (color: string): string => {
  // 根据颜色的亮度调整
  const brightness = 0.8 // 调整亮度
  const r = Math.round(parseInt(color.slice(1, 3), 16) * brightness)
  const g = Math.round(parseInt(color.slice(3, 5), 16) * brightness)
  const b = Math.round(parseInt(color.slice(5, 7), 16) * brightness)
  return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`
}

// 学期和周次选择
const semesterValue = ref<string>('')
const weekValue = ref<number | null>(null)

// 分页相关
const page = ref<number>(1)
const pageSize = ref<number>(10)
const total = ref<number>(0)

// 课程统计数据
interface CourseStatistics {
  totalCourses: number
  totalCredits: number
  totalHours: number
  requiredCredits: number
  totalWeekHours: number
  unpublishedGrades: number
  noTextbookSelected: number
}

const statistics = reactive<CourseStatistics>({
  totalCourses: 0,
  totalCredits: 0,
  totalHours: 0,
  requiredCredits: 150,
  totalWeekHours: 0,
  unpublishedGrades: 0,
  noTextbookSelected: 0,
})

// 统计卡片数据
const statsCardsData = computed<StatItem[]>(() => [
  {
    value: statistics.totalCourses,
    title: '教学任务数',
    type: 'default',
    clickable: false,
  },
  {
    value: statistics.totalHours,
    title: '总学时数',
    type: 'default',
    clickable: false,
  },
  {
    value: statistics.totalWeekHours,
    title: '总周学时数',
    type: 'default',
    clickable: false,
  },
  {
    value: statistics.unpublishedGrades,
    title: '未提交成绩数',
    type: 'warning',
    clickable: true,
    active: submitStatus.value === '0',
    onClick: () => handleSubmitStatusChange('0'),
  },
  {
    value: statistics.noTextbookSelected,
    title: '未选教材数',
    type: 'warning',
    clickable: true,
    active: textbookStatus.value === '0',
    onClick: () => handleTextbookFilter('0'),
  },
])

// 课程图标颜色
const iconColors = ['#007AFF', '#FF9500', '#4CD964', '#5AC8FA', '#FF2D55', '#FFCC00', '#8E8E93']

// 扩展TeachingTaskItem类型以包含UI需要的属性
interface ExtendedTeachingTaskItem extends TeachingTaskItem {
  icon?: string
  iconBgColor?: string
  status?: number
}

// 课程列表
const currentCourses = ref<ExtendedTeachingTaskItem[]>([])

// 加载状态
const loading = ref<boolean>(false)
const isLoading = ref<boolean>(false)

// 获取课程图标和颜色
const getCourseIcon = (course: TeachingTaskItem): { icon: string; color: string } => {
  // 默认图标
  let icon = 'view'

  // 根据课程名称选择图标
  const courseName = course.courseName.toLowerCase()
  if (courseName.includes('数据') || courseName.includes('算法')) {
    icon = 'code'
  } else if (courseName.includes('数学') || courseName.includes('计算')) {
    icon = 'calculator'
  } else if (courseName.includes('语言') || courseName.includes('编程')) {
    icon = 'code'
  } else if (courseName.includes('数据库') || courseName.includes('存储')) {
    icon = 'database'
  } else if (
    courseName.includes('网络') ||
    courseName.includes('服务器') ||
    courseName.includes('web')
  ) {
    icon = 'wifi'
  } else if (courseName.includes('管理') || courseName.includes('营销')) {
    icon = 'shop'
  }

  // 使用课程ID选择颜色
  const colorIndex = course.id % iconColors.length
  const color = iconColors[colorIndex]

  return { icon, color }
}

// 加载课程数据
const loadCourseData = () => {
  if (isLoading.value) return

  isLoading.value = true
  loading.value = true

  const params = {
    page: page.value,
    pageSize: pageSize.value,
    sortBy: 'id',
    sortOrder: 'desc',
    semesters: semesterValue.value ? [semesterValue.value] : undefined,
    submitStatus: submitStatus.value,
    textbookUse: textbookStatus.value,
  }

  getTeachingTaskListBySemester(params)
    .then((res) => {
      // 处理课程数据
      currentCourses.value = (res.list || []).map((course) => {
        const { icon, color } = getCourseIcon(course)
        return {
          ...course,
          icon,
          iconBgColor: color,
          status: course.taskExecutionStatus || 1,
        }
      })

      // 更新统计数据
      statistics.totalCourses = res.total || 0
      total.value = res.total || 0

      // 计算统计信息
      let totalHours = 0
      let totalCredits = 0
      let totalWeekHours = 0
      let unpublishedGrades = 0
      let noTextbookSelected = 0

      currentCourses.value.forEach((course) => {
        totalHours += parseFloat(course.courseTotalHours) || 0
        totalCredits += course.credit || 0
        totalWeekHours += parseFloat(course.weekHours) || 0
        if (course.submitStatus === '未提交') {
          unpublishedGrades++
        }
        if (
          course.textbookUse === 0 ||
          course.textbookUse === null ||
          course.textbookUse === undefined
        ) {
          noTextbookSelected++
        }
      })

      statistics.totalHours = totalHours
      statistics.totalCredits = totalCredits
      statistics.totalWeekHours = totalWeekHours
      statistics.unpublishedGrades = unpublishedGrades
      statistics.noTextbookSelected = noTextbookSelected
    })
    .catch((error) => {
      console.error('获取教学任务列表失败:', error)
    })
    .finally(() => {
      loading.value = false
      // 延迟重置加载标志，防止短时间内重复请求
      setTimeout(() => {
        isLoading.value = false
      }, 500)
    })
}

// 学期周次变化处理
const handleSemesterWeekChange = (data: {
  semester: { value: string }
  week: { value: number | null }
}) => {
  semesterValue.value = data.semester.value
  weekValue.value = data.week.value
  page.value = 1 // 重置页码
  loadCourseData()
}

// 页码变化处理
const handlePageChange = (newPage: number) => {
  page.value = newPage
  loadCourseData()
}

// 跳转到任务管理页面
const goToTaskManagement = (courseId: number) => {
  const selectedCourse = currentCourses.value.find((course) => course.id === courseId)

  if (selectedCourse) {
    // 将课程数据保存到store中
    const teachingTaskStore = useTeachingTaskStore()
    teachingTaskStore.setCurrentTask(selectedCourse)

    // 跳转到任务管理页面
    uni.navigateTo({
      url: `/pages/teacher/teaching-affairs/task-management/index?id=${courseId}`,
    })
  } else {
    toast.error('未找到课程信息')
  }
}

// 空状态按钮跳转处理函数
const goToCourseSelection = () => {
  uni.navigateTo({
    url: '/pages/teacher/teaching-affairs/course-selection',
  })
}

// 清除筛选条件
const clearFilters = () => {
  submitStatus.value = null
  textbookStatus.value = null
  page.value = 1
  loadCourseData()
}

// 初始化
onMounted(() => {
  updateNavbarTitle()
  loadDicts()
})
</script>

<template>
  <view class="container">
    <!-- 学年学期选择器 -->
    <view class="mb-2">
      <SemesterWeekPicker
        v-model:semesterValue="semesterValue"
        v-model:weekValue="weekValue"
        @change="handleSemesterWeekChange"
        :showAllSemester="false"
        :format-semester="true"
        :show-week="false"
        size="large"
      />
    </view>

    <!-- 筛选状态指示 -->
    <view v-if="submitStatus !== null || textbookStatus !== null" class="filter-indicator mb-2">
      <wd-icon name="filter" size="16" class="filter-indicator-icon" />
      <text class="filter-indicator-text">
        当前筛选：{{
          submitStatus === '0' ? '未提交成绩' : submitStatus === '1' ? '已提交成绩' : ''
        }}
        {{ textbookStatus === '0' ? '未选教材' : textbookStatus === '1' ? '已选教材' : '' }}
      </text>
      <wd-icon name="close" size="16" class="filter-clear-icon" @click="clearFilters" />
    </view>

    <!-- 统计卡片 - 使用StatsCards组件 -->
    <StatsCards :stats="statsCardsData" />

    <!-- 当前学期课程列表 -->
    <view class="section-title">当前学期课程</view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-state">
      <wd-loading size="30" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 当前课程列表 -->
    <view v-else-if="currentCourses.length > 0">
      <view
        v-for="course in currentCourses"
        :key="course.id"
        class="course-card"
        @click="goToTaskManagement(course.id)"
      >
        <!-- 课程头部信息 -->
        <view class="course-header">
          <view class="course-icon" :style="{ 'background-color': course.iconBgColor }">
            <wd-icon :name="course.icon" size="20" />
          </view>
          <view class="course-info">
            <view class="course-name">{{ course.courseName }}</view>
            <view class="course-teacher">{{ course.className }} | {{ course.courseCode }}</view>
            <view class="course-semester">
              <wd-icon name="calendar" size="14" class="semester-icon" />
              {{ course.studyYear }}学年 第{{ course.studyTerm }}学期
            </view>
          </view>
          <view class="header-action">
            <wd-button
              size="small"
              type="primary"
              custom-style="background-color: #007aff; border-color: #007aff; border-radius: 6px;"
              @click.stop="goToTaskManagement(course.id)"
            >
              查看
            </wd-button>
          </view>
        </view>

        <!-- 课程详细信息 -->
        <view class="course-body">
          <!-- 课程元数据 -->
          <view class="course-meta">
            <view class="meta-item">{{ course.courseTotalHours }}总学时</view>
            <view class="meta-item">{{ course.weekHours }}周学时</view>
            <view class="meta-item">{{ course.weeks }}周数</view>
            <view class="meta-item credit">{{ course.credit }}学分</view>
          </view>

          <!-- 课程安排信息 -->
          <view class="course-schedule">
            <wd-icon name="view" size="14" class="schedule-icon" />
            考核方式：{{ getDictLabel(dictData.SYS_ASSESSMENT_METHOD, course.assessmentMethod) }}
            <text class="coefficient-text">系数：{{ course.coefficient }}</text>
          </view>

          <!-- 课程状态信息 -->
          <view class="course-actions">
            <view class="course-status">
              <wd-tag
                custom-style="margin-right: 8px; font-size: 12px; padding: 4px 10px; border-radius: 14px; margin-bottom: 8px;"
                :bg-color="
                  getCalmerColor(
                    getDictItemColor(
                      dictData.SYS_SUBMIT_STATUS,
                      String(course.submitStatus),
                      'submitStatus',
                    ),
                  )
                "
                :custom-color="
                  getDictItemColor(
                    dictData.SYS_SUBMIT_STATUS,
                    String(course.submitStatus),
                    'submitStatus',
                  )
                "
              >
                成绩：{{ course.submitStatus }}
              </wd-tag>
              <wd-tag
                custom-style="font-size: 12px; padding: 4px 10px; border-radius: 14px; margin-bottom: 8px;"
                :bg-color="
                  getCalmerColor(
                    getDictItemColor(
                      dictData.SYS_JW_JCXY,
                      String(course.textbookUse),
                      'textbookUse',
                    ),
                  )
                "
                :custom-color="
                  getDictItemColor(dictData.SYS_JW_JCXY, String(course.textbookUse), 'textbookUse')
                "
              >
                教材：{{ getDictLabel(dictData.SYS_JW_JCXY, String(course.textbookUse)) }}
              </wd-tag>
            </view>
          </view>
        </view>
      </view>

      <!-- 分页组件 -->
      <Pagination
        :total="total"
        :page="page"
        :pageSize="pageSize"
        @update:page="handlePageChange"
      />
    </view>

    <!-- 空状态 -->
    <view v-else class="empty-state">
      <wd-icon name="clipboard-check" size="60" color="#d1d1d6" />
      <view class="empty-text">当前学期未选择任何课程</view>
      <!-- <wd-button
        type="primary"
        custom-style="background-color: #007aff; border-color: #007aff; border-radius: 6px;"
        @click="goToCourseSelection"
      >
        去选课
      </wd-button> -->
    </view>

    <!-- Toast提示 -->
    <wd-toast />
  </view>
</template>

<style lang="scss" scoped>
.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 100vh;
  padding: 24rpx;
  background-color: #f7f8fc;
}

// 统计卡片样式已迁移到StatsCards组件

.section-title {
  margin: 24rpx 0 16rpx;
  font-size: 16px;
  font-weight: 600;
}

.course-card {
  margin-bottom: 16px;
  overflow: hidden;
  cursor: pointer;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
}

.course-header {
  position: relative;
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f2f2f7;
}

.course-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  margin-right: 12px;
  color: white;
  border-radius: 12px;
}

.course-info {
  flex: 1;
}

.course-name {
  margin-bottom: 2px;
  font-size: 17px;
  font-weight: 600;
}

.course-teacher {
  font-size: 13px;
  color: #8e8e93;
}

.course-semester {
  display: flex;
  align-items: center;
  margin-top: 4px;
  font-size: 13px;
  color: #007aff;
}

.semester-icon {
  margin-right: 4px;
  color: #007aff;
}

.header-action {
  position: absolute;
  top: 16px;
  right: 10px;
}

.course-body {
  padding: 16px;
}

.course-meta {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.meta-item {
  padding: 4px 10px;
  margin-right: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #606266;
  background-color: #f2f2f7;
  border-radius: 14px;

  &.credit {
    color: #007aff;
    background-color: #e6f7ff;
  }
}

.course-schedule {
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;

  .coefficient-text {
    margin-left: 16px;
  }
}

.schedule-icon {
  margin-right: 4px;
  color: #8e8e93;
}

.course-actions {
  display: flex;
  justify-content: flex-start;
  padding-top: 16px;
  margin-top: 16px;
  border-top: 1px solid #f2f2f7;
}

.course-status {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-right: 24px;
  font-size: 14px;
  color: #606266;
}

.empty-state {
  padding: 60px 20px;
  text-align: center;
  background-color: white;
  border-radius: 12px;
}

.empty-text {
  margin-top: 16px;
  margin-bottom: 24px;
  font-size: 16px;
  color: #8e8e93;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background-color: white;
  border-radius: 12px;
}

.loading-text {
  margin-top: 16px;
  font-size: 16px;
  color: #8e8e93;
}

// 筛选状态指示样式
.filter-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 24rpx;
  margin-bottom: 16rpx;
  background-color: #f5f7fa;
  border-radius: 10rpx;
  box-shadow: 0 1rpx 8rpx rgba(0, 0, 0, 0.08);
}

.filter-indicator-icon {
  margin-right: 8rpx;
  color: #007aff;
}

.filter-indicator-text {
  flex: 1;
  font-size: 14px;
  color: #606266;
  text-align: center;
}

.filter-clear-icon {
  margin-left: 16rpx;
  color: #8e8e93;
  cursor: pointer;
}

// 工具类
.mb-2 {
  margin-bottom: 16rpx;
}

.mb-3 {
  margin-bottom: 24rpx;
}

.px-2 {
  padding-right: 16rpx;
  padding-left: 16rpx;
}
</style>
