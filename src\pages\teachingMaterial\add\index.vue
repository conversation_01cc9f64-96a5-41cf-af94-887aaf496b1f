<route lang="json5">
{
  style: {
    navigationBarTitleText: '新增教材',
  },
}
</route>
<template>
  <view class="teaching-material-add-container p-4">
    <!-- 表单容器 -->
    <view class="form-container">
      <!-- 基本信息部分 -->
      <view class="form-section">
        <view class="section-title">基本信息</view>

        <!-- 教材类别 -->
        <view class="form-item">
          <view class="form-row-flex">
            <text class="form-label required">教材类别</text>
            <view class="form-content">
              <wd-picker
                v-model="formData.materialCategory"
                :columns="materialCategoryOptions"
                placeholder="请选择"
                :disabled="loading"
              >
                <wd-cell
                  title="选择教材类别"
                  :value="
                    loading
                      ? '加载中...'
                      : materialCategoryOptions.find(
                          (item) => item.value === formData.materialCategory,
                        )?.label
                  "
                  :is-link="!loading"
                />
              </wd-picker>
            </view>
          </view>
        </view>

        <!-- 教材类型 -->
        <view class="form-item">
          <view class="form-row-flex">
            <text class="form-label required">教材类型</text>
            <view class="form-content">
              <wd-picker
                v-model="formData.materialType"
                :columns="materialTypeOptions"
                placeholder="请选择"
                :disabled="loading"
              >
                <wd-cell
                  title="选择教材类型"
                  :value="
                    loading
                      ? '加载中...'
                      : materialTypeOptions.find((item) => item.value === formData.materialType)
                          ?.label
                  "
                  :is-link="!loading"
                />
              </wd-picker>
            </view>
          </view>
        </view>

        <!-- 开关选项组 -->
        <view class="switch-group">
          <!-- 教育部统编教材 -->
          <view class="switch-item">
            <view class="switch-row">
              <text class="switch-label required">教育部统编教材</text>
              <view class="switch-control">
                <view class="switch-wrapper">
                  <switch
                    :checked="formData.isEducationCompiled === '1'"
                    color="#1890ff"
                    @change="(e) => (formData.isEducationCompiled = e.detail.value ? '1' : '0')"
                    class="switch-small"
                  />
                  <text class="switch-status">
                    {{ formData.isEducationCompiled === '1' ? '是' : '否' }}
                  </text>
                </view>
              </view>
            </view>
          </view>

          <!-- 国家获奖教材 -->
          <view class="switch-item">
            <view class="switch-row">
              <text class="switch-label required">国家获奖教材</text>
              <view class="switch-control">
                <view class="switch-wrapper">
                  <switch
                    :checked="formData.isNationalAward === '1'"
                    color="#1890ff"
                    @change="(e) => (formData.isNationalAward = e.detail.value ? '1' : '0')"
                    class="switch-small"
                  />
                  <text class="switch-status">
                    {{ formData.isNationalAward === '1' ? '是' : '否' }}
                  </text>
                </view>
              </view>
            </view>
          </view>

          <!-- 校企合作开发教材 -->
          <view class="switch-item">
            <view class="switch-row">
              <text class="switch-label required">校企合作开发教材</text>
              <view class="switch-control">
                <view class="switch-wrapper">
                  <switch
                    :checked="formData.isSchoolEnterprise === '1'"
                    color="#1890ff"
                    @change="(e) => (formData.isSchoolEnterprise = e.detail.value ? '1' : '0')"
                    class="switch-small"
                  />
                  <text class="switch-status">
                    {{ formData.isSchoolEnterprise === '1' ? '是' : '否' }}
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 教材名称 -->
        <view class="form-item">
          <view class="form-row-flex">
            <text class="form-label required">教材名称</text>
            <view class="form-content">
              <input
                class="form-input"
                v-model="formData.materialName"
                placeholder="请输入教材名称"
                :disabled="isEditMode"
                :class="{ 'bg-gray-100': isEditMode }"
              />
            </view>
          </view>
        </view>

        <!-- 主要编者 -->
        <view class="form-item">
          <view class="form-row-flex">
            <text class="form-label required">主要编者</text>
            <view class="form-content">
              <input
                class="form-input"
                v-model="formData.mainEditor"
                placeholder="请输入主要编者"
                :disabled="isEditMode"
                :class="{ 'bg-gray-100': isEditMode }"
              />
            </view>
          </view>
        </view>

        <!-- 编著者总数 -->
        <view class="form-item">
          <view class="form-row-flex">
            <text class="form-label required">编著者总数</text>
            <view class="form-content">
              <input
                class="form-input"
                v-model="formData.editorCount"
                type="number"
                placeholder="请输入编著者总数"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 分类信息部分 -->
      <view class="form-section">
        <view class="section-title">分类信息</view>

        <!-- 适用层次 -->
        <view class="form-item">
          <view class="form-row-column">
            <text class="form-label required">适用层次</text>
            <view v-if="loading" class="loading-tip">加载中...</view>
            <view v-else class="checkbox-group">
              <checkbox-group @change="(e) => handleCheckboxChange('applicableLevels', e)">
                <view
                  v-for="item in applicableLevelOptions"
                  :key="item.value"
                  class="checkbox-item"
                >
                  <label>
                    <checkbox
                      :value="item.value"
                      :checked="formData.applicableLevels.includes(item.value)"
                      color="#1890ff"
                    />
                    <text class="checkbox-label">{{ item.label }}</text>
                  </label>
                </view>
              </checkbox-group>
              <view v-if="applicableLevelOptions.length === 0" class="empty-tip">暂无数据</view>
            </view>
          </view>
        </view>

        <!-- 对应领域 -->
        <view class="form-item">
          <view class="form-row-column">
            <text class="form-label required">对应领域</text>
            <view v-if="loading" class="loading-tip">加载中...</view>
            <view v-else class="checkbox-group">
              <checkbox-group @change="(e) => handleCheckboxChange('correspondingFields', e)">
                <view
                  v-for="item in correspondingFieldOptions"
                  :key="item.value"
                  class="checkbox-item"
                >
                  <label>
                    <checkbox
                      :value="item.value"
                      :checked="formData.correspondingFields.includes(item.value)"
                      color="#1890ff"
                    />
                    <text class="checkbox-label">{{ item.label }}</text>
                  </label>
                </view>
              </checkbox-group>
              <view v-if="correspondingFieldOptions.length === 0" class="empty-tip">暂无数据</view>
            </view>
          </view>
        </view>

        <!-- 教材特色 -->
        <view class="form-item">
          <view class="form-row-column">
            <text class="form-label required">教材特色</text>
            <view v-if="loading" class="loading-tip">加载中...</view>
            <view v-else class="checkbox-group">
              <checkbox-group @change="(e) => handleCheckboxChange('materialFeatures', e)">
                <view
                  v-for="item in materialFeatureOptions"
                  :key="item.value"
                  class="checkbox-item"
                >
                  <label>
                    <checkbox
                      :value="item.value"
                      :checked="formData.materialFeatures.includes(item.value)"
                      color="#1890ff"
                    />
                    <text class="checkbox-label">{{ item.label }}</text>
                  </label>
                </view>
              </checkbox-group>
              <view v-if="materialFeatureOptions.length === 0" class="empty-tip">暂无数据</view>
            </view>
          </view>
        </view>
      </view>

      <!-- 出版信息部分 -->
      <view class="form-section">
        <view class="section-title">出版信息</view>

        <!-- 出版社 -->
        <view class="form-item">
          <view class="form-row-flex">
            <text class="form-label">出版社</text>
            <view class="form-content">
              <input
                class="form-input"
                :class="[
                  formData.materialCategory === SELF_PRINT_MATERIAL_VALUE || isEditMode
                    ? 'bg-gray-100'
                    : '',
                ]"
                v-model="formData.publisher"
                placeholder="请输入出版社"
                :disabled="formData.materialCategory === SELF_PRINT_MATERIAL_VALUE || isEditMode"
              />
            </view>
          </view>
        </view>

        <!-- ISBN -->
        <view class="form-item">
          <view class="form-row-flex">
            <text class="form-label" :class="{ required: isIsbnRequired }">ISBN</text>
            <view class="form-content">
              <input
                :class="[isEditMode ? 'bg-gray-100' : '']"
                class="form-input"
                v-model="formData.isbn"
                placeholder="请输入ISBN"
                :disabled="isEditMode"
              />
            </view>
          </view>
        </view>

        <!-- 分类号 -->
        <view class="form-item">
          <view class="form-row-flex">
            <text class="form-label">分类号</text>
            <view class="form-content">
              <input
                class="form-input"
                v-model="formData.classificationNumber"
                placeholder="请输入分类号"
              />
            </view>
          </view>
        </view>

        <!-- 印次 -->
        <view class="form-item">
          <view class="form-row-flex">
            <text class="form-label">印次</text>
            <view class="form-content">
              <input class="form-input" v-model="formData.printingTimes" placeholder="请输入印次" />
            </view>
          </view>
        </view>

        <!-- 版次 -->
        <view class="form-item">
          <view class="form-row-flex">
            <text class="form-label">版次</text>
            <view class="form-content">
              <picker
                mode="date"
                fields="month"
                :value="formData.edition"
                @change="(e) => (formData.edition = e.detail.value)"
              >
                <view class="picker-display">
                  <text v-if="formData.edition">{{ formData.edition }}</text>
                  <text v-else class="placeholder">请选择版次</text>
                  <wd-icon name="chevron-down" class="text-gray-500" size="16px" />
                </view>
              </picker>
              <view class="form-tip">
                <text class="tip-text">自编教材填立项时间，每年的6月、12月</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 定价 -->
        <view class="form-item">
          <view class="form-row-flex">
            <text class="form-label">定价</text>
            <view class="form-content">
              <view class="price-input-container" :class="[isEditMode ? 'bg-gray-100' : '']">
                <text class="price-prefix">¥</text>
                <input
                  class="form-input price-input"
                  v-model="formData.price"
                  type="digit"
                  placeholder="请输入定价"
                  :disabled="isEditMode"
                />
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 其他信息部分 -->
      <view class="form-section">
        <view class="section-title">其他信息</view>

        <!-- 数字资源 -->
        <view class="form-item">
          <view class="form-row-flex">
            <text class="form-label">数字资源</text>
            <view class="form-content">
              <view class="digit-input-container">
                <input
                  class="form-input digit-input"
                  v-model="formData.digitalResource"
                  type="digit"
                  placeholder="请输入数字资源大小"
                />
                <text class="digit-suffix">GB</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 备注 -->
        <view class="form-item">
          <view class="form-row-column">
            <text class="form-label">备注</text>
            <view class="form-content">
              <textarea class="form-textarea" v-model="formData.remark" placeholder="请输入备注" />
            </view>
          </view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="button-group">
        <button class="submit-button" @click="handleSubmit">提交</button>
        <button class="cancel-button" @click="handleCancel">取消</button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { loadDictData, getDictOptions } from '@/utils/dict'
import {
  addTeachingMaterial,
  getTeachingMaterialDetail,
  updateTeachingMaterialByTeacher,
} from '@/service/teachingMaterial'
import type {
  TeachingMaterialAddRequest,
  TeachingMaterialDetailResponse,
  TeacherUpdateTeachingMaterialRequest,
} from '@/types/teachingMaterial'

// 表单数据
const formData = reactive({
  materialCategory: '', // 教材类别
  materialType: '', // 教材类型
  isEducationCompiled: '0', // 教育部统编教材
  isNationalAward: '0', // 国家获奖教材
  isSchoolEnterprise: '0', // 校企合作开发教材
  applicableLevels: [], // 适用层次
  correspondingFields: [], // 对应领域
  materialFeatures: [], // 教材特色
  materialName: '', // 教材名称
  mainEditor: '', // 主要编者
  editorCount: '', // 编著者总数
  publisher: '', // 出版社
  isbn: '', // ISBN
  classificationNumber: '', // 分类号
  printingTimes: '', // 印次
  edition: '', // 版次
  projectTime: '', // 自编教材填立项时间
  price: '', // 定价
  digitalResource: '', // 数字资源(GB)
  remark: '', // 备注
  booksCode: '', // 教材代码
})

// 教材ID
const teachingMaterialId = ref<number | null>(null)
// 页面标题
const pageTitle = ref('新增教材')
// 是否为编辑模式
const isEditMode = ref(false)

// 加载状态
const loading = ref(false)
// 控制ISBN是否必填
const isIsbnRequired = ref(true)
// 自印教材的值
const SELF_PRINT_MATERIAL_VALUE = ref('')

// 字典选项
const materialCategoryOptions = ref<{ label: string; value: string }[]>([])
const materialTypeOptions = ref<{ label: string; value: string }[]>([])
const applicableLevelOptions = ref<{ label: string; value: string }[]>([])
const correspondingFieldOptions = ref<{ label: string; value: string }[]>([])
const materialFeatureOptions = ref<{ label: string; value: string }[]>([])

// 加载字典数据
const loadDicts = async () => {
  loading.value = true
  try {
    const dicts = await loadDictData([
      'DM_JCLBDM', // 教材类别
      'DM_JCLXDM', // 教材类型
      'DM_JCSYCC', // 适用层次
      'DM_JCDYLY', // 对应领域
      'DM_JCTSDM', // 教材特色
    ])

    materialCategoryOptions.value = getDictOptions(dicts.DM_JCLBDM)
    materialTypeOptions.value = getDictOptions(dicts.DM_JCLXDM)
    applicableLevelOptions.value = getDictOptions(dicts.DM_JCSYCC)
    correspondingFieldOptions.value = getDictOptions(dicts.DM_JCDYLY)
    materialFeatureOptions.value = getDictOptions(dicts.DM_JCTSDM)

    // 查找"自印教材"的值
    const selfPrintMaterial = materialCategoryOptions.value.find(
      (option) => option.label === '自印教材',
    )
    if (selfPrintMaterial) {
      SELF_PRINT_MATERIAL_VALUE.value = selfPrintMaterial.value
    }

    console.log('教材类别字典数据:', materialCategoryOptions.value)
    console.log('教材类型字典数据:', materialTypeOptions.value)
    console.log('适用层次字典数据:', applicableLevelOptions.value)
    console.log('对应领域字典数据:', correspondingFieldOptions.value)
    console.log('教材特色字典数据:', materialFeatureOptions.value)
  } catch (error) {
    console.error('加载字典数据失败：', error)
    uni.showToast({
      title: '加载字典数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 获取教材详情
const fetchTeachingMaterialDetail = async (id: number) => {
  loading.value = true
  try {
    const res = await getTeachingMaterialDetail({ id })
    console.log('获取到的教材详情:', res)
    // 后续会在这里处理数据回填
    return res
  } catch (error) {
    console.error('获取教材详情失败:', error)
    uni.showToast({
      title: '获取教材详情失败',
      icon: 'none',
    })
    return null
  } finally {
    loading.value = false
  }
}

// 监听教材类别变化
watch(
  () => formData.materialCategory,
  (newValue) => {
    if (newValue === SELF_PRINT_MATERIAL_VALUE.value) {
      // 自印教材，自动填充出版社为校本编印，ISBN非必填
      formData.publisher = '校本编印'
      isIsbnRequired.value = false
    } else {
      // 其他类别，恢复默认行为
      isIsbnRequired.value = true
    }
  },
)

// 组件挂载时加载字典数据和检查是否有教材ID
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = (currentPage as any).options || {}

  // 检查是否有教材ID参数
  if (options.jcxxid) {
    const id = Number(options.jcxxid)
    if (!isNaN(id) && id > 0) {
      teachingMaterialId.value = id
      isEditMode.value = true
      pageTitle.value = '编辑教材'

      // 设置页面标题
      uni.setNavigationBarTitle({
        title: pageTitle.value,
      })
    }
  }

  // 先加载字典数据，然后获取教材详情
  loadDicts().then(() => {
    // 如果有教材ID，获取教材详情
    if (teachingMaterialId.value) {
      fetchTeachingMaterialDetail(teachingMaterialId.value).then((detailData) => {
        if (detailData) {
          // 回填数据到表单
          fillFormWithDetailData(detailData)
        }
      })
    }
  })
})

// 将详情数据回填到表单
const fillFormWithDetailData = (detailData: TeachingMaterialDetailResponse) => {
  // 回填基本信息
  formData.materialCategory = detailData.category || ''
  formData.materialType = detailData.type || ''
  formData.isEducationCompiled = detailData.isNationalStandard === 1 ? '1' : '0'
  formData.isNationalAward = detailData.isNationalAward === 1 ? '1' : '0'
  formData.isSchoolEnterprise = detailData.sfxqhzkfjc === 1 ? '1' : '0'
  formData.materialName = detailData.name || ''
  formData.mainEditor = detailData.mainEditor || ''
  formData.editorCount = detailData.editorNumber?.toString() || ''
  formData.booksCode = detailData.booksCode || ''

  // 回填分类信息
  formData.applicableLevels = Array.isArray(detailData.suitableLevel)
    ? detailData.suitableLevel
    : []

  formData.correspondingFields = Array.isArray(detailData.correspondingDomain)
    ? detailData.correspondingDomain
    : []

  formData.materialFeatures = Array.isArray(detailData.feature) ? detailData.feature : []

  // 回填出版信息
  formData.publisher = detailData.publishingHouse || ''
  formData.isbn = detailData.isbn || ''
  formData.classificationNumber = detailData.classNumber || ''
  formData.printingTimes = detailData.printingTime || ''
  formData.edition = detailData.publicationTime || ''
  formData.price = detailData.money || ''

  // 回填其他信息
  formData.digitalResource = detailData.filesize || ''
  formData.remark = detailData.remark || ''

  console.log('回填后的表单数据:', formData)
}

// 处理复选框变化
const handleCheckboxChange = (field: string, event: any) => {
  console.log(`${field} 变化事件:`, event)
  // checkbox-group的change事件会返回包含所有选中项value的数组
  const values = event.detail.value

  // 直接将选中的值数组赋值给对应字段
  formData[field] = values

  console.log(`${field} 更新后的值:`, formData[field])
}

// 验证表单
const validateForm = () => {
  // 必填字段验证
  if (!formData.materialCategory) {
    uni.showToast({
      title: '请选择教材类别',
      icon: 'none',
    })
    return false
  }

  if (!formData.materialType) {
    uni.showToast({
      title: '请选择教材类型',
      icon: 'none',
    })
    return false
  }

  if (!formData.materialName) {
    uni.showToast({
      title: '请输入教材名称',
      icon: 'none',
    })
    return false
  }

  if (!formData.mainEditor) {
    uni.showToast({
      title: '请输入主要编者',
      icon: 'none',
    })
    return false
  }

  if (!formData.editorCount) {
    uni.showToast({
      title: '请输入编著者总数',
      icon: 'none',
    })
    return false
  }

  // ISBN验证，根据isIsbnRequired决定是否必填
  if (isIsbnRequired.value && !formData.isbn) {
    uni.showToast({
      title: '请输入ISBN',
      icon: 'none',
    })
    return false
  }

  // ISBN格式验证，当有值时进行验证
  if (formData.isbn) {
    // 验证ISBN是否为13位纯数字
    const isbnRegex = /^\d{13}$/
    if (!isbnRegex.test(formData.isbn)) {
      uni.showToast({
        title: 'ISBN号不正确，长度应为纯数字13位',
        icon: 'none',
      })
      return false
    }
  }

  if (formData.applicableLevels.length === 0) {
    uni.showToast({
      title: '请选择适用层次',
      icon: 'none',
    })
    return false
  }

  if (formData.correspondingFields.length === 0) {
    uni.showToast({
      title: '请选择对应领域',
      icon: 'none',
    })
    return false
  }

  if (formData.materialFeatures.length === 0) {
    uni.showToast({
      title: '请选择教材特色',
      icon: 'none',
    })
    return false
  }

  return true
}

// 提交表单数据到API
const submitFormData = async () => {
  // 基础表单数据，共同部分
  const baseData = {
    category: formData.materialCategory,
    type: formData.materialType,
    isNationalStandard: formData.isEducationCompiled === '1' ? 1 : 0,
    isNationalAward: formData.isNationalAward === '1' ? 1 : 0,
    sfxqhzkfjc: formData.isSchoolEnterprise === '1' ? 1 : 0,
    suitableLevel: formData.applicableLevels,
    correspondingDomain: formData.correspondingFields,
    feature: formData.materialFeatures,
    name: formData.materialName,
    mainEditor: formData.mainEditor,
    editorNumber: Number(formData.editorCount),
    publishingHouse: formData.publisher,
    isbn: formData.isbn,
    classNumber: formData.classificationNumber,
    printingTime: formData.printingTimes,
    publicationTime: formData.edition,
    money: formData.price,
    remark: formData.remark,
    booksCode: formData.booksCode,
  }

  try {
    if (isEditMode.value && teachingMaterialId.value) {
      // 编辑模式，调用更新接口
      const updateData: TeacherUpdateTeachingMaterialRequest = {
        ...baseData,
        id: teachingMaterialId.value,
        filesize: formData.digitalResource || '0', // string类型
        isTeachOffice: false,
        isTeacherUpdate: true,
      }

      const res = await updateTeachingMaterialByTeacher(updateData)

      uni.showToast({
        title: '更新成功',
        icon: 'success',
      })
    } else {
      // 新增模式，调用原有接口
      const addData: TeachingMaterialAddRequest = {
        ...baseData,
        filesize: Number(formData.digitalResource || 0), // number类型
      }

      const res = await addTeachingMaterial(addData)

      uni.showToast({
        title: '添加成功',
        icon: 'success',
      })
    }

    // 成功后返回上一页
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } catch (error) {
    console.error(isEditMode.value ? '更新教材失败：' : '添加教材失败：', error)
  }
}

// 提交表单
const handleSubmit = () => {
  if (!validateForm()) {
    return
  }

  submitFormData()
}

// 取消操作
const handleCancel = () => {
  uni.navigateBack()
}
</script>

<style lang="scss">
.bg-gray-100 {
  background-color: rgb(243 244 246 / var(--un-bg-opacity)) !important;
}
.teaching-material-add-container {
  min-height: 100vh;
  background-color: #f5f5f5;

  .form-container {
    width: 100%;
  }

  .form-section {
    box-sizing: border-box;
    padding: 24rpx;
    margin-bottom: 24rpx;
    background-color: #ffffff;
    border-radius: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  }

  .section-title {
    padding-bottom: 16rpx;
    margin-bottom: 20rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
    border-bottom: 1px solid #f0f0f0;
  }

  .form-item {
    box-sizing: border-box;
    width: 100%;
    margin-bottom: 20rpx;
  }

  .form-row-flex {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .form-row {
    display: flex;
    align-items: center;
  }

  .form-row-column {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    width: 100%;
  }

  .form-label {
    margin-bottom: 12rpx;
    font-size: 28rpx;
    color: #333333;

    &.required::before {
      margin-right: 4rpx;
      color: #f5222d;
      content: '*';
    }
  }

  .form-content {
    box-sizing: border-box;
    width: 100%;
  }

  .form-input {
    box-sizing: border-box;
    width: 100%;
    height: 80rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    color: #333333;
    background-color: #ffffff;
    border: 1px solid #e8e8e8;
    border-radius: 8rpx;
  }

  .form-textarea {
    box-sizing: border-box;
    width: 100%;
    height: 180rpx;
    padding: 20rpx;
    font-size: 28rpx;
    color: #333333;
    background-color: #ffffff;
    border: 1px solid #e8e8e8;
    border-radius: 8rpx;
  }

  .switch-group {
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin-bottom: 20rpx;
  }

  .switch-item {
    box-sizing: border-box;
    flex: 1;
    min-width: 50%;
    padding-right: 16rpx;
    margin-bottom: 16rpx;
  }

  .switch-row {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 10rpx 16rpx;
    border-radius: 8rpx;
  }

  .switch-label {
    font-size: 28rpx;
    color: #333333;

    &.required::before {
      margin-right: 4rpx;
      color: #f5222d;
      content: '*';
    }
  }

  .switch-control {
    display: flex;
    align-items: center;
  }

  .switch-wrapper {
    display: flex;
    align-items: center;
  }

  .switch-small {
    margin-right: 8rpx;
    transform: scale(0.8);
  }

  .switch-status {
    min-width: 30rpx;
    font-size: 24rpx;
    font-weight: 500;
    color: #1890ff;
    text-align: center;
  }

  .checkbox-group {
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    margin-top: 16rpx;
  }

  .checkbox-item {
    display: flex;
    align-items: center;
    margin-right: 32rpx;
    margin-bottom: 16rpx;
  }

  .checkbox-label {
    margin-left: 8rpx;
    font-size: 28rpx;
    color: #333333;
  }

  .picker-display {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 80rpx;
    padding: 0 20rpx;
    font-size: 28rpx;
    color: #333333;
    background-color: #ffffff;
    border: 1px solid #e8e8e8;
    border-radius: 8rpx;

    .placeholder {
      color: #999999;
    }
  }

  .price-input-container {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    width: 100%;
    background-color: #ffffff;
    border: 1px solid #e8e8e8;
    border-radius: 8rpx;
  }

  .price-prefix {
    padding: 0 16rpx;
    font-size: 28rpx;
    color: #333333;
  }

  .price-input {
    flex: 1;
    background-color: transparent;
    border: none;
  }

  .digit-input-container {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    width: 100%;
    background-color: #ffffff;
    border: 1px solid #e8e8e8;
    border-radius: 8rpx;
  }

  .digit-input {
    flex: 1;
    background-color: transparent;
    border: none;
  }

  .digit-suffix {
    padding: 0 16rpx;
    font-size: 28rpx;
    color: #333333;
  }

  .button-group {
    display: flex;
    gap: 32rpx;
    justify-content: center;
    margin-top: 48rpx;
    margin-bottom: 48rpx;
  }

  .submit-button {
    width: 240rpx;
    height: 80rpx;
    font-size: 28rpx;
    line-height: 80rpx;
    color: #ffffff;
    text-align: center;
    background-color: #1890ff;
    border-radius: 40rpx;
  }

  .cancel-button {
    width: 240rpx;
    height: 80rpx;
    font-size: 28rpx;
    line-height: 80rpx;
    color: #666666;
    text-align: center;
    background-color: #f5f5f5;
    border: 1px solid #d9d9d9;
    border-radius: 40rpx;
  }

  .loading-tip {
    padding: 16rpx 0;
    font-size: 28rpx;
    color: #999999;
  }

  .empty-tip {
    width: 100%;
    padding: 20rpx 0;
    font-size: 28rpx;
    color: #999999;
    text-align: center;
  }

  .form-tip {
    margin-top: 8rpx;
  }

  .tip-text {
    font-size: 24rpx;
    font-weight: 500;
    color: #1890ff;
  }
}
</style>
