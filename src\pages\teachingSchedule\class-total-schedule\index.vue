<route lang="json5">
{
  style: {
    navigationBarTitleText: '班级总课表',
  },
}
</route>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import ScheduleLayout from '../components/ScheduleLayout.vue'

// 当前学期
const yearValue = ref('')

// 当前日期
const currentDate = ref(formatDate(new Date()))

// 格式化日期为YYYY-MM-DD
function formatDate(date: Date): string {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 校区列表
const campusList = reactive([
  { label: '全部校区', value: 'all' },
  { label: '东校区', value: 'east' },
  { label: '西校区', value: 'west' },
  { label: '南校区', value: 'south' },
  { label: '北校区', value: 'north' },
])

// 当前选中的校区
const currentCampus = ref('all')

// 院系列表
const departmentList = reactive([
  { label: '全部院系', value: 'all' },
  { label: '计算机学院', value: 'computer' },
  { label: '数学学院', value: 'math' },
  { label: '物理学院', value: 'physics' },
  { label: '化学学院', value: 'chemistry' },
  { label: '生物学院', value: 'biology' },
  { label: '外国语学院', value: 'foreign' },
  { label: '经济管理学院', value: 'economics' },
])

// 当前选中的院系
const currentDepartment = ref('all')

// 班级课表列表
const classTotalScheduleList = ref<
  Array<{
    id: string
    className: string
    grade: string
    department: string
    campus: string
    studentCount: number
    courseCount: number
    weeklyHours: number
    status: 'normal' | 'overload' | 'underload'
  }>
>([])

// 搜索关键词
const searchKeyword = ref('')

// 获取班级总课表数据
const fetchClassTotalSchedule = () => {
  // 模拟数据，实际项目中应该从API获取
  classTotalScheduleList.value = [
    {
      id: '1',
      className: '计算机2021-1班',
      grade: '2021',
      department: 'computer',
      campus: 'east',
      studentCount: 42,
      courseCount: 8,
      weeklyHours: 32,
      status: 'normal',
    },
    {
      id: '2',
      className: '软件工程2022-2班',
      grade: '2022',
      department: 'computer',
      campus: 'east',
      studentCount: 45,
      courseCount: 7,
      weeklyHours: 28,
      status: 'underload',
    },
    {
      id: '3',
      className: '数学2021-1班',
      grade: '2021',
      department: 'math',
      campus: 'west',
      studentCount: 38,
      courseCount: 9,
      weeklyHours: 36,
      status: 'overload',
    },
    {
      id: '4',
      className: '物理2023-2班',
      grade: '2023',
      department: 'physics',
      campus: 'west',
      studentCount: 40,
      courseCount: 8,
      weeklyHours: 32,
      status: 'normal',
    },
    {
      id: '5',
      className: '化学2022-3班',
      grade: '2022',
      department: 'chemistry',
      campus: 'south',
      studentCount: 36,
      courseCount: 8,
      weeklyHours: 30,
      status: 'normal',
    },
    {
      id: '6',
      className: '生物2024-1班',
      grade: '2024',
      department: 'biology',
      campus: 'north',
      studentCount: 44,
      courseCount: 6,
      weeklyHours: 24,
      status: 'underload',
    },
    {
      id: '7',
      className: '英语2023-1班',
      grade: '2023',
      department: 'foreign',
      campus: 'east',
      studentCount: 35,
      courseCount: 10,
      weeklyHours: 38,
      status: 'overload',
    },
    {
      id: '8',
      className: '经济2022-1班',
      grade: '2022',
      department: 'economics',
      campus: 'south',
      studentCount: 48,
      courseCount: 8,
      weeklyHours: 32,
      status: 'normal',
    },
  ]
}

// 筛选班级总课表列表
const filteredClassTotalScheduleList = computed(() => {
  let result = classTotalScheduleList.value

  // 按校区筛选
  if (currentCampus.value !== 'all') {
    result = result.filter((item) => item.campus === currentCampus.value)
  }

  // 按院系筛选
  if (currentDepartment.value !== 'all') {
    result = result.filter((item) => item.department === currentDepartment.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter((item) => item.className.toLowerCase().includes(keyword))
  }

  return result
})

// 处理学年变更
const handleYearChange = (data: { label: string; value: string }) => {
  yearValue.value = data.value
  // 重新获取数据
  fetchClassTotalSchedule()
}

// 处理日期变更
const handleDateChange = (date: string) => {
  currentDate.value = date
  // 重新获取数据
  fetchClassTotalSchedule()
}

// 处理校区变更
const handleCampusChange = (value: string) => {
  currentCampus.value = value
  // 重新获取数据
  fetchClassTotalSchedule()
}

// 处理院系变更
const handleDepartmentChange = (value: string) => {
  currentDepartment.value = value
  // 重新获取数据
  fetchClassTotalSchedule()
}

// 处理搜索
const handleSearch = () => {
  // 触发搜索，重新获取数据
  fetchClassTotalSchedule()
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'normal':
      return '正常'
    case 'overload':
      return '超负荷'
    case 'underload':
      return '负荷不足'
    default:
      return '未知'
  }
}

// 获取状态样式类
const getStatusClass = (status: string) => {
  switch (status) {
    case 'normal':
      return 'status-normal'
    case 'overload':
      return 'status-overload'
    case 'underload':
      return 'status-underload'
    default:
      return ''
  }
}

// 查看班级课表详情
const viewClassScheduleDetail = (classItem: any) => {
  uni.showToast({
    title: `查看${classItem.className}的课表`,
    icon: 'none',
  })
  // 实际项目中应该跳转到班级课表详情页
}

onMounted(() => {
  fetchClassTotalSchedule()
})
</script>

<template>
  <ScheduleLayout
    title="班级总课表"
    :subtitle="`共${filteredClassTotalScheduleList.length}个班级`"
    :hasData="filteredClassTotalScheduleList.length > 0"
    :yearValue="yearValue"
    :showDatePicker="true"
    :currentDate="currentDate"
    @yearChange="handleYearChange"
    @dateChange="handleDateChange"
  >
    <!-- 筛选条件 -->
    <template #filter>
      <!-- 查询表单部分 -->
      <view class="query-section">
        <view class="query-form">
          <!-- 校区选择 -->
          <view class="form-item">
            <view class="form-row">
              <text class="form-label">校区</text>
              <view class="form-content">
                <wd-picker
                  :columns="campusList"
                  :value="currentCampus"
                  @change="
                    (value) => {
                      handleCampusChange(value)
                    }
                  "
                >
                  <wd-cell
                    title="选择校区"
                    :value="campusList.find((item) => item.value === currentCampus)?.label"
                  />
                </wd-picker>
              </view>
            </view>
          </view>

          <!-- 院系选择 -->
          <view class="form-item">
            <view class="form-row">
              <text class="form-label">院系</text>
              <view class="form-content">
                <wd-picker
                  :columns="departmentList"
                  :value="currentDepartment"
                  @change="
                    (value) => {
                      handleDepartmentChange(value)
                    }
                  "
                >
                  <wd-cell
                    title="选择院系"
                    :value="departmentList.find((item) => item.value === currentDepartment)?.label"
                  />
                </wd-picker>
              </view>
            </view>
          </view>

          <!-- 搜索框 -->
          <view class="form-item">
            <view class="search-box">
              <view class="search-input-container">
                <wd-icon name="search" class="text-gray-500" size="16px" />
                <input
                  class="search-input"
                  v-model="searchKeyword"
                  placeholder="搜索班级名称"
                  confirm-type="search"
                  @confirm="handleSearch"
                />
              </view>
            </view>
          </view>

          <!-- 查询按钮 -->
          <view class="query-button-container">
            <wd-button type="primary" block @click="handleSearch">查询</wd-button>
          </view>
        </view>
      </view>
    </template>

    <!-- 班级总课表内容 -->
    <template #content>
      <view class="class-list">
        <view
          v-for="classItem in filteredClassTotalScheduleList"
          :key="classItem.id"
          class="class-item"
          @click="viewClassScheduleDetail(classItem)"
        >
          <view class="class-info">
            <view class="class-header">
              <text class="class-name">{{ classItem.className }}</text>
              <view :class="['class-status', getStatusClass(classItem.status)]">
                {{ getStatusText(classItem.status) }}
              </view>
            </view>

            <view class="class-detail">
              <view class="detail-row">
                <view class="detail-item">
                  <wd-icon name="location" class="text-gray-500" size="16px" />
                  <text class="detail-text">
                    {{ campusList.find((item) => item.value === classItem.campus)?.label }}
                  </text>
                </view>
                <view class="detail-item">
                  <wd-icon name="books" class="text-gray-500" size="16px" />
                  <text class="detail-text">
                    {{ departmentList.find((item) => item.value === classItem.department)?.label }}
                  </text>
                </view>
              </view>

              <view class="detail-row">
                <view class="detail-item">
                  <wd-icon name="usergroup" class="text-gray-500" size="16px" />
                  <text class="detail-text">{{ classItem.studentCount }}人</text>
                </view>
                <view class="detail-item">
                  <wd-icon name="time" class="text-gray-500" size="16px" />
                  <text class="detail-text">
                    {{ classItem.courseCount }}门课 / {{ classItem.weeklyHours }}学时每周
                  </text>
                </view>
              </view>
            </view>

            <view class="class-stats">
              <view class="stats-item">
                <text class="stats-label">课程数</text>
                <text class="stats-value">{{ classItem.courseCount }}</text>
              </view>
              <view class="stats-item">
                <text class="stats-label">周学时</text>
                <text class="stats-value">{{ classItem.weeklyHours }}</text>
              </view>
              <view class="stats-item">
                <text class="stats-label">学生数</text>
                <text class="stats-value">{{ classItem.studentCount }}</text>
              </view>
            </view>
          </view>
          <view class="class-action">
            <wd-icon name="arrow-right" class="text-gray-400" size="20px" />
          </view>
        </view>
      </view>
    </template>
  </ScheduleLayout>
</template>

<style lang="scss" scoped>
.query-form {
  padding: 24rpx 32rpx;
}

.form-item {
  margin-bottom: 24rpx;
}

.form-row {
  display: flex;
  align-items: center;
}

.form-label {
  flex-shrink: 0;
  width: 140rpx;
  margin-right: 12rpx;
  font-size: 28rpx;
  color: #333333;
}

.form-content {
  flex: 1;
  overflow: hidden;
  border-radius: 12rpx;
}

.query-button-container {
  margin-top: 32rpx;
}

.search-box {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.search-input-container {
  display: flex;
  flex: 1;
  align-items: center;
  padding: 0 16rpx;
  border: 1px solid #e5e5e5;
  border-radius: 32rpx;
}

.search-input {
  flex: 1;
  height: 64rpx;
  padding: 0 12rpx;
  font-size: 28rpx;
}

.class-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.class-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.class-info {
  flex: 1;
}

.class-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.class-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.class-status {
  padding: 4rpx 16rpx;
  font-size: 24rpx;
  border-radius: 16rpx;
}

.status-normal {
  color: #52c41a;
  background-color: rgba(82, 196, 26, 0.1);
}

.status-overload {
  color: #f5222d;
  background-color: rgba(245, 34, 45, 0.1);
}

.status-underload {
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
}

.class-detail {
  margin-bottom: 20rpx;
}

.detail-row {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  margin-bottom: 12rpx;
}

.detail-item {
  display: flex;
  gap: 8rpx;
  align-items: center;
}

.detail-text {
  font-size: 26rpx;
  color: #666666;
}

.class-stats {
  display: flex;
  padding-top: 16rpx;
  margin-top: 16rpx;
  border-top: 1px solid #f0f0f0;
}

.stats-item {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
}

.stats-label {
  font-size: 24rpx;
  color: #999999;
}

.stats-value {
  margin-top: 4rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.class-action {
  margin-left: 16rpx;
}
</style>
