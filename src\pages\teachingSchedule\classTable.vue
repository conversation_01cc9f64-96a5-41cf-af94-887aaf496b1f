<route lang="json5">
{
  style: {
    navigationBarTitleText: '教学安排查询',
  },
}
</route>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'

// 功能卡片列表
const functionCards = reactive([
  {
    name: '班级课程表',
    icon: 'calendar',
    path: './scheduleView/index',
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-500',
    type: 'class',
    active: true,
  },
  {
    name: '教师课程表',
    icon: 'usergroup',
    path: './scheduleView/index',
    bgColor: 'bg-amber-100',
    textColor: 'text-amber-500',
    type: 'teacher',
    active: true,
  },
  {
    name: '场地使用表',
    icon: 'location',
    path: './venueSchedule/index',
    bgColor: 'bg-green-100',
    textColor: 'text-green-500',
    active: true,
  },
  {
    name: '课程课表',
    icon: 'books',
    path: './scheduleView/index',
    bgColor: 'bg-purple-100',
    textColor: 'text-purple-500',
    type: 'course',
    active: true,
  },
  {
    name: '教学进程表',
    icon: 'chart',
    path: './teaching-progress/index',
    bgColor: 'bg-cyan-100',
    textColor: 'text-cyan-500',
    active: true,
  },
  {
    name: '班级总课表',
    icon: 'a-rootlist',
    path: './class-total-schedule/index',
    bgColor: 'bg-rose-100',
    textColor: 'text-rose-500',
    active: true,
  },
  {
    name: '教师总课表',
    icon: 'usergroup-add',
    path: './teacher-total-schedule/index',
    bgColor: 'bg-blue-100',
    textColor: 'text-blue-500',
    active: true,
  },
  {
    name: '场地总使用表',
    icon: 'view-module',
    path: './venue-total-schedule',
    bgColor: 'bg-yellow-100',
    textColor: 'text-yellow-600',
    active: false,
  },
])

// 点击功能卡片
const handleCardClick = (card: any) => {
  // 检查功能是否可用
  if (!card.active) {
    uni.showToast({
      title: '该功能暂未开放',
      icon: 'none',
    })
    return
  }

  // 根据卡片类型跳转到不同页面
  if (card.name === '班级课程表' || card.name === '教师课程表' || card.name === '课程课表') {
    // 需要传递type参数的页面
    uni.navigateTo({
      url: `${card.path}?type=${card.type}`,
    })
  } else if (
    card.name === '场地使用表' ||
    card.name === '教学进程表' ||
    card.name === '班级总课表' ||
    card.name === '教师总课表'
  ) {
    // 不需要传递额外参数的页面
    uni.navigateTo({
      url: `${card.path}`,
    })
  } else {
    // 其他功能暂未实现
    uni.showToast({
      title: '该功能暂未实现',
      icon: 'none',
    })
  }
}
</script>

<template>
  <view class="container">
    <view class="header">
      <text class="header-title">课程表管理</text>
      <text class="header-subtitle">查看各类课程表信息</text>
    </view>

    <!-- 功能卡片网格 -->
    <view class="card-grid">
      <view
        v-for="(card, index) in functionCards"
        :key="index"
        :class="['card-item', card.active ? 'card-active' : 'card-disabled']"
        @click="handleCardClick(card)"
      >
        <view class="icon-container" :class="card.bgColor">
          <wd-icon :name="card.icon" :class="card.textColor" size="24px" />
        </view>
        <text class="card-name">{{ card.name }}</text>
        <view v-if="!card.active" class="card-disabled-mark">未开放</view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 99vh;
  padding: 24rpx;
  background-color: #f5f5f7;
}

.header {
  display: flex;
  flex-direction: column;
  padding: 32rpx;
  margin-bottom: 24rpx;
  background-color: #ffffff;
  border-radius: 32rpx;
}

.header-title {
  margin-bottom: 8rpx;
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
}

.header-subtitle {
  font-size: 24rpx;
  color: #8e8e93;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
  padding: 20rpx;
  background-color: #ffffff;
  border-radius: 32rpx;
}

.card-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
  cursor: pointer;
  border-radius: 24rpx;
  transition: all 0.3s;

  &:active {
    background-color: #f2f2f7;
    transform: scale(0.97);
  }
}

.card-active {
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-disabled {
  opacity: 0.6;
}

.card-disabled-mark {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  padding: 4rpx 8rpx;
  font-size: 20rpx;
  color: #ffffff;
  background-color: #999999;
  border-radius: 10rpx;
}

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 96rpx;
  height: 96rpx;
  margin-bottom: 16rpx;
  border-radius: 50%;
}

.card-name {
  font-size: 26rpx;
  color: #333333;
  text-align: center;
}
</style>
