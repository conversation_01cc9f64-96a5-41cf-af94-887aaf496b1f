<script setup lang="ts">
import { defineProps } from 'vue'

const props = defineProps({
  contentType: {
    type: String,
    default: 'schedule', // 'schedule' 或 'venue'
  },
  title: {
    type: String,
    default: '课程表',
  },
  subtitle: {
    type: String,
    default: '',
  },
  hasData: {
    type: Boolean,
    default: false,
  },
})
</script>

<template>
  <view class="schedule-content">
    <view class="content-header">
      <text class="content-title">{{ title }}</text>
      <text v-if="subtitle" class="content-subtitle">{{ subtitle }}</text>
    </view>

    <view class="content-container">
      <!-- 当没有数据时显示占位符 -->
      <view v-if="!hasData" class="content-placeholder">
        <wd-icon
          :name="contentType === 'schedule' ? 'time' : 'info-circle'"
          size="48px"
          class="text-gray-400"
        />
        <text class="placeholder-text">
          {{ contentType === 'schedule' ? '暂无课程表数据' : '暂无符合条件的场地' }}
        </text>
        <text v-if="contentType === 'schedule'" class="placeholder-desc">
          请选择查询条件后点击查询按钮
        </text>
      </view>
      <!-- 实际内容将通过插槽传入 -->
      <slot v-else></slot>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.schedule-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.content-header {
  display: flex;
  flex-direction: column;
  padding: 24rpx 32rpx;
  border-bottom: 1px solid #f2f2f7;
}

.content-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.content-subtitle {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #8e8e93;
}

.content-container {
  flex: 1;
  min-height: 800rpx;
  padding: 24rpx;
  overflow-y: auto;
}

.content-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 600rpx;
}

.placeholder-text {
  margin-top: 24rpx;
  font-size: 32rpx;
  color: #666666;
}

.placeholder-desc {
  margin-top: 12rpx;
  font-size: 24rpx;
  color: #8e8e93;
}
</style>
