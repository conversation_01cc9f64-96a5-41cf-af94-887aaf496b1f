<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  filterType: {
    type: String,
    default: 'class', // 'class', 'teacher', 'course', 'venue'
  },
  collegeList: {
    type: Array,
    default: () => [],
  },
  classList: {
    type: Array,
    default: () => [],
  },
  departmentList: {
    type: Array,
    default: () => [],
  },
  teacherList: {
    type: Array,
    default: () => [],
  },
  courseList: {
    type: Array,
    default: () => [],
  },
  venueTypes: {
    type: Array,
    default: () => [],
  },
  currentVenueType: {
    type: String,
    default: 'all',
  },
  queryForm: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['queryFormChange', 'venueTypeChange', 'query'])

// 处理表单变化
const handleFormChange = (field: string, value: string) => {
  emit('queryFormChange', { field, value })
}

// 处理场地类型变化
const handleVenueTypeChange = (type: string) => {
  emit('venueTypeChange', type)
}

// 查询
const handleQuery = () => {
  emit('query')
}
</script>

<template>
  <view class="filter-container">
    <!-- 班级课表筛选 -->
    <view v-if="filterType === 'class'">
      <view class="query-form">
        <!-- 二级学院选择 -->
        <view class="form-item">
          <view class="form-row">
            <text class="form-label">二级学院</text>
            <view class="form-content">
              <wd-picker
                :columns="collegeList"
                :value="queryForm.collegeId"
                @change="(value) => handleFormChange('collegeId', value)"
              >
                <wd-cell
                  title="选择二级学院"
                  :value="collegeList.find((item) => item.value === queryForm.collegeId)?.label"
                />
              </wd-picker>
            </view>
          </view>
        </view>

        <!-- 班级选择 -->
        <view class="form-item">
          <view class="form-row">
            <text class="form-label">班级</text>
            <view class="form-content">
              <wd-picker
                :columns="classList"
                :value="queryForm.classId"
                @change="(value) => handleFormChange('classId', value)"
              >
                <wd-cell
                  title="选择班级"
                  :value="classList.find((item) => item.value === queryForm.classId)?.label"
                />
              </wd-picker>
            </view>
          </view>
        </view>

        <!-- 查询按钮 -->
        <view class="query-button-container">
          <wd-button type="primary" block @click="handleQuery">查询</wd-button>
        </view>
      </view>
    </view>

    <!-- 教师课表筛选 -->
    <view v-if="filterType === 'teacher'">
      <view class="query-form">
        <!-- 部门选择 -->
        <view class="form-item">
          <view class="form-row">
            <text class="form-label">部门</text>
            <view class="form-content">
              <wd-picker
                :columns="departmentList"
                :value="queryForm.departmentId"
                @change="(value) => handleFormChange('departmentId', value)"
              >
                <wd-cell
                  title="选择部门"
                  :value="
                    departmentList.find((item) => item.value === queryForm.departmentId)?.label
                  "
                />
              </wd-picker>
            </view>
          </view>
        </view>

        <!-- 教师选择 -->
        <view class="form-item">
          <view class="form-row">
            <text class="form-label">教师</text>
            <view class="form-content">
              <wd-picker
                :columns="teacherList"
                :value="queryForm.teacherId"
                @change="(value) => handleFormChange('teacherId', value)"
              >
                <wd-cell
                  title="选择教师"
                  :value="teacherList.find((item) => item.value === queryForm.teacherId)?.label"
                />
              </wd-picker>
            </view>
          </view>
        </view>

        <!-- 查询按钮 -->
        <view class="query-button-container">
          <wd-button type="primary" block @click="handleQuery">查询</wd-button>
        </view>
      </view>
    </view>

    <!-- 课程课表筛选 -->
    <view v-if="filterType === 'course'">
      <view class="query-form">
        <!-- 课程选择 -->
        <view class="form-item">
          <view class="form-row">
            <text class="form-label">课程</text>
            <view class="form-content">
              <wd-picker
                :columns="courseList"
                :value="queryForm.courseId"
                @change="(value) => handleFormChange('courseId', value)"
              >
                <wd-cell
                  title="选择课程"
                  :value="courseList.find((item) => item.value === queryForm.courseId)?.label"
                />
              </wd-picker>
            </view>
          </view>
        </view>

        <!-- 查询按钮 -->
        <view class="query-button-container">
          <wd-button type="primary" block @click="handleQuery">查询</wd-button>
        </view>
      </view>
    </view>

    <!-- 场地筛选 -->
    <view v-if="filterType === 'venue'" class="filter-section">
      <scroll-view scroll-x class="filter-scroll" :show-scrollbar="false">
        <view class="filter-items">
          <view
            v-for="item in venueTypes"
            :key="item.value"
            :class="['filter-item', currentVenueType === item.value ? 'filter-active' : '']"
            @click="handleVenueTypeChange(item.value)"
          >
            {{ item.label }}
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.filter-container {
  width: 100%;
  margin-bottom: 16rpx;
}

.query-section {
  margin-bottom: 24rpx;
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.query-form {
  padding: 24rpx 32rpx;
}

.form-item {
  margin-bottom: 24rpx;
}

.form-row {
  display: flex;
  align-items: center;
}

.form-label {
  flex-shrink: 0;
  width: 140rpx;
  margin-right: 12rpx;
  font-size: 28rpx;
  color: #333333;
}

.form-content {
  flex: 1;
  overflow: hidden;
  border-radius: 12rpx;
}

.query-button-container {
  margin-top: 32rpx;
}

.filter-section {
  margin-bottom: 16rpx;
}

.filter-scroll {
  width: 100%;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.filter-items {
  display: flex;
  padding: 16rpx;
  white-space: nowrap;
}

.filter-item {
  padding: 12rpx 24rpx;
  margin-right: 16rpx;
  font-size: 28rpx;
  color: #666666;
  background-color: #f2f2f7;
  border-radius: 32rpx;
  transition: all 0.3s;
}

.filter-active {
  color: #ffffff;
  background-color: #1890ff;
}
</style>
