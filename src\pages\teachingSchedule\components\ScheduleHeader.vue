<script setup lang="ts">
import { ref, defineProps, defineEmits } from 'vue'
import SchoolYearPicker from '@/components/SchoolYearPicker/index.vue'

const props = defineProps({
  title: {
    type: String,
    default: '课程表',
  },
  yearValue: {
    type: String,
    default: '',
  },
  showDatePicker: {
    type: Boolean,
    default: false,
  },
  currentDate: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['yearChange', 'dateChange', 'search'])

// 搜索关键词
const searchKeyword = ref('')

// 处理学年变更
const handleYearChange = (data: { label: string; value: string }) => {
  emit('yearChange', data)
}

// 日期变更
const handleDateChange = (e: any) => {
  emit('dateChange', e.detail.value)
}

// 处理搜索
const handleSearch = () => {
  emit('search', searchKeyword.value)
}
</script>

<template>
  <view class="schedule-header">
    <!-- 学年学期选择器 -->
    <view v-if="!showDatePicker" class="mb-2 year-picker-container">
      <SchoolYearPicker
        :yearValue="yearValue"
        yearLabel="学期"
        :size="'large'"
        @yearChange="handleYearChange"
      />
    </view>

    <!-- 日期选择器和搜索框 -->
    <view v-if="showDatePicker" class="search-section">
      <view class="date-picker">
        <picker
          mode="date"
          :value="currentDate"
          start="2023-01-01"
          end="2025-12-31"
          @change="handleDateChange"
        >
          <view class="date-display">
            <wd-icon name="calendar" class="text-gray-500" size="20px" />
            <text class="date-text">{{ currentDate }}</text>
            <wd-icon name="chevron-down" class="text-gray-500" size="16px" />
          </view>
        </picker>
      </view>
      <view class="search-box">
        <wd-icon name="search" class="text-gray-500" size="20px" />
        <input
          v-model="searchKeyword"
          class="search-input"
          placeholder="搜索关键词"
          confirm-type="search"
          @confirm="handleSearch"
        />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.schedule-header {
  width: 100%;
  margin-bottom: 16rpx;
}

.year-picker-container {
  overflow: hidden;
  border-radius: 16rpx;
}

.search-section {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  padding: 20rpx;
  margin-bottom: 16rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.date-picker {
  width: 100%;
}

.date-display {
  display: flex;
  gap: 8rpx;
  align-items: center;
  padding: 12rpx 0;
}

.date-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.search-box {
  display: flex;
  align-items: center;
  padding: 12rpx 24rpx;
  background-color: #f2f2f7;
  border-radius: 32rpx;
}

.search-input {
  flex: 1;
  height: 64rpx;
  margin-left: 16rpx;
  font-size: 28rpx;
}
</style>
