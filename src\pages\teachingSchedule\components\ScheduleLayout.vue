<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import SchoolYearPicker from '@/components/SchoolYearPicker/index.vue'

const props = defineProps({
  title: {
    type: String,
    default: '课程表',
  },
  subtitle: {
    type: String,
    default: '',
  },
  hasData: {
    type: Boolean,
    default: false,
  },
  yearValue: {
    type: String,
    default: '',
  },
  showDatePicker: {
    type: Boolean,
    default: false,
  },
  currentDate: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['yearChange', 'dateChange'])

// 处理学年变更
const handleYearChange = (data: { label: string; value: string }) => {
  emit('yearChange', data)
}

// 日期变更
const handleDateChange = (e: any) => {
  emit('dateChange', e.detail.value)
}
</script>

<template>
  <view class="layout-container">
    <!-- 上部分：学年学期和表单筛选区域 -->
    <view class="filter-section">
      <!-- 学年学期选择器 -->
      <view class="mb-2 year-picker-container">
        <SchoolYearPicker
          :yearValue="yearValue"
          yearLabel="学期"
          :size="'large'"
          @yearChange="handleYearChange"
        />
      </view>

      <!-- 日期选择器（如果需要） -->
      <view v-if="showDatePicker" class="date-picker-container">
        <picker
          mode="date"
          :value="currentDate"
          start="2023-01-01"
          end="2025-12-31"
          @change="handleDateChange"
        >
          <view class="date-display">
            <wd-icon name="calendar" class="text-gray-500" size="20px" />
            <text class="date-text">{{ currentDate }}</text>
            <wd-icon name="chevron-down" class="text-gray-500" size="16px" />
          </view>
        </picker>
      </view>

      <!-- 通过插槽让调用者传入自定义的筛选组件 -->
      <view class="filter-container">
        <view class="query-section">
          <slot name="filter"></slot>
        </view>
      </view>
    </view>

    <!-- 下部分：内容展示区域 -->
    <view class="content-section">
      <view class="content-header">
        <text class="content-title">{{ title }}</text>
        <text v-if="subtitle" class="content-subtitle">{{ subtitle }}</text>
      </view>

      <view class="content-container">
        <!-- 当没有数据时显示占位符 -->
        <view v-if="!hasData" class="content-placeholder">
          <wd-icon name="time" size="48px" class="text-gray-400" />
          <text class="placeholder-text">暂无数据</text>
          <text class="placeholder-desc">请选择查询条件后点击查询按钮</text>
        </view>
        <!-- 实际内容将通过插槽传入 -->
        <slot v-else name="content"></slot>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.layout-container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  min-height: 99vh;
  padding: 24rpx;
  background-color: #f5f5f7;
}

.filter-section {
  margin-bottom: 16rpx;
}

.year-picker-container {
  margin-bottom: 16rpx;
  overflow: hidden;
  border-radius: 16rpx;
}

.date-picker-container {
  padding: 16rpx;
  margin-bottom: 16rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.date-display {
  display: flex;
  gap: 8rpx;
  align-items: center;
  padding: 12rpx 0;
}

.date-text {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}
/* 添加从ScheduleFilter.vue抽取的背景样式 */
.filter-container {
  width: 100%;
  margin-bottom: 16rpx;
}
/* 查询部分的背景样式 */
.query-section {
  margin-bottom: 24rpx;
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}
/* 过滤器滚动区域的背景样式 */
.filter-scroll {
  width: 100%;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.content-section {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  background-color: #ffffff;
  border-radius: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.content-header {
  display: flex;
  flex-direction: column;
  padding: 24rpx 32rpx;
  border-bottom: 1px solid #f2f2f7;
}

.content-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
}

.content-subtitle {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #8e8e93;
}

.content-container {
  flex: 1;
  min-height: 800rpx;
  padding: 24rpx;
  overflow-y: auto;
}

.content-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 600rpx;
}

.placeholder-text {
  margin-top: 24rpx;
  font-size: 32rpx;
  color: #666666;
}

.placeholder-desc {
  margin-top: 12rpx;
  font-size: 24rpx;
  color: #8e8e93;
}
</style>
