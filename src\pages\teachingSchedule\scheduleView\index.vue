<route lang="json5">
{
  style: {
    navigationBarTitleText: '课程表查看',
  },
}
</route>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue'
import ScheduleFilter from '../components/ScheduleFilter.vue'
import ScheduleLayout from '../components/ScheduleLayout.vue'

// 查询条件
const queryForm = reactive({
  type: 'class', // 查询类型：class-班级，teacher-教师，course-课程
  semester: '', // 学期
  collegeId: '', // 二级学院ID
  classId: '', // 班级ID
  departmentId: '', // 部门ID
  teacherId: '', // 教师ID
  courseId: '', // 课程ID
})

// 页面标题
const pageTitle = computed(() => {
  if (queryForm.type === 'class') return '班级课程表'
  if (queryForm.type === 'teacher') return '教师课程表'
  if (queryForm.type === 'course') return '课程课表'
  return '课程表查看'
})

// 课程表副标题
const scheduleSubtitle = ref('2023-2024学年第一学期 第1周')

// 二级学院列表
const collegeList = ref([
  { label: '计算机学院', value: '1' },
  { label: '电子工程学院', value: '2' },
  { label: '机械工程学院', value: '3' },
])

// 按学院分类的班级列表
const classByCollegeMap = reactive({
  '1': [
    { label: '计算机科学与技术1班', value: '101' },
    { label: '软件工程2班', value: '102' },
    { label: '网络工程3班', value: '103' },
  ],
  '2': [
    { label: '电子信息工程1班', value: '201' },
    { label: '通信工程2班', value: '202' },
  ],
  '3': [
    { label: '机械设计制造1班', value: '301' },
    { label: '自动化2班', value: '302' },
  ],
})

// 当前学院下的班级列表
const currentClassList = computed(() => {
  if (!queryForm.collegeId) return []
  return classByCollegeMap[queryForm.collegeId] || []
})

// 部门列表
const departmentList = ref([
  { label: '教务处', value: '1' },
  { label: '计算机系', value: '2' },
  { label: '电子系', value: '3' },
  { label: '机械系', value: '4' },
])

// 按部门分类的教师列表
const teacherByDepartmentMap = reactive({
  '1': [
    { label: '张主任', value: '101' },
    { label: '李主任', value: '102' },
  ],
  '2': [
    { label: '王教授', value: '201' },
    { label: '赵副教授', value: '202' },
    { label: '钱讲师', value: '203' },
  ],
  '3': [
    { label: '孙教授', value: '301' },
    { label: '周副教授', value: '302' },
  ],
  '4': [
    { label: '吴教授', value: '401' },
    { label: '郑讲师', value: '402' },
  ],
})

// 当前部门下的教师列表
const currentTeacherList = computed(() => {
  if (!queryForm.departmentId) return []
  return teacherByDepartmentMap[queryForm.departmentId] || []
})

// 课程列表
const courseList = ref([
  { label: '高等数学', value: '1001' },
  { label: '大学英语', value: '1002' },
  { label: '计算机基础', value: '1003' },
  { label: '数据结构', value: '1004' },
  { label: '操作系统', value: '1005' },
  { label: '数据库原理', value: '1006' },
  { label: '软件工程', value: '1007' },
  { label: '计算机网络', value: '1008' },
])

// 是否有数据
const hasData = ref(false)

// 监听学院变化，更新班级选择
watch(
  () => queryForm.collegeId,
  (newCollegeId) => {
    if (newCollegeId && queryForm.type === 'class') {
      const classes = classByCollegeMap[newCollegeId] || []
      if (classes.length > 0) {
        queryForm.classId = classes[0].value
      } else {
        queryForm.classId = ''
      }
    }
  },
)

// 监听部门变化，更新教师选择
watch(
  () => queryForm.departmentId,
  (newDepartmentId) => {
    if (newDepartmentId && queryForm.type === 'teacher') {
      const teachers = teacherByDepartmentMap[newDepartmentId] || []
      if (teachers.length > 0) {
        queryForm.teacherId = teachers[0].value
      } else {
        queryForm.teacherId = ''
      }
    }
  },
)

// 切换查询类型
const changeQueryType = (type: string) => {
  queryForm.type = type
  // 重置相关字段
  if (type === 'class') {
    queryForm.departmentId = ''
    queryForm.teacherId = ''
    queryForm.courseId = ''
    // 默认选择第一个学院
    if (collegeList.value.length > 0) {
      queryForm.collegeId = collegeList.value[0].value
      // 班级会通过watch自动设置
    }
  } else if (type === 'teacher') {
    queryForm.collegeId = ''
    queryForm.classId = ''
    queryForm.courseId = ''
    // 默认选择第一个部门
    if (departmentList.value.length > 0) {
      queryForm.departmentId = departmentList.value[0].value
      // 教师会通过watch自动设置
    }
  } else if (type === 'course') {
    queryForm.collegeId = ''
    queryForm.classId = ''
    queryForm.departmentId = ''
    queryForm.teacherId = ''
    // 默认选择第一个课程
    if (courseList.value.length > 0) {
      queryForm.courseId = courseList.value[0].value
    }
  }

  // 更新导航栏标题
  uni.setNavigationBarTitle({
    title: pageTitle.value,
  })
}

// 处理学年变更
const handleYearChange = (data: { label: string; value: string }) => {
  console.log('学年变更:', data)
  queryForm.semester = data.value
  // 这里可以添加其他逻辑，比如重新获取周次或班级列表等
}

// 处理表单变化
const handleFormChange = (data: { field: string; value: string }) => {
  const { field, value } = data
  queryForm[field] = value
}

// 查询课程表
const querySchedule = () => {
  // 这里添加查询逻辑
  console.log('查询条件：', queryForm)
  uni.showToast({
    title: '查询功能暂未实现',
    icon: 'none',
  })
}

// 初始化页面基本设置
const initBasicSettings = () => {
  // 根据当前查询类型设置默认值
  if (queryForm.type === 'class') {
    // 设置默认学院
    if (collegeList.value.length > 0) {
      queryForm.collegeId = collegeList.value[0].value
      // 班级会通过watch自动设置
    }
  } else if (queryForm.type === 'teacher') {
    // 设置默认部门
    if (departmentList.value.length > 0) {
      queryForm.departmentId = departmentList.value[0].value
      // 教师会通过watch自动设置
    }
  } else if (queryForm.type === 'course') {
    // 设置默认课程
    if (courseList.value.length > 0) {
      queryForm.courseId = courseList.value[0].value
    }
  }

  // 设置导航栏标题
  uni.setNavigationBarTitle({
    title: pageTitle.value,
  })
}

// 首次加载时初始化
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  // @ts-expect-error uniapp的页面对象类型定义不完整
  const query = currentPage?.options || {}
  console.log('页面参数：', query)

  // 如果有传入type参数，则设置查询类型
  if (
    query.type &&
    (query.type === 'class' || query.type === 'teacher' || query.type === 'course')
  ) {
    changeQueryType(query.type)
  }

  // 初始化基本设置
  initBasicSettings()
})
</script>

<template>
  <ScheduleLayout
    :title="pageTitle"
    :subtitle="scheduleSubtitle"
    :hasData="hasData"
    :yearValue="queryForm.semester"
    :showDatePicker="false"
    @yearChange="handleYearChange"
  >
    <!-- 上部分：筛选表单 -->
    <template #filter>
      <!-- 查询条件区域 -->
      <view class="query-tabs">
        <view
          :class="['query-tab', queryForm.type === 'class' ? 'query-tab-active' : '']"
          @click="changeQueryType('class')"
        >
          班级课表
        </view>
        <view
          :class="['query-tab', queryForm.type === 'teacher' ? 'query-tab-active' : '']"
          @click="changeQueryType('teacher')"
        >
          教师课表
        </view>
        <view
          :class="['query-tab', queryForm.type === 'course' ? 'query-tab-active' : '']"
          @click="changeQueryType('course')"
        >
          课程课表
        </view>
      </view>

      <!-- 筛选条件 -->
      <ScheduleFilter
        :filterType="queryForm.type"
        :collegeList="collegeList"
        :classList="currentClassList"
        :departmentList="departmentList"
        :teacherList="currentTeacherList"
        :courseList="courseList"
        :queryForm="queryForm"
        @queryFormChange="handleFormChange"
        @query="querySchedule"
      />
    </template>

    <!-- 下部分：课程表内容 -->
    <template #content>
      <!-- 这里将来放置课程表具体内容 -->
      <view>课程表内容将在这里显示</view>
    </template>
  </ScheduleLayout>
</template>

<style lang="scss" scoped>
.query-tabs {
  display: flex;
  padding: 16rpx;
  margin-bottom: 16rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.query-tab {
  padding: 12rpx 24rpx;
  margin-right: 16rpx;
  font-size: 28rpx;
  color: #666666;
  background-color: #f2f2f7;
  border-radius: 28rpx;
  transition: all 0.3s;
}

.query-tab-active {
  color: #ffffff;
  background-color: #3a8eff;
}
</style>
