<route lang="json5">
{
  style: {
    navigationBarTitleText: '教师总课表',
  },
}
</route>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import ScheduleLayout from '../components/ScheduleLayout.vue'

// 当前学期
const yearValue = ref('')

// 当前日期
const currentDate = ref(formatDate(new Date()))

// 格式化日期为YYYY-MM-DD
function formatDate(date: Date): string {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 部门列表
const departmentList = reactive([
  { label: '全部部门', value: 'all' },
  { label: '计算机学院', value: 'computer' },
  { label: '数学学院', value: 'math' },
  { label: '物理学院', value: 'physics' },
  { label: '化学学院', value: 'chemistry' },
  { label: '生物学院', value: 'biology' },
  { label: '外国语学院', value: 'foreign' },
  { label: '经济管理学院', value: 'economics' },
])

// 当前选中的部门
const currentDepartment = ref('all')

// 教师列表
const teacherList = ref<Array<{ label: string; value: string }>>([
  { label: '全部教师', value: 'all' },
])

// 当前选中的教师
const currentTeacher = ref('all')

// 教师课表列表
const teacherTotalScheduleList = ref<
  Array<{
    id: string
    teacherName: string
    teacherId: string
    department: string
    title: string
    courseCount: number
    weeklyHours: number
    studentCount: number
    status: 'normal' | 'overload' | 'underload'
  }>
>([])

// 搜索关键词
const searchKeyword = ref('')

// 获取教师总课表数据
const fetchTeacherTotalSchedule = () => {
  // 模拟数据，实际项目中应该从API获取
  teacherTotalScheduleList.value = [
    {
      id: '1',
      teacherName: '张教授',
      teacherId: 'T2021001',
      department: 'computer',
      title: '教授',
      courseCount: 3,
      weeklyHours: 12,
      studentCount: 120,
      status: 'normal',
    },
    {
      id: '2',
      teacherName: '李副教授',
      teacherId: 'T2021002',
      department: 'computer',
      title: '副教授',
      courseCount: 4,
      weeklyHours: 16,
      studentCount: 160,
      status: 'overload',
    },
    {
      id: '3',
      teacherName: '王讲师',
      teacherId: 'T2021003',
      department: 'math',
      title: '讲师',
      courseCount: 2,
      weeklyHours: 8,
      studentCount: 80,
      status: 'underload',
    },
    {
      id: '4',
      teacherName: '刘教授',
      teacherId: 'T2021004',
      department: 'physics',
      title: '教授',
      courseCount: 3,
      weeklyHours: 12,
      studentCount: 90,
      status: 'normal',
    },
    {
      id: '5',
      teacherName: '赵副教授',
      teacherId: 'T2021005',
      department: 'chemistry',
      title: '副教授',
      courseCount: 3,
      weeklyHours: 12,
      studentCount: 95,
      status: 'normal',
    },
    {
      id: '6',
      teacherName: '钱讲师',
      teacherId: 'T2021006',
      department: 'biology',
      title: '讲师',
      courseCount: 5,
      weeklyHours: 20,
      studentCount: 200,
      status: 'overload',
    },
    {
      id: '7',
      teacherName: '孙教授',
      teacherId: 'T2021007',
      department: 'foreign',
      title: '教授',
      courseCount: 2,
      weeklyHours: 8,
      studentCount: 60,
      status: 'underload',
    },
    {
      id: '8',
      teacherName: '周副教授',
      teacherId: 'T2021008',
      department: 'economics',
      title: '副教授',
      courseCount: 3,
      weeklyHours: 12,
      studentCount: 110,
      status: 'normal',
    },
  ]

  // 根据当前选中的部门，更新教师列表
  updateTeacherList()
}

// 更新教师列表
const updateTeacherList = () => {
  // 先添加"全部教师"选项
  const teachers = [{ label: '全部教师', value: 'all' }]

  // 根据当前选中的部门筛选教师
  const filteredTeachers = teacherTotalScheduleList.value.filter(
    (item) => currentDepartment.value === 'all' || item.department === currentDepartment.value,
  )

  // 将筛选后的教师添加到列表中
  filteredTeachers.forEach((teacher) => {
    teachers.push({
      label: teacher.teacherName,
      value: teacher.teacherId,
    })
  })

  teacherList.value = teachers

  // 如果当前选中的教师不在新的教师列表中，重置为"全部教师"
  if (!teachers.some((item) => item.value === currentTeacher.value)) {
    currentTeacher.value = 'all'
  }
}

// 筛选教师总课表列表
const filteredTeacherTotalScheduleList = computed(() => {
  let result = teacherTotalScheduleList.value

  // 按部门筛选
  if (currentDepartment.value !== 'all') {
    result = result.filter((item) => item.department === currentDepartment.value)
  }

  // 按教师筛选
  if (currentTeacher.value !== 'all') {
    result = result.filter((item) => item.teacherId === currentTeacher.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(
      (item) =>
        item.teacherName.toLowerCase().includes(keyword) ||
        item.teacherId.toLowerCase().includes(keyword),
    )
  }

  return result
})

// 处理学年变更
const handleYearChange = (data: { label: string; value: string }) => {
  yearValue.value = data.value
  // 重新获取数据
  fetchTeacherTotalSchedule()
}

// 处理日期变更
const handleDateChange = (date: string) => {
  currentDate.value = date
  // 重新获取数据
  fetchTeacherTotalSchedule()
}

// 处理部门变更
const handleDepartmentChange = (value: string) => {
  currentDepartment.value = value
  // 更新教师列表
  updateTeacherList()
  // 重新获取数据
  fetchTeacherTotalSchedule()
}

// 处理教师变更
const handleTeacherChange = (value: string) => {
  currentTeacher.value = value
  // 重新获取数据
  fetchTeacherTotalSchedule()
}

// 处理搜索
const handleSearch = () => {
  // 触发搜索，重新获取数据
  fetchTeacherTotalSchedule()
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'normal':
      return '正常'
    case 'overload':
      return '超负荷'
    case 'underload':
      return '负荷不足'
    default:
      return '未知'
  }
}

// 获取状态样式类
const getStatusClass = (status: string) => {
  switch (status) {
    case 'normal':
      return 'status-normal'
    case 'overload':
      return 'status-overload'
    case 'underload':
      return 'status-underload'
    default:
      return ''
  }
}

// 查看教师课表详情
const viewTeacherScheduleDetail = (teacher: any) => {
  uni.showToast({
    title: `查看${teacher.teacherName}的课表`,
    icon: 'none',
  })
  // 实际项目中应该跳转到教师课表详情页
}

onMounted(() => {
  fetchTeacherTotalSchedule()
})
</script>

<template>
  <ScheduleLayout
    title="教师总课表"
    :subtitle="`共${filteredTeacherTotalScheduleList.length}位教师`"
    :hasData="filteredTeacherTotalScheduleList.length > 0"
    :yearValue="yearValue"
    :showDatePicker="true"
    :currentDate="currentDate"
    @yearChange="handleYearChange"
    @dateChange="handleDateChange"
  >
    <!-- 筛选条件 -->
    <template #filter>
      <!-- 查询表单部分 -->
      <view class="query-section">
        <view class="query-form">
          <!-- 部门选择 -->
          <view class="form-item">
            <view class="form-row">
              <text class="form-label">部门</text>
              <view class="form-content">
                <wd-picker
                  :columns="departmentList"
                  :value="currentDepartment"
                  @change="
                    (value) => {
                      handleDepartmentChange(value)
                    }
                  "
                >
                  <wd-cell
                    title="选择部门"
                    :value="departmentList.find((item) => item.value === currentDepartment)?.label"
                  />
                </wd-picker>
              </view>
            </view>
          </view>

          <!-- 教师选择 -->
          <view class="form-item">
            <view class="form-row">
              <text class="form-label">教师</text>
              <view class="form-content">
                <wd-picker
                  :columns="teacherList"
                  :value="currentTeacher"
                  @change="
                    (value) => {
                      handleTeacherChange(value)
                    }
                  "
                >
                  <wd-cell
                    title="选择教师"
                    :value="teacherList.find((item) => item.value === currentTeacher)?.label"
                  />
                </wd-picker>
              </view>
            </view>
          </view>

          <!-- 搜索框 -->
          <view class="form-item">
            <view class="search-box">
              <view class="search-input-container">
                <wd-icon name="search" class="text-gray-500" size="16px" />
                <input
                  class="search-input"
                  v-model="searchKeyword"
                  placeholder="搜索教师姓名或工号"
                  confirm-type="search"
                  @confirm="handleSearch"
                />
              </view>
            </view>
          </view>

          <!-- 查询按钮 -->
          <view class="query-button-container">
            <wd-button type="primary" block @click="handleSearch">查询</wd-button>
          </view>
        </view>
      </view>
    </template>

    <!-- 教师总课表内容 -->
    <template #content>
      <view class="teacher-list">
        <view
          v-for="teacher in filteredTeacherTotalScheduleList"
          :key="teacher.id"
          class="teacher-item"
          @click="viewTeacherScheduleDetail(teacher)"
        >
          <view class="teacher-info">
            <view class="teacher-header">
              <text class="teacher-name">{{ teacher.teacherName }}</text>
              <view :class="['teacher-status', getStatusClass(teacher.status)]">
                {{ getStatusText(teacher.status) }}
              </view>
            </view>

            <view class="teacher-detail">
              <view class="detail-row">
                <view class="detail-item">
                  <wd-icon name="user" class="text-gray-500" size="16px" />
                  <text class="detail-text">{{ teacher.teacherId }}</text>
                </view>
                <view class="detail-item">
                  <wd-icon name="books" class="text-gray-500" size="16px" />
                  <text class="detail-text">
                    {{ departmentList.find((item) => item.value === teacher.department)?.label }}
                  </text>
                </view>
              </view>

              <view class="detail-row">
                <view class="detail-item">
                  <wd-icon name="a-controlplatform" class="text-gray-500" size="16px" />
                  <text class="detail-text">{{ teacher.title }}</text>
                </view>
                <view class="detail-item">
                  <wd-icon name="time" class="text-gray-500" size="16px" />
                  <text class="detail-text">
                    {{ teacher.courseCount }}门课 / {{ teacher.weeklyHours }}学时每周
                  </text>
                </view>
              </view>
            </view>

            <view class="teacher-stats">
              <view class="stats-item">
                <text class="stats-label">课程数</text>
                <text class="stats-value">{{ teacher.courseCount }}</text>
              </view>
              <view class="stats-item">
                <text class="stats-label">周学时</text>
                <text class="stats-value">{{ teacher.weeklyHours }}</text>
              </view>
              <view class="stats-item">
                <text class="stats-label">学生数</text>
                <text class="stats-value">{{ teacher.studentCount }}</text>
              </view>
            </view>
          </view>
          <view class="teacher-action">
            <wd-icon name="arrow-right" class="text-gray-400" size="20px" />
          </view>
        </view>
      </view>
    </template>
  </ScheduleLayout>
</template>

<style lang="scss" scoped>
.query-form {
  padding: 24rpx 32rpx;
}

.form-item {
  margin-bottom: 24rpx;
}

.form-row {
  display: flex;
  align-items: center;
}

.form-label {
  flex-shrink: 0;
  width: 140rpx;
  margin-right: 12rpx;
  font-size: 28rpx;
  color: #333333;
}

.form-content {
  flex: 1;
  overflow: hidden;
  border-radius: 12rpx;
}

.query-button-container {
  margin-top: 32rpx;
}

.search-box {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.search-input-container {
  display: flex;
  flex: 1;
  align-items: center;
  padding: 0 16rpx;
  border: 1px solid #e5e5e5;
  border-radius: 32rpx;
}

.search-input {
  flex: 1;
  height: 64rpx;
  padding: 0 12rpx;
  font-size: 28rpx;
}

.teacher-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.teacher-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.teacher-info {
  flex: 1;
}

.teacher-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.teacher-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.teacher-status {
  padding: 4rpx 16rpx;
  font-size: 24rpx;
  border-radius: 16rpx;
}

.status-normal {
  color: #52c41a;
  background-color: rgba(82, 196, 26, 0.1);
}

.status-overload {
  color: #f5222d;
  background-color: rgba(245, 34, 45, 0.1);
}

.status-underload {
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
}

.teacher-detail {
  margin-bottom: 20rpx;
}

.detail-row {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  margin-bottom: 12rpx;
}

.detail-item {
  display: flex;
  gap: 8rpx;
  align-items: center;
}

.detail-text {
  font-size: 26rpx;
  color: #666666;
}

.teacher-stats {
  display: flex;
  padding-top: 16rpx;
  margin-top: 16rpx;
  border-top: 1px solid #f0f0f0;
}

.stats-item {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
}

.stats-label {
  font-size: 24rpx;
  color: #999999;
}

.stats-value {
  margin-top: 4rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.teacher-action {
  margin-left: 16rpx;
}
</style>
