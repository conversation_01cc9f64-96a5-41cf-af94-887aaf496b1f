<route lang="json5">
{
  style: {
    navigationBarTitleText: '教学进程表',
  },
}
</route>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import ScheduleLayout from '../components/ScheduleLayout.vue'

// 当前学期
const yearValue = ref('')

// 当前日期
const currentDate = ref(formatDate(new Date()))

// 格式化日期为YYYY-MM-DD
function formatDate(date: Date): string {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 学院列表
const collegeList = reactive([
  { label: '全部学院', value: 'all' },
  { label: '计算机学院', value: 'computer' },
  { label: '数学学院', value: 'math' },
  { label: '物理学院', value: 'physics' },
  { label: '化学学院', value: 'chemistry' },
  { label: '生物学院', value: 'biology' },
])

// 当前选中的学院
const currentCollege = ref('all')

// 专业列表
const majorList = reactive([
  { label: '全部专业', value: 'all' },
  { label: '计算机科学与技术', value: 'cs' },
  { label: '软件工程', value: 'se' },
  { label: '人工智能', value: 'ai' },
  { label: '数据科学', value: 'ds' },
  { label: '网络工程', value: 'ne' },
])

// 当前选中的专业
const currentMajor = ref('all')

// 年级列表
const gradeList = reactive([
  { label: '全部年级', value: 'all' },
  { label: '2021级', value: '2021' },
  { label: '2022级', value: '2022' },
  { label: '2023级', value: '2023' },
  { label: '2024级', value: '2024' },
])

// 当前选中的年级
const currentGrade = ref('all')

// 教学进度类型
const progressTypes = reactive([
  { label: '全部类型', value: 'all' },
  { label: '理论课', value: 'theory' },
  { label: '实验课', value: 'experiment' },
  { label: '实践课', value: 'practice' },
])

// 当前选中的进度类型
const currentProgressType = ref('all')

// 课程进度列表
const progressList = ref<
  Array<{
    id: string
    courseName: string
    courseCode: string
    teacherName: string
    className: string
    totalWeeks: number
    currentWeek: number
    progressPercent: number
    type: string
    college: string
    major: string
    grade: string
    status: 'normal' | 'delayed' | 'ahead'
  }>
>([])

// 搜索关键词
const searchKeyword = ref('')

// 获取课程进度列表数据
const fetchProgressList = () => {
  // 模拟数据，实际项目中应该从API获取
  progressList.value = [
    {
      id: '1',
      courseName: '计算机网络',
      courseCode: 'CS301',
      teacherName: '张教授',
      className: '计算机2021-1班',
      totalWeeks: 16,
      currentWeek: 8,
      progressPercent: 50,
      type: 'theory',
      college: 'computer',
      major: 'cs',
      grade: '2021',
      status: 'normal',
    },
    {
      id: '2',
      courseName: '数据结构',
      courseCode: 'CS201',
      teacherName: '李教授',
      className: '软件2022-2班',
      totalWeeks: 16,
      currentWeek: 10,
      progressPercent: 62.5,
      type: 'theory',
      college: 'computer',
      major: 'se',
      grade: '2022',
      status: 'ahead',
    },
    {
      id: '3',
      courseName: '操作系统实验',
      courseCode: 'CS302L',
      teacherName: '王教授',
      className: '计算机2021-3班',
      totalWeeks: 8,
      currentWeek: 3,
      progressPercent: 37.5,
      type: 'experiment',
      college: 'computer',
      major: 'cs',
      grade: '2021',
      status: 'delayed',
    },
    {
      id: '4',
      courseName: '人工智能导论',
      courseCode: 'AI101',
      teacherName: '刘教授',
      className: '人工智能2023-1班',
      totalWeeks: 16,
      currentWeek: 7,
      progressPercent: 43.75,
      type: 'theory',
      college: 'computer',
      major: 'ai',
      grade: '2023',
      status: 'normal',
    },
    {
      id: '5',
      courseName: '软件工程实践',
      courseCode: 'SE301P',
      teacherName: '赵教授',
      className: '软件2022-1班',
      totalWeeks: 10,
      currentWeek: 6,
      progressPercent: 60,
      type: 'practice',
      college: 'computer',
      major: 'se',
      grade: '2022',
      status: 'normal',
    },
  ]
}

// 筛选课程进度列表
const filteredProgressList = computed(() => {
  let result = progressList.value

  // 按学院筛选
  if (currentCollege.value !== 'all') {
    result = result.filter((item) => item.college === currentCollege.value)
  }

  // 按专业筛选
  if (currentMajor.value !== 'all') {
    result = result.filter((item) => item.major === currentMajor.value)
  }

  // 按年级筛选
  if (currentGrade.value !== 'all') {
    result = result.filter((item) => item.grade === currentGrade.value)
  }

  // 按进度类型筛选
  if (currentProgressType.value !== 'all') {
    result = result.filter((item) => item.type === currentProgressType.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(
      (item) =>
        item.courseName.toLowerCase().includes(keyword) ||
        item.teacherName.toLowerCase().includes(keyword) ||
        item.className.toLowerCase().includes(keyword) ||
        item.courseCode.toLowerCase().includes(keyword),
    )
  }

  return result
})

// 处理学年变更
const handleYearChange = (data: { label: string; value: string }) => {
  yearValue.value = data.value
  // 重新获取数据
  fetchProgressList()
}

// 处理日期变更
const handleDateChange = (date: string) => {
  currentDate.value = date
  // 重新获取数据
  fetchProgressList()
}

// 处理学院变更
const handleCollegeChange = (e: any) => {
  currentCollege.value = collegeList[e.detail.value].value
}

// 处理专业变更
const handleMajorChange = (e: any) => {
  currentMajor.value = majorList[e.detail.value].value
}

// 处理年级变更
const handleGradeChange = (e: any) => {
  currentGrade.value = gradeList[e.detail.value].value
}

// 处理进度类型变更
const handleProgressTypeChange = (e: any) => {
  currentProgressType.value = progressTypes[e.detail.value].value
}

// 处理搜索
const handleSearch = () => {
  // 触发搜索，重新获取数据
  fetchProgressList()
}

// 获取进度状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'normal':
      return '正常'
    case 'delayed':
      return '滞后'
    case 'ahead':
      return '超前'
    default:
      return '未知'
  }
}

// 获取进度状态样式类
const getStatusClass = (status: string) => {
  switch (status) {
    case 'normal':
      return 'status-normal'
    case 'delayed':
      return 'status-delayed'
    case 'ahead':
      return 'status-ahead'
    default:
      return ''
  }
}

onMounted(() => {
  fetchProgressList()
})
</script>

<template>
  <ScheduleLayout
    title="教学进程表"
    :subtitle="`共${filteredProgressList.length}门课程`"
    :hasData="filteredProgressList.length > 0"
    :yearValue="yearValue"
    :showDatePicker="true"
    :currentDate="currentDate"
    @yearChange="handleYearChange"
    @dateChange="handleDateChange"
  >
    <!-- 筛选条件 -->
    <template #filter>
      <!-- 查询表单部分 -->
      <view class="query-section">
        <view class="query-form">
          <!-- 学院选择 -->
          <view class="form-item">
            <view class="form-row">
              <text class="form-label">学院</text>
              <view class="form-content">
                <wd-picker
                  :columns="collegeList"
                  :value="currentCollege"
                  @change="
                    (value) => {
                      currentCollege = value
                      fetchProgressList()
                    }
                  "
                >
                  <wd-cell
                    title="选择学院"
                    :value="collegeList.find((item) => item.value === currentCollege)?.label"
                  />
                </wd-picker>
              </view>
            </view>
          </view>

          <!-- 专业选择 -->
          <view class="form-item">
            <view class="form-row">
              <text class="form-label">专业</text>
              <view class="form-content">
                <wd-picker
                  :columns="majorList"
                  :value="currentMajor"
                  @change="
                    (value) => {
                      currentMajor = value
                      fetchProgressList()
                    }
                  "
                >
                  <wd-cell
                    title="选择专业"
                    :value="majorList.find((item) => item.value === currentMajor)?.label"
                  />
                </wd-picker>
              </view>
            </view>
          </view>

          <!-- 年级选择 -->
          <view class="form-item">
            <view class="form-row">
              <text class="form-label">年级</text>
              <view class="form-content">
                <wd-picker
                  :columns="gradeList"
                  :value="currentGrade"
                  @change="
                    (value) => {
                      currentGrade = value
                      fetchProgressList()
                    }
                  "
                >
                  <wd-cell
                    title="选择年级"
                    :value="gradeList.find((item) => item.value === currentGrade)?.label"
                  />
                </wd-picker>
              </view>
            </view>
          </view>

          <!-- 课程类型选择 -->
          <view class="form-item">
            <view class="form-row">
              <text class="form-label">课程类型</text>
              <view class="form-content">
                <wd-picker
                  :columns="progressTypes"
                  :value="currentProgressType"
                  @change="
                    (value) => {
                      currentProgressType = value
                      fetchProgressList()
                    }
                  "
                >
                  <wd-cell
                    title="选择课程类型"
                    :value="progressTypes.find((item) => item.value === currentProgressType)?.label"
                  />
                </wd-picker>
              </view>
            </view>
          </view>

          <!-- 搜索框 -->
          <view class="form-item">
            <view class="search-box">
              <view class="search-input-container">
                <wd-icon name="search" class="text-gray-500" size="16px" />
                <input
                  class="search-input"
                  v-model="searchKeyword"
                  placeholder="搜索课程名称、教师、班级或课程代码"
                  confirm-type="search"
                  @confirm="handleSearch"
                />
              </view>
            </view>
          </view>

          <!-- 查询按钮 -->
          <view class="query-button-container">
            <wd-button type="primary" block @click="handleSearch">查询</wd-button>
          </view>
        </view>
      </view>
    </template>

    <!-- 课程进度列表内容 -->
    <template #content>
      <view class="progress-list">
        <view v-for="progress in filteredProgressList" :key="progress.id" class="progress-item">
          <view class="progress-info">
            <view class="progress-header">
              <text class="progress-name">{{ progress.courseName }}</text>
              <view :class="['progress-status', getStatusClass(progress.status)]">
                {{ getStatusText(progress.status) }}
              </view>
            </view>

            <view class="progress-detail">
              <view class="detail-row">
                <view class="detail-item">
                  <wd-icon name="books" class="text-gray-500" size="16px" />
                  <text class="detail-text">{{ progress.courseCode }}</text>
                </view>
                <view class="detail-item">
                  <wd-icon name="usergroup" class="text-gray-500" size="16px" />
                  <text class="detail-text">{{ progress.className }}</text>
                </view>
              </view>

              <view class="detail-row">
                <view class="detail-item">
                  <wd-icon name="user" class="text-gray-500" size="16px" />
                  <text class="detail-text">{{ progress.teacherName }}</text>
                </view>
                <view class="detail-item">
                  <wd-icon name="time" class="text-gray-500" size="16px" />
                  <text class="detail-text">
                    第{{ progress.currentWeek }}/{{ progress.totalWeeks }}周
                  </text>
                </view>
              </view>
            </view>

            <view class="progress-bar-container">
              <view class="progress-bar-label">
                <text>教学进度</text>
                <text>{{ progress.progressPercent }}%</text>
              </view>
              <view class="progress-bar-bg">
                <view
                  class="progress-bar-fill"
                  :class="[
                    progress.status === 'delayed'
                      ? 'fill-delayed'
                      : progress.status === 'ahead'
                        ? 'fill-ahead'
                        : 'fill-normal',
                  ]"
                  :style="{ width: `${progress.progressPercent}%` }"
                ></view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </template>
  </ScheduleLayout>
</template>

<style lang="scss" scoped>
.query-form {
  padding: 24rpx 32rpx;
}

.form-item {
  margin-bottom: 24rpx;
}

.form-row {
  display: flex;
  align-items: center;
}

.form-label {
  flex-shrink: 0;
  width: 140rpx;
  margin-right: 12rpx;
  font-size: 28rpx;
  color: #333333;
}

.form-content {
  flex: 1;
  overflow: hidden;
  border-radius: 12rpx;
}

.query-button-container {
  margin-top: 32rpx;
}

.search-box {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.search-input-container {
  display: flex;
  flex: 1;
  align-items: center;
  padding: 0 16rpx;
  border: 1px solid #e5e5e5;
  border-radius: 32rpx;
}

.search-input {
  flex: 1;
  height: 64rpx;
  padding: 0 12rpx;
  font-size: 28rpx;
}

.progress-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.progress-item {
  padding: 24rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.progress-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.progress-status {
  padding: 4rpx 16rpx;
  font-size: 24rpx;
  border-radius: 16rpx;
}

.status-normal {
  color: #52c41a;
  background-color: rgba(82, 196, 26, 0.1);
}

.status-delayed {
  color: #f5222d;
  background-color: rgba(245, 34, 45, 0.1);
}

.status-ahead {
  color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
}

.progress-detail {
  margin-bottom: 20rpx;
}

.detail-row {
  display: flex;
  flex-wrap: wrap;
  gap: 24rpx;
  margin-bottom: 12rpx;
}

.detail-item {
  display: flex;
  gap: 8rpx;
  align-items: center;
}

.detail-text {
  font-size: 26rpx;
  color: #666666;
}

.progress-bar-container {
  margin-top: 16rpx;
}

.progress-bar-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8rpx;
  font-size: 24rpx;
  color: #666666;
}

.progress-bar-bg {
  position: relative;
  width: 100%;
  height: 16rpx;
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}

.progress-bar-fill {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.fill-normal {
  background-color: #52c41a;
}

.fill-delayed {
  background-color: #f5222d;
}

.fill-ahead {
  background-color: #1890ff;
}
</style>
