<route lang="json5">
{
  style: {
    navigationBarTitleText: '场地使用表',
  },
}
</route>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import ScheduleLayout from '../components/ScheduleLayout.vue'

// 当前学期
const yearValue = ref('')

// 当前日期
const currentDate = ref(formatDate(new Date()))

// 格式化日期为YYYY-MM-DD
function formatDate(date: Date): string {
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${year}-${month}-${day}`
}

// 校区列表
const campusList = reactive([
  { label: '全部校区', value: 'all' },
  { label: '东校区', value: 'east' },
  { label: '西校区', value: 'west' },
  { label: '南校区', value: 'south' },
  { label: '北校区', value: 'north' },
])

// 当前选中的校区
const currentCampus = ref('all')

// 建筑楼列表
const buildingList = reactive([
  { label: '全部建筑', value: 'all' },
  { label: '教学楼A栋', value: 'teachingA' },
  { label: '教学楼B栋', value: 'teachingB' },
  { label: '实验楼', value: 'laboratory' },
  { label: '行政楼', value: 'admin' },
  { label: '体育馆', value: 'sports' },
  { label: '艺术楼', value: 'arts' },
])

// 当前选中的建筑楼
const currentBuilding = ref('all')

// 场地类型选项
const venueTypes = reactive([
  { label: '全部类型', value: 'all' },
  { label: '教室', value: 'classroom' },
  { label: '实验室', value: 'laboratory' },
  { label: '会议室', value: 'meeting' },
  { label: '体育场', value: 'sports' },
  { label: '音乐室', value: 'music' },
])

// 当前选中的场地类型
const currentVenueType = ref('all')

// 场地列表
const venueList = ref<
  Array<{
    id: string
    name: string
    type: string
    capacity: number
    location: string
    status: 'free' | 'occupied' | 'maintenance'
    campus: string
    building: string
  }>
>([])

// 搜索关键词
const searchKeyword = ref('')

// 获取场地列表数据
const fetchVenueList = () => {
  // 模拟数据，实际项目中应该从API获取
  venueList.value = [
    {
      id: '1',
      name: '教学楼A101',
      type: 'classroom',
      capacity: 60,
      location: '教学楼A栋1层',
      status: 'free',
      campus: 'east',
      building: 'teachingA',
    },
    {
      id: '2',
      name: '实验楼B203',
      type: 'laboratory',
      capacity: 40,
      location: '实验楼B栋2层',
      status: 'occupied',
      campus: 'west',
      building: 'laboratory',
    },
    {
      id: '3',
      name: '会议室C302',
      type: 'meeting',
      capacity: 20,
      location: '行政楼C栋3层',
      status: 'free',
      campus: 'north',
      building: 'admin',
    },
    {
      id: '4',
      name: '体育馆主场',
      type: 'sports',
      capacity: 200,
      location: '体育馆1层',
      status: 'maintenance',
      campus: 'south',
      building: 'sports',
    },
    {
      id: '5',
      name: '音乐厅',
      type: 'music',
      capacity: 100,
      location: '艺术楼2层',
      status: 'free',
      campus: 'east',
      building: 'arts',
    },
  ]
}

// 筛选场地列表
const filteredVenueList = computed(() => {
  let result = venueList.value

  // 按校区筛选
  if (currentCampus.value !== 'all') {
    result = result.filter((item) => item.campus === currentCampus.value)
  }

  // 按建筑楼筛选
  if (currentBuilding.value !== 'all') {
    result = result.filter((item) => item.building === currentBuilding.value)
  }

  // 按场地类型筛选
  if (currentVenueType.value !== 'all') {
    result = result.filter((item) => item.type === currentVenueType.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(
      (item) =>
        item.name.toLowerCase().includes(keyword) || item.location.toLowerCase().includes(keyword),
    )
  }

  return result
})

// 处理学年变更
const handleYearChange = (data: { label: string; value: string }) => {
  yearValue.value = data.value
  // 重新获取数据
  fetchVenueList()
}

// 处理校区变更
const handleCampusChange = (e: any) => {
  currentCampus.value = campusList[e.detail.value].value
}

// 处理建筑楼变更
const handleBuildingChange = (e: any) => {
  currentBuilding.value = buildingList[e.detail.value].value
}

// 处理场地类型变更
const handleVenueTypeChange = (e: any) => {
  currentVenueType.value = venueTypes[e.detail.value].value
}

// 处理搜索
const handleSearch = () => {
  // 触发搜索，重新获取数据
  fetchVenueList()
}

// 查看场地详情
const viewVenueDetail = (venue: any) => {
  uni.showToast({
    title: `查看${venue.name}的详情`,
    icon: 'none',
  })
  // 实际项目中应该跳转到场地详情页
}

onMounted(() => {
  fetchVenueList()
})
</script>

<template>
  <ScheduleLayout
    title="场地使用表"
    :subtitle="`共${filteredVenueList.length}个场地`"
    :hasData="filteredVenueList.length > 0"
    :yearValue="yearValue"
    :currentDate="currentDate"
    @yearChange="handleYearChange"
  >
    <!-- 筛选条件 -->
    <template #filter>
      <!-- 查询表单部分 -->
      <view class="query-section">
        <view class="query-form">
          <!-- 校区选择 -->
          <view class="form-item">
            <view class="form-row">
              <text class="form-label">校区</text>
              <view class="form-content">
                <wd-picker
                  :columns="campusList"
                  :value="currentCampus"
                  @change="
                    (value) => {
                      currentCampus = value
                      fetchVenueList()
                    }
                  "
                >
                  <wd-cell
                    title="选择校区"
                    :value="campusList.find((item) => item.value === currentCampus)?.label"
                  />
                </wd-picker>
              </view>
            </view>
          </view>

          <!-- 建筑楼选择 -->
          <view class="form-item">
            <view class="form-row">
              <text class="form-label">建筑楼</text>
              <view class="form-content">
                <wd-picker
                  :columns="buildingList"
                  :value="currentBuilding"
                  @change="
                    (value) => {
                      currentBuilding = value
                      fetchVenueList()
                    }
                  "
                >
                  <wd-cell
                    title="选择建筑楼"
                    :value="buildingList.find((item) => item.value === currentBuilding)?.label"
                  />
                </wd-picker>
              </view>
            </view>
          </view>

          <!-- 场地类型选择 -->
          <view class="form-item">
            <view class="form-row">
              <text class="form-label">场地类型</text>
              <view class="form-content">
                <wd-picker
                  :columns="venueTypes"
                  :value="currentVenueType"
                  @change="
                    (value) => {
                      currentVenueType = value
                      fetchVenueList()
                    }
                  "
                >
                  <wd-cell
                    title="选择场地类型"
                    :value="venueTypes.find((item) => item.value === currentVenueType)?.label"
                  />
                </wd-picker>
              </view>
            </view>
          </view>

          <!-- 搜索框 -->
          <view class="form-item">
            <view class="search-box">
              <view class="search-input-container">
                <wd-icon name="search" class="text-gray-500" size="16px" />
                <input
                  class="search-input"
                  v-model="searchKeyword"
                  placeholder="搜索场地名称或位置"
                  confirm-type="search"
                  @confirm="handleSearch"
                />
              </view>
            </view>
          </view>

          <!-- 查询按钮 -->
          <view class="query-button-container">
            <wd-button type="primary" block @click="handleSearch">查询</wd-button>
          </view>
        </view>
      </view>
    </template>

    <!-- 场地列表内容 -->
    <template #content>
      <view class="venue-list">
        <view
          v-for="venue in filteredVenueList"
          :key="venue.id"
          class="venue-item"
          @click="viewVenueDetail(venue)"
        >
          <view class="venue-info">
            <view class="venue-header">
              <text class="venue-name">{{ venue.name }}</text>
              <view
                :class="[
                  'venue-status',
                  venue.status === 'free'
                    ? 'status-free'
                    : venue.status === 'occupied'
                      ? 'status-occupied'
                      : 'status-maintenance',
                ]"
              >
                {{
                  venue.status === 'free'
                    ? '空闲'
                    : venue.status === 'occupied'
                      ? '使用中'
                      : '维护中'
                }}
              </view>
            </view>
            <view class="venue-detail">
              <view class="detail-item">
                <wd-icon name="location" class="text-gray-500" size="16px" />
                <text class="detail-text">{{ venue.location }}</text>
              </view>
              <view class="detail-item">
                <wd-icon name="usergroup" class="text-gray-500" size="16px" />
                <text class="detail-text">容量: {{ venue.capacity }}人</text>
              </view>
            </view>
          </view>
          <view class="venue-action">
            <wd-icon name="arrow-right" class="text-gray-400" size="20px" />
          </view>
        </view>
      </view>
    </template>
  </ScheduleLayout>
</template>

<style lang="scss" scoped>
.query-form {
  padding: 24rpx 32rpx;
}

.form-item {
  margin-bottom: 24rpx;
}

.form-row {
  display: flex;
  align-items: center;
}

.form-label {
  flex-shrink: 0;
  width: 140rpx;
  margin-right: 12rpx;
  font-size: 28rpx;
  color: #333333;
}

.form-content {
  flex: 1;
  overflow: hidden;
  border-radius: 12rpx;
}

.query-button-container {
  margin-top: 32rpx;
}

.search-box {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.search-input-container {
  display: flex;
  flex: 1;
  align-items: center;
  padding: 0 16rpx;
  border: 1px solid #e5e5e5;
  border-radius: 32rpx;
}

.search-input {
  flex: 1;
  height: 64rpx;
  padding: 0 12rpx;
  font-size: 28rpx;
}

.venue-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.venue-item {
  display: flex;
  align-items: center;
  padding: 24rpx;
  margin-bottom: 16rpx;
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.venue-info {
  flex: 1;
}

.venue-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.venue-name {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.venue-status {
  padding: 4rpx 16rpx;
  font-size: 24rpx;
  border-radius: 16rpx;
}

.status-free {
  color: #52c41a;
  background-color: rgba(82, 196, 26, 0.1);
}

.status-occupied {
  color: #f5222d;
  background-color: rgba(245, 34, 45, 0.1);
}

.status-maintenance {
  color: #faad14;
  background-color: rgba(250, 173, 20, 0.1);
}

.venue-detail {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.detail-item {
  display: flex;
  gap: 8rpx;
  align-items: center;
}

.detail-text {
  font-size: 26rpx;
  color: #666666;
}

.venue-action {
  margin-left: 16rpx;
}
</style>
