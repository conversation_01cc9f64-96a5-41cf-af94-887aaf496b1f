<route lang="json5">
{
  style: {
    navigationBarTitleText: '审批详情',
  },
}
</route>

<script lang="ts" setup>
import { ref, onMounted, watch, computed } from 'vue'
import { getWorkflowDetail, terminationTask, revokeTask } from '@/service/workflow'
import type {
  WorkflowDetailResponse,
  WorkflowProcessItem,
  TerminationTaskRequest,
  RevokeTaskRequest,
} from '@/types/workflow'
import type { FlowStep } from '@/components/workflow/FlowStepItem.vue'
import { useMessage } from 'wot-design-uni'
import { useUserStore } from '@/store/user'
import { debounce } from '@/utils/index'

// 导入组件
import FormInfo from '@/components/workflow/FormInfo.vue'
import AccessReason from '@/components/workflow/AccessReason.vue'
import AttachmentList from '@/components/workflow/AttachmentList.vue'
import ApprovalProcess from '@/components/workflow/ApprovalProcess.vue'

// 获取用户信息
const userStore = useUserStore()
const currentUserId = computed(() => String(userStore.userInfo.username))

// 页面状态
const id = ref<string | number>('')
const loading = ref(true)
const workflowData = ref<WorkflowDetailResponse | null>(null)
const flowSteps = ref<FlowStep[][]>([])

/**
 * 强制驳回相关逻辑
 */
const forcedRevoke = (() => {
  // 强制驳回模态框实例
  const messageBox = useMessage('wd-message-box-forced-revoke')
  // 强制驳回备注
  const commentRef = ref('')
  // 确认按钮加载状态
  const isSubmitting = ref(false)

  /**
   * 判断当前用户是否已经审核过流程
   */
  const hasUserApproved = computed(() => {
    if (!workflowData.value?.process) return false
    console.log('检查用户是否已审核过流程')

    const branch = workflowData.value.process[0]

    // 查找当前用户审核过的节点
    const userNode = branch.find(
      (node) =>
        (node.status === 1 || node.status === 2) &&
        String(node.user_id).includes(currentUserId.value) &&
        node.level !== '-1',
    )

    if (!userNode) return false

    // 检查该节点后是否有其他已审核节点
    return !branch.some(
      (node) =>
        node.level !== '-1' &&
        Number(node.level) > Number(userNode.level) &&
        (node.status === 1 || node.status === 2),
    )
  })

  /**
   * 判断是否显示强制驳回按钮
   */
  const shouldShow = computed(() => {
    console.log('检查是否显示强制驳回按钮')

    const form = workflowData.value?.form
    if (!form) return false

    // 流程状态：0-进行中，1-已通过，2-已拒绝，3-已撤销，4-已驳回
    const isInProgress = [0, 1, 2].includes(form.status)

    return form.allowForcedRejection && hasUserApproved.value && isInProgress
  })

  /**
   * 打开强制驳回确认框
   */
  const showConfirm = () => {
    // 重置状态
    commentRef.value = ''
    isSubmitting.value = false

    // 打开确认框
    messageBox
      .confirm({
        title: '强制驳回',
        confirmButtonText: '确认驳回',
        cancelButtonText: '取消',
      })
      .then(handleConfirm)
      .catch(() => {
        console.log('用户取消强制驳回操作')
      })
  }

  /**
   * 处理确认驳回操作
   */
  const handleConfirm = debounce(async () => {
    // 验证输入
    if (!commentRef.value.trim()) {
      uni.showToast({
        title: '请输入强制驳回原因',
        icon: 'none',
      })
      return
    }

    // 防止重复提交
    if (isSubmitting.value) return
    isSubmitting.value = true

    try {
      // 显示加载提示
      uni.showLoading({ title: '提交中...', mask: true })

      // 关闭确认弹窗
      messageBox.close()

      // 提交驳回请求
      const params: RevokeTaskRequest = {
        taskId: Number(workflowData.value?.node.id || 0),
        message: commentRef.value,
        status: 3, // 驳回状态码
        revoke: true, // 标记为驳回操作
      }

      const res = await revokeTask(params)

      // 显示成功消息
      uni.hideLoading()
      uni.showToast({
        title: res.msg || '强制驳回成功',
        icon: 'success',
        duration: 2000,
      })

      // 刷新页面数据
      setTimeout(fetchWorkflowDetail, 1500)
    } catch (error) {
      uni.hideLoading()
      uni.showToast({
        title: error.msg || '操作失败',
        icon: 'none',
        duration: 2000,
      })
    } finally {
      isSubmitting.value = false
    }
  }, 500)

  return {
    // 返回对象的属性，而不是ref对象本身
    get comment() {
      return commentRef.value
    },
    set comment(val: string) {
      commentRef.value = val
    },
    get isSubmitting() {
      return isSubmitting.value
    },
    get shouldShow() {
      return shouldShow.value
    },
    showConfirm,
  }
})()

/**
 * 获取工作流详情数据
 */
const fetchWorkflowDetail = async () => {
  if (!id.value) return

  try {
    loading.value = true
    const res = await getWorkflowDetail(id.value)
    workflowData.value = res
    updateFlowSteps()
  } catch (error) {
    console.error('获取工作流详情失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

/**
 * 更新审批流程数据，处理多级流程分支
 */
const updateFlowSteps = () => {
  if (!workflowData.value) return

  // 检查整个工作流是否已驳回
  const isWorkflowRevoked = workflowData.value.form.status === 4

  // 将二维数组的每个分支转换为对应的流程步骤
  flowSteps.value = workflowData.value.process.map((branch) => {
    return branch.map((item, index) => {
      // 确定流程状态
      let status: 'success' | 'rejected' | 'active' | 'waiting' | 'revoked' | 'returned' = 'waiting'
      let isRevoked = false

      // 如果整个工作流已被驳回，标记所有节点为驳回状态
      if (isWorkflowRevoked) {
        status = 'revoked'
        isRevoked = true
      } else {
        // 处理节点状态：1-通过，2-拒绝，3-驳回，0-等待，4-驳回
        const itemStatus = Number(item.status)
        if (itemStatus === 1) {
          status = 'success' // 通过状态
        } else if (itemStatus === 2) {
          status = 'rejected' // 拒绝状态
        } else if (itemStatus === 3) {
          status = 'returned' // 驳回状态
        } else if (itemStatus === 4) {
          status = 'revoked' // 驳回状态
          isRevoked = true
        } else if (
          index === 0 ||
          (index > 0 && [1, 2, 3].includes(Number(branch[index - 1].status)))
        ) {
          status = 'active' // 激活状态（当前节点或前一个节点已完成）
        }
      }

      // 获取审批意见和评论信息
      const { comment, commentInfo, isRejected } = extractCommentInfo(item, isRevoked)

      return {
        id: item.id,
        name: item.name,
        handler: item.user_name,
        time: item.time || '等待处理',
        status,
        comment,
        commentInfo,
        isRejected,
        isRevoked,
      }
    })
  })
}

/**
 * 从流程项中提取评论信息
 */
const extractCommentInfo = (item: WorkflowProcessItem, isRevoked: boolean) => {
  let comment = ''
  let commentInfo = null
  let isRejected = false

  // 检查 info 数组中是否有审批意见
  if (item.info && Array.isArray(item.info) && item.info.length > 0) {
    // 获取第一条意见的内容
    const infoItem = item.info[0]
    if (infoItem.value && infoItem.value.trim() !== '') {
      comment = infoItem.value
      commentInfo = {
        user: infoItem.name,
        time: infoItem.time,
        content: infoItem.value,
        status: infoItem.status, // 保存意见状态：1-通过，2-拒绝，3-驳回
      }
      // 标记是否是拒绝意见
      isRejected = infoItem.status === 2 || infoItem.status === 3
    }
  }

  // 如果有驳回备注，使用作为意见
  if (item.status === 4 && item.remark) {
    comment = item.remark
    commentInfo = {
      user: item.user_name || '',
      time: item.time || '',
      content: item.remark,
      status: 4, // 驳回状态
    }
  }

  return {
    comment,
    commentInfo,
    isRejected,
  }
}

// 页面加载时获取数据
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.$page?.options || {}

  if (options.id) {
    id.value = options.id
    fetchWorkflowDetail()
  } else {
    uni.showToast({
      title: '参数错误',
      icon: 'none',
    })
  }
})
</script>

<template>
  <view class="workflow-detail bg-gray-100 min-h-screen px-4 py-4">
    <!-- 加载中状态 -->
    <wd-loading text="加载中..." :mask="true" v-if="loading" />

    <!-- 自定义强制驳回弹框 -->
    <wd-message-box selector="wd-message-box-forced-revoke">
      <view class="revoke-form p-2">
        <!-- 强制驳回原因 -->
        <view class="mb-4">
          <view class="text-sm text-red-500 mb-2 font-bold">强制驳回原因 (必填)</view>
          <textarea
            v-model="forcedRevoke.comment"
            placeholder="请输入强制驳回原因..."
            class="w-full custom-textarea"
            :maxlength="200"
            auto-height
          ></textarea>
          <view class="text-xs text-gray-400 text-right mt-1">
            {{ forcedRevoke.comment.length }}/200
          </view>
        </view>
      </view>
    </wd-message-box>

    <template v-if="workflowData && !loading">
      <!-- 表单基本信息 -->
      <FormInfo :workflow-data="workflowData" />

      <!-- 访问事由 -->
      <AccessReason v-if="workflowData.formData.dfsy" :content="workflowData.formData.dfsy" />

      <!-- 附件 -->
      <AttachmentList
        :photo-url="workflowData.formData.zp"
        :attachment-urls="workflowData.formData.fjlb"
        :file-urls="workflowData.formData.file"
      />

      <!-- 审批流程 -->
      <ApprovalProcess :workflow-data="workflowData" :flow-steps="flowSteps" />

      <!-- 固定在底部的强制驳回按钮 -->
      <view
        class="forced-revoke-action fixed left-0 right-0 bottom-0 bg-white shadow-md p-4 z-10 flex justify-between"
        v-if="forcedRevoke.shouldShow"
      >
        <wd-button type="error" class="flex-1" @click="forcedRevoke.showConfirm">
          强制驳回
        </wd-button>
      </view>
    </template>

    <!-- 无数据显示 -->
    <view class="flex flex-col items-center justify-center py-10" v-if="!loading && !workflowData">
      <wd-icon name="info-circle" size="50px" color="#c0c4cc"></wd-icon>
      <view class="text-gray-500 mt-4">暂无数据</view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.workflow-detail {
  min-height: 100vh;
  padding-bottom: calc(var(--window-bottom) + 80rpx);
}
/* 强制驳回按钮容器 */
.forced-revoke-action {
  padding-bottom: calc(env(safe-area-inset-bottom) + 32rpx);
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.05);
}
/* 自定义textarea样式 */
.custom-textarea {
  box-sizing: border-box;
  width: 100%;
  min-height: 150rpx;
  max-height: 300rpx;
  padding: 20rpx;
  margin-top: 8rpx;
  overflow-y: auto;
  font-size: 28rpx;
  line-height: 1.5;
  word-break: break-word;
  word-wrap: break-word;
  white-space: pre-wrap;
  resize: none;
  background-color: #ffffff;
  border: 2rpx solid #dcdfe6;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.03);
}
/* 自定义textarea焦点样式 */
.custom-textarea:focus {
  border-color: #ef4444;
  outline: none;
  box-shadow: 0 0 0 2rpx rgba(239, 68, 68, 0.2);
}
/* 自定义textarea占位符颜色 */
.custom-textarea::placeholder {
  color: #c0c4cc;
}
/* 弹框样式 */
.revoke-form {
  box-sizing: border-box;
  width: 100%;
  max-height: 60vh;
  overflow-y: auto;
  word-break: break-word;
}
</style>
