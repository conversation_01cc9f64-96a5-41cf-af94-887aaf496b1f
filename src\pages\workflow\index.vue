<route lang="json5">
{
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '工作流程',
  },
}
</route>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch, onBeforeUnmount } from 'vue'
import { getWorkflowList as apiGetWorkflowList, getWorkflowCategoryList } from '@/service/workflow'
import { getApplyList } from '@/service/apply'
import type {
  WorkflowItem,
  WorkflowResponse,
  WorkflowQuery,
  WorkflowCategory,
} from '@/types/workflow'
import type { ApplyListQuery, ApplyListResponse, ApplyListItem } from '@/types/apply'
import Pagination from '@/components/Pagination/index.vue'
import { useUserStore } from '@/store/user'
import { loadDictData, getDictLabel, getDictClass } from '@/utils/dict'
import type { DictData } from '@/types/system'

// 获取用户信息
const userStore = useUserStore()

// 页面标题
const pageTitle = ref('工作流程')

// 标签页配置 - 调整顺序，将"待我审批"放在第一位
const tabOptions = ['待我审批', '我发起的', '我已处理' /* , '抄送我的' */]
const activeTabIndex = ref(0)

// 搜索关键词
const searchKeyword = ref('')

// 搜索防抖定时器
const searchTimer = ref<number | undefined>(undefined)

// 当前选中的过滤项
const activeFilter = ref<string>('全部')

// 过滤标签列表
const filterTags = ref<string[]>(['全部', '请假', '报销', '采购', '会议', '用车'])

// 工作流分类列表
const categoryList = ref<WorkflowCategory[]>([])
// 添加一个全部选项
const allCategoryOption = { name: '全部流程', code: '' }
// 当前选中的工作流分类索引
const selectedCategoryIndex = ref(0)
// 当前选中的工作流分类
const selectedCategory = ref<WorkflowCategory | typeof allCategoryOption>(allCategoryOption)

// 快捷操作列表
const quickActions = [
  {
    id: 1,
    name: '发起流程',
    icon: 'add',
    color: 'linear-gradient(135deg, #0a84ff, #0055d4)',
  },
  {
    id: 2,
    name: '待我审批',
    icon: 'check',
    color: 'linear-gradient(135deg, #34c759, #248a3d)',
  },
  {
    id: 3,
    name: '草稿箱',
    icon: 'note',
    color: 'linear-gradient(135deg, #ff9500, #c93400)',
  },
]

// 工作流类型对应的图标和颜色
const workflowTypeMap = {
  leave: {
    icon: 'calendar',
    color: 'linear-gradient(135deg, #0a84ff, #0055d4)',
  },
  reimburse: {
    icon: 'money-circle',
    color: 'linear-gradient(135deg, #ff9500, #c93400)',
  },
  purchase: {
    icon: 'cart',
    color: 'linear-gradient(135deg, #5e5ce6, #3634a3)',
  },
}

// 工作流状态对应的样式
const workflowStatusMap = {
  pending: {
    text: '审批中',
    bgColor: '#fff2e5',
    textColor: '#ff9500',
  },
  waitingApproval: {
    text: '待审批',
    bgColor: '#fff2e5',
    textColor: '#ff9500',
  },
  approved: {
    text: '已通过',
    bgColor: '#e8f8ee',
    textColor: '#34c759',
  },
  rejected: {
    text: '已拒绝',
    bgColor: '#fee',
    textColor: '#ff3b30',
  },
}

// 分页相关状态
const page = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 工作流列表数据
const workflowList = ref<WorkflowItem[]>([])
const loading = ref(false)

// 字典数据
const dictData = ref<Record<string, DictData[]>>({
  DM_SPZT_NEW: [],
})

// 加载审批状态字典
const loadDict = async () => {
  try {
    const dicts = await loadDictData(['DM_SPZT_NEW'])
    dictData.value = dicts
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}

// 获取工作流分类列表
const fetchCategoryList = async () => {
  try {
    const res = await getWorkflowCategoryList()
    // 添加全部选项到列表开头
    categoryList.value = [allCategoryOption, ...res]
  } catch (error) {
    console.error('获取工作流分类列表失败:', error)
    uni.showToast({
      title: '获取工作流分类列表失败',
      icon: 'none',
    })
  }
}

// 处理分类选择变化
const handleCategoryChange = (e: any) => {
  // 只有在"我已处理"标签页时才处理分类选择变化
  if (activeTabIndex.value === 2) {
    const index = e.detail.value
    selectedCategoryIndex.value = index
    selectedCategory.value = categoryList.value[index]

    // 切换分类时重置页码
    page.value = 1
    getWorkflowList()
  }
}

// 获取工作流列表
const getWorkflowList = async () => {
  loading.value = true
  try {
    // 构建查询参数
    const params: any = {
      page: page.value,
      pageSize: pageSize.value,
      sortBy: 'applyTime',
      sortOrder: 'desc',
      todoType: 'new',
      listType: 'list',
      id: [],
      form_title: '', // 初始化为空字符串而不是数组
      create_user_name: '',
      applyTime: [],
      currentStepName: [],
      status: '',
      process_status: [],
      approval_opinion: [],
      checkTime: [],
    }

    // 如果是"我已处理"标签页且选择了特定的工作流分类（非"全部"），添加到查询条件
    if (activeTabIndex.value === 2 && selectedCategory.value.code) {
      params.processType = [selectedCategory.value.code]
    }

    // 根据当前标签页设置不同的查询参数
    if (activeTabIndex.value === 0) {
      // 待我审批 - status=0 表示未审核
      params.status = 0
    } else if (activeTabIndex.value === 1) {
      // 我发起的 - 使用getApplyList接口获取数据
      try {
        const applyParams: ApplyListQuery = {
          page: page.value,
          pageSize: pageSize.value,
          sortBy: 'create_time',
          sortOrder: 'desc',
        }

        // 如果有搜索关键词，添加到查询条件
        if (searchKeyword.value && typeof searchKeyword.value === 'string') {
          applyParams.form_title = searchKeyword.value
        }

        // 如果选择了特定的过滤标签（非"全部"），添加到查询条件
        if (activeFilter.value !== '全部') {
          applyParams.form_title = searchKeyword.value || activeFilter.value
        }

        const res = await getApplyList(applyParams)

        // 将ApplyListItem类型转换为WorkflowItem类型
        workflowList.value = res.items.map((item: ApplyListItem) => {
          // 将ApplyListItem转换为WorkflowItem
          return {
            approval_opinion: '', // 申请表单列表项没有审批意见
            checkTime: null, // 申请表单列表项没有审核时间
            status: 0, // 默认状态为0（未审核）
            currentStepName: item.name || '', // 使用节点名称作为当前步骤名称
            applyTime: item.create_time, // 使用创建时间作为申请时间
            create_user_name: item.user_name || '', // 使用审批人作为创建人名称
            form_title: item.form_title,
            id: item.id,
            title: item.title,
            code: null, // 申请表单列表项没有代码
            process_status: parseInt(item.process_status) || 0, // 将字符串转换为数字
            createTime: item.create_time,
            approvalStatusColor: '', // 没有审批状态颜色
            approvalStatus: '', // 没有审批状态
            processStatusColor: '', // 没有流程状态颜色
            processStatus: item.process_status, // 使用流程状态
          } as WorkflowItem
        })

        total.value = res.total

        // 已获取数据，结束loading
        loading.value = false
        return
      } catch (error) {
        console.error('获取我发起的申请列表失败:', error)
        uni.showToast({
          title: '获取我发起的申请列表失败',
          icon: 'none',
        })
        loading.value = false
        return
      }
    } else if (activeTabIndex.value === 2) {
      // 我已处理 - 根据选中的筛选条件查询对应状态的数据

      // 根据选择的审批结果筛选项设置status参数
      if (activeFilter.value === '通过') {
        params.status = 1
      } else if (activeFilter.value === '不通过') {
        params.status = 2
      } else if (activeFilter.value === '驳回') {
        params.status = 3
      } else if (activeFilter.value === '未审') {
        params.status = 0
      }

      // 处理表单标题过滤
      if (searchKeyword.value && typeof searchKeyword.value === 'string') {
        params.form_title = searchKeyword.value
      } else if (
        activeFilter.value !== '全部' &&
        !['通过', '不通过', '驳回', '未审'].includes(activeFilter.value)
      ) {
        params.form_title = activeFilter.value
      }

      // 如果选择了"全部"状态或者是表单类型筛选，则需要获取所有状态的数据
      if (
        activeFilter.value === '全部' ||
        !['全部', '通过', '不通过', '驳回', '未审'].includes(activeFilter.value)
      ) {
        try {
          // 获取所有状态的数据
          const allStatusParams = { ...params }
          delete allStatusParams.status // 删除status参数，获取所有状态

          const res = await apiGetWorkflowList(allStatusParams)
          workflowList.value = res.items
          total.value = res.total

          loading.value = false
          return
        } catch (error) {
          console.error('获取已处理工作流列表失败:', error)
          uni.showToast({
            title: '获取已处理工作流列表失败',
            icon: 'none',
          })
          loading.value = false
          return
        }
      } else {
        // 已经设置了特定的status值，直接请求数据
        try {
          const res = await apiGetWorkflowList(params)
          workflowList.value = res.items
          total.value = res.total

          loading.value = false
          return
        } catch (error) {
          console.error('获取已处理工作流列表失败:', error)
          uni.showToast({
            title: '获取已处理工作流列表失败',
            icon: 'none',
          })
          loading.value = false
          return
        }
      }
    }

    // 如果有搜索关键词，添加到表单标题查询中
    if (searchKeyword.value && typeof searchKeyword.value === 'string') {
      params.form_title = searchKeyword.value
    }

    // 如果选择了特定的过滤标签（非"全部"），添加到表单标题查询中
    if (
      activeFilter.value !== '全部' &&
      activeFilter.value !== '通过' &&
      activeFilter.value !== '不通过' &&
      activeFilter.value !== '驳回' &&
      activeFilter.value !== '未审'
    ) {
      params.form_title = activeFilter.value
    }

    const res = await apiGetWorkflowList(params)
    workflowList.value = res.items
    total.value = res.total
  } catch (error) {
    console.error('获取工作流列表失败:', error)
    uni.showToast({
      title: '获取工作流列表失败',
      icon: 'none',
    })
  } finally {
    loading.value = false
  }
}

// 监听页码变化
watch(page, () => {
  getWorkflowList()
})

// 处理标签页切换
const handleTabChange = (index: number) => {
  activeTabIndex.value = index
  // 重置过滤项
  activeFilter.value = '全部'
  // 重置分类选择
  selectedCategoryIndex.value = 0
  selectedCategory.value = allCategoryOption
  // 切换标签页时重置页码
  page.value = 1
  // 更新过滤标签
  updateFilterTags()

  // 只有在"我已处理"标签页时才获取工作流分类列表
  if (index === 2) {
    fetchCategoryList()
  }

  getWorkflowList()
}

// 处理过滤标签点击
const handleFilterChange = (filter: string) => {
  activeFilter.value = filter
  // 切换过滤项时重置页码
  page.value = 1
  getWorkflowList()
}

// 处理搜索
const handleSearch = (value: any) => {
  console.log(value)

  // 确保只保存纯文本值，避免保存对象
  searchKeyword.value = typeof value.value === 'string' ? value.value : ''

  // 使用防抖，避免频繁请求
  if (searchTimer.value) {
    clearTimeout(searchTimer.value)
  }

  searchTimer.value = setTimeout(() => {
    // 搜索时重置页码
    page.value = 1
    getWorkflowList()
    searchTimer.value = undefined
  }, 500)
}

// 清除搜索关键词
const clearSearch = () => {
  searchKeyword.value = ''
  // 清除搜索时重置页码
  page.value = 1
  getWorkflowList()
}

// 处理快捷操作点击
const handleQuickAction = (actionId: number) => {
  console.log('点击了快捷操作:', actionId)
  // 根据ID执行不同的操作
}

// 处理工作流点击
const handleWorkflowItemClick = (workflow: WorkflowItem) => {
  // 待我审批列表的未处理项，跳转到审批页面
  if (activeTabIndex.value === 0 && workflow.process_status === 0) {
    uni.navigateTo({
      url: `/pages/workflow/approve?id=${workflow.id}`,
    })
  } else {
    // 其他情况跳转到详情页面
    uni.navigateTo({
      url: `/pages/workflow/detail?id=${workflow.id}`,
    })
  }
}

// 处理工作流操作
const handleWorkflowAction = (workflowId: number, action: string) => {
  console.log('工作流操作:', workflowId, action)
  // 根据不同的操作执行不同的逻辑
  if (action === '查看详情') {
    // 跳转到详情页
    uni.navigateTo({
      url: `/pages/workflow/detail?id=${workflowId}`,
    })
  } else if (action === '审批') {
    // 跳转到审批页面
    uni.navigateTo({
      url: `/pages/workflow/approve?id=${workflowId}`,
    })
  }
}

// 查看全部
const viewAll = (section: string) => {
  console.log('查看全部:', section)
  // 跳转到对应的全部列表页面
}

// 获取工作流状态样式
const getWorkflowStatus = (item: WorkflowItem) => {
  // 如果字典数据已加载
  if (dictData.value.DM_SPZT_NEW && dictData.value.DM_SPZT_NEW.length > 0) {
    // 根据process_status获取字典中对应的状态
    const statusValue = item.process_status !== undefined ? item.process_status.toString() : ''
    const dictItem = dictData.value.DM_SPZT_NEW.find((dict) => dict.dictValue === statusValue)

    if (dictItem) {
      // 根据listClass设置样式
      const listClassStyleMap: Record<string, { bgColor: string; textColor: string }> = {
        primary: {
          bgColor: '#fff2e5',
          textColor: '#ff9500',
        },
        green: {
          bgColor: '#e8f8ee',
          textColor: '#34c759',
        },
        danger: {
          bgColor: '#fee',
          textColor: '#ff3b30',
        },
        red: {
          bgColor: '#ffebee',
          textColor: '#f44336',
        },
        orange: {
          bgColor: '#fff3e0',
          textColor: '#ff9800',
        },
      }

      // 获取对应的样式，如果找不到则使用默认样式
      const style = listClassStyleMap[dictItem.listClass] || {
        bgColor: '#e2f2ff',
        textColor: '#0084ff',
      }

      return {
        text: dictItem.dictLabel,
        bgColor: style.bgColor,
        textColor: style.textColor,
      }
    }
  }

  // 如果字典数据未加载或找不到对应状态，使用原始逻辑
  if (item.process_status === 0) {
    return {
      text: '审批中',
      bgColor: '#fff2e5',
      textColor: '#ff9500',
    }
  } else if (item.process_status === 1) {
    return {
      text: '已通过',
      bgColor: '#e8f8ee',
      textColor: '#34c759',
    }
  } else if (item.process_status === 2) {
    return {
      text: '已拒绝',
      bgColor: '#fee',
      textColor: '#ff3b30',
    }
  } else if (item.process_status === 3) {
    return {
      text: '已驳回',
      bgColor: '#ffebee',
      textColor: '#f44336',
    }
  } else if (item.process_status === 4) {
    return {
      text: '已撤销',
      bgColor: '#fff3e0',
      textColor: '#ff9800',
    }
  } else {
    return {
      text: '处理中',
      bgColor: '#e2f2ff',
      textColor: '#0084ff',
    }
  }
}

// 获取工作流类型图标
const getWorkflowTypeIcon = (item: WorkflowItem) => {
  // 根据标题或内容判断工作流类型
  const title = item.title || item.form_title || ''

  if (title.includes('请假')) {
    return {
      icon: 'calendar',
      color: 'linear-gradient(135deg, #0a84ff, #0055d4)',
    }
  } else if (title.includes('报销')) {
    return {
      icon: 'money-circle',
      color: 'linear-gradient(135deg, #ff9500, #c93400)',
    }
  } else if (title.includes('采购')) {
    return {
      icon: 'cart',
      color: 'linear-gradient(135deg, #5e5ce6, #3634a3)',
    }
  } else {
    return {
      icon: 'note',
      color: 'linear-gradient(135deg, #34c759, #248a3d)',
    }
  }
}

// 根据当前标签页更新过滤标签列表
const updateFilterTags = () => {
  if (activeTabIndex.value === 2) {
    // 如果是"我已处理"标签页，则过滤标签应包含处理结果
    filterTags.value = ['全部', '通过', '不通过', '驳回', '未审']
  } else {
    // 其他标签页使用默认过滤标签
    filterTags.value = []
  }
}

onShow(() => {
  // 加载字典数据
  loadDict()
  getWorkflowList()
})
// 初始加载工作流列表
onMounted(() => {
  // 只有在"我已处理"标签页时才获取工作流分类列表
  if (activeTabIndex.value === 2) {
    fetchCategoryList()
  }

  // 检查本地存储中是否有要显示的标签页索引
  try {
    const activeTab = uni.getStorageSync('WORKFLOW_ACTIVE_TAB')
    if (activeTab !== '' && activeTab >= 0 && activeTab < tabOptions.length) {
      handleTabChange(activeTab)
      // 使用后清除，避免影响下次正常进入
      uni.removeStorageSync('WORKFLOW_ACTIVE_TAB')
    }
  } catch (e) {
    console.error('获取本地存储的标签页索引失败:', e)
  }

  updateFilterTags()
  getWorkflowList()

  // 监听从首页发出的标签切换事件
  uni.$on('switch-workflow-tab', handleGlobalTabSwitch)
})

// 组件卸载前清除事件监听
onBeforeUnmount(() => {
  uni.$off('switch-workflow-tab')
})

// 处理从首页发出的标签切换事件
const handleGlobalTabSwitch = (index: number) => {
  if (index >= 0 && index < tabOptions.length) {
    handleTabChange(index)
  }
}
</script>

<template>
  <view class="workflow-container">
    <!-- 标题 -->
    <view class="header">
      <view class="page-title">{{ pageTitle }}</view>

      <!-- 标签页 -->
      <view class="tabs">
        <view
          v-for="(tab, index) in tabOptions"
          :key="index"
          class="tab"
          :class="{ active: activeTabIndex === index }"
          @tap="handleTabChange(index)"
        >
          {{ tab }}
        </view>
      </view>

      <!-- 流程类型选择器 - 只在"我已处理"标签页显示 -->
      <view v-if="activeTabIndex === 2" class="category-picker-container">
        <picker
          :range="categoryList"
          range-key="name"
          :value="selectedCategoryIndex"
          @change="handleCategoryChange"
        >
          <view class="category-picker">
            <view class="picker-value">{{ selectedCategory.name }}</view>
            <view class="picker-arrow">
              <wd-icon name="arrow-down" />
            </view>
          </view>
        </picker>
      </view>

      <!-- 搜索框 -->
      <view class="search-box">
        <wd-input
          v-model="searchKeyword"
          placeholder="搜索流程"
          clearable
          prefix-icon="search"
          clear-trigger="focus"
          @input="handleSearch"
          @clear="clearSearch"
        />
      </view>

      <!-- 过滤标签 -->
      <scroll-view scroll-x class="filter-row">
        <view
          v-for="(filter, index) in filterTags"
          :key="index"
          class="filter-item"
          :class="{ active: activeFilter === filter }"
          @tap="handleFilterChange(filter)"
        >
          {{ filter }}
        </view>
      </scroll-view>
    </view>

    <!-- 工作流列表 -->
    <view v-if="loading" class="loading-container">
      <wd-loading color="#007aff" />
    </view>
    <view v-else-if="workflowList.length === 0" class="empty-state">
      <view class="empty-icon">
        <wd-icon name="info-circle" />
      </view>
      <view class="empty-text">暂无数据</view>
    </view>
    <view v-else class="workflow-list">
      <view
        v-for="workflow in workflowList"
        :key="workflow.id"
        class="workflow-item"
        @tap="handleWorkflowItemClick(workflow)"
      >
        <view class="workflow-header">
          <view class="workflow-type">
            <view
              class="workflow-icon"
              :style="{ background: getWorkflowTypeIcon(workflow).color }"
            >
              <wd-icon :name="getWorkflowTypeIcon(workflow).icon" />
            </view>
            <view class="workflow-info">
              <view class="workflow-title">{{ workflow.title || workflow.form_title }}</view>
              <view class="workflow-number">编号: {{ workflow.id || workflow.code }}</view>
              <view class="workflow-date">{{ workflow.applyTime }}</view>
            </view>
          </view>
          <view
            class="workflow-status"
            :style="{
              backgroundColor: getWorkflowStatus(workflow).bgColor,
              color: getWorkflowStatus(workflow).textColor,
            }"
          >
            {{ getWorkflowStatus(workflow).text }}
          </view>
        </view>
        <view class="workflow-content">
          <view class="workflow-submitter-info">当前审批人: {{ workflow.create_user_name }}</view>
          <!-- 如果审批已完成（process_status为1已通过或2已拒绝），显示审批说明 -->
          <view
            v-if="workflow.process_status === 1 || workflow.process_status === 2"
            class="workflow-opinion"
          >
            审批说明: {{ workflow.approval_opinion || '无' }}
          </view>
          <!-- 否则显示当前步骤 -->
          <view v-else class="workflow-step">当前步骤: {{ workflow.currentStepName }}</view>
        </view>
        <view class="workflow-footer">
          <view class="workflow-submitter">
            <view class="submitter-avatar">
              <wd-icon name="user-avatar" />
            </view>
            <view class="submitter-name">{{ workflow.create_user_name }}</view>
          </view>
          <view class="workflow-actions">
            <view class="workflow-action">
              {{ activeTabIndex === 0 && workflow.process_status === 0 ? '审批' : '查看详情' }}
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 分页 -->
    <Pagination
      v-if="workflowList.length > 0"
      :total="total"
      :page="page"
      :pageSize="pageSize"
      @update:page="page = $event"
    />
  </view>
</template>

<style lang="scss">
.workflow-container {
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
  padding: 30rpx;
  padding-bottom: 140rpx;
  background-color: #f2f5f8;
}

.header {
  width: 100%;
  margin-bottom: 30rpx;
}

.page-title {
  margin-bottom: 30rpx;
  font-size: 44rpx;
  font-weight: 700;
}

.tabs {
  display: flex;
  padding: 6rpx;
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.tab {
  flex: 1;
  padding: 24rpx 0;
  font-size: 28rpx;
  font-weight: 500;
  color: #666;
  text-align: center;
  border-radius: 16rpx;
}

.tab.active {
  color: white;
  background-color: #007aff;
}

.search-box {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 30rpx;
}

// 为wot-design-uni组件添加自定义样式
:deep(.wd-input__inner) {
  height: 90rpx !important;
  padding: 0 0 0 10rpx !important;
  font-size: 32rpx !important;
  background-color: #ffffff !important;
  border-radius: 45rpx !important;
  transition: all 0.3s ease !important;
}

:deep(.wd-input__prefix) {
  left: 0rpx !important;
  margin-left: 30rpx;
}

:deep(.wd-icon) {
  font-size: 40rpx !important;
  // color: #007aff !important;
}

:deep(.wd-input__clear) {
  width: 40rpx !important;
  height: 40rpx !important;
  margin-right: 40rpx !important;
  line-height: 40rpx !important;
  color: #999 !important;
  background-color: #f0f0f0 !important;
  border-radius: 50% !important;
  transition: all 0.2s ease !important;
}

:deep(.wd-input__clear:active) {
  background-color: #e0e0e0 !important;
  transform: scale(0.95) !important;
}

.filter-row {
  display: flex;
  margin-bottom: 30rpx;
  white-space: nowrap;
}

.filter-item {
  display: inline-block;
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f2f2f7;
  border-radius: 36rpx;
  transition: all 0.3s ease;
}

.filter-item.active {
  color: #007aff;
  background-color: #ebf5ff;
}

.quick-actions {
  display: flex;
  padding: 30rpx;
  margin-bottom: 40rpx;
  background-color: white;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.quick-action {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.quick-action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90rpx;
  height: 90rpx;
  margin-bottom: 16rpx;
  color: white;
  border-radius: 24rpx;
}

.quick-action-icon :deep(.wd-icon) {
  font-size: 42rpx !important;
  color: white !important;
}

.quick-action-title {
  font-size: 26rpx;
  color: #333;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.view-more {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #999;
}

.view-more :deep(.wd-icon) {
  margin-left: 4rpx;
  font-size: 28rpx !important;
  color: #999 !important;
}

.workflow-list {
  margin-bottom: 40rpx;
}

.workflow-item {
  padding: 30rpx;
  margin-bottom: 24rpx;
  background-color: white;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.workflow-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.workflow-type {
  display: flex;
  align-items: center;
}

.workflow-icon {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  border-radius: 20rpx;
}

.workflow-icon :deep(.wd-icon) {
  font-size: 36rpx !important;
  color: white !important;
}

.workflow-info {
  flex: 1;
}

.workflow-title {
  max-width: 400rpx; /* 控制最大宽度，根据实际需要调整 */
  margin-bottom: 6rpx;
  overflow: hidden;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.workflow-number {
  margin-bottom: 6rpx;
  font-size: 26rpx;
  color: #999;
}

.workflow-date {
  font-size: 26rpx;
  color: #999;
}

.workflow-status {
  align-self: flex-start;
  padding: 8rpx 20rpx;
  font-size: 26rpx;
  border-radius: 8rpx;
}

.workflow-content {
  margin-bottom: 24rpx;
  font-size: 28rpx;
  color: #666;
}

.workflow-submitter-info {
  margin-bottom: 8rpx;
}

.workflow-step,
.workflow-opinion {
  padding: 8rpx 0;
  line-height: 1.4;
}

.workflow-opinion {
  font-style: italic;
  color: #555;
}

.workflow-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 24rpx;
  border-top: 1px solid #f2f2f7;
}

.workflow-submitter {
  display: flex;
  align-items: center;
}

.submitter-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  margin-right: 16rpx;
  background-color: #f2f2f7;
  border-radius: 50%;
}

.submitter-avatar :deep(.wd-icon) {
  font-size: 28rpx !important;
  color: #666 !important;
}

.submitter-name {
  font-size: 26rpx;
  color: #666;
}

.workflow-actions {
  display: flex;
}

.workflow-action {
  margin-left: 30rpx;
  font-size: 26rpx;
  color: #007aff;
}

.empty-state {
  padding: 50rpx 30rpx;
  text-align: center;
  background-color: white;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.empty-icon {
  margin-bottom: 30rpx;
  font-size: 96rpx;
  color: #ccc;
}

.empty-icon :deep(.wd-icon) {
  font-size: 96rpx !important;
  color: #ccc !important;
}

.empty-text {
  margin-bottom: 40rpx;
  font-size: 30rpx;
  color: #999;
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200rpx;
}

.category-picker-container {
  margin-bottom: 30rpx;
}

.category-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 90rpx;
  padding: 0 30rpx;
  font-size: 32rpx;
  color: #333;
  background-color: #ffffff;
  border-radius: 45rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.picker-arrow {
  display: flex;
  align-items: center;
}

.picker-arrow :deep(.wd-icon) {
  font-size: 32rpx !important;
  color: #999 !important;
  transition: transform 0.3s ease;
}
</style>
