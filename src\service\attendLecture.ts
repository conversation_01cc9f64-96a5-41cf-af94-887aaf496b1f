/**
 * 听课评价相关API
 */
import request from '@/utils/request'
import type {
  EvaluationProjectQuery,
  EvaluationProjectResponse,
  AttendLectureAddQuery,
  AttendLectureAddResponse,
  AttendLectureQuery,
  AttendLectureResponse,
  AttendLectureStatisticsData,
  AttendLectureEvaluationDataQuery,
  AttendLectureEvaluationDataItem,
  AttendLectureUpdateQuery,
  AttendLectureUpdateResponse,
} from '@/types/attendLecture'
import { useUserStore } from '@/store/user'
import { getEnvBaseUrl } from '@/utils'

/**
 * 获取听课评价项目
 * @param params 请求参数
 * @returns 听课评价项目列表
 */
export function getEvaluationProject(
  params: EvaluationProjectQuery,
): Promise<EvaluationProjectResponse['data']> {
  return request('/teacher/attendLecture/evaluationProject', {
    method: 'POST',
    data: params,
  })
}

/**
 * 添加听课评价
 * @param params 请求参数
 * @returns 添加结果
 */
export function addAttendLecture(
  params: AttendLectureAddQuery,
): Promise<AttendLectureAddResponse['data']> {
  return request('/teacher/attendLecture/add', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取被听课记录列表
 * @param params 查询参数
 * @returns 听课记录响应数据
 */
export function getAttendLectureList(params: AttendLectureQuery): Promise<AttendLectureResponse> {
  return request('/teacher/attendLecture/be', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取听课统计数据
 * @returns 听课统计响应数据
 */
export function getAttendLectureStatistics(): Promise<AttendLectureStatisticsData> {
  return request('/teacher/attendLecture/statistics', {
    method: 'POST',
    data: {},
  })
}

/**
 * 获取听课评价数据
 * @param params 查询参数，包含听课记录ID
 * @returns 听课评价数据列表
 */
export function getAttendLectureEvaluationData(
  params: AttendLectureEvaluationDataQuery,
): Promise<AttendLectureEvaluationDataItem[]> {
  return request('/teacher/attendLecture/evaluationData', {
    method: 'POST',
    data: params,
  })
}

/**
 * 更新听课评价
 * @param params 请求参数
 * @returns 更新结果
 */
export function updateAttendLecture(
  params: AttendLectureUpdateQuery,
): Promise<AttendLectureUpdateResponse['data']> {
  return request('/teacher/attendLecture/update', {
    method: 'POST',
    data: params,
  })
}
