import request from '@/utils/request'
import type {
  IUserInfo,
  IRefreshTokenByWechatParams,
  IRefreshTokenResponse,
  IGetUserInfoResponse,
  ICaptchaFlagResponse,
  ICaptchaResponse,
  ILoginRequest,
  ILoginResponse,
} from '@/types/auth'
import type { MenuItem } from '@/store/menu'

/**
 * 通过微信token刷新系统token
 * @param params 刷新token所需参数
 */
export function refreshTokenByWechat(
  params: IRefreshTokenByWechatParams,
): Promise<IRefreshTokenResponse> {
  return request('/auth/refreshTokenByWechat', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取用户信息
 */
export function getUserInfo(): Promise<IGetUserInfoResponse> {
  return request('/auth/getUserInfo', {
    method: 'GET',
  })
}

/**
 * 获取权限码
 */
export function getAccessCodes(): Promise<string[]> {
  return request('/auth/getAccessCodes', {
    method: 'GET',
  })
}

/**
 * 获取用户菜单
 * @param isMobileMenu 是否获取移动端菜单，1表示移动端菜单
 */
export function getUserMenus(isMobileMenu: number = 1): Promise<MenuItem[]> {
  return request(`/auth/getUserMenus?is_mobile_menu=${isMobileMenu}`, {
    method: 'GET',
  })
}

/**
 * 检查是否启用验证码
 */
export function getCaptchaFlag(): Promise<ICaptchaFlagResponse> {
  return request('/common/getCaptchaOpenFlag', {
    method: 'GET',
  })
}

/**
 * 获取验证码
 * @param type 验证码类型，默认为base64
 */
export function getCaptcha(type: string = 'base64'): Promise<ICaptchaResponse> {
  return request('/common/captcha', {
    method: 'GET',
    params: { type },
  })
}

/**
 * 用户登录
 * @param data 登录参数
 */
export function login(data: ILoginRequest): Promise<ILoginResponse> {
  return request('/auth/login', {
    method: 'POST',
    data,
  })
}
