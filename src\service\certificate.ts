import request from '@/utils/request'
import type { CertificateQuery, CertificateResponse } from '@/types/certificate'

/**
 * 获取学生证书列表
 * @param params 查询参数
 * @returns 证书列表响应
 */
export function getStudentCertificates(
  params: CertificateQuery = { page: 1, pageSize: 10 },
): Promise<CertificateResponse> {
  return request('/student_server/certificate', {
    method: 'POST',
    data: params,
  })
}
