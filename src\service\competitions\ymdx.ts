/**
 * 云端学习相关API
 */
import request from '@/utils/request'
import {
  PlayerQuestionInfoData,
  PlayerQuestionInfoParams,
  PlayerAnswerParams,
  PlayerAnswerData,
  PlayerHandleQuickParams,
  PlayerHandleQuickData,
  PlayerScoreParams,
  PlayerScoreData,
} from '@/types/competitions/ymdx'

/**
 * 获取玩家问题信息
 * @param params 请求参数
 * @returns 问题信息
 */
export function getPlayerQuestionInfo(
  params: PlayerQuestionInfoParams,
): Promise<PlayerQuestionInfoData> {
  const { xmid, rybh } = params
  return request<PlayerQuestionInfoData>('/topic/playerQuestionInfo', {
    method: 'GET',
    params: { xmid, rybh },
  })
}

/**
 * 提交玩家答题
 * @param params 请求参数
 * @returns 答题结果
 */
export function postPlayerAnswer(params: PlayerAnswerParams): Promise<PlayerAnswerData> {
  return request<PlayerAnswerData>('/topic/playerAnswer', {
    method: 'POST',
    data: params,
  })
}

/**
 * 玩家快速处理问题
 * @param params 请求参数
 * @returns 处理结果
 */
export function postPlayerHandleQuick(
  params: PlayerHandleQuickParams,
): Promise<PlayerHandleQuickData> {
  return request<PlayerHandleQuickData>('/topic/playerHandleQuick', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取选手得分
 * @param params 请求参数
 * @returns 选手得分信息
 */
export function getPlayerScore(params: PlayerScoreParams): Promise<PlayerScoreData> {
  const { xmid, rybh } = params
  return request<PlayerScoreData>('/topic/playerScore', {
    method: 'GET',
    params: { xmid, rybh },
  })
}

/**
 * 检查用户是否在参赛名单中或场次是否开始
 * @param params
 * @returns
 */
export function checkPlayerEligibility(params: { xmid: string; rybh: string }): Promise<{
  can_answer: boolean
  message: string
}> {
  return request('/topic/playerCanAnswer', {
    method: 'GET',
    data: params,
  })
}
