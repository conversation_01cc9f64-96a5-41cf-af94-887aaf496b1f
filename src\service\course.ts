import request from '@/utils/request'
import type {
  CourseYear,
  CourseYearParams,
  CourseSelectParams,
  CourseListResponse,
  CourseDateSectionParams,
  CourseDateSection,
  CourseDateSectionResponse,
} from '@/types/course'

/**
 * 获取学年列表
 * @param params 参数
 * @returns 学年列表
 */
export function getCourseYearList(params: CourseYearParams): Promise<CourseYear[]> {
  return request('/course/yearList', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取课程列表
 * @param params 查询参数
 * @returns 课程列表
 */
export function getCourseList(params: CourseSelectParams): Promise<CourseListResponse> {
  return request('/course', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取课程日期和节次
 * @param params 查询参数
 * @returns 课程日期和节次列表
 */
export function getCourseDateSection(
  params: CourseDateSectionParams,
): Promise<CourseDateSection[]> {
  return request('/course/dateSection', {
    method: 'POST',
    data: params,
  })
}
