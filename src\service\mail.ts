import request from '@/utils/request'
import type { MailQuery, MailResponse, MailInfoQuery, MailInfoResponse } from '@/types/mail'

// 获取邮件列表
export function getMailList(params: MailQuery): Promise<MailResponse> {
  return request('/mail', {
    method: 'POST',
    data: params,
  })
}

// 获取邮件详情
export function getMailInfo(params: MailInfoQuery): Promise<MailInfoResponse> {
  return request('/mailInfo', {
    method: 'GET',
    params,
  })
}
