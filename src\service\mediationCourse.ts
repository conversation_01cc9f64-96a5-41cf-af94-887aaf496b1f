import request from '@/utils/request'
import type {
  MediationCourseQuery,
  MediationCourseResponse,
  MediationCourseDetailQuery,
  MediationCourseDetailResponse,
  AdjustCourseParams,
  AdjustCourseResponse,
  ScheduleChangePlanQuery,
  ScheduleChangePlanResponse,
  ScheduleChangeApplyParams,
  ScheduleChangeApplyResponse,
  ScheduleChangeDeleteParams,
  ScheduleChangeDeleteResponse,
} from '@/types/mediationCourse'

/**
 * 获取调停课申请列表
 * @param params 查询参数
 * @returns 调停课申请列表响应
 */
export function getMediationCourseApplyList(
  params: MediationCourseQuery = {},
): Promise<MediationCourseResponse> {
  return request('/teacher/mediationCourseApply', {
    method: 'GET',
    params: params as Record<string, unknown>,
  })
}

/**
 * 获取调停课申请详情
 * @param params 查询参数
 * @returns 调停课申请详情响应
 */
export function getMediationCourseApplyDetail(
  params: MediationCourseDetailQuery,
): Promise<MediationCourseDetailResponse['data']> {
  return request('/teacher/mediationCourseApply', {
    method: 'GET',
    params: params as unknown as Record<string, unknown>,
  })
}

/**
 * 提交调课申请
 * @param params 调课申请参数
 * @returns 调课申请响应
 */
export function submitAdjustCourse(params: AdjustCourseParams): Promise<AdjustCourseResponse> {
  return request('/teacher/adjustCourse', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取调课计划
 * @param params 调课计划查询参数
 * @returns 调课计划响应
 */
export function getScheduleChangePlan(
  params: ScheduleChangePlanQuery,
): Promise<ScheduleChangePlanResponse['data']> {
  return request('/teacher/scheduleChange/plan', {
    method: 'GET',
    params: params as unknown as Record<string, unknown>,
  })
}

/**
 * 提交调课申请
 * @param params 调课申请参数
 * @returns 调课申请响应
 */
export function submitScheduleChangeApply(
  params: ScheduleChangeApplyParams,
): Promise<ScheduleChangeApplyResponse['data']> {
  return request('/teacher/scheduleChange/apply', {
    method: 'POST',
    data: params,
  })
}

/**
 * 删除调课记录
 * @param params 删除参数，包含调课记录ID
 * @returns 删除操作响应
 */
export function deleteScheduleChange(
  params: ScheduleChangeDeleteParams,
): Promise<ScheduleChangeDeleteResponse['data']> {
  return request('/teacher/scheduleChange/delete', {
    method: 'POST',
    data: params,
  })
}
