/**
 * 组织机构相关API
 */
import request from '@/utils/request'
import {
  OrganizationQuery,
  OrganizationResponse,
  SubstanceOrg,
  SubstanceListQuery,
  SubstanceListResponse,
} from '@/types/organization'

/**
 * 获取组织机构列表
 * @param params 查询参数
 * @returns 组织机构列表
 */
export function getOrganizationList(params: OrganizationQuery = {}): Promise<OrganizationResponse> {
  return request('/organization', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取实体机构列表
 * @returns 实体机构列表（仅包含code和name字段）
 */
export function getSubstanceList(): Promise<{ code: string; name: string }[]> {
  return request('/organization/substanceList', {
    method: 'POST',
    data: {},
  })
}

/**
 * 获取实体机构列表（支持参数）
 * @param params 查询参数
 * @returns 实体机构列表（仅包含code和name字段）
 */
export function getSubstanceListWithParams(
  params: SubstanceListQuery = { isSubstance: 1 },
): Promise<SubstanceListResponse> {
  return request('/organization/substanceList', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取树形班级列表
 * @returns 树形班级列表
 */
export function getTreeClassList(): Promise<SubstanceOrg[]> {
  return request('/organization/treeClassList', {
    method: 'POST',
    data: {},
  })
}
