// service/salary.ts

import request from '@/utils/request'
import type { SalaryQuery, SalaryResponse, SalaryItemEntry, SalaryDetail } from '@/types/salary'

/**
 * 获取工资信息
 * @param params 查询参数
 */
export function getSalaryInfo(params: SalaryQuery): Promise<Record<string, SalaryItemEntry[][]>> {
  return request('/teacher/salary', {
    method: 'POST',
    data: params,
  })
}

/**
 * 处理工资数据,将数组转换为对象格式
 * @param data 原始工资数据
 */
export function formatSalaryData(data: Record<string, SalaryItemEntry[][]>): SalaryDetail {
  const result: SalaryDetail = {
    incomeItems: {},
    deductItems: {},
    baseInfo: {},
  }

  // 收入项列表
  const incomeItemList = [
    '职岗工资',
    '级别薪级',
    '生活补贴',
    '教护津贴',
    '岗位津贴',
    '职务津贴',
    '地区补贴',
    '考勤奖',
    '特岗津贴',
    '提租补贴',
    '其他补',
    '应发工资',
    // 津贴类项目
    '考核津贴',
    '效益津贴',
    '在岗津贴',
    '补系数',
    '其他贴1',
    '其他贴2',
    '其他贴3',
    '应发津贴',
    // 新增项目
    '入岚补',
    '基础绩效奖',
    '文明奖',
    '专职思政补',
    '专职辅导员补',
    '课题劳务费',
    '女职工卫生费',
    // 年终相关项目
    '补贴一',
    '补贴二',
    '补贴三',
    '年终应发',
    '年终实发',
  ]

  // 扣除项列表
  const deductItemList = [
    '医保',
    '失保',
    '公积金',
    '养老保险',
    '职业年金',
    '工会费',
    '房租',
    '水费',
    '电费',
    '网租',
    '第一次扣税',
    '其他扣1',
    '扣工资1',
    '应扣合计',
    // 新增项目
    '其他扣2',
    '扣工资2',
    '第二次扣税',
    '补扣三险两金',
    // 年终相关扣除项
    '其他扣(年终)',
    '扣工资(年终)',
    '年终计税',
  ]

  // 基本信息列表
  const baseInfoList = ['人员代码', '姓名', '年', '月', '实发工资', '实发津贴']

  // 遍历所有的数据集
  Object.keys(data).forEach((key) => {
    const dataArray = data[key]
    if (dataArray && dataArray.length > 0 && dataArray[0].length > 0) {
      const items = dataArray[0]

      items.forEach((item) => {
        const { salaryItem, salaryValue } = item
        if (incomeItemList.includes(salaryItem)) {
          result.incomeItems[salaryItem as keyof SalaryDetail['incomeItems']] = salaryValue
        } else if (deductItemList.includes(salaryItem)) {
          result.deductItems[salaryItem as keyof SalaryDetail['deductItems']] = salaryValue
        } else if (baseInfoList.includes(salaryItem)) {
          // 将人员代码映射为teacherCode
          const key =
            salaryItem === '人员代码'
              ? 'teacherCode'
              : salaryItem === '姓名'
                ? 'name'
                : salaryItem === '年'
                  ? 'year'
                  : salaryItem === '月'
                    ? 'month'
                    : salaryItem
          result.baseInfo[key as keyof SalaryDetail['baseInfo']] = salaryValue
        }
      })
    }
  })

  return result
}
