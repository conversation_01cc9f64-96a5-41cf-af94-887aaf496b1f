import request from '@/utils/request'
import type {
  ScheduleResponse,
  ScheduleTimeItem,
  TeacherScheduleQuery,
  TeacherScheduleResponse,
} from '@/types/schedule'

/**
 * 获取课程表
 * @param semester 学期
 * @param week 周次
 * @param showxxq 是否显示小学期 (0: 不显示, 1: 显示)
 * @returns Promise<ScheduleResponse>
 */
export function getSchedule(
  semester: string,
  week: number,
  showxxq: number = 0,
): Promise<ScheduleResponse> {
  return request('/schedule', {
    method: 'POST',
    data: {
      semester,
      week,
      showxxq,
    },
  })
}

/**
 * 获取课程时间表
 * @returns Promise<ScheduleTimeResponse> 课程时间表数据
 */
export function getScheduleTime(dqz: number): Promise<Record<string, ScheduleTimeItem>> {
  return request('/scheduleTime', {
    method: 'POST',
    data: {
      dqz,
    },
  })
}

/**
 * 获取教师课程表列表
 * @param params 查询参数
 * @returns 教师课程表列表
 */
export function getTeacherScheduleList(
  params?: TeacherScheduleQuery,
): Promise<TeacherScheduleResponse> {
  return request('/teacher/schedule/list', {
    method: 'GET',
    params: params as Record<string, unknown>,
  })
}
