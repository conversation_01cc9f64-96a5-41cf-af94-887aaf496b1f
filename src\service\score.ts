import request from '@/utils/request'
import {
  TotalScoreQuery,
  TotalScoreResponse,
  TeacherTotalScoreQuery,
  TeacherTotalScoreResponse,
  SetScoreTypeRequest,
  SetScoreTypeResponse,
  GenerateTotalScoreRequest,
  GenerateTotalScoreResponse,
  SubmitTotalScoreRequest,
  SubmitTotalScoreResponse,
  DailyScoreQuery,
  DailyScoreResponse,
  TotalScoreStatResponse,
} from '@/types/score'

/**
 * 获取学生总评成绩
 * @param params 查询参数
 * @returns 总评成绩数据
 */
export function getStudentTotalScore(params: TotalScoreQuery): Promise<TotalScoreResponse> {
  return request('/student/myTotalScore', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取学生总评成绩统计数据
 * @returns 总评成绩统计数据
 */
export function getStudentTotalScoreStat(): Promise<TotalScoreStatResponse['data']> {
  return request('/student/totalScoreStat', {
    method: 'POST',
    data: {},
  })
}

/**
 * 获取教师任务总评成绩列表
 * @param params 查询参数
 * @returns 教师任务总评成绩数据
 */
export function getTeacherTotalScoreList(
  params: TeacherTotalScoreQuery,
): Promise<TeacherTotalScoreResponse['data']> {
  return request('/teacher/teachingTask/totalScore/list', {
    method: 'POST',
    data: params,
  })
}

/**
 * 设置成绩评定类型
 * @param params 请求参数
 * @returns 设置结果
 */
export function setScoreType(params: SetScoreTypeRequest): Promise<SetScoreTypeResponse> {
  return request('/teacher/teachingTask/totalScore/setScoreType', {
    method: 'POST',
    data: params,
  })
}

/**
 * 生成总评成绩
 * @param params 请求参数
 * @returns 生成结果
 */
export function generateTotalScore(
  params: GenerateTotalScoreRequest,
): Promise<GenerateTotalScoreResponse> {
  return request('/teacher/teachingTask/totalScore/generate', {
    method: 'POST',
    data: params,
  })
}

/**
 * 提交总评成绩
 * @param params 请求参数
 * @returns 提交结果
 */
export function submitTotalScore(
  params: SubmitTotalScoreRequest,
): Promise<SubmitTotalScoreResponse> {
  return request('/teacher/teachingTask/totalScore/submit', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取日常成绩列表
 * @param params 查询参数
 * @returns 日常成绩数据
 */
export function getDailyScoreList(params: DailyScoreQuery): Promise<DailyScoreResponse['data']> {
  return request('/teacher/teachingTask/dailyScore/list', {
    method: 'GET',
    params: params as unknown as Record<string, unknown>,
  })
}
