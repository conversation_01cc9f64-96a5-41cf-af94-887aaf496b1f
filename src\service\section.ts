import request from '@/utils/request'
import type { SectionConfigResponse, Section, SectionListParams } from '@/types/section'

/**
 * 获取课节时间配置
 */
export function getSectionConfig(): Promise<SectionConfigResponse> {
  return request('/sectionConfig', {
    method: 'GET',
  })
}

/**
 * 获取课节列表
 * @returns 课节列表
 */
export function getSectionList(): Promise<Section[]> {
  return request('/sectionList', {
    method: 'POST',
  })
}

/**
 * 获取课节列表（带参数）
 * @param params 请求参数
 * @returns 课节列表
 */
export function getSectionListWithParams(params: SectionListParams): Promise<Section[]> {
  return request('/sectionList', {
    method: 'POST',
    data: params,
  })
}
