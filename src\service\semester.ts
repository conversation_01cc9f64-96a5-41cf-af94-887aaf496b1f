import request from '@/utils/request'
import type { SemesterResponse, SemesterConfigResponse, SchoolYearResponse } from '@/types/semester'

/**
 * 获取学期列表
 * @returns Promise<SemesterResponse>
 */
export function getSemesterList(): Promise<SemesterResponse> {
  return request('/semesters', {
    method: 'GET',
  })
}

/**
 * 获取学年列表
 * @returns Promise<SchoolYearResponse>
 */
export function getSchoolYearList(): Promise<SchoolYearResponse> {
  return new Promise((resolve) => {
    // 先获取学期列表，然后从中提取学年信息
    getSemesterList().then((res) => {
      // 从学期中提取学年信息（格式：yyyy-yyyy）
      const schoolYears: Set<string> = new Set()
      const schoolYearList: SchoolYearResponse['schoolYears'] = []

      // 遍历学期列表，提取学年部分
      res.semesters.forEach((semester) => {
        // 学期值格式为"yyyy-yyyy-t"，提取"yyyy-yyyy"部分作为学年
        const yearPart = semester.value.split('-').slice(0, 2).join('-')
        if (!schoolYears.has(yearPart)) {
          schoolYears.add(yearPart)
          schoolYearList.push({
            label: `${yearPart}学年`,
            value: yearPart,
            // 如果包含当前学期，则该学年为当前学年
            isCurrent: semester.isCurrent,
          })
        }
      })

      // 按学年降序排序
      schoolYearList.sort((a, b) => b.value.localeCompare(a.value))

      resolve({
        code: 200,
        msg: '获取学年列表成功',
        time: Date.now(),
        schoolYears: schoolYearList,
      })
    })
  })
}

/**
 * 获取学期配置信息
 * @param semester 学期值
 * @returns Promise<SemesterConfigResponse>
 */
export function getSemesterConfig(semester: string): Promise<SemesterConfigResponse> {
  return request('/semesterConfig', {
    method: 'POST',
    data: {
      semester,
    },
  })
}
