import request from '@/utils/request'
import type { DictItem, ApiResponse, DictData } from '@/types/system'
import type { SemesterOption, SemesterSelectResponse } from '@/types/semester'

// 获取字典数据
export function getDict(dictType: string): Promise<DictItem[]> {
  return request('/system/get_dict', {
    method: 'POST',
    data: {
      dict_type: dictType,
    },
  })
}

// 获取学年学期选项
export function getSemesterSelect(): Promise<SemesterSelectResponse> {
  return request('/system/get_semester_select', {
    method: 'GET',
    data: {
      type: 'xnxq',
    },
  })
}

/**
 * 获取字典数据
 * @param dictType 字典类型
 * @returns 字典数据列表
 */
export function getDictData(dictType: string): Promise<DictData[]> {
  return request(`/system/dict/data`, {
    method: 'GET',
    params: {
      type: dictType,
    },
  })
}
