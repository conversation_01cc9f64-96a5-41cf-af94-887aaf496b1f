import request from '@/utils/request'
import type {
  TeachOfficeTeachingTaskQuery,
  TeachOfficeTeachingTaskResponse,
} from '@/types/teachOfficeTeachingTask'

/**
 * 获取教研室教学任务列表
 * @param params 查询参数
 * @returns 教学任务列表响应
 */
export function getTeachOfficeTeachingTaskList(
  params: TeachOfficeTeachingTaskQuery,
): Promise<TeachOfficeTeachingTaskResponse['data']> {
  return request('/teacher/teachOfficeTeachingTask/list', {
    method: 'POST',
    data: params,
  })
}
