import type {
  TeachingJournalItem,
  TeachingJournalListResponse,
  TeachingJournalQueryParams,
} from '@/types/teaching'

// 模拟数据
const mockData: TeachingJournalItem[] = [
  {
    id: '1',
    courseName: 'Java程序设计',
    week: '第12周 周一1-2节',
    time: '2023-12-04 08:00-09:40',
    location: '实训楼304',
    className: '软件技术2班',
    attendance: '应到42人，实到40人，缺勤2人',
    content:
      '1. 多线程概念讲解：进程与线程的区别，多线程的优势\n2. 创建线程的方式：继承Thread类、实现Runnable接口、使用Callable和Future\n3. 线程的生命周期和状态转换\n4. 线程安全问题：线程同步、锁机制\n5. 简单的多线程案例实现',
    feedback:
      '1. 学生对线程的创建方式掌握较好，能够实现简单的多线程程序\n2. 线程安全问题理解有一定难度，约30%的学生在理解同步机制时遇到困难\n3. 课后需加强线程安全和锁机制的练习\n4. 部分学生在案例实现中表现较好，能举一反三',
    progress: '第11章/15章',
    homework: '1. 完成多线程练习作业\n2. 预习线程池相关知识\n3. 复习线程同步机制',
    remarks: '部分学生请假参加比赛，需要后续补课',
    status: '1',
  },
  {
    id: '2',
    courseName: 'Java程序设计',
    week: '第12周 周四5-6节',
    time: '2023-12-07 14:00-15:40',
    location: '实训楼304',
    className: '软件技术2班',
    attendance: {
      total: 42,
      present: 40,
      absent: 2,
    },
    status: '0',
  },
]

// 获取教学日志列表
export const getTeachingJournalList = async (
  params: TeachingJournalQueryParams,
): Promise<TeachingJournalListResponse> => {
  // 模拟API调用
  await new Promise((resolve) => setTimeout(resolve, 500))

  let filteredData = [...mockData]

  // 根据查询参数过滤数据
  if (params.courseName) {
    filteredData = filteredData.filter((item) => item.courseName.includes(params.courseName!))
  }
  if (params.className) {
    filteredData = filteredData.filter((item) => item.className.includes(params.className!))
  }
  if (params.week) {
    filteredData = filteredData.filter((item) => item.week.includes(params.week!))
  }
  if (params.status) {
    filteredData = filteredData.filter((item) => item.status === params.status)
  }

  // 分页
  const start = (params.page - 1) * params.pageSize
  const end = start + params.pageSize
  const items = filteredData.slice(start, end)

  return {
    items,
    total: filteredData.length,
  }
}

// 获取教学日志详情
export const getTeachingJournalDetail = async (id: string): Promise<TeachingJournalItem> => {
  // 模拟API调用
  await new Promise((resolve) => setTimeout(resolve, 500))

  const item = mockData.find((item) => item.id === id)
  if (!item) {
    throw new Error('教学日志不存在')
  }

  return item
}

// 保存教学日志
export const saveTeachingJournal = async (data: TeachingJournalItem): Promise<void> => {
  // 模拟API调用
  await new Promise((resolve) => setTimeout(resolve, 500))

  const index = mockData.findIndex((item) => item.id === data.id)
  if (index > -1) {
    mockData[index] = data
  } else {
    mockData.push(data)
  }
}
