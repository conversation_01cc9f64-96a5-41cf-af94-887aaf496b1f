/**
 * 教学日志相关API接口
 */
import request from '@/utils/request'
import type {
  TeachingLogQueryParams,
  TeachingLogResponse,
  TeachingLogManageQueryParams,
  TeachingLogManageResponse,
} from '@/types/teachingLog'

/**
 * 获取教学日志列表
 * @param params 查询参数
 * @returns 教学日志列表数据
 */
export function getTeachingLogList(params: TeachingLogQueryParams) {
  return request<TeachingLogResponse['data']>('/teacher/teachingLog', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教学日志管理列表
 * @param params 查询参数
 * @returns 教学日志管理列表数据
 */
export function getTeachingLogManageList(params: TeachingLogManageQueryParams) {
  return request<TeachingLogManageResponse['data']>('/teacher/teachingLog/manage', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教学日志详情
 * @param id 教学日志ID
 * @returns 教学日志详情数据
 */
export function getTeachingLogDetail(id: number) {
  return request<TeachingLogResponse['data']['items'][0]>(`/teacher/teachingLog/${id}`, {
    method: 'GET',
  })
}

/**
 * 更新教学日志
 * @param id 教学日志ID
 * @param data 更新数据
 * @returns 更新结果
 */
export function updateTeachingLog(
  id: number,
  data: Partial<TeachingLogResponse['data']['items'][0]>,
) {
  return request<{ id: number }>(`/teacher/teachingLog/${id}`, {
    method: 'PUT',
    data,
  })
}

/**
 * 创建教学日志
 * @param data 创建数据
 * @returns 创建结果
 */
export function createTeachingLog(data: Partial<TeachingLogResponse['data']['items'][0]>) {
  return request<{ id: number }>('/teacher/teachingLog', {
    method: 'POST',
    data,
  })
}

/**
 * 删除教学日志
 * @param id 教学日志ID
 * @returns 删除结果
 */
export function deleteTeachingLog(id: number) {
  return request<{ success: boolean }>(`/teacher/teachingLog/${id}`, {
    method: 'DELETE',
  })
}

/**
 * 教师确认教学日志
 * @param id 教学日志ID
 * @returns 确认结果
 */
export function confirmTeachingLog(id: number) {
  return request<{ success: boolean }>(`/teacher/teachingLog/confirm/${id}`, {
    method: 'POST',
  })
}

/**
 * 批量确认教学日志
 * @param ids 教学日志ID数组
 * @returns 确认结果
 */
export function batchConfirmTeachingLog(ids: number[]) {
  return request<{ success: boolean }>('/teacher/teachingLog/batchConfirm', {
    method: 'POST',
    data: { ids },
  })
}
