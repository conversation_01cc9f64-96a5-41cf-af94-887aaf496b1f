import request from '@/utils/request'
import type {
  TeachingMaterialQuery,
  TeachingMaterialResponse,
  TeachingMaterialReceiveQuery,
  TeachingMaterialReceiveResponse,
  TeachingMaterialSelectionQuery,
  TeachingMaterialSelectionResponse,
  TeachingMaterialStatisticsResponse,
  TeachingMaterialChangeQuery,
  TeachingMaterialChangeResponse,
  TeachingMaterialNoNeedQuery,
  TeachingMaterialNoNeedResponse,
  CancelTeachingMaterialParams,
  UpdateTeachingMaterialNoNeedParams,
  CreateTeachingMaterialNoNeedParams,
  TeachingMaterialAddRequest,
  TeachingMaterialAddResponse,
  TeachingMaterialDetailRequest,
  TeachingMaterialDetailResponse,
  TeacherUpdateTeachingMaterialRequest,
  TeacherUpdateTeachingMaterialResponse,
} from '@/types/teachingMaterial'

/**
 * 获取教材列表
 * @param params 查询参数
 * @returns 教材列表数据
 */
export function getTeachingMaterialList(
  params: TeachingMaterialQuery,
): Promise<TeachingMaterialResponse> {
  return request('/teachingMaterial/list', {
    method: 'POST',
    data: params,
  })
}

/**
 * 根据关键词搜索教材
 * @param keyword 搜索关键词
 * @param page 页码
 * @param pageSize 每页数量
 * @returns 教材列表数据
 */
export function searchTeachingMaterials(
  keyword: string,
  page = 1,
  pageSize = 30,
): Promise<TeachingMaterialResponse> {
  return getTeachingMaterialList({
    page,
    pageSize,
    searchKeyword: keyword,
  })
}

/**
 * 根据ID获取教材详情
 * @param id 教材ID
 * @returns 教材详情
 */
export function getTeachingMaterialById(id: number): Promise<any> {
  return request(`/teachingMaterial/detail/${id}`, {
    method: 'GET',
  })
}

/**
 * 获取教材领用列表
 * @param params 查询参数
 * @returns 教材领用列表数据
 */
export function getTeachingMaterialReceiveList(
  params: TeachingMaterialReceiveQuery,
): Promise<TeachingMaterialReceiveResponse> {
  return request('/teachingMaterial/receiveList', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教材选用列表
 * @param params 查询参数
 * @returns 教材选用列表数据
 */
export function getTeachingMaterialSelectionList(
  params: TeachingMaterialSelectionQuery,
): Promise<TeachingMaterialSelectionResponse> {
  return request('/teacher/teachingMaterial/list', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教材统计数据
 * @returns 教材统计数据
 */
export function getTeachingMaterialStatistics(): Promise<TeachingMaterialStatisticsResponse> {
  return request('/teacher/teachingMaterial/statistics', {
    method: 'POST',
  })
}

/**
 * 获取教材变更列表
 * @param params 查询参数
 * @returns 教材变更列表响应数据
 */
export function getTeachingMaterialChangeList(
  params: TeachingMaterialChangeQuery,
): Promise<TeachingMaterialChangeResponse> {
  return request('/teacher/teachingMaterial/changeList', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取不需要教学材料的列表
 * @param params 查询参数
 * @returns 不需要教学材料的列表数据
 */
export function getTeachingMaterialNoNeedList(
  params: TeachingMaterialNoNeedQuery,
): Promise<TeachingMaterialNoNeedResponse> {
  return request('/teacher/teachingMaterial/noNeedList', {
    method: 'POST',
    data: params,
  })
}

/**
 * 更新无需征订教材任务信息
 * @param params 无需征订教材任务信息
 * @returns 更新结果
 */
export function updateTeachingMaterialNoNeed(
  params: UpdateTeachingMaterialNoNeedParams,
): Promise<any> {
  return request('/teacher/teachingTask/teachingMaterial/noNeed/update', {
    method: 'POST',
    data: params,
  })
}

/**
 * 创建无需征订教材任务
 * @param params 创建参数
 * @returns 创建结果
 */
export function createTeachingMaterialNoNeed(
  params: CreateTeachingMaterialNoNeedParams,
): Promise<any> {
  return request('/teacher/teachingTask/teachingMaterial/noNeed/create', {
    method: 'POST',
    data: params,
  })
}

/**
 * 取消教材选用
 * @param params 取消教材选用参数
 * @returns 操作结果
 */
export function cancelTeachingMaterial(params: CancelTeachingMaterialParams): Promise<any> {
  return request('/teacher/teachingTask/teachingMaterial/cancel', {
    method: 'POST',
    data: params,
  })
}

/**
 * 添加教材
 * @param data 教材信息
 * @returns Promise
 */
export function addTeachingMaterial(
  data: TeachingMaterialAddRequest,
): Promise<TeachingMaterialAddResponse> {
  return request('/teachingMaterial/add', {
    method: 'POST',
    data,
  })
}

/**
 * 获取教材详情
 * @param params 请求参数，包含教材ID
 * @returns 教材详情
 */
export function getTeachingMaterialDetail(
  params: TeachingMaterialDetailRequest,
): Promise<TeachingMaterialDetailResponse> {
  return request('/teachingMaterial/detail', {
    method: 'POST',
    data: params,
  })
}

/**
 * 教师更新教材
 * @param data 教材更新信息
 * @returns 更新结果
 */
export function updateTeachingMaterialByTeacher(
  data: TeacherUpdateTeachingMaterialRequest,
): Promise<TeacherUpdateTeachingMaterialResponse> {
  return request('/teachingMaterial/teacherUpdate', {
    method: 'POST',
    data,
  })
}
