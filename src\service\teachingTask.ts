/**
 * 教学任务相关API接口
 */
import request from '@/utils/request'
import type {
  TeachingTaskQueryParams,
  TeachingTaskItem,
  TeachingTaskListResponse,
  TeachingTaskDetail,
  TeachingTaskQuery,
  TeachingTaskPaginationQuery,
  TeachingTaskResponse,
  TeachingScheduleArrangementRequest,
  TeachingScheduleArrangementResponse,
  TeachingTaskSessionRequest,
  TeachingTaskSessionResponse,
  TeachingScheduleRequest,
  TeachingScheduleResponse,
  TeachingScheduleCheckRequest,
  TeachingScheduleCheckItem,
  TeachingScheduleCheckResponse,
  TeachingScheduleArrangementListRequest,
  TeachingScheduleArrangementListResponse,
  TeachingPlanTableRequest,
  TeachingPlanTableResponse,
  StudentListQuery,
  StudentListResponse,
  TeachingTaskNoticeQuery,
  TeachingTaskNoticeResponse,
} from '@/types/teachingTask'

/**
 * 根据学期获取教学任务列表
 * @param params 查询参数，包含semesters数组
 * @returns 教学任务列表数据
 */
export function getTeachingTaskListBySemester(params: TeachingTaskQueryParams) {
  return request<TeachingTaskListResponse['data']>('/home/<USER>', {
    method: 'GET',
    params,
  })
}

// 获取教学任务详情
export function getTeachingTaskDetail(id: number): Promise<TeachingTaskDetail> {
  return request(`/teachingTask/detail/${id}`, {
    method: 'GET',
  })
}

/**
 * 获取教学任务列表 (新版API)
 * @param params 查询参数
 * @returns 教学任务列表响应
 */
export function getTeachingTaskList(params: TeachingTaskQuery): Promise<TeachingTaskResponse> {
  return request('/home/<USER>', {
    method: 'GET',
    params,
  })
}

/**
 * 获取教学任务列表 (分页查询)
 * @param params 查询参数，包含分页和搜索关键词
 * @returns 教学任务列表响应数据
 */
export function getTeachingTaskListWithPagination(
  params: TeachingTaskPaginationQuery,
): Promise<TeachingTaskResponse> {
  return request('/home/<USER>', {
    method: 'GET',
    params,
  })
}

/**
 * 获取教学任务课程安排
 * @param params 教学任务课程安排请求参数
 * @returns 教学任务课程安排响应
 */
export function getTeachingScheduleArrangement(
  params: TeachingScheduleArrangementRequest,
): Promise<TeachingScheduleArrangementResponse['data']> {
  return request('/teacher/teachingTask/TeachingScheduleArrangement/jxrcap', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教学任务课程节次信息
 * @param params 教学任务课程节次请求参数
 * @returns 教学任务课程节次响应数据
 */
export function getTeachingTaskSessions(
  params: TeachingTaskSessionRequest,
): Promise<TeachingTaskSessionResponse['data']> {
  return request('/teacher/teachingTask/TeachingScheduleArrangement/jxrcap', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教学日程安排
 * @param params 教学日程安排请求参数
 * @returns 教学日程安排响应数据
 */
export function getTeachingSchedule(
  params: TeachingScheduleRequest,
): Promise<TeachingScheduleResponse['data']> {
  return request('/teacher/teachingTask/TeachingScheduleArrangement/jxrcap', {
    method: 'POST',
    data: params,
  })
}

/**
 * 检查教学安排冲突
 * @param params 教学安排检查请求参数
 * @returns 教学安排检查结果
 */
export function checkTeachingScheduleConflicts(
  params: TeachingScheduleCheckRequest,
): Promise<TeachingScheduleCheckItem[]> {
  return request('/teacher/teachingTask/TeachingScheduleArrangement/jxrcap', {
    method: 'POST',
    data: params,
  })
}

/**
 * 清空教学日程安排
 * @param jxrwid 教学任务ID
 * @returns 清空结果，true表示成功
 */
export function clearTeachingScheduleArrangement(jxrwid: string): Promise<boolean> {
  return request('/teacher/teachingTask/TeachingScheduleArrangement/clear', {
    method: 'POST',
    data: { jxrwid },
  })
}

/**
 * 获取教学任务安排列表
 * @param params 教学任务ID
 * @returns 教学任务安排列表数据
 */
export function getTeachingScheduleArrangementList(
  params: TeachingScheduleArrangementListRequest,
): Promise<TeachingScheduleArrangementListResponse> {
  return request('/teacher/teachingTask/TeachingScheduleArrangement/getList', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教学计划表
 * @param params 请求参数
 * @returns 教学计划表数据
 */
export function getTeachingPlanTable(
  params: TeachingPlanTableRequest,
): Promise<TeachingPlanTableResponse['data']> {
  return request('/teacher/teachingTask/teachingPlanTable', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教学任务学生列表
 * @param taskId 教学任务ID
 * @param params 查询参数
 * @returns 学生列表数据
 */
export function getTeachingTaskStudentList(
  taskId: number,
  params: StudentListQuery,
): Promise<StudentListResponse> {
  return request(`/teacher/teachingTask/studentList/${taskId}`, {
    method: 'GET',
    params,
  })
}

/**
 * 获取教学任务通知列表
 * @param teachTaskId 教学任务ID
 * @param params 查询参数
 * @returns 通知列表响应
 */
export function getTeachingTaskNoticeList(
  teachTaskId: number,
  params: TeachingTaskNoticeQuery,
): Promise<TeachingTaskNoticeResponse> {
  return request(`/teacher/teachingTask/noticeList/${teachTaskId}`, {
    method: 'GET',
    params,
  })
}
