import request from '@/utils/request'
import type {
  TextbookInfo,
  TextbookListQuery,
  TextbookListResponse,
  AddTextbookParams,
  ApprovalBranch,
  TextbookEvaluation,
  TeachingTask,
  AddTeachingMaterialParams,
  TeachingTaskMaterialQuery,
  TeachingTaskMaterialResponse,
  TextbookEvaluationParams,
  UpdateTeachingMaterialParams,
  ChangeTeachingMaterialParams,
  UpdateChangeTeachingMaterialParams,
} from '@/types/textbook'

/**
 * 获取教学任务教材列表
 * @param params 查询参数
 * @returns 教学任务教材列表数据
 */
export function getTeachingTaskMaterialList(
  params: TeachingTaskMaterialQuery,
): Promise<TeachingTaskMaterialResponse> {
  delete params.list_type
  return request('/teacher/teachingTask/teachingMaterial/list', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取教材详情
 * @param id 教材ID
 */
export function getTextbookDetail(id: number): Promise<TextbookInfo> {
  return request('/textbook/detail', {
    method: 'POST',
    data: { id },
  })
}

/**
 * 添加教材
 * @param data 教材信息
 */
export function addTextbook(data: AddTextbookParams): Promise<{ id: number }> {
  return request('/textbook/add', {
    method: 'POST',
    data,
  })
}

/**
 * 删除教材
 * @param id 教材ID
 */
export function deleteTextbook(id: number): Promise<void> {
  return request('/teacher/teachingTask/teachingMaterial/del', {
    method: 'POST',
    data: { id },
  })
}

/**
 * 获取教材审批记录
 * @param id 教材ID
 */
export function getTextbookApproval(id: number): Promise<ApprovalBranch[]> {
  return request('/textbook/approval/history', {
    method: 'POST',
    data: { id },
  })
}

/**
 * 获取教材评价列表
 * @param textbookId 教材ID
 */
export function getTextbookEvaluations(textbookId: number): Promise<TextbookEvaluation[]> {
  return request('/textbook/evaluation/list', {
    method: 'POST',
    data: { textbookId },
  })
}

/**
 * 添加教材评价
 * @param data 评价信息
 */
export function addTextbookEvaluation(data: {
  textbookId: number
  rating: number
  content: string
}): Promise<{ id: number }> {
  return request('/textbook/evaluation/add', {
    method: 'POST',
    data,
  })
}

/**
 * 获取教学任务列表
 */
export function getTeachingTasks(): Promise<TeachingTask[]> {
  return request('/textbook/teaching-tasks', {
    method: 'POST',
    data: {},
  })
}

/**
 * 获取教学任务详情
 * @param taskId 任务ID
 */
export function getTeachingTaskDetail(taskId: number): Promise<TeachingTask> {
  return request('/textbook/teaching-task/detail', {
    method: 'POST',
    data: { taskId },
  })
}

/**
 * 添加教材选用
 * @param data 教材选用信息
 */
export function addTeachingMaterial(data: AddTeachingMaterialParams): Promise<any> {
  return request('/teacher/teachingTask/teachingMaterial/add', {
    method: 'POST',
    data,
  })
}

/**
 * 更新教材
 * @param data 更新教材参数
 * @returns Promise
 */
export function updateTeachingMaterial(data: UpdateTeachingMaterialParams): Promise<any> {
  return request('/teacher/teachingTask/teachingMaterial/update', {
    method: 'POST',
    data,
  })
}

/**
 * 提交教材评价
 * @param data 教材评价信息
 */
export function submitTextbookEvaluation(data: TextbookEvaluationParams): Promise<any> {
  return request('/teacher/teachingTask/teachingMaterial/evaluation', {
    method: 'POST',
    data,
  })
}

/**
 * 提交教材变更申请
 * @param data 教材变更申请参数
 */
export function changeTeachingMaterial(data: ChangeTeachingMaterialParams): Promise<any> {
  return request('/teacher/teachingTask/teachingMaterial/changeApply', {
    method: 'POST',
    data,
  })
}

/**
 * 更新教材变更申请
 * @param data 更新教材变更申请参数
 */
export function updateChangeTeachingMaterial(
  data: UpdateChangeTeachingMaterialParams,
): Promise<any> {
  return request('/teacher/teachingTask/teachingMaterial/updateChangeApply', {
    method: 'POST',
    data,
  })
}
