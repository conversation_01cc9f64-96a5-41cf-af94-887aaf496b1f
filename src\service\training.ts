import request from '@/utils/request'
import type { TrainingConferenceQuery, TrainingConferenceResponse } from '@/types/training'

/**
 * 获取教师培训会议列表
 * @param params 查询参数
 * @returns 培训会议列表数据
 */
export function getTrainingConferenceList(
  params: TrainingConferenceQuery,
): Promise<TrainingConferenceResponse> {
  return request('/teacher/trainingConference', {
    method: 'POST',
    data: params,
  })
}
