import request from '@/utils/request'
import type { TransitQuery, TransitResponse } from '@/types/transit'

/**
 * 获取出入校记录列表
 * @param params 查询参数
 * @returns 出入校记录列表
 */
export function getTransitList(params: TransitQuery): Promise<TransitResponse> {
  return request('/transit', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取出入校记录统计数据
 * @returns 统计数据
 */
export function getTransitStats(): Promise<any> {
  return request('/transit/stats', {
    method: 'POST',
  })
}
