import request from '@/utils/request'
import {
  WorkflowQuery,
  WorkflowResponse,
  WorkflowDetailResponse,
  CompleteTaskRequest,
  TerminationTaskRequest,
  ApprovalTaskResponse,
  GetTaskRequest,
  TaskDetail,
  WorkflowTimelineRequest,
  WorkflowTimelineResponse,
  RevokeTaskRequest,
  WorkflowCategoryListResponse,
} from '@/types/workflow'

/**
 * 获取工作流列表
 * @param params 查询参数
 * @returns 工作流列表数据
 */
export function getWorkflowList(params: WorkflowQuery): Promise<WorkflowResponse> {
  return request('/home/<USER>', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取工作流列表
 * @param params 查询参数
 * @returns 工作流列表数据
 */
export function getWorkflowList4new(): Promise<WorkflowResponse> {
  return request('/home/<USER>', {
    method: 'POST',
    data: { todoType: 'new' },
  })
}

/**
 * 获取待办工作流列表
 * @param page 页码
 * @param pageSize 每页数量
 * @returns 工作流列表数据
 */
export function getTodoWorkflowList(page = 1, pageSize = 10): Promise<WorkflowResponse> {
  return getWorkflowList({
    page,
    pageSize,
    sortBy: 'status',
    sortOrder: 'asc',
    todoType: 'new',
    listType: 'list',
    id: [],
    form_title: [],
    create_user_name: [],
    applyTime: [],
    currentStepName: [],
    status: [],
    process_status: [],
    approval_opinion: [],
    checkTime: [],
  })
}

/**
 * 获取已办工作流列表
 * @param page 页码
 * @param pageSize 每页数量
 * @returns 工作流列表数据
 */
export function getDoneWorkflowList(page = 1, pageSize = 10): Promise<WorkflowResponse> {
  return getWorkflowList({
    page,
    pageSize,
    sortBy: 'checkTime',
    sortOrder: 'desc',
    todoType: 'done',
    listType: 'list',
    id: [],
    form_title: [],
    create_user_name: [],
    applyTime: [],
    currentStepName: [],
    status: [],
    process_status: [],
    approval_opinion: [],
    checkTime: [],
  })
}

/**
 * 获取工作流详情
 * @param id 工作流节点ID
 * @returns 工作流详情数据
 */
export function getWorkflowDetail(id: number | string): Promise<WorkflowDetailResponse> {
  return request(`/home/<USER>/${id}`, {
    method: 'GET',
  })
}

/**
 * 审批通过任务
 * @param params 审批参数
 * @returns 审批结果
 */
export function completeTask(params: CompleteTaskRequest): Promise<ApprovalTaskResponse> {
  return request('/workflow/task/completeTask', {
    method: 'POST',
    data: params,
  })
}

/**
 * 审批拒绝任务
 * @param params 拒绝参数
 * @returns 审批结果
 */
export function terminationTask(params: TerminationTaskRequest): Promise<ApprovalTaskResponse> {
  return request('/workflow/task/terminationTask', {
    method: 'POST',
    data: params,
  })
}

/**
 * 审批完成后的结束操作
 * @param params 审批参数
 * @returns 审批结果
 */
export function completeTaskEnd(params: CompleteTaskRequest): Promise<ApprovalTaskResponse> {
  return request('/workflow/task/completeTaskEnd', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取工作流任务详情
 * @param params 请求参数
 * @returns 任务详情
 */
export function getWorkflowTask(params: GetTaskRequest): Promise<TaskDetail> {
  return request('/workflow/task/getTask', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取工作流时间线
 * @param params 请求参数
 * @returns 工作流时间线数据
 */
export function getWorkflowTimeline(
  params: WorkflowTimelineRequest,
): Promise<WorkflowTimelineResponse> {
  return request('/workflow/task/timeline', {
    method: 'POST',
    data: params,
  })
}

/**
 * 撤销工作流任务
 * @param params 撤销参数
 * @returns 撤销结果
 */
export function revokeTask(params: RevokeTaskRequest): Promise<ApprovalTaskResponse> {
  return request('/workflow/task/terminationTask', {
    method: 'POST',
    data: params,
  })
}

/**
 * 获取工作流分类列表
 * @returns 工作流分类列表
 */
export function getWorkflowCategoryList(): Promise<WorkflowCategoryListResponse> {
  return request('/workflow/category/list', {
    method: 'GET',
  })
}
