import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { AttendLectureItem } from '@/types/teacher'

// 定义选中课程的类型
export interface SelectedCourse {
  id: number
  jxrwid: number
  courseName: string
  courseCode: string
  teacherName: string
  className: string
  location: string
  dateTime: {
    date: string
    weekday: string
    time: string
    section: string
  }
}

/**
 * 听课记录相关状态管理
 */
export const useAttendLectureStore = defineStore(
  'attend-lecture',
  () => {
    // 当前选择的学年
    const selectedYear = ref<string>('')
    const selectedTerm = ref<string>('')
    // 当前选中的课程
    const selectedCourse = ref<SelectedCourse | null>(null)

    // 当前选中的完整听课记录
    const selectedLectureRecord = ref<AttendLectureItem | null>(null)

    // 设置当前选择的学年
    const setSelectedYear = (year: string) => {
      selectedYear.value = year
    }
    const setSelectedTerm = (term: string) => {
      selectedTerm.value = term
    }
    // 设置当前选中的课程
    const setSelectedCourse = (course: SelectedCourse) => {
      selectedCourse.value = course
    }

    // 设置当前选中的完整听课记录
    const setSelectedLectureRecord = (record: AttendLectureItem) => {
      selectedLectureRecord.value = record
    }

    // 清除当前选中的课程
    const clearSelectedCourse = () => {
      selectedCourse.value = null
    }

    // 清除当前选中的完整听课记录
    const clearSelectedLectureRecord = () => {
      selectedLectureRecord.value = null
      selectedCourse.value = null
    }

    return {
      selectedYear,
      selectedTerm,
      selectedCourse,
      selectedLectureRecord,
      setSelectedYear,
      setSelectedTerm,
      setSelectedCourse,
      setSelectedLectureRecord,
      clearSelectedCourse,
      clearSelectedLectureRecord,
    }
  },
  {
    persist: true, // 开启持久化
  },
)
