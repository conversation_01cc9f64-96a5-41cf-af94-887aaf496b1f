import { defineStore } from 'pinia'
import { ref } from 'vue'

// 竞赛项目信息接口
interface ICompetitionInfo {
  projectId: string // 项目ID
  // 后续可以根据需要添加更多字段
}

// 初始化竞赛项目信息
const initCompetitionState: ICompetitionInfo = {
  projectId: '',
}

export const useCompetitionStore = defineStore(
  'competition',
  () => {
    const competitionInfo = ref<ICompetitionInfo>({ ...initCompetitionState })

    // 设置项目ID
    const setProjectId = (id: string) => {
      competitionInfo.value.projectId = id
    }

    // 获取项目ID
    const getProjectId = () => {
      return competitionInfo.value.projectId
    }

    // 清除竞赛项目信息
    const clearCompetitionInfo = () => {
      competitionInfo.value = { ...initCompetitionState }
    }

    return {
      competitionInfo,
      setProjectId,
      getProjectId,
      clearCompetitionInfo,
    }
  },
  {
    persist: true, // 开启持久化存储
  },
)
