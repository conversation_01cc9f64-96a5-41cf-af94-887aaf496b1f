import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { DictData } from '@/types/system'

export const useDictStore = defineStore(
  'dict',
  () => {
    // 使用ref存储字典缓存
    const dictCache = ref<Record<string, DictData[]>>({})

    // 设置字典缓存
    const setDictCache = (dictType: string, data: DictData[]) => {
      dictCache.value[dictType] = data
    }

    // 获取字典缓存
    const getDictCache = (dictType: string) => {
      return dictCache.value[dictType]
    }

    // 检查字典是否存在于缓存中
    const hasDictCache = (dictType: string) => {
      return !!dictCache.value[dictType]
    }

    // 清除某个字典的缓存
    const clearDictCache = (dictType: string) => {
      if (dictCache.value[dictType]) {
        delete dictCache.value[dictType]
      }
    }

    // 清除所有字典缓存
    const clearAllDictCache = () => {
      dictCache.value = {}
    }

    return {
      dictCache,
      setDictCache,
      getDictCache,
      hasDictCache,
      clearDictCache,
      clearAllDictCache,
    }
  },
  {
    persist: true, // 启用持久化存储
  },
)
