import { defineStore } from 'pinia'
import { ref } from 'vue'

// 课程信息接口定义
export interface CourseInfo {
  courseName: string
  scheduleDate: string
  sections: string
  venue: string
  // 调课计划查询所需的参数
  scheduleParams?: {
    changeType: number
    scheduleId: number
    teachTaskId: number
  }
  // 可能还包含其他相关信息
  [key: string]: any
}

// PC端调课数据格式接口定义
export interface AdjustCourseData {
  type: 'stop' | 'change' | 'make_up' // 停课、调课或补课
  scheduleId: number
  key: string
  teachingTaskId: number
  sourceRemark: string
  targetRemark: string
  ks: number
  targetWeek: number
  targetDate: string
  targetSection: string
  targetVenueCode: string | undefined
  targetVenueName?: string // 添加场地名称字段
  teacherCode: string
  teachingClassCode: string
  mergeTeachingTaskId: number | null
  _X_ROW_KEY: string
  // 补课特有字段
  jxrwid?: number
}

export const useMediationCourseStore = defineStore('mediationCourse', () => {
  // 当前选中的课程信息
  const currentCourseInfo = ref<CourseInfo | null>(null)

  // 调课数据存储 - 按PC端格式存储，key为scheduleId
  const adjustCourseDataMap = ref<Record<string, AdjustCourseData>>({})

  // 调停课时间范围
  const timeRange = ref<{
    startDate: number
    endDate: number
  }>({
    startDate: Date.now() - 60 * 24 * 60 * 60 * 1000, // 默认前60天
    endDate: Date.now() - 30 * 24 * 60 * 60 * 1000, // 默认前30天
  })

  // 设置当前课程信息
  const setCurrentCourseInfo = (courseInfo: CourseInfo) => {
    currentCourseInfo.value = courseInfo
  }

  // 清除当前课程信息
  const clearCurrentCourseInfo = () => {
    currentCourseInfo.value = null
  }

  // 获取当前课程信息
  const getCurrentCourseInfo = () => {
    return currentCourseInfo.value
  }

  // 添加调课数据
  const addAdjustCourseData = (scheduleId: string, data: AdjustCourseData) => {
    adjustCourseDataMap.value[scheduleId] = data
    console.log('调课数据已存储:', adjustCourseDataMap.value)
  }

  // 获取所有调课数据
  const getAllAdjustCourseData = () => {
    return adjustCourseDataMap.value
  }

  // 获取指定调课数据
  const getAdjustCourseData = (scheduleId: string) => {
    return adjustCourseDataMap.value[scheduleId]
  }

  // 清除调课数据
  const clearAdjustCourseData = () => {
    adjustCourseDataMap.value = {}
  }

  // 移除指定调课数据
  const removeAdjustCourseData = (scheduleId: string) => {
    delete adjustCourseDataMap.value[scheduleId]
  }

  // 设置时间范围
  const setTimeRange = (startDate: number, endDate: number) => {
    timeRange.value = { startDate, endDate }
  }

  // 获取时间范围
  const getTimeRange = () => {
    return timeRange.value
  }

  // 格式化时间为 YYYY-MM-DD 格式
  const formatTimeToDate = (timestamp: number): string => {
    return new Date(timestamp).toISOString().split('T')[0]
  }

  return {
    currentCourseInfo,
    adjustCourseDataMap,
    timeRange,
    setCurrentCourseInfo,
    clearCurrentCourseInfo,
    getCurrentCourseInfo,
    addAdjustCourseData,
    getAllAdjustCourseData,
    getAdjustCourseData,
    clearAdjustCourseData,
    removeAdjustCourseData,
    setTimeRange,
    getTimeRange,
    formatTimeToDate,
  }
})
