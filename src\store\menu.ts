import { defineStore } from 'pinia'

export interface MenuMeta {
  icon: string
  title: string
  id: number
  name: string
  url: string
  isCom: string
  hideInMenu?: boolean
  noBasicLayout?: boolean
}

export interface MenuItem {
  id: number
  pid: number
  type: string
  meta: MenuMeta
  name: string
  path: string
  redirect: string | null
  component: string
  keepAlive: boolean
  children?: MenuItem[]
}

export const useMenuStore = defineStore('menu', {
  state: () => {
    return {
      menus: [] as MenuItem[],
    }
  },

  actions: {
    setMenus(menus: MenuItem[]) {
      this.menus = menus
    },

    // 获取所有菜单
    getAllMenus() {
      return this.menus
    },

    // 获取扁平化的菜单列表(不包含父级菜单)
    getFlatMenus() {
      const flatMenus: MenuItem[] = []
      const flatten = (items: MenuItem[]) => {
        items.forEach((item) => {
          if (item.children && item.children.length > 0) {
            flatten(item.children)
          } else {
            flatMenus.push(item)
          }
        })
      }
      flatten(this.menus)
      return flatMenus
    },

    // 根据路径查找菜单
    findMenuByPath(path: string) {
      const flatMenus = this.getFlatMenus()
      return flatMenus.find((item) => item.path === path)
    },

    // 清空菜单
    clearMenus() {
      this.menus = []
    },
  },

  persist: false, // 开启持久化
})
