import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { TeachingMaterialNoNeedItem } from '@/types/teachingMaterial'

export const useNoNeedMaterialStore = defineStore(
  'noNeedMaterial',
  () => {
    // 当前选中的无需征订教材任务
    const currentNoNeedMaterial = ref<TeachingMaterialNoNeedItem | null>(null)

    // 设置当前无需征订教材任务
    const setCurrentNoNeedMaterial = (material: TeachingMaterialNoNeedItem) => {
      currentNoNeedMaterial.value = material
    }

    // 清除当前无需征订教材任务
    const clearCurrentNoNeedMaterial = () => {
      currentNoNeedMaterial.value = null
    }

    // 获取当前无需征订教材任务
    const getCurrentNoNeedMaterial = () => {
      return currentNoNeedMaterial.value
    }

    return {
      currentNoNeedMaterial,
      setCurrentNoNeedMaterial,
      clearCurrentNoNeedMaterial,
      getCurrentNoNeedMaterial,
    }
  },
  {
    persist: {
      storage: localStorage,
      paths: ['currentNoNeedMaterial'],
    },
  },
)
