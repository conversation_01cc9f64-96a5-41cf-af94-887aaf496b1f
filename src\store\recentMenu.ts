import { defineStore } from 'pinia'
import type { MenuItem } from './menu'

/**
 * 最近访问菜单项接口
 */
export interface RecentMenuItem {
  id: number
  title: string
  path: string
  icon: string
  color: string
  count: number
  lastVisitTime: number
}

// 学生默认菜单数据
const defaultStudentMenus: RecentMenuItem[] = [
  {
    id: 165,
    title: '我的个人信息',
    path: '/student/my-info',
    icon: 'user-avatar',
    color: 'blue',
    count: 1,
    lastVisitTime: 1745285651681,
  },
  {
    id: 176,
    title: '我的班级同学',
    path: '/student-info/my-classmates',
    icon: 'group-presentation',
    color: 'green',
    count: 1,
    lastVisitTime: 1745285653950,
  },
  {
    id: 244,
    title: '我的课表',
    path: '/student/StudentSchedule/scheduleTable',
    icon: 'calendar',
    color: 'green',
    count: 1,
    lastVisitTime: 1745285655955,
  },
  {
    id: 228,
    title: '我的选课列表',
    path: '/student/select-course',
    icon: 'data-table',
    color: 'teal',
    count: 1,
    lastVisitTime: 1745285664593,
  },
  {
    id: 210,
    title: '日常成绩查询',
    path: '/student/score',
    icon: 'report',
    color: 'yellow',
    count: 1,
    lastVisitTime: 1745285666269,
  },
  {
    id: 177,
    title: '我的总评成绩',
    path: '/student/total-score/index',
    icon: 'chart-bar',
    color: 'purple',
    count: 1,
    lastVisitTime: 1745285668094,
  },
  {
    id: 175,
    title: '我的考勤',
    path: '/attendance',
    icon: 'time',
    color: 'red',
    count: 1,
    lastVisitTime: 1745285669757,
  },
  {
    id: 163,
    title: '我的请假',
    path: '/myLeave',
    icon: 'edit',
    color: 'orange',
    count: 1,
    lastVisitTime: 1745285672370,
  },
]

// 教师默认菜单数据
const defaultTeacherMenus: RecentMenuItem[] = [
  {
    id: 163,
    title: '我的教学任务',
    path: '/teacher/teaching-affairs/teaching-tasks',
    icon: 'task',
    color: 'blue',
    count: 1,
    lastVisitTime: 1745285672370,
  },
  {
    id: 297,
    title: '我的工资',
    path: '/Salary/salaryInfo',
    icon: 'money',
    color: 'orange',
    count: 1,
    lastVisitTime: 1745285705590,
  },
  {
    id: 295,
    title: '个人信息',
    path: '/Information/teacherInfo',
    icon: 'user-avatar',
    color: 'blue',
    count: 1,
    lastVisitTime: 1745285707518,
  },
  {
    id: 294,
    title: '教工通讯录',
    path: '/Information/teacherContacts',
    icon: 'phone',
    color: 'teal',
    count: 1,
    lastVisitTime: 1745285708928,
  },
  {
    id: 271,
    title: '非教学工作量',
    path: '/teacher/teacher-profile/non-workload',
    icon: 'document-tasks',
    color: 'green',
    count: 1,
    lastVisitTime: 1745285711898,
  },
  {
    id: 293,
    title: '教师课程表',
    path: '/teacher/scheduleTable',
    icon: 'calendar',
    color: 'green',
    count: 1,
    lastVisitTime: 1745285714183,
  },
  {
    id: 264,
    title: '我的调停课',
    path: '/teacher/teaching-affairs/mediation-course',
    icon: 'edit',
    color: 'yellow',
    count: 1,
    lastVisitTime: 1745285716538,
  },
  {
    id: 258,
    title: '教学日志信息',
    path: '/teacher/teaching-affairs/teaching-plan',
    icon: 'notebook',
    color: 'purple',
    count: 1,
    lastVisitTime: 1745285718421,
  },
]

export const useRecentMenuStore = defineStore('recentMenu', {
  state: () => {
    return {
      recentMenus: [] as RecentMenuItem[],
      // 最大记录条数
      maxRecentItems: 8,
    }
  },

  getters: {
    /**
     * 获取最近访问的菜单列表（按最后访问时间排序）
     */
    getRecentMenusByTime(): RecentMenuItem[] {
      return [...this.recentMenus].sort((a, b) => b.lastVisitTime - a.lastVisitTime)
    },

    /**
     * 获取访问次数最多的菜单列表
     */
    getRecentMenusByCount(): RecentMenuItem[] {
      return [...this.recentMenus].sort((a, b) => b.count - a.count)
    },
  },

  actions: {
    /**
     * 记录菜单访问
     * @param menu 菜单项
     * @param iconName 图标名称
     * @param colorName 颜色名称
     */
    recordMenuVisit(menu: MenuItem, iconName: string, colorName: string) {
      // 查找是否已存在该菜单记录
      const index = this.recentMenus.findIndex((item) => item.id === menu.id)
      const now = Date.now()

      if (index !== -1) {
        // 更新已有记录
        this.recentMenus[index].count++
        this.recentMenus[index].lastVisitTime = now
      } else {
        // 添加新记录
        const newMenuItem: RecentMenuItem = {
          id: menu.id,
          title: menu.meta.title,
          path: menu.path,
          icon: iconName,
          color: colorName,
          count: 1,
          lastVisitTime: now,
        }

        // 如果超过最大记录数，移除访问次数最少的记录
        if (this.recentMenus.length >= this.maxRecentItems) {
          // 按访问次数排序
          const sortedMenus = [...this.recentMenus].sort((a, b) => a.count - b.count)
          // 移除访问次数最少的
          const leastUsedMenu = sortedMenus[0]
          const removeIndex = this.recentMenus.findIndex((item) => item.id === leastUsedMenu.id)
          this.recentMenus.splice(removeIndex, 1)
        }

        // 添加新记录
        this.recentMenus.push(newMenuItem)
      }
    },

    /**
     * 根据用户类型初始化默认菜单
     * @param userType 用户类型：1-学生，2-教师
     */
    initDefaultMenus(userType: number) {
      // 只有在没有菜单记录时才初始化默认菜单
      if (this.recentMenus.length === 0) {
        if (userType === 1) {
          // 学生
          this.recentMenus = [...defaultStudentMenus]
        } else if (userType === 2) {
          // 教师
          this.recentMenus = [...defaultTeacherMenus]
        }
      }
    },

    /**
     * 清空最近访问记录
     */
    clearRecentMenus() {
      this.recentMenus = []
    },
  },

  persist: true, // 开启持久化
})
