import { defineStore } from 'pinia'
import { ref } from 'vue'
import {
  getTeachingScheduleArrangement,
  getTeachingSchedule,
  clearTeachingScheduleArrangement,
} from '@/service/teachingTask'
import {
  TeachingScheduleArrangementResponse,
  TeachingScheduleResponse,
  TeachingScheduleCheckRequest,
  TeachingScheduleCheckItem,
} from '@/types/teachingTask'

// 定义表单数据接口
interface FormData {
  className: string
  courseName: string
  courseHours: number
  weeklyHours: number
  weeks: number
  scheduleConfig: string[]
  frequency: number
}

// 定义日程开放时间接口
interface ScheduleTime {
  openTime: string
}

// 定义时间安排项接口
interface TimeArrangementItem {
  id: number
  weekType: string
  weekDay: string
  period: string
}

// 定义周使用项接口
interface WeekItem {
  week: number
  hasClass: boolean
}

// 定义教学任务日常安排Store
export const useTeachingDailyArrangementStore = defineStore(
  'teachingDailyArrangement',
  () => {
    // 表单数据
    const formData = ref<FormData>({
      className: '',
      courseName: '',
      courseHours: 0,
      weeklyHours: 0,
      weeks: 0,
      scheduleConfig: [],
      frequency: 1,
    })
    const stepOneResponse = ref<TeachingScheduleArrangementResponse['data']>(null)

    // 校历信息
    const calendarInfo = ref<string[]>([])

    // 教学日程安排数据
    const teachingScheduleData = ref<TeachingScheduleResponse['data']>(null)

    // 日程开放时间
    const scheduleTime = ref<ScheduleTime>({
      openTime: '',
    })

    // 时间安排
    const timeArrangements = ref<TimeArrangementItem[]>([])

    // 周使用情况
    const weekUsage = ref<WeekItem[]>([])

    // 实习周安排
    const internshipArrangement = ref<string>('')

    // 是否禁用编辑
    const disabled = ref<boolean>(false)

    // 教学安排检查参数
    const scheduleCheckParams = ref<TeachingScheduleCheckRequest | null>(null)

    // 教学安排检查结果
    const scheduleCheckResults = ref<TeachingScheduleCheckItem[]>([])

    // 解析排课进程说明，生成时间安排数据
    const parseScheduleConfig = (scheduleConfig: string[]) => {
      const result: TimeArrangementItem[] = []

      scheduleConfig.forEach((item, index) => {
        // 解析格式如："每周|1-19 周1 5-6节", "双周|1-19 周4 3-4节"
        const parts = item.split('|')
        if (parts.length !== 2) return

        // 解析周类型：每周/单周/双周
        let weekType = '0' // 默认为全部(每周)
        if (parts[0] === '单周') weekType = '1'
        else if (parts[0] === '双周') weekType = '2'

        // 解析后半部分："1-19 周1 5-6节"
        const detailParts = parts[1].trim().split(' ')
        if (detailParts.length < 2) return

        // 解析星期："周1" -> "1"
        const weekDay = detailParts[1].replace('周', '')

        // 解析节次："5-6节" -> "5"（简化处理，只取开始节次）
        const periodPart = detailParts[2] || ''

        const period = periodPart

        result.push({
          id: index + 1,
          weekType,
          weekDay,
          period,
        })
      })

      return result
    }

    // 解析周次范围，生成周使用情况数据
    const parseWeekRange = (scheduleConfig: string[], totalWeeks: number) => {
      // 初始化所有周为不上课
      const result: WeekItem[] = Array.from({ length: totalWeeks }, (_, i) => ({
        week: i + 1,
        hasClass: false,
      }))

      scheduleConfig.forEach((item) => {
        // 解析格式如："每周|1-19 周1 5-6节"
        const parts = item.split('|')
        if (parts.length !== 2) return

        // 解析周次范围："1-19"
        const detailParts = parts[1].trim().split(' ')
        if (detailParts.length < 1) return

        const weekRange = detailParts[0]
        const weekRangeParts = weekRange.split('-')

        if (weekRangeParts.length === 2) {
          const startWeek = parseInt(weekRangeParts[0])
          const endWeek = parseInt(weekRangeParts[1])

          // 设置范围内的周为上课
          for (let i = startWeek - 1; i < endWeek && i < result.length; i++) {
            result[i].hasClass = true
          }
        }
      })

      return result
    }

    // 获取教学日程安排数据
    const fetchTeachingScheduleData = async (taskId: string) => {
      console.log('开始获取教学日程安排数据，taskId:', taskId)

      if (!taskId) {
        console.log('taskId为空，无法获取教学日程安排数据')
        uni.showToast({
          title: '缺少任务ID参数',
          icon: 'none',
        })
        return
      }

      try {
        // 获取教学日程安排数据
        const response = await getTeachingSchedule({
          jxrwid: taskId,
          step: 3,
        })

        console.log('获取到教学日程安排数据:', response)

        // 存储教学日程安排数据
        teachingScheduleData.value = response

        console.log('教学日程安排数据获取和处理完成')
      } catch (error) {
        console.error('获取教学日程安排失败:', error)
      }
    }

    // 解析校历信息
    const parseCalendarInfo = (xlxx: string): string[] => {
      if (!xlxx) return []
      // 按换行符分割字符串，并过滤掉空行
      return xlxx.split('\r\n').filter((item) => item.trim() !== '')
    }

    // 获取教学任务课程安排数据
    const fetchTeachingScheduleArrangement = async (taskId: string) => {
      console.log('开始获取教学任务课程安排数据，taskId:', taskId)

      if (!taskId) {
        console.log('taskId为空，无法获取教学任务课程安排数据')
        uni.showToast({
          title: '缺少任务ID参数',
          icon: 'none',
        })
        return
      }

      try {
        // 获取教学任务课程安排数据
        const response = await getTeachingScheduleArrangement({
          jxrwid: taskId,
          step: 1,
        })

        console.log('获取到教学任务课程安排数据:', response)

        // 处理获取到的数据
        if (response) {
          const { jxrw, kfsj, data, xlxx } = response
          stepOneResponse.value = response

          // 处理校历信息
          if (xlxx) {
            calendarInfo.value = parseCalendarInfo(xlxx)
          }

          if (jxrw) {
            // 获取排课/进程信息数组
            const scheduleConfig = data && data.pkjcsm ? data.pkjcsm : []

            // 获取上课次数
            const frequency = data && data.skcs ? data.skcs : 1

            // 获取总周数
            const totalWeeks = response.zs || 0

            // 更新表单数据
            formData.value = {
              className: jxrw.bjmc || '',
              courseName: `${jxrw.kcmc} [${jxrw.ksz} - ${jxrw.jsz}周] ${jxrw.xn}(${jxrw.xq})`,
              courseHours: Number(jxrw.xqzxs) || 0,
              weeklyHours: Number(jxrw.zxs) || 0,
              weeks: totalWeeks,
              scheduleConfig,
              frequency,
            }

            // 解析排课进程说明，生成时间安排数据
            timeArrangements.value = parseScheduleConfig(scheduleConfig)

            // 解析周次范围，生成周使用情况数据
            const weekUsageData = parseWeekRange(scheduleConfig, totalWeeks)

            // 如果有排课/进程信息，设置为禁用状态
            disabled.value = scheduleConfig.length > 0

            // 如果处于disabled状态，确保第一周被选中
            if (disabled.value && weekUsageData.length > 0) {
              weekUsageData[0].hasClass = true
            }

            // 更新周使用情况
            weekUsage.value = weekUsageData
          }

          // 更新日程开放时间
          if (kfsj) {
            scheduleTime.value = { openTime: kfsj }
          }
        }

        console.log('教学任务课程安排数据获取和处理完成')
      } catch (error) {
        console.error('获取教学任务课程安排失败:', error)
        uni.showToast({
          title: '获取课程安排失败',
          icon: 'none',
        })
      }
    }

    // 清空日常安排
    const clearArrangement = async () => {
      // 获取当前任务ID
      const currentTaskId = stepOneResponse.value?.jxrw?.id

      if (!currentTaskId) {
        uni.showToast({
          title: '缺少任务ID参数',
          icon: 'none',
        })
        return
      }

      try {
        // 调用API清空日常安排
        const result = await clearTeachingScheduleArrangement(String(currentTaskId))

        if (result) {
          // 清空成功，重置本地状态
          formData.value = {
            className: '',
            courseName: '',
            courseHours: 0,
            weeklyHours: 0,
            weeks: 0,
            scheduleConfig: [],
            frequency: 1,
          }
          scheduleTime.value = { openTime: '' }
          timeArrangements.value = []
          weekUsage.value = []
          internshipArrangement.value = ''
          disabled.value = false
          teachingScheduleData.value = null
          calendarInfo.value = []
          scheduleCheckParams.value = null
          scheduleCheckResults.value = []

          // 重新获取数据
          fetchTeachingScheduleArrangement(String(currentTaskId))

          uni.showToast({
            title: '日常安排已清空',
            icon: 'success',
          })
        }
      } catch (error) {
        console.error('清空日常安排失败:', error)
      }
    }

    // 更新上课频率
    const updateFrequency = (value: number) => {
      // 确保值不小于1
      formData.value.frequency = Math.max(1, value)
    }

    // 设置禁用状态
    const setDisabled = (value: boolean) => {
      disabled.value = value
    }

    // 设置教学安排检查参数
    const setScheduleCheckParams = (params: TeachingScheduleCheckRequest) => {
      scheduleCheckParams.value = params
    }

    // 设置教学安排检查结果
    const setScheduleCheckResults = (results: TeachingScheduleCheckItem[]) => {
      scheduleCheckResults.value = results
    }

    return {
      formData,
      scheduleTime,
      timeArrangements,
      weekUsage,
      internshipArrangement,
      disabled,
      teachingScheduleData,
      calendarInfo,
      scheduleCheckParams,
      scheduleCheckResults,
      fetchTeachingScheduleData,
      fetchTeachingScheduleArrangement,
      clearArrangement,
      updateFrequency,
      setDisabled,
      setScheduleCheckParams,
      setScheduleCheckResults,
    }
  },
  {
    persist: {
      storage: localStorage,
      paths: [
        'formData',
        'scheduleTime',
        'timeArrangements',
        'weekUsage',
        'internshipArrangement',
        'teachingScheduleData',
        'calendarInfo',
        'scheduleCheckParams',
        'scheduleCheckResults',
      ],
    },
  },
)
