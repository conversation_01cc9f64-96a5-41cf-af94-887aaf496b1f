import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { TeachingTaskItem } from '@/types/teachingTask'

export const useTeachingTaskStore = defineStore(
  'teachingTask',
  () => {
    // 当前选中的教学任务
    const currentTask = ref<TeachingTaskItem | null>(null)
    // 当前教材的教学任务ID
    const currentJxrwid = ref<string>('')
    // 当前教材的标题
    const currentTitle = ref<string>('')

    // 设置当前教学任务
    const setCurrentTask = (task: TeachingTaskItem) => {
      currentTask.value = task
    }

    // 清除当前教学任务
    const clearCurrentTask = () => {
      currentTask.value = null
    }

    // 获取当前教学任务
    const getCurrentTask = () => {
      return currentTask.value
    }

    // 设置当前教材的教学任务ID和标题
    const setCurrentMaterialTask = (jxrwid: string, title: string) => {
      currentJxrwid.value = jxrwid
      currentTitle.value = title
    }

    // 获取当前教材的教学任务ID
    const getCurrentJxrwid = () => {
      return currentJxrwid.value
    }

    // 获取当前教材的标题
    const getCurrentTitle = () => {
      return currentTitle.value
    }

    // 清除当前教材的教学任务ID和标题
    const clearCurrentMaterialTask = () => {
      currentJxrwid.value = ''
      currentTitle.value = ''
    }

    return {
      currentTask,
      currentJxrwid,
      currentTitle,
      setCurrentTask,
      clearCurrentTask,
      getCurrentTask,
      setCurrentMaterialTask,
      getCurrentJxrwid,
      getCurrentTitle,
      clearCurrentMaterialTask,
    }
  },
  {
    persist: {
      storage: localStorage,
      paths: ['currentTask', 'currentJxrwid', 'currentTitle'],
    },
  },
)
