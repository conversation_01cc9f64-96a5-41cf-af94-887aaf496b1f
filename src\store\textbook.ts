import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { TeachingTaskMaterialItem } from '@/types/textbook'

export const useTextbookStore = defineStore(
  'textbook',
  () => {
    // 当前选中的教材
    const currentTextbook = ref<TeachingTaskMaterialItem | null>(null)

    // 设置当前教材
    const setCurrentTextbook = (textbook: TeachingTaskMaterialItem) => {
      currentTextbook.value = textbook
    }

    // 清除当前教材
    const clearCurrentTextbook = () => {
      currentTextbook.value = null
    }

    // 获取当前教材
    const getCurrentTextbook = () => {
      return currentTextbook.value
    }

    return {
      currentTextbook,
      setCurrentTextbook,
      clearCurrentTextbook,
      getCurrentTextbook,
    }
  },
  {
    persist: {
      storage: localStorage,
      paths: ['currentTextbook'],
    },
  },
)
