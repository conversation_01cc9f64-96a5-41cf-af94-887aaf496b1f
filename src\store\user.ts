import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { clearDictCache } from '@/utils/dict'
import { useRecentMenuStore } from './recentMenu'

interface IUserInfo {
  id: number
  realname: string
  username: string
  avatar: string
  last_login_time: string
  roleName: string
  nickname: string
  openid: string
  token: string
  department: string
  className: string
  userType?: number // 1: 学生, 2: 教师
  jobName?: string // 职称
  email?: string
  gender?: string
  phone?: string
}

// 重定向信息接口
interface IRedirectInfo {
  path: string
  query: Record<string, string>
}

const initUserState: IUserInfo = {
  id: 0,
  realname: '',
  username: '',
  avatar: '',
  last_login_time: '',
  roleName: '',
  nickname: '',
  openid: '',
  token: '',
  department: '',
  className: '',
  // 可选字段不需要初始化值
}

const initTokenState = {
  token: '',
  refresh_token: '',
}

// 初始化重定向信息
const initRedirectInfo: IRedirectInfo = {
  path: '',
  query: {},
}

export const useUserStore = defineStore(
  'user',
  () => {
    const userInfo = ref<IUserInfo>({ ...initUserState })
    const tokenInfo = ref({ ...initTokenState })
    const accessCodes = ref<string[]>([])
    const redirectInfo = ref<IRedirectInfo>({ ...initRedirectInfo })

    const setUserInfo = (val: IUserInfo) => {
      // 处理头像URL，如果不是以https://开头，则添加前缀
      if (val.avatar && !val.avatar.startsWith('http')) {
        val.avatar = `https://${val.avatar}`
      }
      userInfo.value = val
    }

    const setTokenInfo = (val: { token: string; refresh_token: string }) => {
      tokenInfo.value = val
    }

    const setAccessCodes = (codes: string[]) => {
      accessCodes.value = codes
    }

    /**
     * 设置重定向信息
     * @param info 重定向信息
     * @param force 是否强制覆盖已有值
     */
    const setRedirectInfo = (info: IRedirectInfo, force: boolean = false) => {
      // 如果重定向信息已存在且force为false，则不更新
      if (!force && redirectInfo.value.path !== initRedirectInfo.path) {
        console.log('重定向信息已存在，不进行覆盖更新')
        return
      }
      redirectInfo.value = info
    }

    /**
     * 清除重定向信息
     */
    const clearRedirectInfo = () => {
      redirectInfo.value = { ...initRedirectInfo }
    }

    const clearUserInfo = () => {
      userInfo.value = { ...initUserState }
      tokenInfo.value = { ...initTokenState }
      accessCodes.value = []
    }

    const reset = () => {
      clearUserInfo()
      clearRedirectInfo()
    }

    const logout = () => {
      clearUserInfo()
      // 清除持久化存储
      uni.removeStorageSync('user')
      // 清空字典缓存数据
      clearDictCache()
      // 清空最近访问菜单记录
      const recentMenuStore = useRecentMenuStore()
      recentMenuStore.clearRecentMenus()
    }

    const isLogined = computed(() => !!tokenInfo.value.token)

    return {
      userInfo,
      tokenInfo,
      accessCodes,
      redirectInfo,
      setUserInfo,
      setTokenInfo,
      setAccessCodes,
      setRedirectInfo,
      clearRedirectInfo,
      clearUserInfo,
      isLogined,
      reset,
      logout,
    }
  },
  {
    persist: true,
  },
)
