// @import './iconfont.css';

.test {
  // 可以通过 @apply 多个样式封装整体样式
  @apply mt-4 ml-4;

  padding-top: 4px;
  color: red;
}

:root,
page {
  // 修改按主题色
  // --wot-color-theme: #37c2bc;

  // 修改按钮背景色
  // --wot-button-primary-bg-color: green;
}

@font-face {
  font-family: 'wd-icons';
  font-style: normal;
  font-weight: normal;
  src:
    url('/static/font/font_4245058_s5cpwl25n7o.woff2?t=1696817709651') format('woff2'),
    url('/static/font/font_4245058_s5cpwl25n7o.woff?t=1696817709651') format('woff'),
    url('/static/font/font_4245058_s5cpwl25n7o.ttf?t=1696817709651') format('truetype');
}
