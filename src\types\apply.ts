/**
 * 申请表单相关类型定义
 */

/**
 * 申请表单查询参数
 */
export interface ApplyListQuery {
  /** 页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 排序字段 */
  sortBy?: string
  /** 排序方式 */
  sortOrder?: 'asc' | 'desc'
  /** 表单标题 */
  form_title?: string
  /** 分类编码 */
  category?: string
  /** 创建时间 */
  create_time?: string
  /** 节点名称 */
  name?: string
  /** 流程状态：0-待审批，1-已审批，2-已拒绝 */
  process_status?: number[]
  /** 流程类型 */
  processType?: string[]
  /** 审批人 */
  user_name?: string
  /** 更新时间 */
  update_time?: string
}

/**
 * 申请表单列表项
 */
export interface ApplyListItem {
  /** 表单标题 */
  form_title: string
  /** 标题 */
  title: string
  /** 创建时间 */
  create_time: string
  /** 更新时间 */
  update_time: string
  /** 流程状态：0-待审批，1-已审批，2-已拒绝 */
  process_status: string
  /** 节点名称 */
  name: string
  /** 审批人 */
  user_name: string
  /** ID */
  id: number
}

/**
 * 申请表单列表响应数据
 */
export interface ApplyListResponse {
  /** 申请表单列表 */
  items: ApplyListItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总数 */
  total: number
}
