/**
 * 听课评价项目相关类型定义
 */

/**
 * 听课评价项目请求参数
 */
export interface EvaluationProjectQuery {
  /** 学年学期，格式为：学年|学期，如：2024-2025|2 */
  semesters: string
}

/**
 * 听课评价项目单项
 */
export interface EvaluationProjectItem {
  /** 评价项目ID */
  id: number
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 使用对象 */
  sydx: string
  /** 使用对象名称 */
  sydxmc: string
  /** 使用教学任务类别 */
  syjxrwlb: string
  /** 使用教学任务类别名称 */
  syjxrwlbmc: string
  /** 题目类型 */
  tmlx: string
  /** 题目类型名称 */
  tmlxmc: string
  /** 测评题目 */
  cptm: string
  /** 测评选项，格式为：选项1|分值1&&选项2|分值2 */
  cpxx: string
  /** 测评题目说明 */
  cptmsm: string
  /** 排序号 */
  pxh: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
}

/**
 * 听课评价项目响应数据
 */
export interface EvaluationProjectData {
  /** 评价项目列表 */
  items: EvaluationProjectItem[]
  /** 消息 */
  message: string
}

/**
 * 听课评价项目响应
 */
export interface EvaluationProjectResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: EvaluationProjectData
}

/**
 * 测评选项项目
 */
export interface EvaluationOptionItem {
  /** 评价项目ID */
  id: string
  /** 分值描述 */
  scoreDesc: string
  /** 分值 */
  score: string
}

/**
 * 附件项目
 */
export interface AttachmentItem {
  /** 文件路径 */
  path: string
  /** 文件名称 */
  name: string
}

/**
 * 听课评价添加请求参数
 */
export interface AttendLectureAddQuery {
  /** 教学任务ID */
  jxrwid: number | string
  /** 授课计划ID */
  skjhid: number | string
  /** 课型 */
  kx: string
  /** 教学内容 */
  jxnr: string
  /** 听课记录 */
  tkjl: string
  /** 授课进度/教案 */
  skjdja: string
  /** 教师学生情况 */
  jsxsqk: string
  /** 听课意见 */
  tkyj: string
  /** 听课评价 */
  tkpj: string | number
  /** 测评选项数组，格式为"ID_分值描述_分值" */
  cpxx: string[]
  /** 附件列表数组，格式为"文件路径|文件名" */
  fjlb?: string[]
}

/**
 * 听课评价添加响应
 */
export interface AttendLectureAddResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据，成功时返回ID，失败时返回0 */
  data: number
}

/**
 * 听课记录查询参数
 */
export interface AttendLectureQuery {
  /** 页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 学年学期，格式：学年|学期，如：2023-2024|1 */
  semesters: string
  /** 授课日期范围 */
  giveLessonsDate: string[]
  /** 节次 */
  sectionShow?: string
  /** 教师姓名 */
  teacherName?: string
  /** 部门名称 */
  deptName?: string
  /** 课程名称 */
  courseName?: string
  /** 班级名称 */
  className?: string
  /** 场地名称 */
  siteName?: string
  /** 听课人意见 */
  lecturerOpinion?: string
  /** 评分 */
  evaluationScore?: string
  /** 审核状态 */
  auditStatus?: string
}

/**
 * 听课记录项
 */
export interface AttendLectureItem {
  /** 记录ID */
  id: number
  /** 听课类型 */
  lectureType: number
  /** 学年 */
  lectureYear: string
  /** 学期 */
  lectureTerm: string
  /** 教学计划ID */
  teachingPlanId: number
  /** 课程类型 */
  courseType: string
  /** 授课教师编码 */
  lectureTeacherCode: string
  /** 授课教师姓名 */
  lectureTeacherName: string
  /** 教学内容 */
  teachingContent: string
  /** 授课记录 */
  lectureRecord: string
  /** 教学进度和计划 */
  teachingProgressAndPlan: string
  /** 师生反馈 */
  teacherStudentFeedback: string
  /** 听课人意见 */
  lecturerOpinion: string
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: string
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作人编码 */
  operatorCode: string
  /** 审核状态 */
  auditStatus: string
  /** 附件列表 */
  attachmentList: string
  /** 评价状态 */
  evaluationStatus: string
  /** 听课评价 */
  lectureEvaluation: string | null
  /** 评分 */
  evaluationScore: number
  /** 评价时间 */
  evaluationTime: string | null
  /** 学年 */
  studyYear: string
  /** 学期 */
  studyTerm: number
  /** 选课ID */
  selectCourseId: number | null
  /** 课程编码 */
  courseCode: string
  /** 课程名称 */
  courseName: string
  /** 课程总学时 */
  courseTotalHours: string
  /** 周学时 */
  weekHours: string
  /** 周数 */
  weeks: number
  /** 学校编码 */
  schoolCode: string
  /** 部门编码 */
  deptCode: string
  /** 部门名称 */
  deptName: string
  /** 班级编码 */
  classCode: string
  /** 班级名称 */
  className: string
  /** 工作量数 */
  workloadNum: string
  /** 确认学时 */
  affirmHours: string
  /** 带队教师姓名 */
  leaderTeacherName: string
  /** 退休人数 */
  retirementNumber: string
  /** 组织 */
  organization: number
  /** 部门名称2 */
  deptName2: string | null
  /** 编制类型编码 */
  establishmentTypeCode: string
  /** 类别编码 */
  categoryCode: string
  /** 编制状态编码 */
  establishmentStatusCode: string
  /** 编制类型名称 */
  establishmentTypeName: string
  /** 类别名称 */
  categoryName: string
  /** 编制状态名称 */
  establishmentStatusName: string
  /** 教研室编码 */
  teachOfficeCode: string
  /** 教研室名称 */
  teachOfficeName: string
  /** 聘用日期 */
  employmentDate: string | null
  /** 入职日期 */
  entryDate: string | null
  /** 来源 */
  source: string | null
  /** 来源名称 */
  sourceName: string | null
  /** 在职人员 */
  inStaff: string | null
  /** 是否双师型 */
  isDualQualification: string | null
  /** 退休日期 */
  retirementDate: string | null
  /** 离职日期 */
  resignationDate: string | null
  /** 学校ID */
  schoolId: string
  /** 名称 */
  name: string
  /** 编码 */
  code: string
  /** 是否教学部门 */
  isTeachingDept: number
  /** 父级ID */
  parentId: number
  /** 教学任务ID */
  teachingTasksId: number
  /** 周期 */
  cycle: number
  /** 授课日期 */
  giveLessonsDate: string
  /** 周次 */
  week: number
  /** 节次 */
  section: string
  /** 节次显示 */
  sectionShow: string
  /** 授课内容 */
  giveLessonsContent: string
  /** 授课模式 */
  giveLessonsMode: string
  /** 授课模式名称 */
  giveLessonsModeName: string
  /** 场地类型 */
  siteType: string
  /** 场地编码 */
  siteCode: string
  /** 场地名称 */
  siteName: string
  /** 班级ID */
  classId: string
  /** 作业数量 */
  homeworkNum: number
  /** 作业检查模式 */
  homeworkCheckMode: string
  /** 作业检查模式名称 */
  homeworkCheckModeName: string
  /** 作业内容 */
  homeworkContent: string
  /** 课时 */
  classHour: number
  /** 质量 */
  quality: number
  /** 教学计划状态 */
  teachingPlanStatus: number
  /** 工作量状态 */
  workloadStatus: number
  /** 教师确认状态 */
  teacherConfirmStatus: number
  /** 教师确认日期 */
  teacherConfirmDate: string
  /** 教师确认编码 */
  teacherConfirmCode: string
  /** 教师确认姓名 */
  teacherConfirmName: string
  /** 学生确认状态 */
  studentConfirmStatus: number
  /** 学生确认日期 */
  studentConfirmDate: string
  /** 学生确认编码 */
  studentConfirmCode: string
  /** 学生确认姓名 */
  studentConfirmName: string
  /** 审批状态 */
  approvalStatus: string
  /** 评价状态名称 */
  evaluationStatusName: string
  /** 审核状态 */
  shzt: number
  /** 审核状态名称 */
  auditStatusName: string
}

/**
 * 听课记录响应数据
 */
export interface AttendLectureResponse {
  /** 听课记录列表 */
  items: AttendLectureItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总记录数 */
  total: number
  /** 信息提示 */
  info: string
}

/**
 * 学年听课统计项
 */
export interface XnStatisticsItem {
  /** 标题，例如：2024-2025(1) */
  title: string
  /** 键值 */
  key: number
  /** 总数 */
  total: number
  /** 平均分数 */
  avgScore: string
  /** 已完成总数 */
  alreadyTotal: number
  /** 被听课总数 */
  beTotal: number
}

/**
 * 听课统计响应数据
 */
export interface AttendLectureStatisticsData {
  /** 总数 */
  total: number
  /** 审核总数 */
  auditTotal: number
  /** 通过总数 */
  passTotal: number
  /** 拒绝总数 */
  rejectTotal: number
  /** 评价分数 */
  evaluationScore: number
  /** 被听课总数 */
  beTotal: number
  /** 学年统计 */
  xnStatistics: XnStatisticsItem[]
}

/**
 * 听课评价数据查询参数
 */
export interface AttendLectureEvaluationDataQuery {
  /** 听课记录ID */
  tkjlid: number
}

/**
 * 听课评价数据项
 */
export interface AttendLectureEvaluationDataItem {
  /** 评价ID */
  id: number
  /** 听课记录ID */
  tkjlid: number
  /** 授课计划ID */
  skjhid: number
  /** 测评题目ID */
  cptmid: number
  /** 测评选项内容 */
  cpxxnr: string
  /** 测评分值 */
  cpfz: number
  /** 测评人 */
  cpr: string
  /** 测评人姓名 */
  cprxm: string
  /** 测评时间 */
  cpsj: string
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 使用对象 */
  sydx: string
  /** 使用对象名称 */
  sydxmc: string
  /** 题目类型名称 */
  tmlxmc: string
  /** 测评题目 */
  cptm: string
  /** 测评选项，格式为：选项1|分值1&&选项2|分值2 */
  cpxx: string
  /** 测评题目说明 */
  cptmsm: string
  /** 排序号 */
  pxh: number
}

/**
 * 听课评价数据响应
 */
export interface AttendLectureEvaluationDataResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: AttendLectureEvaluationDataItem[]
}

/**
 * 听课评价更新请求参数
 */
export interface AttendLectureUpdateQuery extends AttendLectureAddQuery {
  /** 听课评价记录ID */
  id: number | string
}

/**
 * 听课评价更新响应
 */
export interface AttendLectureUpdateResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据，成功时返回数组，失败时返回错误信息 */
  data: any[]
}
