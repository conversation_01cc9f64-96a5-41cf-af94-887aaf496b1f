/**
 * 验证码开关响应数据
 */
export interface ICaptchaFlagResponse {
  flag: boolean
}

/**
 * 验证码响应数据
 */
export interface ICaptchaResponse {
  uuid: string
  base64: string
}

/**
 * 用户信息
 */
export interface IUserInfo {
  id: number
  realname: string
  username: string
  avatar: string
  lastLoginTime: string
  phone: string
  department: string
  jobName: string
  userType: number
  token: string
  refresh_token: string
  last_login_time?: string
  roleName?: string
  nickname?: string
  openid?: string
  className?: string
}

/**
 * 登录响应数据
 */
export interface ILoginResponse {
  userInfo: IUserInfo
  token: string
}

/**
 * 权限码响应数据
 */
export type IAccessCodesResponse = string[]

/**
 * 后端登录状态响应
 */
export interface IBackendLoginStatusResponse {
  status?: boolean
}

/**
 * 微信刷新token请求参数
 */
export interface IRefreshTokenByWechatParams {
  token: string
  uid: number
}

/**
 * 微信刷新token响应数据
 */
export interface IRefreshTokenResponse {
  type: string
  token: string
}

/**
 * 获取用户信息响应数据
 */
export interface IGetUserInfoResponse {
  userInfo: IUserInfo
}

/**
 * 登录请求参数
 */
export interface ILoginRequest {
  username: string
  password: string
  captcha?: string
  captcha_uuid?: string
}
