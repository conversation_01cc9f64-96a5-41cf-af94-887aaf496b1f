/**
 * 证书相关类型定义
 */

/**
 * 证书查询参数
 */
export interface CertificateQuery {
  /** 当前页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 学生编号 */
  studentCode?: string
  /** 学生姓名 */
  studentName?: string
  /** 证书名称 */
  certificateName?: string
  /** 发证日期 */
  issueDate?: string
  /** 证书编号 */
  certificateNumber?: string
  /** 创建时间 */
  createTime?: string
  /** 学生确认状态 */
  studentConfirmStatusStr?: string
  /** 学生确认日期 */
  studentConfirmDate?: string
  /** 备注 */
  remark?: string
}

/**
 * 证书项
 */
export interface CertificateItem {
  /** ID */
  id: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 学生编号 */
  studentCode: string
  /** 学生姓名 */
  studentName: string
  /** 证书代码 */
  certificateCode: number
  /** 证书名称 */
  certificateName: string
  /** 发证日期 */
  issueDate: string
  /** 证书编号 */
  certificateNumber: string
  /** 操作人ID */
  operatorId: string
  /** 学生确认状态 */
  studentConfirmStatus: number
  /** 学生确认日期 */
  studentConfirmDate: string
  /** 证书类型 */
  certificateType: string
  /** 证书类型名称 */
  certificateTypeName: string
  /** 证书等级 */
  certificateLevel: string
  /** 证书等级名称 */
  certificateLevelName: string
  /** 颁发单位 */
  issuingUnit: string
  /** 证书类别 */
  certificateCategory: string
  /** 级别代码 */
  levelCode: number
  /** 级别名称 */
  levelName: string
  /** 是否1+X证书 */
  is1XCertificate: number
  /** 考核地点 */
  assessmentLocation: string
  /** 相关专业 */
  relatedMajor: string
  /** 相关专业名称 */
  relatedMajorName: string
  /** 学生确认状态字符串 */
  studentConfirmStatusStr: string
}

/**
 * 证书响应
 */
export interface CertificateResponse {
  /** 证书列表 */
  items: CertificateItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总数 */
  total: number
}
