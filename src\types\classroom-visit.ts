/**
 * 推门听课相关类型定义
 */

// 标签类型
export interface Tag {
  name: string
  bgColor: string
  textColor: string
}

// 今日可听课程项
export interface AvailableCourse {
  id?: string
  name: string
  status: string
  teacher: string
  time: string
  tags: Tag[]
  location: string
}

// 推荐课程项
export interface RecommendedCourse {
  id?: string
  name: string
  tag: Tag
  teacher: string
  schedule: string
  rating: number
  reviewCount: number
}

// 听课统计项
export interface StatisticsItem {
  name: string
  value: number
  color: string
}

// 筛选条件项
export interface FilterItem {
  id: string
  name: string
}

// 选项卡项
export interface TabItem {
  id: string
  name: string
}

// 推门听课记录项
export interface ClassroomVisitRecord {
  id: string
  courseName: string
  teacherName: string
  visitDate: string
  visitTime: string
  location: string
  status: string // 已评价/未评价
  rating?: number
}

// 听课评价详情
export interface ClassroomVisitEvaluation {
  id: string
  courseName: string
  teacherName: string
  visitDate: string
  visitTime: string
  location: string
  teachingContent: string
  teachingMethod: string
  studentInteraction: string
  overallRating: number
  strengths: string
  suggestions: string
  comments: string
}

// 听课评价表单
export interface EvaluationForm {
  visitRecordId: string
  teachingContent: string
  teachingMethod: string
  studentInteraction: string
  overallRating: number
  strengths: string
  suggestions: string
  comments: string
}

// 课程预约请求
export interface CourseReservationRequest {
  courseId: string
  visitDate: string
  visitTime: string
}

// 课程预约响应
export interface CourseReservationResponse {
  success: boolean
  message: string
  reservationId?: string
}
