/**
 * 云端学习问题信息相关类型定义
 */

/**
 * 问题选项类型
 */
export interface QuestionOption {
  /**
   * 选项名称，如"A.尊重自然"
   */
  name: string
  /**
   * 是否为文本区域
   */
  textarea: boolean
  /**
   * 选项值，如"A"
   */
  value: string
}

/**
 * 问题状态枚举
 */
export enum QuestionStatus {
  /**
   * 等待开始抢答
   */
  WAITING_OPEN = 'waiting_open',
  /**
   * 抢答中
   */
  WAITING_QUICK = 'waiting_quick',
  /**
   * 答题中
   */
  DOING = 'doing',
  /**
   * 已结束
   */
  DONE = 'done',
}

/**
 * 问题信息类型
 */
export interface QuestionInfo {
  /**
   * 问题标题
   */
  title: string
  /**
   * 问题选项
   */
  opt: QuestionOption[]
  /**
   * 问题类别
   */
  category: string
  /**
   * 剩余时间（秒）
   */
  time: number
  /**
   * 问题总数
   */
  total: number
  /**
   * 剩余问题数
   */
  left: number
  /**
   * 问题类型，如"单选题"
   */
  type: string
  /**
   * 问题状态
   * waiting_open: 等待开始抢答
   * waiting_quick: 抢答中
   * doing: 答题中
   * done: 已结束
   */
  status: string
}

/**
 * 问题信息响应数据
 */
export interface PlayerQuestionInfoData {
  /**
   * 是否可以答题
   */
  can_answer: boolean
  /**
   * 问题类别
   */
  category: string
  /**
   * 当前问题ID
   */
  current_question: string
  /**
   * 问题详细信息
   */
  question_info: QuestionInfo
}

/**
 * 获取问题信息请求参数
 */
export interface PlayerQuestionInfoParams {
  /**
   * 项目ID
   */
  xmid: number | string
  /**
   * 人员编号
   */
  rybh: number | string
}

/**
 * 玩家答题请求参数
 */
export interface PlayerAnswerParams {
  /**
   * 项目ID
   */
  xmid: number | string
  /**
   * 问题ID
   */
  question_id: number | string
  /**
   * 人员编号
   */
  rybh: number | string
  /**
   * 答案，如"A,B,C"
   */
  answer: string
}

/**
 * 玩家答题响应数据
 */
export interface PlayerAnswerData {
  is_match: boolean
  /**
   * 得分，0为答题错误
   */
  score: string
}

/**
 * 玩家快速处理问题请求参数
 */
export interface PlayerHandleQuickParams {
  /**
   * 项目ID
   */
  xmid: number | string
  /**
   * 问题ID
   */
  question_id: number | string
  /**
   * 人员编号
   */
  rybh: number | string
}

/**
 * 玩家快速处理问题响应数据
 */
export interface PlayerHandleQuickData {
  /**
   * 处理结果标志
   */
  flag: boolean
  /**
   * 答题时间限制（秒）
   */
  time?: number
}

/**
 * 获取选手得分请求参数
 */
export interface PlayerScoreParams {
  /**
   * 项目ID
   */
  xmid: number | string
  /**
   * 人员编号
   */
  rybh: number | string
}

/**
 * 获取选手得分响应数据
 */
export interface PlayerScoreData {
  /**
   * 选手得分
   */
  total_score: number
}
