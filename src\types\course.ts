export interface CourseYear {
  /** 学年ID */
  id: number
  /** 学年 */
  year: number
}

export interface CourseYearParams {
  /** 返回格式，select表示下拉列表格式 */
  format: 'select'
}

export interface CourseSelectParams {
  /** 页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 返回格式，select表示下拉列表格式 */
  format: 'select'
  /** 是否不分页 */
  notPage: number
}

export interface Course {
  /** 合并任务ID */
  mergeTaskId?: number | null
  /** 课程ID */
  id: number
  /** 学校代码 */
  schoolCode: string
  /** 课程类别 */
  courseCategory: string
  /** 课程代码 */
  courseCode: string
  /** 课程名称 */
  courseName: string
  /** 课程别名 */
  courseAlias: string
  /** 课程英文名称 */
  kcywmc: string
  /** 教研室代码 */
  teachOfficeCode: string
  /** 教研室名称 */
  teachOfficeName: string
  /** 拼音快速索引 */
  pykssy: string
  /** 是否教学任务 */
  isTeachingTask: string
  /** 是否默认空间 */
  isDefaultSpace: string
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作员编号 */
  oprybh: string
  /** 特征数量 */
  tzsl: number
  /** 最后发布时间 */
  zhfbsj: string
  /** 通用课程类别 */
  commonCourseCategory: string
  /** 优秀课程类别 */
  excellentCourseCategory: string
  /** 课程简介 */
  courseIntro: string
  /** 教师代码 */
  teacherCode: string
  /** 教师名称 */
  teacherName: string
  /** 是否课程整合 */
  isCourseIntegration: string
  /** 证书ID */
  certificateId: number
  /** 证书名称 */
  certificateName: string
  /** 相关竞赛名称 */
  relatedCompetitionName: string
  /** 是否校企合作课程 */
  isSchoolEnterpriseCourse: string
  /** 单位ID */
  unitId: number
  /** 单位名称 */
  unitName: string
  /** 是否线上课程 */
  isOnlineCourse: string
  /** 线上课程URL */
  onlineCourseUrl: string
  /** 是否政治案例课程 */
  isPoliticalExamplesCourse: string
  /** 主要资源类型 */
  mainResourceType: string
  /** 学校领导代码 */
  schoolLeaderCode: string
  /** 学校领导名称 */
  schoolLeaderName: string
  /** 单位领导名称 */
  unitLeaderName: string
  /** 合作开始时间 */
  collaborateStartTime: string
  /** 合作结束时间 */
  collaborateEndTime: string
  /** 校企合作合同号 */
  xqhzhth: string
  /** 字典代码 */
  dictionaryCode: string
  /** 字典名称 */
  dictionaryName: string
}

export interface CourseListResponse {
  /** 课程列表 */
  items: Course[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总数 */
  total: number
}

export interface CourseDateSectionParams {
  /** 选择类型 */
  select: number
  /** 教学任务ID */
  jxrwid: number
}

export interface CourseDateSection {
  /** 上课计划ID */
  skjhid: number
  /** 上课计划名称 */
  skjhmc: string
}

export interface CourseDateSectionResponse {
  /** 上课计划列表 */
  data: CourseDateSection[]
}
