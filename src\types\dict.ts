/**
 * 城市数据相关类型定义
 */

/**
 * 区县级数据项
 */
export interface DistrictItem {
  /**
   * 显示名称
   */
  label: string
  /**
   * 区县编码值
   */
  value: string
}

/**
 * 市级数据项
 */
export interface CityItem {
  /**
   * 显示名称
   */
  label: string
  /**
   * 城市编码值
   */
  value: string
  /**
   * 区县列表
   */
  children?: DistrictItem[]
}

/**
 * 省级数据项
 */
export interface ProvinceItem {
  /**
   * 显示名称
   */
  label: string
  /**
   * 省份编码值
   */
  value: string
  /**
   * 城市列表
   */
  children?: CityItem[]
}

/**
 * 城市字典数据响应
 */
export type CityDictResponse = ProvinceItem[]
