// 邮件列表项接口
export interface MailItem {
  id: number
  recipient: string
  recipientName: string
  viewStatus: string
  viewTime: string
  emailContentId: number
  deleteTag: number
  createTime: number
  updateTime: number
  operatorId: number
  wechatPush: number
  mailInId: number
  sender: string
  senderName: string
  senderDepartment: string
  senderDepartmentName: string
  sendTime: string
  recipientNames: string
  recipients: string
  subject: string
  content: string
  attachments: string
  attachmentSize: number
  attachmentPath: string | null
  templateCategory: string | null
  fileCategory: string | null
  emailType: number
  extensionField1: string
  extensionField2: string
  meetingStartTime: string
  meetingEndTime: string
  wechatPushFlag: number
  viewStatusColor: string
}

// 邮件列表请求参数接口
export interface MailQuery {
  page: number
  pageSize: number
  type: string
  subject: string
  senderName: string
  sendTime: string
  viewStatus: string
  viewTime: string
  sortBy?: string
  sortOrder?: string
  recipients?: string
  wechatPushFlag?: string
}

// 邮件列表响应接口
export interface MailResponse {
  items: MailItem[]
  total: number
}

// 邮件详情请求参数接口
export interface MailInfoQuery {
  type: string // 邮件类型，如 'in' 表示收件箱
  id: number // 邮件ID
}

// 邮件详情响应接口
export interface MailInfoResponse {
  content: string // 邮件内容
  sender: string // 发送者ID
  senderName: string // 发送者姓名
  subject: string // 邮件主题
  sendTime: string // 发送时间
  attachmentSize: number // 附件大小
  attachments: string // 附件信息
  ckzt: number // 查看状态
  cksj: string // 查看时间
  MailInId: number // 邮件ID
  extensionField1: string // 扩展字段1
}
