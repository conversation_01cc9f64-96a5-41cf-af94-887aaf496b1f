export interface MediationQueryParams {
  page: number
  pageSize: number
  sortBy: string
  sortOrder: string
  id?: string
  className?: string
  courseName?: string
  operatorName?: string
  operationItemName?: string
  adjustmentPlanContent?: string
  operationReason?: string
  operationTime?: string[]
  startTime?: string
  endTime?: string
  approvalStatus?: string
}

export interface MediationCourseItem {
  id: number
  adjustmentType: number
  operationItem: string
  operationItemName: string
  teachingTaskId: number
  adjustmentPlanTitle: string
  adjustmentPlanContent: string
  operationReason: string
  relatedAttachments: string
  operationTime: string
  operator: string
  operatorName: string
  approvalStatus: string
  approvalData: string
  relatedLeaveId: number
  totalAdjustmentHours: number
  create_time: number
  update_time: number
  deltag: number
  operatorCode: string
  businessDeptCode: string
  selectCourseId: null | number
  studyYear: string
  studyTerm: number
  courseName: string
  schoolCode: string
  deptCode: string
  deptName: string
  teachOfficeCode: string
  leaderTeacherCode: string
  leaderTeacherName: string
  classCode: string
  className: string
}

export interface MediationResponse {
  items: MediationCourseItem[]
  query: Record<string, any>
  total: number
}
