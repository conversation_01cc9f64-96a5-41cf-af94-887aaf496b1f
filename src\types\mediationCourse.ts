// 调停课申请类型定义

/**
 * 教学任务信息项
 */
export interface TeachingTaskInfo {
  /** 操作类型：停课(stop)、调课(change)、补课(make_up) */
  type: 'stop' | 'change' | 'make_up'
  /** 涉及课时数 */
  ztkss: number
  /** 教学任务ID */
  jxrwid: number
  /** 关联教学任务ID */
  link_jxrwid: string
}

/**
 * 当前节点信息
 */
export interface CurrentNodeInfo {
  /** 用户名 */
  user_name: string
  /** 用户标签 */
  user_label: string
  /** 节点ID */
  id: number
}

/**
 * 调停课申请项
 */
export interface MediationCourseItem {
  /** 申请ID */
  id: number
  /** 调停课类型：1-教师申请 2-教学部门申请 */
  ttklx: number
  /** 教学任务信息JSON字符串 */
  jxrwxx: string
  /** 调停课方案明细 */
  ttkfadm: string
  /** 调停课方案说明 */
  ttkfasm: string
  /** 调停课原因 */
  ttkyy: string
  /** 关联调停课申请表ID */
  glttksqbid: string
  /** 附件列表 */
  fjlb: string
  /** 调停课开始时间 */
  ttkkssj: string
  /** 调停课结束时间 */
  ttkjssj: string
  /** 当前节点ID */
  curr_node_id: number
  /** 操作人 */
  czr: string
  /** 操作人姓名 */
  czrxm: string
  /** 审批状态：0-待审批 1-已通过 2-已拒绝 */
  spzt: number
  /** 审批时间 */
  spsj: string | null
  /** 涉及总课时数 */
  ztkss: number
  /** 创建时间(时间戳) */
  create_time: number
  /** 更新时间(时间戳) */
  update_time: number
  /** 删除标记：0-未删除 1-已删除 */
  deltag: number
  /** 操作员编号 */
  oprybh: string
  /** 当前节点信息 */
  hasCurrNode: CurrentNodeInfo | null
}

/**
 * 调停课申请查询参数
 */
export interface MediationCourseQuery {
  /** 联合查询类型 */
  type?: string
  /** 页码 */
  page?: number | string
  /** 每页数量 */
  pageSize?: number | string
  /** 申请ID */
  id?: string | number
  /** 调停课类型 */
  ttklx?: string | number
  /** 操作人姓名 */
  czrxm?: string
  /** 调停课原因 */
  ttkyy?: string
  /** 调停课开始时间 */
  ttkkssj?: string
  /** 审批状态 */
  spzt?: string | number
}

/**
 * 调停课申请列表响应
 */
export interface MediationCourseResponse {
  /** 调停课申请列表 */
  items: MediationCourseItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总记录数 */
  total: number
}

/**
 * 调课申请参数
 */
export interface AdjustCourseParams {
  /** 开始时间（时间戳） */
  startDate: number
  /** 结束时间（时间戳） */
  endDate: number
  /** 调课节次 */
  newSection: string
  /** 授课场地 */
  newVenue?: string
  /** 方案数量 */
  planCount?: string
  /** 调课原因 */
}

/**
 * 调课申请响应
 */
export interface AdjustCourseResponse {
  /** 响应码 */
  code: number
  /** 响应消息 */
  msg: string
  /** 响应时间 */
  time: number
  /** 响应数据 */
  data?: any
}

/**
 * 调课计划查询参数
 */
export interface ScheduleChangePlanQuery {
  /** 变更类型 */
  changeType: number
  /** 课程安排ID */
  scheduleId: number
  /** 教学任务ID */
  teachTaskId: number
  /** 开始目标日期 */
  beginTargetDate: string
  /** 结束目标日期 */
  endTargetDate: string
  /** 场地代码 */
  venueCode?: string
  /** 计划数量 */
  planCount?: number
  /** 节次 */
  section?: string
  /** 合班楼栋 */
  hbld?: number
}

/**
 * 调课计划项目
 */
export interface ScheduleChangePlanItem {
  /** 备注 */
  remark: string
  /** 变更类型 */
  changeType: number
  /** 变更类型名称 */
  changeTypeName: string
  /** 原课程安排ID */
  oldScheduleId: number
  /** 目标日期 */
  targetDate: string
  /** 目标星期数字 */
  targetNumberWeek: string
  /** 目标周次 */
  targetWeek: number
  /** 目标节次 */
  targetSection: string[]
  /** 目标节次名称 */
  targetSectionName: string[]
  /** 目标场地 */
  targetVenue: string
  /** 交换课程安排ID */
  switchScheduleId: number
  /** 场地类别 */
  venueCategory: string
  /** 变更方式 */
  changeWay: string
  /** 教学任务ID（用于API请求） */
  teachTaskId: number
  /** 目标场地名称 */
  targetVenueName: string
  /** 教师代码 */
  teacherCode: string
  /** 教师姓名 */
  teacherName: string
  /** 教学班代码 */
  teachingClassCode: string
  /** 教学班名称 */
  teachingClassName: string
  /** 教学课程名称 */
  teachingCourseName: string
  /** 合并教学任务ID */
  mergeTeachingTaskId: number
  /** 教学任务ID */
  teachingTaskId: number
  /** 其他合并课程安排 */
  otherMergeSchedule: ScheduleChangePlanItem[]
  /** 唯一标识键 */
  key: string
}

/**
 * 调课计划响应
 */
export interface ScheduleChangePlanResponse {
  /** 响应码 */
  code: number
  /** 响应消息 */
  msg: string
  /** 响应时间 */
  time: number
  /** 调课计划数据 */
  data: ScheduleChangePlanItem[]
}

/**
 * 调课申请提交参数
 */
export interface ScheduleChangeApplyParams {
  /** 调课方案键值数组，包含调课、停课、补课等操作的详细信息 */
  key: string[]
  /** 调停课类型：1-教师申请 2-教学部门申请 */
  ttklx: string
  /** 调停课原因 */
  ttkyy: string
  /** 附件列表，格式：文件路径|文件名 */
  fjlb: string
  /** 开始时间，格式：YYYY-MM-DD */
  begin_time: string
  /** 结束时间，格式：YYYY-MM-DD */
  end_time: string
  /** 申请ID，新建时为空字符串 */
  id: string
}

/**
 * 调课申请提交响应
 */
export interface ScheduleChangeApplyResponse {
  /** 响应码：1-成功 */
  code: number
  /** 响应消息 */
  msg: string
  /** 响应时间戳 */
  time: number
  /** 响应数据，通常为空数组 */
  data: any[]
}

/**
 * 调课删除请求参数
 */
export interface ScheduleChangeDeleteParams {
  /** 调课记录ID */
  id: number
}

/**
 * 调课删除响应
 */
export interface ScheduleChangeDeleteResponse {
  /** 响应码 */
  code: number
  /** 响应消息 */
  msg: string
  /** 响应时间 */
  time: number
  /** 响应数据 */
  data?: any
}

/**
 * 原课程安排信息
 */
export interface OldScheduleInfo {
  /** 课程名称 */
  kcmc: string
  /** 教学任务ID */
  jxrwid: number
  /** 授课教师 */
  skjs: string
  /** 授课教师姓名 */
  skjsxm: string
  /** 授课日期 */
  skrq: string
  /** 周次 */
  zc: number
  /** 星期数 */
  xqs: number
  /** 节次 */
  jc: string
  /** 节次显示 */
  jcshow: string
  /** 授课班级名称 */
  skbjmc: string
  /** 授课场地代码 */
  skcddm: string
  /** 授课场地名称 */
  skcdmc: string
  /** 课程安排ID */
  id: number
  /** 课时数 */
  ks: number
  /** 教学任务信息 */
  jxrw: {
    /** 教学任务ID */
    id: number
    /** 课程名称 */
    kcmc: string
    /** 班级名称 */
    bjmc: string
    /** 授课合班 */
    sshb: string | null
  }
}

/**
 * 调停课计划详情项
 */
export interface MediationCoursePlanItem {
  /** 备注 */
  remark: string
  /** 变更类型：0-调课 1-补课 2-停课 */
  changeType: number
  /** 变更类型名称 */
  changeTypeName: string
  /** 原课程安排ID */
  oldScheduleId: number
  /** 目标日期 */
  targetDate: string
  /** 目标星期数字 */
  targetNumberWeek: string
  /** 目标周次 */
  targetWeek: number
  /** 目标节次 */
  targetSection: string[]
  /** 目标节次名称 */
  targetSectionName: string[]
  /** 目标场地 */
  targetVenue: string
  /** 交换课程安排ID */
  switchScheduleId: number
  /** 场地类别 */
  venueCategory: string
  /** 变更方式 */
  changeWay: string
  /** 教学课程名称 */
  teachingCourseName: string
  /** 教学班级名称 */
  teachingClassName: string
  /** 目标场地名称 */
  targetVenueName: string
  /** 合并教学任务ID */
  mergeTeachingTaskId: string
  /** 教学任务ID */
  teachingTaskId: number
  /** 原课程安排信息 */
  oldSchedule: OldScheduleInfo
  /** 唯一标识键 */
  key: string
}

/**
 * 调停课申请详情
 */
export interface MediationCourseDetail extends MediationCourseItem {
  /** 计划列表 */
  plan_list: MediationCoursePlanItem[]
}

/**
 * 调停课申请详情查询参数
 */
export interface MediationCourseDetailQuery {
  /** 申请ID */
  id: number | string
  /** 查询类型 */
  type: 'detail'
}

/**
 * 调停课申请详情响应
 */
export interface MediationCourseDetailResponse {
  /** 响应码 */
  code: number
  /** 响应消息 */
  msg: string
  /** 响应时间 */
  time: number
  /** 详情数据 */
  data: MediationCourseDetail
}
