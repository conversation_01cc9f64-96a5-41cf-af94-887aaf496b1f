/**
 * 组织机构类型定义
 */

/**
 * 组织机构信息接口
 */
export interface Organization {
  /** 组织机构ID */
  id: string
  /** 组织机构名称 */
  name: string
  /** 机构类别 */
  jglb: number
  /** 机构类别名称 */
  jglbmc: string
  /** 父级ID */
  parentId: string | null
  /** 机构编码 */
  code: string
  /** 排序ID */
  orderId: number
  /** 是否实体 (0-否, 1-是) */
  isSubstance: number
  /** 是否实体字符串 */
  isSubstanceStr: string
  /** 是否为党群机构 (0-否, 1-是) */
  isPartyMass: number
  /** 是否为党群机构字符串 */
  isPartyMassStr: string
  /** 是否为教学部门字符串 */
  isTeachingDeptStr: string
  /** 是否为教学部门 (0-否, 1-是) */
  isTeachingDept: number
  /** 是否为行政机构字符串 */
  isAdminAgencyStr: string
  /** 是否为行政机构 (0-否, 1-是) */
  isAdminAgency: number
  /** 是否为党组织字符串 */
  isPartyOrganizationStr: string
  /** 是否为党组织 (0-否, 1-是) */
  isPartyOrganization: number
  /** 状态 */
  status: number
  /** 说明 */
  instructions: string
  /** 电话 */
  phone: string
  /** 层级 */
  level: number
  /** 系统联动码 */
  systemLinkageCode: string
  /** 数据联动码 */
  dataLinkageCode: string
  /** 标签数组 */
  tags: string[]
  /** 标签名称数组 */
  tagsName: string[]
  /** 校区列表 */
  campusList: string[]
  /** 校区名称列表 */
  campusNameList: string[]
  /** 负责人列表 */
  leaderList: string[]
  /** 负责人名称列表 */
  leaderNameList: string[]
  /** 备注 */
  remark: string
  /** 子级组织机构 */
  children: Organization[]
}

/**
 * 组织机构查询参数
 */
export interface OrganizationQuery {
  /** 可以添加查询参数，目前为空 */
}

/**
 * 组织机构查询结果
 */
export type OrganizationResponse = Organization[]

export interface SubstanceOrg {
  id: number
  /** 机构类别 */
  jglb: number
  /** 机构类别名称 */
  jglbmc: string
  /** 机构代码 */
  code: string
  /** 机构名称 */
  name: string
  /** 机构简称 */
  jgjc: string
  /** 学校ID */
  schoolId: string
  /** 父级ID */
  parentId: number
  /** 路径 */
  path: string
  /** 层级 */
  level: number
  /** 是否实体机构 */
  isSubstance: number
  /** 是否教学部门 */
  isTeachingDept: number
  /** 是否行政机构 */
  isAdminAgency: number
  /** 是否党组织 */
  isPartyOrganization: number
  /** 状态 */
  status: number
  /** 说明 */
  instructions: string
  /** 备注 */
  remark: string
  /** 排序ID */
  orderId: number
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 电话 */
  phone: string
  /** 系统关联代码 */
  systemLinkageCode: string
  /** 数据关联代码 */
  dataLinkageCode: string
  /** 数据关联名称 */
  dataLinkageName: string
  /** 团系统代码 */
  fellowshipSystemCode: string
  /** 团系统名称 */
  fellowshipSystemName: string
  /** 是否党群 */
  isPartyMass: number
  /** 子元素 */
  children?: OrgClass[]
}

export interface OrgClass {
  /** ID */
  id: number
  /** 所属学校 */
  ssxy: string
  /** 校区代码 */
  campusCode: string
  /** 校区名称 */
  campusName: string
  /** 系部代码 */
  deptCode: string
  /** 系部名称 */
  deptName: string
  /** 专业代码 */
  majorCode: string
  /** 专业名称 */
  majorName: string
  /** 培养对象 */
  cultivateObj: string
  /** 培养对象名称 */
  cultivateObjName: string
  /** 培养层次 */
  cultivateLevel: string
  /** 培养层次名称 */
  cultivateLevelName: string
  /** 专业方向 */
  zyfx: string | null
  /** 专业方向名称 */
  majorDirection: string | null
  /** 班级代码 */
  classCode: string
  /** 班级名称 */
  className: string
  /** 班级简称 */
  classAbbreviation: string
  /** 年级 */
  grade: number
  /** 学制年限 */
  eduYear: number
  /** 学制显示 */
  eduYearShow: string
  /** 创建日期 */
  createDate: string
  /** 毕业年份 */
  graduateYear: number
  /** 限制人数 */
  limitCount: number
  /** 班级人数 */
  classCount: number
  /** 班级在校男生人数 */
  bjzxrs_m: number
  /** 班级在校女生人数 */
  bjzxrs_f: number
  /** 班级实际人数 */
  classInCount: number
  /** 班级实际在校男生人数 */
  bjsjzxrs_m: number
  /** 班级实际在校女生人数 */
  bjsjzxrs_f: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 状态 */
  status: number
  /** 辅导员代码 */
  counsellorCode: string
  /** 辅导员名称 */
  counsellorName: string
  /** 学委 */
  xw: string
  /** 学习委员姓名 */
  studyCommitteeName: string
  /** 学委辅助考勤开放 */
  xwfzkqkf: number
  /** 钉钉二维码 */
  ddqrcode: string | null
  /** 校友代码 */
  alumniCode: string
  /** 校友名称 */
  alumniName: string
  /** 代码 */
  code: string
  /** 名称 */
  name: string
}

/**
 * 实体机构查询参数
 */
export interface SubstanceListQuery {
  /** 是否实体机构 (1-是, 0-否) */
  isSubstance?: number
}

/**
 * 实体机构列表响应
 */
export interface SubstanceListItem {
  /** 机构代码 */
  code: string
  /** 机构名称 */
  name: string
}

/**
 * 实体机构列表响应
 */
export type SubstanceListResponse = SubstanceListItem[]
