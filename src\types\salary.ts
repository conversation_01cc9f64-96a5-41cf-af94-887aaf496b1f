// types/salary.ts

// 请求参数接口
export interface TeacherSalaryParams {
  year: number // 年份
  month: number // 月份
}

// 响应数据接口
export interface TeacherSalaryResponse {
  code: number // 状态码
  msg: string // 消息
  time: number // 时间戳
  data: string // HTML格式的表格内容
}

// 单个工资项目条目
export interface SalaryItemEntry {
  id: number
  teacherCode: string
  salaryImportId: number
  salaryItem: string
  salaryValue: string
  create_time: number
  update_time: number
  deltag: number
  year: string
  month: number
}

// 工资数据查询参数
export interface SalaryQuery {
  year: number
  month: number | string // 月份，可以是数字或空字符串("")
  isMobile: number
}

// 工资数据响应
export interface SalaryResponse {
  code: number
  msg: string
  time: number
  data: Record<string, SalaryItemEntry[][]>
}

// 工资项目分类
export interface SalaryDetail {
  // 收入项
  incomeItems: {
    职岗工资?: string
    级别薪级?: string
    生活补贴?: string
    教护津贴?: string
    岗位津贴?: string
    职务津贴?: string
    地区补贴?: string
    考勤奖?: string
    特岗津贴?: string
    提租补贴?: string
    其他补?: string
    应发工资?: string
    // 津贴类项目
    考核津贴?: string
    效益津贴?: string
    在岗津贴?: string
    补系数?: string
    其他贴1?: string
    其他贴2?: string
    其他贴3?: string
    应发津贴?: string
    // 新增项目
    入岚补?: string
    基础绩效奖?: string
    文明奖?: string
    专职思政补?: string
    专职辅导员补?: string
    课题劳务费?: string
    女职工卫生费?: string
    // 年终相关项目
    补贴一?: string
    补贴二?: string
    补贴三?: string
    年终应发?: string
    年终实发?: string
  }
  // 扣除项
  deductItems: {
    医保?: string
    失保?: string
    公积金?: string
    养老保险?: string
    职业年金?: string
    工会费?: string
    房租?: string
    水费?: string
    电费?: string
    网租?: string
    第一次扣税?: string
    其他扣1?: string
    扣工资1?: string
    应扣合计?: string
    // 新增项目
    其他扣2?: string
    扣工资2?: string
    第二次扣税?: string
    补扣三险两金?: string
    // 年终相关扣除项
    '其他扣(年终)'?: string
    '扣工资(年终)'?: string
    年终计税?: string
  }
  // 基本信息
  baseInfo: {
    teacherCode?: string
    name?: string
    year?: string
    month?: string
    实发工资?: string
    实发津贴?: string
  }
}
