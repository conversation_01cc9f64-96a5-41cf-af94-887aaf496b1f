/**
 * 总评成绩相关类型定义
 */

/**
 * 总评成绩查询参数
 */
export interface TotalScoreQuery {
  /** 页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 学期标识数组 */
  semesters?: string[]
  /** 课程名称 */
  kcmc?: string
  /** 教师姓名 */
  zdjsxm?: string
  /** 学分 */
  kcxf?: string
  /** 成绩类型 */
  cjpdlx?: string
  /** 成绩 */
  cj?: string
  /** 绩点 */
  cjjd?: string
  /** 成绩性质 */
  cjsx?: string
  /** 是否通过 */
  tgbz?: string
  /** 提交时间范围 */
  cjtjsj?: string[]
}

/**
 * 总评成绩项
 */
export interface ScoreItem {
  /** 成绩ID */
  id: number
  /** 所属班级编号 */
  ssbj: string
  /** 班级名称 */
  ssbjmc: string
  /** 学生学号 */
  xsxh: string
  /** 座位号 */
  zwh: number
  /** 学生姓名 */
  xm: string
  /** 教学任务ID */
  jxrwid: number
  /** 选课信息ID */
  xkxxid: number | null
  /** 学年 */
  xn: string
  /** 学期 */
  xq: string
  /** 教师ID */
  zdjs: string
  /** 教师姓名 */
  zdjsxm: string
  /** 课程代码 */
  kcdm: string
  /** 课程名称 */
  kcmc: string
  /** 课程性质 */
  kcxz: string
  /** 学分 */
  kcxf: number
  /** 成绩类型 */
  cjpdlx: string
  /** 成绩 */
  cj: number
  /** 百分制成绩 */
  bfzcj: number
  /** 是否通过 */
  tgbz: number
  /** 成绩绩点 */
  cjjd: string
  /** 成绩性质 */
  cjsx: string
  /** 是否提交 */
  cjtjbz: number
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 成绩提交人员 */
  cjtjry: string
  /** 提交时间 */
  cjtjsj: string
  /** 学期标识 */
  semesters: string
}

/**
 * 总评成绩查询响应
 */
export interface TotalScoreResponse {
  /** 成绩列表 */
  items: ScoreItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总数量 */
  total: number
}

/**
 * 学期成绩统计数据
 */
export interface SemesterScoreStat {
  /** 学年学期 */
  xnxq: string
  /** 任务数 */
  rws: number
  /** 总分 */
  zf: number
  /** 总绩点 */
  jd: number
  /** 学分 */
  xf: number
  /** 优秀数 */
  yxs: number
  /** 良好数 */
  lhs: number
  /** 中等数 */
  zds: number
  /** 不及格数 */
  bjgs: number
  /** 键值 */
  key: number
  /** 平均分 */
  pjf: string
  /** 平均绩点 */
  pjjd: string
}

/**
 * 学期平均分数据
 */
export interface SemesterAvgData {
  /** 平均分数据 */
  data: string[]
  /** 学年学期 */
  xnxq: string[]
}

/**
 * 总评成绩统计响应
 */
export interface TotalScoreStatResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: {
    /** 总任务数 */
    totalRw: number
    /** 平均分 */
    pjf: string
    /** 平均绩点 */
    pjjd: string
    /** 学分 */
    xf: number
    /** 优秀数 */
    yxs: number
    /** 良好数 */
    lhs: number
    /** 中等数 */
    zds: number
    /** 不及格数 */
    bjgs: number
    /** 各学期数据 */
    pageData: Record<string, SemesterScoreStat>
    /** 类型数据 */
    typeData: SemesterAvgData
    /** 任务数 */
    rws: number
  }
}

/**
 * 教师任务总评成绩查询参数
 */
export interface TeacherTotalScoreQuery {
  /** 页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 教学任务ID */
  jxrwid: string
}

/**
 * 教师任务总评成绩项
 */
export interface TeacherTotalScoreItem {
  /** 成绩ID */
  id: number
  /** 所属班级编号 */
  ssbj: string
  /** 所属班级名称 */
  ssbjmc: string
  /** 学生学号 */
  xsxh: string
  /** 座位号 */
  zwh: number
  /** 学生姓名 */
  xm: string
  /** 教学任务ID */
  jxrwid: number
  /** 选课信息ID */
  xkxxid: number | null
  /** 学年 */
  xn: string
  /** 学期 */
  xq: string
  /** 主讲教师代码 */
  zdjs: string
  /** 主讲教师姓名 */
  zdjsxm: string
  /** 课程代码 */
  kcdm: string
  /** 课程名称 */
  kcmc: string
  /** 课程性质 */
  kcxz: string
  /** 课程学分 */
  kcxf: number
  /** 成绩评定类型 */
  cjpdlx: string
  /** 成绩 */
  cj: number
  /** 百分制总成绩 */
  bfzcj: number
  /** 通过标志 */
  tgbz: number
  /** 成绩绩点 */
  cjjd: string
  /** 成绩属性 */
  cjsx: string
  /** 成绩提交标志 */
  cjtjbz: number
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 成绩提交人员 */
  cjtjry: string
  /** 成绩提交时间 */
  cjtjsj: string
}

/**
 * 教师任务总评成绩查询响应
 */
export interface TeacherTotalScoreResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: {
    /** 成绩列表 */
    items: TeacherTotalScoreItem[]
    /** 查询参数 */
    query: Record<string, any>
    /** 总数量 */
    total: number
  }
}

/**
 * 设置成绩评定类型请求参数
 */
export interface SetScoreTypeRequest {
  /** 教学任务ID */
  jxrwid: string
  /** 成绩评定类型 */
  cjptlx: string
}

/**
 * 设置成绩评定类型响应数据
 */
export interface SetScoreTypeData {
  /** 评估类型 */
  assessmentType: string
  /** 更新时间 */
  update_time: number
}

/**
 * 设置成绩评定类型响应
 */
export interface SetScoreTypeResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: SetScoreTypeData
}

/**
 * 生成总评成绩请求参数
 */
export interface GenerateTotalScoreRequest {
  /** 教学任务ID */
  jxrwid: string
}

/**
 * 生成总评成绩响应
 */
export interface GenerateTotalScoreResponse {
  /** 状态码 */
  code: string
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: any[]
}

/**
 * 提交总评成绩请求参数
 */
export interface SubmitTotalScoreRequest {
  /** 教学任务ID */
  jxrwid: string
}

/**
 * 提交总评成绩响应
 */
export interface SubmitTotalScoreResponse {
  /** 状态码 */
  code: number
  frontMsg: string
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: null
}

/**
 * 日常成绩查询参数
 */
export interface DailyScoreQuery {
  /** 页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 教学任务ID */
  jxrwid: number
  /** 日常成绩登记ID */
  rccjdjid: number
}

/**
 * 日常成绩项
 */
export interface DailyScoreItem {
  /** ID */
  id: number
  /** 日常成绩登记ID */
  rccjdjid: number
  /** 所属班级 */
  ssbj: string
  /** 所属班级名称 */
  ssbjmc: string
  /** 学生学号 */
  xsxh: string
  /** 座位号 */
  zwh: number
  /** 学生姓名 */
  xm: string
  /** 教学任务ID */
  jxrwid: number
  /** 选课信息ID */
  xkxxid: number
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 主讲教师 */
  zdjs: string
  /** 主讲教师姓名 */
  zdjsxm: string
  /** 课程代码 */
  kcdm: string
  /** 课程名称 */
  kcmc: string
  /** 课程性质 */
  kcxz: string
  /** 课程学分 */
  kcxf: number
  /** 成绩评定类型 */
  cjpdlx: string
  /** 成绩 */
  cj: number
  /** 百分制总成绩 */
  bfzcj: number
  /** 通过标志 */
  tgbz: number
  /** 成绩绩点 */
  cjjd: string
  /** 成绩属性 */
  cjsx: string
  /** 成绩提交标志 */
  cjtjbz: number
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 成绩提交人员 */
  cjtjry: string
  /** 成绩提交时间 */
  cjtjsj: string
}

/**
 * 日常成绩查询响应
 */
export interface DailyScoreResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: {
    /** 成绩列表 */
    items: DailyScoreItem[]
    /** 查询参数 */
    query: Record<string, any>
    /** 总数量 */
    total: number
  }
}
