/**
 * 课节配置信息接口
 */
export interface SectionInfo {
  /** 节次序号 */
  section: number
  /** 节次名称 */
  sectionName: string
  /** 时间段（上午/中午/下午/晚上） */
  timePeriod: string
  /** 开始时间 */
  startTime: string
  /** 结束时间 */
  endTime: string
  /** 季节制度（冬令制/夏令制） */
  seasonal: string
  /** 校区 */
  campus: string
}

/**
 * 课节配置响应接口
 */
export interface SectionConfigResponse {
  sectionConfig: SectionInfo[]
}

export interface Section {
  /** 课节值 */
  value: string
  /** 课节名称 */
  name: string
  /** 课节标签 */
  label: string
  /** 原始课节值（用于提交） */
  originalValue?: string
}

/**
 * 获取课节列表的请求参数
 */
export interface SectionListParams {
  /** 录入套表库 */
  lrttbk: number
  /** 节次数 */
  jcs: number
}
