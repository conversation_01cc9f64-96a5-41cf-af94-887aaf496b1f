// 教材信息
export interface TeachingMaterial {
  id?: number
  selectionType: string
  teachingTasksId?: number
  textbookInfoId: number
  remark: string
  create_time?: number
  update_time?: number
  deltag?: number
  operatorCode?: string
  reviewStatus?: number
  isFirstSelection: number
  reasonForSelection: string
  isTeachingReference: number
  isExperimentalGuide: number
  textbookEvaluationOpinion: string
  evaluationReviewStatus: number
  selectCourseId?: number
  studyYear?: string
  studyTerm?: number
  courseCode?: string
  courseName?: string
  schoolCode?: string
  deptCode?: string
  teachOfficeCode?: string
  teachOfficeName?: string
  classCode?: string
  className?: string
  leaderTeacherCode?: string
  leaderTeacherName?: string
  teachingMethod?: string
  name: string
  mainEditor: string
  publishingHouse: string
  isbn: string
  publicationTime: string
  printingTime?: string | null
  category: string
  categoryName: string
  type: string
  typeName: string
  _X_ROW_KEY?: string
}

// 教学安排信息
export interface TeachingInfo {
  week: string
  section: string
  sectionKey: string
  siteType: string
  id?: string
  weekday?: string
  _X_ROW_KEY?: string
}

// 课程信息
export interface CourseInfo {
  courseName: string
  courseCode: string
}

// 选课参数
export interface SelectCourseParams {
  xkxxid: string
  optype: 'select'
  xktjid: number
  id?: number
  selectCourseId?: number
  semesters?: string
  deptCode?: string
  courseCode?: {
    courseName: string
    courseCode: string
    [key: string]: any
  }
  teachOfficeCode?: string
  teachOfficeName?: string
  taskType?: string
  courseCategory?: string
  weekHours?: string
  creditHour?: number
  courseTotalHours?: string
  teachingHours?: string
  experimentHours?: string
  computerHours?: string
  virtualHours?: string
  teachingMethod?: string
  assessmentMethod?: string
  campusCode?: string
  startingGrade?: string[]
  startClassDeptCode?: string[]
  class?: string[]
  limitCount?: number
  maxCount?: number
  startWeek?: number
  endWeek?: number
  remark?: string
  teachingInfo?: TeachingInfo[]
  teachingMaterialList?: TeachingMaterial[]
  teacherCode?: string
}

// 选课响应
export interface SelectCourseResponse {
  code: number
  msg: string
  time: number
  data: {
    rxkxsyxs: number
  }
}

// 获取选课列表请求参数
export interface SelectCourseListQuery {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: string
  semesters?: string[]
  courseName?: string
  leaderTeacherName?: string
  className?: string
  courseTotalHours?: string
  weekHours?: string
  creditHour?: string
  workloadNum?: string
  deptName?: string
  teachOfficeName?: string
  campusCode?: string
  startingGrade?: string
  startClassDeptName?: string
  status?: string
  limitCount?: string
  maxCount?: string
  selectedCount?: string
  startWeek?: string
  teachingInfo?: string
  siteName?: string
  taskExecutionStatus?: string
  teachOfficeAudit?: string
  deptAudit?: string
  academicAffairsApproval?: string
  submitStatus?: string
  courseCategory?: string
}

// 选课课程项
export interface SelectCourseItem {
  id: number
  planId: number | null
  selectCourseId: number
  mergeTaskId: number | null
  studyYear: string
  studyTerm: number
  taskType: string
  courseCode: string
  courseName: string
  courseType: string
  mainCourse: number
  assessmentMethod: string
  courseTotalHours: string
  teachingHours: string
  experimentHours: string
  computerHours: string
  virtualHours: string
  weekHours: string
  weekHoursCourseInfo: string
  weeks: number
  weeksCourseInfo: number
  creditHour: number
  schoolCode: string
  deptCode: string
  deptName: string
  teachOfficeCode: string
  teachOfficeName: string
  remark: string
  create_time: number
  update_time: number
  deltag: number
  leaderTeacherCode: string
  leaderTeacherName: string
  otherTeacherName: string
  otherTeacherCode: string
  classCode: string
  className: string
  taskTypeStatus: string
  taskExecutionStatus: string
  sectionRepresentative: null | string
  sectionRepresentativeName: string
  checkingInOpen: number
  isAssessment: string
  assessmentType: string
  submitStatus: string | number
  textbookUse: number
  operatorCode: string
  siteCode: null | string
  siteName: null | string
  startWeek: number
  endWeek: number
  teachingInfo: string
  teachingWeek: null | string
  classWeeks: string
  studentKnowledge: null | string
  followingCourseKnowledge: null | string
  followingCourseSkill: null | string
  teachingOutline: null | string
  knowledgeObjective: null | string
  abilityObjective: null | string
  qualityObjective: string
  assessmentWay: null | string
  pairTeacher: string
  pairTeacherHours: string
  workloadNum: string
  affirmHours: string
  teachingPlanApproval: number
  evaluationShouldNum: null | number
  evaluationActualNum: null | number
  evaluationAverageScore: null | number
  evaluationEffectiveNum: null | number
  evaluationScore: null | number
  isSubmitButton: number
  teachingPlanSubmit: number
  teachingPlanStatus: number
  courseStandardAttachment: number
  isCourseEvaluationLock: number
  teacherManualAnalysis: null | string
  workbookSubmitStatus: number
  workbookTeachOfficeApproval: number
  workbookDeptApproval: number
  isExcludeAttendanceExam: number
  teachingMethod: string
  teachingMethodName: string
  gljxpt: string
  course_id: number
  class_id: number
  campusCode: string
  campusName: string
  startingGrade: string
  startClassDeptCode: string
  startClassDeptName: string
  class: string
  maxCount: number
  limitCount: number
  courseTitle: string
  courseCategory: string
  courseCategoryName: string
  type: string
  teachOfficeAudit: number
  deptAudit: number
  academicAffairsApproval: number
  selectedCount: number
  addPermission: number
  internshipStartStatus: number
  internshipStatus: number
  status: number
  internshipStartTime: string
  internshipEndTime: string
  startingWeek: string
  isEdit: number
  gzldm: string
  isCancel: boolean
}

// 选课列表响应数据
export interface SelectCourseListResponse {
  items: SelectCourseItem[]
  query: Record<string, any>
  total: number
  isAdd?: boolean
  info?: string
  tips?: string
  notice?: {
    id: number
    columnType: number
    audienceType: number
    audienceCode: string
    audienceName: string
    columnCode: string
    columnName: string
    title: string
    summary: string
    content: string
    organizerName: string
    publisher: string
    publisherName: string
    publisherDept: string
    publisherDeptName: string
    publishTime: string
    publisherAddress: string
    publisherCoordinates: string
    lng: number
    lat: number
    deviceSystem: string
    deviceVendor: string
    deviceModel: string
    contentType: number
    viewCount: number
    replyCount: number
    likeCount: number
    readCount: number
    titleImage: string
    imageList: string
    fileList: string
    externalLink: string
    lastReplier: string
    lastReplierName: string
    lastReplyTime: string
    requireLogin: number
    remark: string
    create_time: number
    update_time: number
    deltag: number
    operatorCode: string
    subjectCode: string
    subjectCategory: string
    subjectStatus: number
    isSystemSubject: number
    subjectOrder: number
    subjectAuditStatus: number
    contentFormat: string
    pushStatusToTeacher: number
    pushTimeToTeacher: number
    pushStatusToStudent: number
    pushTimeToStudent: number
  }
  jssj?: number
  xnxq?: string
}

// 学生选课查询参数
export interface SchoolSelectCourseQuery {
  /** 操作类型 */
  optype: string
  /** 页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 学期，格式：学年|学期，如：2024-2025|2 */
  semesters: string
  /** 课程类别名称 */
  courseCategoryName?: string
  /** 课程名称 */
  courseName?: string
  /** 主讲教师名称 */
  leaderTeacherName?: string
  /** 班级名称 */
  className?: string
  /** 课程总学时 */
  courseTotalHours?: string
  /** 周学时 */
  weekHours?: string
  /** 学分 */
  creditHour?: string
  /** 起始周 */
  startWeek?: string
  /** 教学安排 */
  teachingInfo?: string
  /** 教学场地 */
  siteName?: string
  /** 限制人数 */
  limitCount?: string
  /** 已选人数 */
  selectedCount?: string
  /** 校区名称 */
  campusName?: string
}

// 学生选课项数据
export interface StudentCourseItem {
  /** 记录ID */
  id: number
  /** 教学任务ID */
  teachingTasksId: number
  /** 选课ID */
  selectCourseId: number
  /** 学生学号 */
  studentCode: string
  /** 添加类型 */
  addType: number
  /** 添加时间 */
  addTime: string
  /** 分组ID */
  groupingId: number
  /** 分组名称 */
  groupingName: string
  /** 重修成绩ID */
  retakeScoreId: number
  /** 选课来源 */
  selectCourseSource: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作人编码 */
  operatorCode: string
  /** 课程名称 */
  courseName: string
  /** 学年 */
  studyYear: string
  /** 学期 */
  studyTerm: number
  /** 课程类别 */
  courseCategory?: string
  /** 课程类别名称 */
  courseCategoryName: string
  /** 主讲教师名称 */
  leaderTeacherName: string
  /** 课程总学时 */
  courseTotalHours: string
  /** 周学时 */
  weekHours: string
  /** 教学场地 */
  siteName: string | null
  /** 学分 */
  creditHour: number
  /** 起始周 */
  startWeek: string
  /** 结束周 */
  endWeek: number
  /** 教学安排 */
  teachingInfo: string
  /** 班级名称 */
  className: string
  /** 限制人数 */
  maxCount: number
  /** 已选人数 */
  selectedCount: number
  /** 成绩 */
  cj?: number | string
}

// 学期学年设置信息
export interface SemesterConfig {
  /** 配置ID */
  id: number
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 开学时间 */
  kxsj: string
  /** 结束时间 */
  jssj: string
  /** 周数 */
  zs: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 其他配置信息... */
  [key: string]: any
}

// 学生选课列表响应数据
export interface SchoolSelectCourseResponse {
  /** 请求参数 */
  params: Record<string, string>
  /** 选课列表 */
  items: StudentCourseItem[]
  /** 总数 */
  total: number
  /** 是否显示提交按钮 */
  showsbbutton: number
  /** 学生选修统考允许重叠次数 */
  xszxtxyczcs: number
  /** 学年学期设置信息 */
  xnxqszxx: SemesterConfig
  xsxktjszcheck: number
  xsxktjsz: {
    remark: string
  }
  rxkxsyxs: number
}

// 退选课程请求参数
export interface UnselectCourseParams {
  /** 选课记录ID */
  id: number
  /** 备注信息 */
  remark: string
  /** 操作类型，固定为"unSelect" */
  optype: 'unSelect'
}

// 退选课程响应数据
export interface UnselectCourseResponse {
  /** 学生选修统考允许重叠次数 */
  xszxtxyczcs: number
}

// 可选课程查询参数
export interface AvailableCourseQuery {
  optype: 'list'
  page: number
  pageSize: number
  semesters: string
  courseCategory: string
  courseCategoryName: string
  courseName: string
  leaderTeacherName: string
  className: string
  courseTotalHours: string
  weekHours: string
  creditHour: string
  startWeek: string
  teachingInfo: string
  siteName: string
  limitCount: string
  selectedCount: string
  campusName: string
  /** 是否只查看可选课程，1: 只看可选课程，false: 查看全部 */
  check?: number | boolean
}

// 可选课程项
export interface AvailableCourseItem {
  id: number
  teachingTasksId: number
  selectCourseId: number
  classCode: string
  remark: string
  create_time: number
  update_time: number | null
  deltag: number
  operatorId: string
  courseName: string
  courseCode: string
  studyYear: string
  studyTerm: number
  courseCategoryName: string
  leaderTeacherName: string
  courseTotalHours: string
  weekHours: string
  siteName: string | null
  creditHour: number
  startWeek: string
  endWeek: number
  teachingInfo: string
  teachingMethod: string
  className: string
  courseCategory: string
  maxCount: number
  selectedCount: number
  limitCount: number
  deptAudit: number
  audit: number
  mergeTaskId: number
  type: string
  campusName: string
}

// 可选课程响应
export interface AvailableCourseResponse {
  items: AvailableCourseItem[]
  total: number
  /** 教学批次状态 */
  jxcpzt: number
  /** 选课状态标签 */
  label: string
  /** 学年学期设置信息 */
  xnxqszxx: {
    id: number
    xn: string
    xq: number
    kxsj: string
    jssj: string
    zs: number
    remark: string
    create_time: number
    update_time: number
    deltag: number
    ssxy: string
    xsbdsj: string
    xlxx: string
    jsxxksbkssj: string
    jsxxksbjssj: string
    rxkxsxxsl: number
    rxkxkkssj: string
    rxkxkjssj: string
    rxkxksm: string
    rxktxkssj: string
    rxktxjssj: string
    xszxtxczcs: number
    zckjxrcapkfkssj: string
    zckjxrcapkfjssj: string
    fzckjxrcapkfkssj: string
    fzckjxrcapkfjssj: string
    bkkfkssj: string
    bkkfjssj: string
    jxzlxscpkssj: string
    jxzlxscpjssj: string
    jxzlxscpsm: string
    jxzljscpkssj: string
    jxzljscpjssj: string
    jxzljscpsm: string
    jxzlxsqdzgfbl: number
    jxzlxsqdzdfbl: number
    jxzlxscpddbl: number
    sdcpkssj: string
    sdcpjssj: string
    sdcpsm: string
    sdcpzgfbl: number
    sdcpzdfbl: number
    sdcpddbl: number
    bkcxcjlrkssj: string
    bkcxcjlrjssj: string
    cjlrkssj: string
    cjlrjssj: string
    skqrzcts: number
    xsbdlcsm: string
    jxgzlrdkssj: string
    jxgzlrdjssj: string
    cpbdcwxbdm: string
    jcsjsd: string
    jxrcappksz: number
    xltkpksz: number
    rczmpksz: number
    zdpkdsz: number
    cxxkkssj: string
    cxxkjssj: string
    cxxksm: string
    cxkxzsl: number
    pyjhpzkssj: string
    pyjhpzjssj: string
    tkjlzcts: number
    jcxysbkssj: string
    jcxysbjssj: string
    xxqkxsj: string
    xxqjssj: string
    xxqzs: number
    xxqsjxq: string
    xxqsjnj: string
    xxqpczydm: string
    xxqpczymc: string
    zckrcapksrq: string
    dyfzpbl: number
    dyfzpkssj: string
    dyfzpjssj: string
    dyfhpbl: number
    dyfhpkssj: string
    dyfhpjssj: string
    dymxfjssj: string
    dyfxgjssj: string
    zdsqkssj: string
    lssqkssj: string
    lssqjssj: string
    zdsqjssj: string
    ybmxxkkssj: string
    ybmxxkjssj: string
    yxcqffkssj: string
    yxcqffjssj: string
    yxcqkqlx: string
    rsglpz: null | string
    xmjfglpz: string
  }
  /** 是否显示提交按钮 */
  showsbbutton: number
  /** 学生开放时间信息，可能为空数组 */
  xskfsjxx:
    | Array<{
        id: number
        xn: string
        xq: number
        kfkssj: string
        kfjssj: string
        kfxq: string
        kfxqmc: string
        kfnj: string
        kfxb: string
        kfxbmc: string
        xkms: number
        remark: string
        create_time: number
        update_time: number
        deltag: number
        oprybh: string
      }>
    | {
        id: number
        xn: string
        xq: number
        kfkssj: string
        kfjssj: string
        kfxq: string
        kfxqmc: string
        kfnj: string
        kfxb: string
        kfxbmc: string
        xkms: number
        remark: string
        create_time: number
        update_time: number
        deltag: number
        oprybh: string
      }
  /** 教学任务选课开放时间信息 */
  jxrwxkxxkfsjxx: Array<{
    id: number
    xn: string
    xq: number
    kfkssj: string
    kfjssj: string
    kfxq: string
    kfxqmc: string
    kfnj: string
    kfxb: string
    kfxbmc: string
    xkms: number
    remark: string
    create_time: number
    update_time: number
    deltag: number
    oprybh: string
  }>
  /** 学生选课统计设置检查 */
  xsxktjszcheck: number
  /** 学生选课统计设置 */
  xsxktjsz: {
    id: number
    xn: string
    xq: string
    selectbmdm: string
    selectxsnj: string
    bmdm: string
    bmmc: string
    tjplsz: number
    szx: string
    szxsm: string
    remark: string
    create_time: number
    update_time: number
    deltag: number
    oprybh: string
  }
  /** 学生选课限选学时 */
  rxkxsyxs: number
  /** 学年 */
  xn: string
  /** 学期 */
  xq: string
}

// 已选课程查询参数
export interface SelectedCoursesQuery {
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: string
  semesters: string[]
  courseCategory?: string
  courseName?: string
  leaderTeacherName?: string
  className?: string
  courseTotalHours?: string
  weekHours?: string
  creditHour?: string
  startWeek?: string
  teachingInfo?: string
  siteName?: string
  createTime?: string
}

// 已选课程项
export interface SelectedCourseItem {
  id: number
  teachingTasksId: number
  selectCourseId: number
  studentCode: string
  addType: number
  addTime: string
  groupingId: number
  groupingName: string
  retakeScoreId: number
  selectCourseSource: number
  remark: string
  createTime: string
  update_time: number
  deltag: number
  operatorCode: string
  courseName: string
  studyYear: string
  studyTerm: number
  courseCategory: string
  courseCategoryName: string
  maxCount: number
  selectedCount: number
  campusName: string
  leaderTeacherName: string
  courseTotalHours: string
  weekHours: string
  siteName: string | null
  creditHour: number
  startWeek: string
  endWeek: number
  teachingInfo: string
  className: string
}

// 已选课程响应
export interface SelectedCoursesResponse {
  params: Record<string, any>
  items: SelectedCourseItem[]
  total: number
}

/**
 * 选课设置类型
 * 包含学期设置和开放时间等信息
 */
export interface SelectCourseSettingResponse {
  /** 教学批次状态 */
  jxcpzt: number
  /** 状态标签 */
  label: string
  /** 结束时间（时间戳） */
  jssj: number
  /** 开始时间（时间戳） */
  kssj?: number
  /** 学年学期设置信息 */
  xnxqszxx: {
    /** 配置ID */
    id?: number
    /** 学年 */
    xn: string
    /** 学期 */
    xq: number
    /** 开学时间 */
    kxsj?: string
    /** 结束时间 */
    jssj?: string
    /** 周数 */
    zs?: number
    /** 备注 */
    remark?: string
    /** 创建时间 */
    create_time?: number
    /** 更新时间 */
    update_time?: number
    /** 删除标记 */
    deltag?: number
    /** 所属学院 */
    ssxy?: string
    /** 学生报到时间 */
    xsbdsj?: string
    /** 学历信息 */
    xlxx?: string
    /** 教师选课申报开始时间 */
    jsxxksbkssj?: string
    /** 教师选课申报结束时间 */
    jsxxksbjssj?: string
    /** 教师选课修改开始时间 */
    jsxxkxgkssj?: string
    /** 教师选课修改结束时间 */
    jsxxkxgjssj?: string
    /** 教师选课申报说明 */
    jsxksbsm?: string
    /** 人选课信息数量限制 */
    rxkxsxxsl: number
    /** 人选课开课开始时间 */
    rxkxkkssj: string
    /** 人选课开课结束时间 */
    rxkxkjssj: string
    /** 人选课开课说明 */
    rxkxksm: string
    /** 人选课填写开始时间 */
    rxktxkssj?: string
    /** 人选课填写结束时间 */
    rxktxjssj?: string
    /** 学生选修统考重叠次数 */
    xszxtxczcs?: number
    /** 学生选课开始时间 */
    xsxkkssj?: string
    /** 学生选课结束时间 */
    xsxkjssj?: string
    /** 调班参数设置 */
    cpbdcwxbdm?: string
    /** 计时时段 */
    jcsjsd?: string
    /** 重修选课开始时间 */
    cxxkkssj?: string
    /** 重修选课结束时间 */
    cxxkjssj?: string
    /** 重修选课说明 */
    cxxksm?: string
    /** 重修课选择数量 */
    cxkxzsl?: number
    /** 其他配置字段... */
    [key: string]: any
  }
  /** 是否显示提交按钮 */
  showsbbutton: number
  /** 学生开放时间信息 */
  xskfsjxx: {
    /** ID */
    id: number
    /** 学年 */
    xn: string
    /** 学期 */
    xq: number
    /** 开放开始时间 */
    kfkssj: string
    /** 开放结束时间 */
    kfjssj: string
    /** 开放校区编码 */
    kfxq: string
    /** 开放校区名称 */
    kfxqmc: string
    /** 开放年级 */
    kfnj: string
    /** 开放系部编码 */
    kfxb: string
    /** 开放系部名称 */
    kfxbmc: string
    /** 选课门数 */
    xkms: number
    /** 备注 */
    remark: string
    /** 创建时间 */
    create_time: number
    /** 更新时间 */
    update_time: number
    /** 删除标记 */
    deltag: number
    /** 操作人编号 */
    oprybh: string
  }
  /** 教学任务选课信息开放时间信息 */
  jxrwxkxxkfsjxx: Array<{
    /** ID */
    id: number
    /** 学年 */
    xn: string
    /** 学期 */
    xq: number
    /** 开放开始时间 */
    kfkssj: string
    /** 开放结束时间 */
    kfjssj: string
    /** 开放校区编码 */
    kfxq: string
    /** 开放校区名称 */
    kfxqmc: string
    /** 开放年级 */
    kfnj: string
    /** 开放系部编码 */
    kfxb: string
    /** 开放系部名称 */
    kfxbmc: string
    /** 选课门数 */
    xkms: number
    /** 备注 */
    remark: string
    /** 创建时间 */
    create_time: number
    /** 更新时间 */
    update_time: number
    /** 删除标记 */
    deltag: number
    /** 操作人编号 */
    oprybh: string
  }>
  /** 学生选课统计设置检查 */
  xsxktjszcheck: number
  /** 学生选课统计设置 */
  xsxktjsz?: {
    /** ID */
    id?: number
    /** 学年 */
    xn?: string
    /** 学期 */
    xq?: string
    /** 选择部门代码 */
    selectbmdm?: string
    /** 选择学生年级 */
    selectxsnj?: string
    /** 部门代码 */
    bmdm?: string
    /** 部门名称 */
    bmmc?: string
    /** 统计批量设置 */
    tjplsz?: number
    /** 设置项 */
    szx?: string
    /** 设置项说明 */
    szxsm?: string
    /** 备注 */
    remark: string
    /** 创建时间 */
    create_time?: number
    /** 更新时间 */
    update_time?: number
    /** 删除标记 */
    deltag?: number
    /** 操作人编号 */
    oprybh?: string
  }
  /** 已选课程ID列表 */
  xsxkxx?: number[]
  /** 人选课信息学时 */
  rxkxsyxs: number
  /** 学年 */
  xn: string
  /** 学期 */
  xq: string
  /** 图表数据 */
  chartData?: Array<{
    /** 值 */
    value: number
    /** 名称 */
    name: string
    /** 颜色 */
    color: string
  }>
}

/**
 * 教师选课统计响应
 */
export interface SelectCourseStatisticsResponse {
  /** 申报门数 */
  sbms: number
  /** 审核门数 */
  shms: number
  /** 通过信息 */
  tgxx: {
    /** 通过门数 */
    tgms: number
    /** 周学时 */
    zxs: number
    /** 选课人数 */
    xkrs: number
  }
  /** 不通过门数 */
  btgms: number
  /** 总门数信息 */
  zmsxx: {
    /** 总门数 */
    zms: number
    /** 学期总学时 */
    xqzxs: number
    /** 总选课人数 */
    zxkrs: number
  }
}

/**
 * 学生选课成绩查询参数
 */
export interface SelectCourseScoreQuery {
  /** 操作类型 */
  optype: 'selectScore'
  /** 页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 排序字段 */
  sortBy?: string
  /** 排序方式 */
  sortOrder?: string
  /** 学期列表 */
  semesters?: string[]
  /** 课程类别 */
  courseCategory?: string
  /** 课程名称 */
  courseName?: string
  /** 主讲教师名称 */
  leaderTeacherName?: string
  /** 成绩 */
  cj?: string
  /** 班级名称 */
  className?: string
  /** 课程总学时 */
  courseTotalHours?: string
  /** 周学时 */
  weekHours?: string
  /** 学分 */
  creditHour?: string
  /** 起始周 */
  startWeek?: string
  /** 教学安排 */
  teachingInfo?: string
  /** 教学场地 */
  siteName?: string
  /** 创建时间 */
  createTime?: string
}

/**
 * 学生选课成绩响应
 */
export interface SelectCourseScoreResponse {
  /** 选课成绩列表 */
  items: StudentCourseItem[]
  /** 总数 */
  total: number
}
