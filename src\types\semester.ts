/**
 * 学期选项接口
 */
export interface SemesterOption {
  /** 学期标签 */
  label: string
  /** 学期值 */
  value: string
  /** 学期值（格式2：学年|学期） */
  value2: string
  /** 季节制（夏令制/冬令制） */
  seasonal: string
  /** 是否为当前学期 */
  isCurrent?: boolean
  /** 是否为小学期 */
  isXxq?: boolean
}

/**
 * 学年选项接口
 */
export interface SchoolYearOption {
  /** 学年标签 */
  label: string
  /** 学年值 */
  value: string
  /** 是否为当前学年 */
  isCurrent?: boolean
}

/**
 * 学年响应接口
 */
export interface SchoolYearResponse {
  /** 状态码 */
  code: number
  /** 响应消息 */
  msg: string
  /** 响应时间戳 */
  time: number
  /** 学年列表 */
  schoolYears: SchoolYearOption[]
}

/**
 * 学期选择响应接口
 */
export interface SemesterSelectResponse {
  /** 学期选项列表 */
  data: SemesterOption[]
  /** 默认学期值 */
  default: string
  /** 下一个学期值 */
  next: string
  /** 选课学年学期 */
  xkxn: string
}

/**
 * 学期接口响应
 */
export interface SemesterResponse {
  /** 学期列表 */
  semesters: SemesterOption[]
}

/**
 * 校历日历项
 */
export interface CalendarItem {
  /** 放假日期 */
  closedDate: string
  /** 调课日期 */
  tradeDate: string
  /** 备注说明 */
  remark: string
}

/**
 * 学期配置信息
 */
export interface SemesterConfig {
  /** 总周数 */
  totalWeeks: number
  /** 开始日期 */
  startDate: string
  /** 结束日期 */
  endDate: string
  /** 成绩录入开始时间 */
  gradesStartDate: string
  /** 成绩录入结束时间 */
  gradesEndDate: string
  /** 学期说明 */
  descriptions: string
  /** 注册日期 */
  registration: string
  /** 校历日历 */
  calendar: CalendarItem[]
  /** 小学期开始日期 */
  xxqStartDate: string
  /** 小学期结束日期 */
  xxqEndDate: string
  /** 小学期周数 */
  xxqWeek: number
}

/**
 * 学期配置响应
 */
export interface SemesterConfigResponse {
  /** 状态码 */
  code: number
  /** 响应消息 */
  msg: string
  /** 响应时间戳 */
  time: number
  /** 学期配置信息 */
  semestersConfig: SemesterConfig
}
