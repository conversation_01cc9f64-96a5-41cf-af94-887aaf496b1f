// 字典项类型
export interface DictItem {
  label: string
  value: string
}

// 字典接口返回类型
export interface DictResponse {
  code: number
  msg: string
  time: number
  data: DictItem[]
}

// 字典类型枚举
export enum DictTypeEnum {
  /** 证件类型代码 */
  ID_TYPE = 'DM_ZJLXDM',
  /** 婚姻状况代码 */
  MARRIAGE_STATUS = 'DM_HYZK',
  /** 婚姻状况代码(另一种格式) */
  MARRIAGE_STATUS_ALT = 'DM_HYZKDM',
  /** 性别代码 */
  GENDER = 'DM_XBDM',
  /** 政治面貌代码 */
  POLITICAL_STATUS = 'DM_ZZMMDM',
  /** 文化程度/学历代码 */
  EDUCATION_LEVEL = 'DM_WHCDDM',
  /** 学位代码 */
  DEGREE = 'DM_XWDM',
  /** 职务类别代码 */
  POSITION_CATEGORY = 'DM_ZWLBDM',
  /** 职称级别代码 */
  PROFESSIONAL_TITLE_LEVEL = 'DM_ZCJBDM',
  /** 专业技术职务代码 */
  PROFESSIONAL_TITLE = 'DM_ZYJSZWDM',
  /** 计划生育状况代码 */
  FAMILY_PLANNING_STATUS = 'DM_JHSYZK',
  /** 学生健康状况代码 */
  STUDENT_HEALTH_STATUS = 'DM_STJKZKDM',
  /** 户口类型代码 */
  ACCOUNTS_CATEGORY = 'DM_HKLBDM',
  /** 国籍地区代码 */
  NATIONALITY = 'DM_GJDQ',
  /** 港澳台侨外代码 */
  HMT_CODE = 'DM_GATQWDM',
  /** 学生学历代码 */
  STUDENT_EDUCATION = 'DM_XSXLDM',
  /** 是否独生子女代码 */
  IS_ALONE = 'SYS_ALONE_TYPE',
  /** 住宿情况代码 */
  LODGING = 'DM_ZSQKDM',
  /** 居住地类型代码 */
  RESIDENCE_TYPE = 'DM_JZDLXDM',
  /** 城市字典数据 */
  CITY_DICT = 'CITY_DICT',
}

export interface SemesterOption {
  label: string
  value: string
}

export interface ApiResponse<T> {
  code: number
  msg: string
  time: number
  data: T
}

// 字典数据类型
export interface DictData {
  createBy: string
  createTime: string
  cssClass: string
  default: boolean
  dictCode: number
  dictLabel: string
  dictSort: number
  dictType: string
  dictValue: string
  isDefault: string
  listClass: string
  remark: string
  status: string
  updateBy: string
  updateTime: string
}

export interface DictDataResponse {
  code: number
  msg: string
  time: number
  data: DictData[]
}
