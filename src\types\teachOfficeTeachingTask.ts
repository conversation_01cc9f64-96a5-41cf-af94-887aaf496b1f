/**
 * 教研室教学任务相关类型定义
 */

/**
 * 教学任务查询参数
 */
export interface TeachOfficeTeachingTaskQuery {
  /** 页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 搜索关键词 */
  searchKeyword: string
  /** 学期，格式为"学年|学期"，如"2024-2025|2" */
  semesters: string
}

/**
 * 教学任务项
 */
export interface TeachOfficeTeachingTaskItem {
  /** 教学任务ID */
  id: number
  jxrwid: number
  /** 计划ID */
  planId: number | null
  /** 选课ID */
  selectCourseId: number
  /** 合并任务ID */
  mergeTaskId: number | null
  /** 学年 */
  studyYear: string
  /** 学期 */
  studyTerm: number
  /** 任务类型 */
  taskType: string
  /** 课程代码 */
  courseCode: string
  /** 课程名称 */
  courseName: string
  /** 课程类型 */
  courseType: string
  /** 是否主课程 */
  mainCourse: number
  /** 考核方式 */
  assessmentMethod: string
  /** 课程总学时 */
  courseTotalHours: string
  /** 教学学时 */
  teachingHours: string
  /** 实验学时 */
  experimentHours: string
  /** 计算机学时 */
  computerHours: string
  /** 虚拟学时 */
  virtualHours: string
  /** 周学时 */
  weekHours: string
  /** 课程信息周学时 */
  weekHoursCourseInfo: string
  /** 周数 */
  weeks: number
  /** 课程信息周数 */
  weeksCourseInfo: number
  /** 学分 */
  creditHour: number
  /** 学校代码 */
  schoolCode: string | null
  /** 院系代码 */
  deptCode: string
  /** 院系名称 */
  deptName: string
  /** 教研室代码 */
  teachOfficeCode: string
  /** 教研室名称 */
  teachOfficeName: string
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 主讲教师代码 */
  leaderTeacherCode: string
  /** 主讲教师名称 */
  leaderTeacherName: string
  /** 其他教师名称 */
  otherTeacherName: string
  /** 其他教师代码 */
  otherTeacherCode: string | null
  /** 班级代码 */
  classCode: string
  /** 班级名称 */
  className: string
  /** 任务类型状态 */
  taskTypeStatus: string
  /** 任务执行状态 */
  taskExecutionStatus: number
  /** 班级代表 */
  sectionRepresentative: string | null
  /** 班级代表名称 */
  sectionRepresentativeName: string
  /** 是否开启签到 */
  checkingInOpen: number
  /** 是否考核 */
  isAssessment: string
  /** 考核类型 */
  assessmentType: string
  /** 提交状态 */
  submitStatus: number
  /** 教材使用 */
  textbookUse: number
  /** 教材使用说明 */
  jcxysm: string
  /** 操作员代码 */
  operatorCode: string
  /** 场地代码 */
  siteCode: string | null
  /** 场地名称 */
  siteName: string | null
  /** 开始周 */
  startWeek: number
  /** 结束周 */
  endWeek: number
  /** 教学信息 */
  teachingInfo: string
  /** 教学周 */
  teachingWeek: string | null
  /** 班级周 */
  classWeeks: string
  /** 学生知识 */
  studentKnowledge: string | null
  /** 后续课程知识 */
  followingCourseKnowledge: string | null
  /** 后续课程技能 */
  followingCourseSkill: string | null
  /** 教学大纲 */
  teachingOutline: string | null
  /** 知识目标 */
  knowledgeObjective: string | null
  /** 能力目标 */
  abilityObjective: string | null
  /** 素质目标 */
  qualityObjective: string
  /** 评估方式 */
  assessmentWay: string | null
  /** 配对教师 */
  pairTeacher: string
  /** 配对教师学时 */
  pairTeacherHours: string
  /** 工作量数量 */
  workloadNum: string
  /** 确认学时 */
  affirmHours: string
  /** 教学计划审批 */
  teachingPlanApproval: number
  /** 评估应有数量 */
  evaluationShouldNum: number | null
  /** 评估实际数量 */
  evaluationActualNum: number | null
  /** 评估平均分数 */
  evaluationAverageScore: number | null
  /** 评估有效数量 */
  evaluationEffectiveNum: number | null
  /** 评估分数 */
  evaluationScore: number | null
  /** 是否提交按钮 */
  isSubmitButton: number
  /** 教学计划提交 */
  teachingPlanSubmit: number
  /** 教学计划状态 */
  teachingPlanStatus: number
  /** 课程标准附件 */
  courseStandardAttachment: number
  /** 是否锁定课程评估 */
  isCourseEvaluationLock: number
  /** 教师手动分析 */
  teacherManualAnalysis: string | null
  /** 工作簿提交状态 */
  workbookSubmitStatus: number
  /** 工作簿教研室审批 */
  workbookTeachOfficeApproval: number
  /** 工作簿部门审批 */
  workbookDeptApproval: number
  /** 是否排除考勤考试 */
  isExcludeAttendanceExam: number
  /** 教学方法 */
  teachingMethod: string
  /** 教学方法名称 */
  teachingMethodName: string
  /** 管理教学平台 */
  gljxpt: string
  /** 课程ID */
  course_id: number
  /** 班级ID */
  class_id: number
  /** 教学任务ID */
  teachingTasksId: number
  /** 任务执行状态文本 */
  taskExecStatus: string
  /** 年级 */
  grade: string | null
  /** 校区代码 */
  campusCode: string | null
  /** 校区名称 */
  campusName: string | null
  /** 专业代码 */
  majorCode: string | null
  /** 专业名称 */
  majorName: string | null
  /** 班级人数 */
  classInCount: number | null
  /** 开始结束 */
  startEnd: string
}

/**
 * 教学任务响应数据
 */
export interface TeachOfficeTeachingTaskResponse {
  /** 响应代码 */
  code: number
  /** 响应消息 */
  msg: string
  /** 响应时间戳 */
  time: number
  /** 响应数据 */
  data: {
    /** 教学任务列表 */
    items: TeachOfficeTeachingTaskItem[]
    /** 查询参数 */
    query: Record<string, any>
    /** 总数 */
    total: number
  }
}
