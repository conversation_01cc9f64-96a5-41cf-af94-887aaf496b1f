export interface TeachingJournalItem {
  id: string
  courseName: string
  week: string
  time: string
  location: string
  className: string
  attendance:
    | string
    | {
        total: number
        present: number
        absent: number
      }
  content?: string
  feedback?: string
  progress?: string
  homework?: string
  remarks?: string
  status: '0' | '1' // 0: 未填写, 1: 已填写
}

export interface TeachingJournalListResponse {
  items: TeachingJournalItem[]
  total: number
}

export interface TeachingJournalQueryParams {
  courseName?: string
  className?: string
  week?: string
  status?: string
  page: number
  pageSize: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}
