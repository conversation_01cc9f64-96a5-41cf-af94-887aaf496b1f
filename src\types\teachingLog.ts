/**
 * 教学日志相关类型定义
 */

/**
 * 教学日志查询参数
 */
export interface TeachingLogQueryParams {
  /** 当前页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 班级名称 */
  className?: string
  /** 课程名称 */
  courseName?: string
  /** 周次 */
  week?: string
  /** 授课日期范围 */
  giveLessonsDate?: string[]
  /** 节次 */
  sectionShow?: string
  /** 场地名称 */
  siteName?: string
  /** 授课方式名称 */
  giveLessonsModeName?: string
  /** 授课内容 */
  giveLessonsContent?: string
  /** 学生确认状态 */
  studentConfirmStatus?: string | number
  /** 学生确认人 */
  studentConfirmName?: string
  /** 学生确认日期范围 */
  studentConfirmDate?: string[]
  /** 教师确认状态 */
  teacherConfirmStatus?: string | number
  /** 教师确认日期范围 */
  teacherConfirmDate?: string[]
  /** 教学计划状态 */
  teachingPlanStatus?: string | number
  /** 工作量状态 */
  workloadStatus?: string | number
  /** 周期 */
  cycle?: string
  /** 学年学期 */
  semesters?: string[]
  /** 索引签名，允许添加任意字符串索引的属性 */
  [key: string]: unknown
}

/**
 * 教学日志管理查询参数
 */
export interface TeachingLogManageQueryParams {
  /** 当前页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 排序字段 */
  sortBy?: string
  /** 排序方式 */
  sortOrder?: 'asc' | 'desc'
  /** 学年学期 */
  semesters?: string
  /** 状态 */
  status?: string
  /** 授课班级名称 */
  skbjmc?: string
  /** 课程名称 */
  kcmc?: string
  /** 学期数 */
  xqs?: string
  /** 授课日期 */
  skrq?: string[]
  /** 节次 */
  jcshow?: string
  /** 授课教师姓名 */
  skjsxm?: string
  /** 授课方式 */
  skfs?: string
  /** 授课内容 */
  sknl?: string
  /** 场地类型 */
  skcdlx?: string
  /** 场地名称 */
  skcdmc?: string
  /** 课时 */
  ks?: string
  /** 工作量系数 */
  gzlxs?: string
  /** 学生确认状态 */
  skjhxsqrzt?: string
  /** 学生确认时间 */
  skjhxsqrsj?: string
  /** 教师确认状态 */
  skjhjsqrzt?: string
  /** 教师确认时间 */
  skjhjsqrsj?: string
  /** 教学计划状态 */
  skjhzxzt?: string
  /** 工作量录入状态 */
  skjhgzlrdzt?: string
  /** 性质 */
  xz?: string
  /** 备注 */
  remark?: string
  /** 更新时间 */
  update_time?: string
  /** 创建时间 */
  create_time?: string
  /** 周期 */
  cycle?: string
  /** 校区 */
  ssxq?: string
  /** 时间范围 */
  timeRange?: string
  /** 星期 */
  weekday?: string
  /** 节次 */
  section?: string
  /** 场地 */
  location?: string
  /** 部门 */
  department?: string
  /** 索引签名，允许添加任意字符串索引的属性 */
  [key: string]: unknown
}

/**
 * 教学日志项
 */
export interface TeachingLogItem {
  /** ID */
  id: number
  /** 教学任务ID */
  teachingTasksId: number
  /** 周期 */
  cycle: number
  /** 授课日期 */
  giveLessonsDate: string
  /** 授课开始时间 */
  giveLessonsStartTime: string
  /** 授课结束时间 */
  giveLessonsEndTime: string
  /** 周次 */
  week: number
  /** 节次 */
  section: string
  /** 节次显示 */
  sectionShow: string
  /** 授课内容 */
  giveLessonsContent: string
  /** 授课方式 */
  giveLessonsMode: string
  /** 授课方式名称 */
  giveLessonsModeName: string
  /** 作业数量 */
  homeworkNum: number
  /** 作业检查方式 */
  homeworkCheckMode: string
  /** 作业检查方式名称 */
  homeworkCheckModeName: string
  /** 辅助人数组 */
  fzrszs: string
  /** 使用仪器设备数量 */
  syyqsbsl: string
  /** 场地类型 */
  siteType: string
  /** 场地代码 */
  siteCode: string
  /** 场地名称 */
  siteName: string
  /** 教师代码 */
  teacherCode: string
  /** 教师姓名 */
  teacherName: string
  /** 班级ID */
  classId: string
  /** 班级名称 */
  className: string
  /** 实验人数 */
  syrs: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作人代码 */
  operatorCode: string
  /** 课时 */
  classHour: number
  /** 质量 */
  quality: number
  /** 管理ID */
  glid: number
  /** 教学计划状态 */
  teachingPlanStatus: string
  /** 工作量状态 */
  workloadStatus: string
  /** 作业内容 */
  homeworkContent: string
  /** 教师确认状态 */
  teacherConfirmStatus: string
  /** 教师确认日期 */
  teacherConfirmDate: string
  /** 教师确认代码 */
  teacherConfirmCode: string
  /** 教师确认姓名 */
  teacherConfirmName: string
  /** 学生确认状态 */
  studentConfirmStatus: string
  /** 学生确认日期 */
  studentConfirmDate: string
  /** 学生确认代码 */
  studentConfirmCode: string
  /** 学生确认姓名 */
  studentConfirmName: string
  /** 场地设备状态 */
  cdsbzt: number
  /** 场地设备使用情况 */
  cdsbsyqk: string
  /** 审批状态 */
  approvalStatus: number
  /** 网络教学管理ID */
  wljxglid: number
  /** 学生负责ID */
  xsfzid: number
  /** 学生负责名称 */
  xsfzmc: string
  /** 附件内容 */
  attachmentContent: string
  /** 考勤照片 */
  kqzp: string
  /** 听课申请状态 */
  ttbksqzt: number
  /** 加班审批结果 */
  overtimeApprovalResult: number
  /** 作业附件内容 */
  homeworkAttachmentContent: string
  /** 直播平台代码 */
  livePlatformCode: string
  /** 直播平台名称 */
  livePlatformName: string
  /** 直播平台内容 */
  livePlatformContent: string
  /** 教学平台代码 */
  teachingPlatformCode: string
  /** 教学平台名称 */
  teachingPlatformName: string
  /** 教学平台内容 */
  teachingPlatformContent: string
  /** 学生实况审核状态 */
  xsskshzt: number
  /** 课程名称 */
  courseName: string
}

/**
 * 教学日志管理项
 */
export interface TeachingLogManageItem {
  /** ID */
  id: number
  /** 教学任务ID */
  jxrwid: number
  /** 周次 */
  zc: number
  /** 授课日期 */
  skrq: string
  /** 授课开始时间 */
  skkssj: string
  /** 授课结束时间 */
  skjssj: string
  /** 星期数 */
  xqs: number
  /** 节次 */
  jc: string
  /** 节次显示 */
  jcshow: string
  /** 授课内容 */
  sknl: string
  /** 授课方式 */
  skfs: string
  /** 授课方式名称 */
  skfsmc: string
  /** 授课场地类型 */
  skcdlx: string
  /** 授课场地代码 */
  skcddm: string
  /** 授课场地名称 */
  skcdmc: string
  /** 授课教师 */
  skjs: string
  /** 授课教师姓名 */
  skjsxm: string
  /** 授课班级 */
  skbj: string
  /** 授课班级名称 */
  skbjmc: string
  /** 作业数量 */
  zyts: number
  /** 作业批改方式 */
  zypgfs: string
  /** 作业批改方式名称 */
  zypgfsmc: string
  /** 作业内容 */
  zynr: string
  /** 操作人编号 */
  oprybh: string
  /** 课时 */
  ks: number
  /** 性质 */
  xz: number
  /** 备注 */
  remark: string
  /** 删除标记 */
  deltag: number
  /** 教学计划执行状态 */
  skjhzxzt: string
  /** 工作量录入状态 */
  skjhgzlrdzt: number
  /** 教师确认状态 */
  skjhjsqrzt: string
  /** 教师确认时间 */
  skjhjsqrsj: string
  /** 教师确认人员编号 */
  skjhjsqrrybh: string
  /** 教师确认人员姓名 */
  skjhjsqrryxm: string
  /** 学生确认状态 */
  skjhxsqrzt: string
  /** 学生确认时间 */
  skjhxsqrsj: string
  /** 学生确认学生学号 */
  skjhxsqrxsxh: string
  /** 学生确认学生姓名 */
  skjhxsqrxsxm: string
  /** 场地设备使用情况 */
  cdsbsyqk: string
  /** 场地确认审核状态 */
  csqrshzt: number
  /** 更新时间 */
  update_time: string
  /** 创建时间 */
  create_time: string
  /** 审批状态 */
  spzt: number
  /** 实验人数 */
  syrs: number
  /** 考勤照片 */
  kqzp: string
  /** 附件列表 */
  fjlb: string
  /** 直播平台代码 */
  zbptdm: string
  /** 直播平台名称 */
  zbptmc: string
  /** 教学平台代码 */
  jxptdm: string
  /** 教学平台名称 */
  jxptmc: string
  /** 直播平台内容 */
  zbptnr: string
  /** 教学平台内容 */
  jxptnr: string
  /** 学生实况审核状态 */
  xsskshzt: number
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 所属学科 */
  ssxk: string | null
  /** 课程代码 */
  kcdm: string
  /** 课程名称 */
  kcmc: string
  /** 学期总学时 */
  xqzxs: string
  /** 总学时 */
  zxs: string
  /** 总周数 */
  zs: number
  /** 所属学院 */
  ssxy: string
  /** 所属系部 */
  ssxb: string
  /** 所属系部名称 */
  ssxbmc: string
  /** 所属教研室 */
  ssjys: string
  /** 所属教研室名称 */
  ssjysmc: string
  /** 所属班级 */
  ssbj: string
  /** 班级名称 */
  bjmc: string
  /** 工作量系数 */
  gzlxs: string
  /** 研讨学时 */
  yrdxs: string
  /** 指导教师 */
  zdjs: string
  /** 指导教师姓名 */
  zdjsxm: string
  /** 课程性质 */
  kcxz: string
  /** 任务类型状态 */
  rwlxzt: string
}

/**
 * 教学日志列表响应数据
 */
export interface TeachingLogResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: {
    /** 教学日志列表 */
    items: TeachingLogItem[]
    /** 查询参数 */
    query: Record<string, unknown>
    /** 总数 */
    total: number
    /** 周次 */
    week: number
  }
}

/**
 * 教学日志管理列表响应数据
 */
export interface TeachingLogManageResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: {
    /** 教学日志管理列表 */
    items: TeachingLogManageItem[]
    /** 查询参数 */
    query: Record<string, unknown>
    /** 总数 */
    total: number
    /** 周次 */
    week?: number
  }
}
