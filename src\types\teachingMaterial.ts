/**
 * 教材相关类型定义
 */

/**
 * 教材信息查询参数
 */
export interface TeachingMaterialQuery {
  /** 页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 搜索关键词 */
  searchKeyword?: string
}

/**
 * 教材信息响应结果
 */
export interface TeachingMaterialResponse {
  /** 列表数据 */
  items: TeachingMaterial[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总数 */
  total: number
}

/**
 * 教材信息
 */
export interface TeachingMaterial {
  /** 教材ID */
  id: number
  /** 教材类型 */
  booksType: string | null
  /** 教材类型名称 */
  booksTypeName: string | null
  /** 教材名称 */
  name: string
  /** 主编 */
  mainEditor: string
  /** 出版社 */
  publishingHouse: string
  /** ISBN */
  isbn: string
  /** 出版时间 */
  publicationTime: string
  /** 印刷时间 */
  printingTime: string
  /** 教材类别 */
  category: string
  /** 教材类别名称 */
  categoryName: string
  /** 编辑代码 */
  editorCode: string
  /** 拼音索引 */
  pinyinQuickIndex: string
  /** 价格 */
  money: string
  /** 库存数量 */
  stockNumber: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作人员编号 */
  oprybh: string | null
  /** 教材代码 */
  booksCode: string
  /** 类型 */
  type: string
  /** 类型名称 */
  typeName: string
  /** 是否国家标准 */
  isNationalStandard: number
  /** 是否国家奖项 */
  isNationalAward: number
  /** 适用层次 */
  suitableLevel: string
  /** 适用层次名称 */
  suitableLevelName: string
  /** 对应领域 */
  correspondingDomain: string
  /** 对应领域名称 */
  correspondingDomainName: string
  /** 特色 */
  feature: string
  /** 特色名称 */
  featureName: string
  /** 编辑数量 */
  editorNumber: number
  /** 班级数量 */
  classNumber: string
  /** 是否国家计划 */
  isNationalPlan: number
  /** 计划批次 */
  planBatch: string
  /** 教材信息ID */
  textbookInfoId: number
}

/**
 * 教材选择表单数据
 */
export interface TextbookForm {
  /** 教材ID */
  id?: number
  /** 教材名称 */
  name: string
  /** 主编/作者 */
  author: string
  /** 出版社 */
  publisher: string
  /** ISBN */
  isbn: string
  /** 教材代码 */
  code: string
  /** 教材选用类别 */
  selectionType: string
}

/**
 * 教材领用相关类型定义
 */

/**
 * 教材领用列表查询参数
 */
export interface TeachingMaterialReceiveQuery {
  /** 当前页码 */
  page: number
  /** 每页条数 */
  pageSize: number
  /** 排序字段 */
  sortBy: string
  /** 排序方式 */
  sortOrder: 'asc' | 'desc'
  /** 学年学期，格式：学年|学期，如：2024-2025|2 */
  semesters: string
  /** 教材名称 */
  name?: string
  /** ISBN编号 */
  isbn?: string
  /** 主编 */
  mainEditor?: string
  /** 出版社 */
  publishingHouse?: string
  /** 领用数量 */
  receiveNum?: string
  /** 金额 */
  money?: string
  /** 离校日期 */
  leaveData?: string
  /** 结算方式 */
  settlementMethod?: string
}

/**
 * 教材领用信息项
 */
export interface TeachingMaterialReceiveItem {
  /** 领用记录ID */
  id: number
  /** 领用编号 */
  receiveCode: string
  /** 教材ID */
  teachingMaterialId: number
  /** 库存ID */
  inventoryId: number
  /** 领用数量 */
  receiveNum: number
  /** 金额 */
  money: string
  /** 离校ID */
  leaveId: number
  /** 调整金额 */
  adjustMoney: string
  /** 结算方式 */
  settlementMethod: string
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作员编号 */
  oprybh: string
  /** 教材名称 */
  name: string
  /** ISBN编号 */
  isbn: string
  /** 主编 */
  mainEditor: string
  /** 出版社 */
  publishingHouse: string
  /** 出版时间 */
  publicationTime: string
  /** 印刷时间 */
  printingTime: string
  /** 学年 */
  studyYear: string
  /** 学期 */
  studyTerm: number
  /** 离校日期 */
  leaveData: string
}

/**
 * 教材领用列表响应数据
 */
export interface TeachingMaterialReceiveResponse {
  /** 领用记录列表 */
  items: TeachingMaterialReceiveItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总记录数 */
  total: number
}

/**
 * 教材选用列表查询参数
 */
export interface TeachingMaterialSelectionQuery {
  /** 页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 排序字段 */
  sortBy: string
  /** 排序方式 */
  sortOrder: 'asc' | 'desc'
  /** 学年学期，格式：学年|学期，如：2024-2025|2 */
  semesters: string
  /** 选用类别 */
  xylb?: string
  /** 教材名称 */
  jcmc?: string
  /** ISBN编号 */
  isbn?: string
  /** 主编/作者 */
  zybz?: string
  /** 出版社 */
  cbs?: string
  /** 版次 */
  bc?: string
  /** 教材类别 */
  jclb?: string
  /** 教材类型 */
  jclx?: string
  /** 创建时间 */
  create_time?: string
  /** 操作人员编号 */
  oprybh?: string
  /** 是否删除选用 */
  sfscxy?: string
  /** 审核状态 */
  shzt?: string
  /** 评价审核状态 */
  pjshzt?: string
}

/**
 * 教材选用项
 */
export interface TeachingMaterialSelectionItem {
  /** ID */
  id: number
  /** 选用类别 */
  xylb: string
  /** 教学任务ID */
  jxrwid: number
  /** 教材信息ID */
  jcxxid: number
  /** 备注 */
  remark: string
  /** 删除标记 */
  deltag: number
  /** 创建时间 */
  create_time: string
  /** 更新时间 */
  update_time: number
  /** 操作人员编号 */
  oprybh: string
  /** 审核状态 */
  shzt: number
  /** 是否删除选用 */
  sfscxy: string
  /** 选用理由 */
  xyly: string
  /** 是否进行测课书 */
  sfjxcks: string
  /** 是否有指导书 */
  sfsyzds: string
  /** 教材评价意见 */
  jcpjyj: string
  /** 评价审核状态 */
  pjshzt: number
  /** 所属选课 */
  ssxk: number
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 课程代码 */
  kcdm: string
  /** 课程名称 */
  kcmc: string
  /** 所属学院 */
  ssxy: string
  /** 所属系部 */
  ssxb: string
  /** 所属教研室 */
  ssjys: string
  /** 所属教研室名称 */
  ssjysmc: string
  /** 所属班级 */
  ssbj: string
  /** 班级名称 */
  bjmc: string
  /** 主讲教师姓名 */
  zdjsxm: string
  /** 教材名称 */
  jcmc: string
  /** 主编/作者 */
  zybz: string
  /** 出版社 */
  cbs: string | null
  /** ISBN编号 */
  isbn: string
  /** 版次 */
  bc: string | null
  /** 印次 */
  yc: string | null
  /** 教材类别 */
  jclb: string
  /** 教材类别名称 */
  jclbmc: string
  /** 教材类型 */
  jclx: string
  /** 教材类型名称 */
  jclxmc: string
  /** 标题 */
  title: string
  /** 任务ID */
  taskId?: number
}

/**
 * 教材选用列表响应数据
 */
export interface TeachingMaterialSelectionResponse {
  /** 教材选用列表 */
  items: TeachingMaterialSelectionItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总记录数 */
  total: number
  /** 是否可添加 */
  isAdd?: boolean
  /** 结束时间（毫秒时间戳） */
  jssj?: number
  /** 信息描述 */
  info?: string
}

/**
 * 教材统计响应数据
 */
export interface TeachingMaterialStatisticsResponse {
  /** 权限 */
  access: string
  /** 任务总数 */
  tasksTotal: number
  /** 未选用任务数 */
  tasksNoSelect: number
  /** 已选用任务数 */
  tasksSelect: number
  /** 无需选用任务数 */
  tasksNoNeed: number
  /** 教材总数 */
  materialTotal: number
  /** 教材待审批总数 */
  materialApprovalTotal: number
  /** 教材已通过总数 */
  materialPassTotal: number
  /** 教材未通过总数 */
  materialNoPassTotal: number
  /** 当前学年学期 */
  xnxq: string
  /** 信息描述 */
  info?: string
  /** 结束时间（毫秒时间戳） */
  jssj?: number
}

/**
 * 教材变更列表请求参数
 */
export interface TeachingMaterialChangeQuery {
  /** 页码 */
  page?: number
  /** 每页条数 */
  pageSize?: number
  /** 排序字段 */
  sortBy?: string
  /** 排序方式 */
  sortOrder?: 'asc' | 'desc'
  /** 学年学期，格式：学年|学期，如：2025-2026|1 */
  semesters?: string
  /** 任务ID */
  task_id?: string
}

/**
 * 教材变更项
 */
export interface TeachingMaterialChangeItem {
  /** 变更记录ID */
  id: number
  /** 选用类别 */
  xylb: string
  /** 原教材选用ID */
  yjcxyid: number
  /** 所属教学任务ID */
  ssjxrwid: number
  /** 教材ID */
  jcid: number
  /** 是否删除选用 */
  sfscxy: number
  /** 选用理由 */
  xyly: string
  /** 变更原因说明 */
  bgyysm: string
  /** 申请人 */
  sqr: string
  /** 申请人姓名 */
  sqrxm: string
  /** 申请时间 */
  sqsj: string
  /** 删除标记 */
  deltag: number
  /** 教研室审核 */
  jyssh: number
  /** 系部审核 */
  xbsh: number
  /** 教务审核 */
  jwsh: number
  /** 原选用类别 */
  yxylb: string
  /** 原教材ID */
  yjcid: number
  /** 审核状态 */
  shzt: number
  /** 原是否删除选用 */
  ysfscxy: number
  /** 原选用理由 */
  yxyly: string
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 课程代码 */
  kcdm: string
  /** 课程名称 */
  kcmc: string
  /** 所属学院 */
  ssxy: string
  /** 所属系部 */
  ssxb: string
  /** 所属教研室 */
  ssjys: string
  /** 所属教研室名称 */
  ssjysmc: string
  /** 所属班级 */
  ssbj: string
  /** 班级名称 */
  bjmc: string
  /** 指导教师姓名 */
  zdjsxm: string
  /** 原教材名称 */
  yjcmc: string
  /** 原主要编者 */
  yzybz: string
  /** 原出版社 */
  ycbs: string
  /** 原ISBN */
  yisbn: string
  /** 原版次 */
  ybc: string | null
  /** 原印次 */
  yyc: string | null
  /** 原教材类别 */
  yjclb: string
  /** 原教材类别名称 */
  yjclbmc: string
  /** 原教材类型 */
  yjclx: string
  /** 原教材类型名称 */
  yjclxmc: string
  /** 教材名称 */
  jcmc: string
  /** 主要编者 */
  zybz: string
  /** 出版社 */
  cbs: string
  /** ISBN */
  isbn: string
  /** 版次 */
  bc: string | null
  /** 印次 */
  yc: string | null
  /** 教材类别 */
  jclb: string
  /** 教材类别名称 */
  jclbmc: string
  /** 教材类型 */
  jclx: string
  /** 教材类型名称 */
  jclxmc: string
}

/**
 * 教材变更列表响应数据
 */
export interface TeachingMaterialChangeResponse {
  /** 数据列表 */
  items: TeachingMaterialChangeItem[]
  /** 查询条件 */
  query: Record<string, any>
  /** 总数 */
  total: number
}

/**
 * 教学材料相关类型定义
 */

/**
 * 不需要教学材料列表查询参数
 */
export interface TeachingMaterialNoNeedQuery {
  /** 页码 */
  page: number
  /** 每页条数 */
  pageSize: number
  /** 学期，格式：学年|学期，例如：2024-2025|2 */
  semesters: string
}

/**
 * 不需要教学材料列表项
 */
export interface TeachingMaterialNoNeedItem {
  /** ID */
  id: number
  /** 所属计划 */
  ssjh: string | null
  /** 所属选课 */
  ssxk: number
  /** 所属合班 */
  sshb: string | null
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 任务类型 */
  rwlx: string
  /** 课程代码 */
  kcdm: string
  /** 课程名称 */
  kcmc: string
  /** 课程性质 */
  kcxz: string
  /** 主干课程 */
  zgkc: number
  /** 考核方式 */
  khfs: string
  /** 学期总学时 */
  xqzxs: string
  /** 讲课学时 */
  jkxs: string
  /** 实验学时 */
  syxs: string
  /** 实践学时 */
  sjxs: string
  /** 实训学时 */
  sxxs: string
  /** 总学时 */
  zxs: string
  /** 总学时排课信息 */
  zxspkxx: string
  /** 周数 */
  zs: number
  /** 周数排课信息 */
  zspkxx: number
  /** 课程学分 */
  kcxf: number
  /** 所属学院 */
  ssxy: string
  /** 所属系部 */
  ssxb: string
  /** 所属系部名称 */
  ssxbmc: string
  /** 所属教研室 */
  ssjys: string
  /** 所属教研室名称 */
  ssjysmc: string
  /** 备注 */
  remark: string | null
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 主讲教师 */
  zdjs: string
  /** 主讲教师姓名 */
  zdjsxm: string
  /** 其他辅导教师姓名 */
  qtfdjsxm: string
  /** 其他辅导教师 */
  qtfdjs: string
  /** 所属班级 */
  ssbj: string
  /** 班级名称 */
  bjmc: string
  /** 任务类型状态 */
  rwlxzt: string
  /** 执行状态 */
  zxzt: number
  /** 课代表 */
  kdb: string | null
  /** 课代表姓名 */
  kdbxm: string
  /** 课代表负责考勤开放 */
  kdbfzkqkf: number
  /** 是否考核 */
  sfkh: number
  /** 成绩平台类型 */
  cjptlx: string
  /** 成绩提交标志 */
  cjtjbz: string
  /** 教材选用标志 */
  jcxybz: number
  /** 无需征订教材原因 */
  jcxysm?: string
  /** 操作人员编号 */
  oprybh: string
  /** 上课场地代码 */
  skcddm: string | null
  /** 上课场地名称 */
  skcdmc: string | null
  /** 开始周 */
  ksz: number
  /** 结束周 */
  jsz: number
  /** 排课信息 */
  pkxx: string
  /** 教学周次 */
  jxzc: string | null
  /** 排课操作 */
  pkcz: string
  /** 必备知识技能 */
  bbzsjn: string | null
  /** 核心课程提高知识 */
  hxkctgzs: string | null
  /** 核心课程提高技能 */
  hxkctgjn: string | null
  /** 课程教学大纲信息 */
  kcjxdgxx: string | null
  /** 课程教育知识目标 */
  kcjyzsmb: string | null
  /** 课程教育能力目标 */
  kcjynlmb: string | null
  /** 课程教育素质目标 */
  kcjyszmb: string
  /** 课程考核比分 */
  kckhbf: string | null
  /** 见习指导教师 */
  jdzjs: string
  /** 见习指导教师课时 */
  jdzjsks: string
  /** 工作量学时 */
  gzlxs: string
  /** 引入度学时 */
  yrdxs: string
  /** 授课计划审核 */
  skjhsh: number
  /** 测评应参人数 */
  cpycrs: number | null
  /** 测评实参人数 */
  cpscrs: number | null
  /** 测评平均分 */
  cppjf: number | null
  /** 测评有效人数 */
  cpyxrs: number | null
  /** 测评评估得分 */
  cppgdf: number | null
  /** 成绩提交按钮 */
  cjtjan: number
  /** 授课计划提交按钮 */
  skjhtjan: number
  /** 排课状态 */
  pkzt: number
  /** 课程标准附件 */
  kcbzfj: string | null
  /** 成绩免登审定状态 */
  cjmdsdzt: number
  /** 数据分析 */
  sjfx: string | null
  /** 工作手册提交标志 */
  gzsctjbz: number
  /** 工作手册教研室审核 */
  gzscjyssh: number
  /** 工作手册系部审核 */
  gzscxbsh: number
  /** 是否批次质改检查 */
  sfpczgjc: number
  /** 教学方式 */
  jxfs: string
  /** 教学方式名称 */
  jxfsmc: string
  /** 关联教学平台 */
  gljxpt: string
  /** 课程ID */
  course_id: number
  /** 班级ID */
  class_id: number
  /** 任务ID */
  taskId?: number
}

/**
 * 不需要教学材料列表响应
 */
export interface TeachingMaterialNoNeedResponse {
  /** 数据项列表 */
  items: TeachingMaterialNoNeedItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总数 */
  total: number
}

/**
 * 取消教材选用请求参数
 */
export interface CancelTeachingMaterialParams {
  /** 教材选用ID */
  id: number
}

/**
 * 更新无需征订教材参数
 */
export interface UpdateTeachingMaterialNoNeedParams {
  /** 状态 */
  status: string
  /** 无需征订教材原因 */
  reason: string
  /** 任务ID */
  task_id: number
  /** 学年学期，格式：学年|学期，如：2024-2025|2 */
  semesters?: string
  /** 班级名称 */
  className?: string
  /** 课程名称 */
  courseName?: string
  /** 课程总学时 */
  courseTotalHours?: string
  /** 学分 */
  creditHour?: number
  /** 周学时 */
  weekHours?: string
}

/**
 * 创建无需征订教材参数
 */
export interface CreateTeachingMaterialNoNeedParams {
  /** 任务ID */
  taskId: string
  /** 无需征订教材原因 */
  jcxysm: string
}

/**
 * 教材添加请求参数
 */
export interface TeachingMaterialAddRequest {
  /** 教材类别 */
  category: string
  /** 教材类型 */
  type: string
  /** 教育部统编教材 0-否 1-是 */
  isNationalStandard: number
  /** 国家获奖教材 0-否 1-是 */
  isNationalAward: number
  /** 校企合作开发教材 0-否 1-是 */
  sfxqhzkfjc: number
  /** 适用层次 */
  suitableLevel: string[]
  /** 对应领域 */
  correspondingDomain: string[]
  /** 教材特色 */
  feature: string[]
  /** 教材名称 */
  name: string
  /** 主要编者 */
  mainEditor: string
  /** 编著者总数 */
  editorNumber: number
  /** 出版社 */
  publishingHouse: string
  /** ISBN */
  isbn: string
  /** 分类号 */
  classNumber: string
  /** 印次 */
  printingTime: string
  /** 版次(出版时间) */
  publicationTime: string
  /** 定价 */
  money: string
  /** 数字资源大小(GB) */
  filesize: number
  /** 备注 */
  remark: string
}

/**
 * 教材添加响应
 */
export interface TeachingMaterialAddResponse {
  code: number
  msg: string
  time: number
  data: null
}

/**
 * 教材详情请求参数
 */
export interface TeachingMaterialDetailRequest {
  /** 教材ID */
  id: number
}

/**
 * 教材详情响应数据
 */
export interface TeachingMaterialDetailResponse {
  /** 教材ID */
  id: number
  /** 教材类型 */
  booksType: string | null
  /** 教材类型名称 */
  booksTypeName: string | null
  /** 教材名称 */
  name: string
  /** 主编 */
  mainEditor: string
  /** 出版社 */
  publishingHouse: string
  /** ISBN */
  isbn: string
  /** 出版时间 */
  publicationTime: string
  /** 印刷时间 */
  printingTime: string
  /** 教材类别 */
  category: string
  /** 教材类别名称 */
  categoryName: string
  /** 编辑代码 */
  editorCode: string
  /** 拼音索引 */
  pinyinQuickIndex: string
  /** 价格 */
  money: string
  /** 库存数量 */
  stockNumber: number
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作人员编号 */
  oprybh: string
  /** 教材代码 */
  booksCode: string
  /** 类型 */
  type: string
  /** 类型名称 */
  typeName: string
  /** 是否国家标准 */
  isNationalStandard: number
  /** 是否国家奖项 */
  isNationalAward: number
  /** 适用层次 */
  suitableLevel: string[]
  /** 适用层次名称 */
  suitableLevelName: string
  /** 对应领域 */
  correspondingDomain: string[]
  /** 对应领域名称 */
  correspondingDomainName: string
  /** 特色 */
  feature: string[]
  /** 特色名称 */
  featureName: string
  /** 编辑数量 */
  editorNumber: number
  /** 班级数量 */
  classNumber: string
  /** 是否国家计划 */
  isNationalPlan: number
  /** 计划批次 */
  planBatch: string
  /** 文件大小 */
  filesize: string
  /** 是否校企合作开发教材 */
  sfxqhzkfjc: number
}

/**
 * 教师更新教材请求参数
 */
export interface TeacherUpdateTeachingMaterialRequest {
  /** 教材ID */
  id: number
  /** 教材类别 */
  category: string
  /** 教材类型 */
  type: string
  /** 教育部统编教材 0-否 1-是 */
  isNationalStandard: number
  /** 国家获奖教材 0-否 1-是 */
  isNationalAward: number
  /** 校企合作开发教材 0-否 1-是 */
  sfxqhzkfjc: number
  /** 适用层次 */
  suitableLevel: string[]
  /** 对应领域 */
  correspondingDomain: string[]
  /** 教材特色 */
  feature: string[]
  /** 教材代码 */
  booksCode: string
  /** 教材名称 */
  name: string
  /** 主要编者 */
  mainEditor: string
  /** 编著者总数 */
  editorNumber: number
  /** 出版社 */
  publishingHouse: string
  /** ISBN */
  isbn: string
  /** 分类号 */
  classNumber: string
  /** 印次 */
  printingTime: string
  /** 版次(出版时间) */
  publicationTime: string
  /** 定价 */
  money: string
  /** 数字资源大小(GB) */
  filesize: string
  /** 备注 */
  remark: string
  /** 是否教研室更新 */
  isTeachOffice: boolean
  /** 是否教师更新 */
  isTeacherUpdate: boolean
}

/**
 * 教师更新教材响应
 */
export interface TeacherUpdateTeachingMaterialResponse {
  /** 响应码 */
  code: number
  /** 响应消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 响应数据 */
  data: null
}
