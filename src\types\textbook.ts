/**
 * 教材信息接口
 */
export interface TextbookInfo {
  /**
   * 教材ID
   */
  id: number
  /**
   * 教材名称
   */
  name: string
  /**
   * ISBN号
   */
  isbn: string
  /**
   * 主编
   */
  editor: string
  /**
   * 出版社
   */
  publisher: string
  /**
   * 版次
   */
  edition: string
  /**
   * 教材类别
   */
  category: string
  /**
   * 教材类型
   */
  type: string
  /**
   * 选用类别
   */
  usageType: string
  /**
   * 课程类型
   */
  courseType: string
  /**
   * 是否首次选用
   */
  isFirstTime: boolean
  /**
   * 选用时间
   */
  usageTime: string
  /**
   * 操作人
   */
  operator: string
  /**
   * 审核状态
   */
  approvalStatus: string
  /**
   * 评价审阅
   */
  evaluation: string
  /**
   * 封面图片URL
   */
  cover?: string
  /**
   * 备注
   */
  remark?: string
}

/**
 * 教材列表查询参数
 */
export interface TextbookListQuery {
  /**
   * 搜索关键词
   */
  keyword?: string
  /**
   * 审核状态
   */
  status?: string
  /**
   * 页码
   */
  page: number
  /**
   * 每页条数
   */
  pageSize: number
}

/**
 * 教材列表响应
 */
export interface TextbookListResponse {
  /**
   * 总记录数
   */
  total: number
  /**
   * 教材列表
   */
  list: TextbookInfo[]
}

/**
 * 教学任务教材查询参数
 */
export interface TeachingTaskMaterialQuery {
  /**
   * 教学任务ID
   */
  task_id: string
  /**
   * 教材名称（可选，用于搜索）
   */
  jcmc?: string
  /**
   * ISBN编号（可选，用于搜索）
   */
  isbn?: string
  /**
   * 主编者（可选，用于搜索）
   */
  zybz?: string
  /**
   * 教材评审状态（可选，用于筛选）
   */
  pjshzt?: string
  /**
   * 列表类型（selection: 教材选用列表, change: 选用变更申请列表）
   */
  list_type?: string
}

/**
 * 教学任务教材列表项
 */
export interface TeachingTaskMaterialItem {
  /** ID */
  id: number
  /** 选用类别 */
  xylb: string
  /** 教学任务ID */
  jxrwid: number
  /** 教材信息ID */
  jcxxid: number
  /** 备注 */
  remark: string
  /** 删除标记 */
  deltag: number
  /** 创建时间 */
  create_time: string
  /** 更新时间 */
  update_time: number
  /** 操作人员编号 */
  oprybh: string
  /** 审核状态 */
  shzt: number
  /** 是否首次选用 */
  sfscxy: string
  /** 选用理由 */
  xyly: string
  /** 是否教学参考书 */
  sfjxcks: string
  /** 是否有自定义试题 */
  sfsyzds: string
  /** 教材评价意见 */
  jcpjyj: string
  /** 评价审核状态 */
  pjshzt: number
  /** 所属选课 */
  ssxk: number
  /** 学年 */
  xn: string
  /** 学期 */
  xq: number
  /** 课程代码 */
  kcdm: string
  /** 课程名称 */
  kcmc: string
  /** 所属学院 */
  ssxy: string
  /** 所属系部 */
  ssxb: string
  /** 所属教研室 */
  ssjys: string
  /** 所属教研室名称 */
  ssjysmc: string
  /** 所属班级 */
  ssbj: string
  /** 班级名称 */
  bjmc: string
  /** 主导教师姓名 */
  zdjsxm: string
  /** 教材名称 */
  jcmc: string
  /** 主编 */
  zybz: string
  /** 出版社 */
  cbs: string
  /** ISBN */
  isbn: string
  /** 版次 */
  bc: string | null
  /** 印次 */
  yc: string | null
  /** 教材类别 */
  jclb: string
  /** 教材类别名称 */
  jclbmc: string
  /** 教材类型 */
  jclx: string
  /** 教材类型名称 */
  jclxmc: string
  /** 任务ID */
  taskId?: number
}

/**
 * 教学任务教材列表响应
 */
export interface TeachingTaskMaterialResponse {
  /** 教材列表 */
  items: TeachingTaskMaterialItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总记录数 */
  total: number
  /** 教学任务信息 */
  taskInfo: TeachingTaskInfo
}

/**
 * 教学任务信息
 */
export interface TeachingTaskInfo {
  /** 任务ID */
  id: number
  /** 计划ID */
  planId: number | null
  /** 选课ID */
  selectCourseId: number
  /** 合并任务ID */
  mergeTaskId: number | null
  /** 学年 */
  studyYear: string
  /** 学期 */
  studyTerm: number
  /** 任务类型 */
  taskType: string
  /** 课程代码 */
  courseCode: string
  /** 课程名称 */
  courseName: string
  /** 课程类型 */
  courseType: string
  /** 是否主课程 */
  mainCourse: number
  /** 考核方式 */
  assessmentMethod: string
  /** 课程总学时 */
  courseTotalHours: string
  /** 教学学时 */
  teachingHours: string
  /** 实验学时 */
  experimentHours: string
  /** 计算机学时 */
  computerHours: string
  /** 虚拟学时 */
  virtualHours: string
  /** 周学时 */
  weekHours: string
  /** 课程信息周学时 */
  weekHoursCourseInfo: string
  /** 周数 */
  weeks: number
  /** 课程信息周数 */
  weeksCourseInfo: number
  /** 学分 */
  creditHour: number
  /** 学校代码 */
  schoolCode: string
  /** 部门代码 */
  deptCode: string
  /** 部门名称 */
  deptName: string
  /** 教研室代码 */
  teachOfficeCode: string
  /** 教研室名称 */
  teachOfficeName: string
  /** 备注 */
  remark: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 主导教师代码 */
  leaderTeacherCode: string
  /** 主导教师姓名 */
  leaderTeacherName: string
  /** 其他教师姓名 */
  otherTeacherName: string
  /** 其他教师代码 */
  otherTeacherCode: string
  /** 班级代码 */
  classCode: string
  /** 班级名称 */
  className: string
  /** 任务类型状态 */
  taskTypeStatus: string
  /** 任务执行状态 */
  taskExecutionStatus: number
  /** 教学代表 */
  sectionRepresentative: string | null
  /** 教学代表姓名 */
  sectionRepresentativeName: string
  /** 考勤开放 */
  checkingInOpen: number
  /** 是否考核 */
  isAssessment: string
  /** 考核类型 */
  assessmentType: string
  /** 提交状态 */
  submitStatus: number
  /** 教材使用 */
  textbookUse: number
  /** 操作员代码 */
  operatorCode: string
  /** 站点代码 */
  siteCode: string | null
  /** 站点名称 */
  siteName: string | null
  /** 开始周 */
  startWeek: number
  /** 结束周 */
  endWeek: number
  /** 教学信息 */
  teachingInfo: string
  /** 教学周 */
  teachingWeek: string | null
  /** 班级周数 */
  classWeeks: string
  /** 学生知识 */
  studentKnowledge: string | null
  /** 后续课程知识 */
  followingCourseKnowledge: string | null
  /** 后续课程技能 */
  followingCourseSkill: string | null
  /** 教学大纲 */
  teachingOutline: string | null
  /** 知识目标 */
  knowledgeObjective: string | null
  /** 能力目标 */
  abilityObjective: string | null
  /** 素质目标 */
  qualityObjective: string
  /** 考核方式 */
  assessmentWay: string | null
  /** 配对教师 */
  pairTeacher: string
  /** 配对教师学时 */
  pairTeacherHours: string
  /** 工作量数量 */
  workloadNum: string
  /** 确认学时 */
  affirmHours: string
  /** 教学计划审批 */
  teachingPlanApproval: number
  /** 评价应数 */
  evaluationShouldNum: number | null
  /** 评价实数 */
  evaluationActualNum: number | null
  /** 评价平均分 */
  evaluationAverageScore: number | null
  /** 评价有效数 */
  evaluationEffectiveNum: number | null
  /** 评价得分 */
  evaluationScore: number | null
  /** 是否提交按钮 */
  isSubmitButton: number
  /** 教学计划提交 */
  teachingPlanSubmit: number
  /** 教学计划状态 */
  teachingPlanStatus: number
  /** 课程标准附件 */
  courseStandardAttachment: number
  /** 是否课程评价锁定 */
  isCourseEvaluationLock: number
  /** 教师手册分析 */
  teacherManualAnalysis: string | null
  /** 工作簿提交状态 */
  workbookSubmitStatus: number
  /** 工作簿教研室审批 */
  workbookTeachOfficeApproval: number
  /** 工作簿部门审批 */
  workbookDeptApproval: number
  /** 是否排除出勤考试 */
  isExcludeAttendanceExam: number
  /** 教学方式 */
  teachingMethod: string
  /** 教学方式名称 */
  teachingMethodName: string
  /** 管理教学平台 */
  gljxpt: string
  /** 课程ID */
  course_id: number
  /** 班级ID */
  class_id: number
  /** 无需理由 */
  no_need_reason: string
  /** 所属教学任务ID */
  ssjxrwid: string
  /** 标题 */
  title: string
}

/**
 * 教学任务信息
 */
export interface TeachingTask {
  /**
   * 任务ID
   */
  id: number
  /**
   * 学期
   */
  semester: string
  /**
   * 教学班号
   */
  classNo: string
  /**
   * 课程名称
   */
  courseName: string
  /**
   * 课程类型
   */
  courseType: string
  /**
   * 教师姓名
   */
  teacherName: string
  /**
   * 当前选用教材
   */
  currentTextbook?: string
  /**
   * 选用编号
   */
  selectionNo?: string
}

/**
 * 教材添加参数
 */
export interface AddTextbookParams {
  /**
   * 教材名称
   */
  name: string
  /**
   * ISBN号
   */
  isbn: string
  /**
   * 主编
   */
  editor: string
  /**
   * 出版社
   */
  publisher: string
  /**
   * 版次
   */
  edition: string
  /**
   * 教材类别
   */
  category: string
  /**
   * 教材类型
   */
  type: string
  /**
   * 选用类别
   */
  usageType: string
  /**
   * 课程类型
   */
  courseType: string
  /**
   * 是否首次选用
   */
  isFirstTime: boolean
  /**
   * 封面图片URL
   */
  cover?: string
  /**
   * 备注
   */
  remark?: string
  /**
   * 教学任务ID
   */
  taskId?: number
  /**
   * 选用编号
   */
  selectionNo?: string
  /**
   * 选用理由
   */
  selectionReason?: string
  /**
   * 变更原因
   */
  changeReason?: string
}

/**
 * 审批步骤
 */
export interface ApprovalStep {
  /**
   * 步骤名称
   */
  name: string
  /**
   * 处理人
   */
  handler: string
  /**
   * 处理时间
   */
  time: string
  /**
   * 状态：success-已通过，rejected-已拒绝，active-处理中，waiting-等待处理
   */
  status: 'success' | 'rejected' | 'active' | 'waiting'
  /**
   * 审批意见
   */
  comment?: string
  /**
   * 是否拒绝
   */
  isRejected?: boolean
}

/**
 * 审批流程分支
 */
export interface ApprovalBranch {
  /**
   * 分支名称
   */
  name: string
  /**
   * 状态：success-已通过，rejected-已拒绝，active-处理中，waiting-等待处理
   */
  status: 'success' | 'rejected' | 'active' | 'waiting'
  /**
   * 审批步骤
   */
  steps: ApprovalStep[]
}

/**
 * 教材评价
 */
export interface TextbookEvaluation {
  /**
   * 评价ID
   */
  id: number
  /**
   * 教材ID
   */
  textbookId: number
  /**
   * 评价人
   */
  evaluator: string
  /**
   * 评分
   */
  rating: number
  /**
   * 评价内容
   */
  content: string
  /**
   * 评价时间
   */
  createTime: string
}

/**
 * 教材变更申请参数
 */
export interface ChangeTeachingMaterialParams {
  /**
   * 是否首次使用 1:是 0:否
   */
  sfscxy: string
  /**
   * 所属教学任务ID
   */
  ssjxrwid: string
  /**
   * 标题
   */
  title: string
  /**
   * 教材名称
   */
  jcmc?: string
  /**
   * 原教材选用ID
   */
  yjcxyid?: number
  /**
   * 选用类别
   */
  xylb: string | number
  /**
   * 选用理由
   */
  xyly: string
  /**
   * 变更原因说明
   */
  bgyysm: string
  /**
   * 教材ID
   */
  jcid: number
}

/**
 * 教材选用参数
 */
export interface AddTeachingMaterialParams {
  /**
   * 是否首次使用 1:是 0:否
   */
  sfscxy: string
  /**
   * 所属教学任务ID
   */
  ssjxrwid: string
  /**
   * 标题
   */
  title: string
  /**
   * 选用类别
   */
  xylb: string
  /**
   * 教材ID
   */
  jcid: number
  /**
   * 选用理由
   */
  xyly: string
  /**
   * 变更原因说明（仅变更模式使用）
   */
  bgyysm?: string
}

/**
 * 更新教材参数
 */
export interface UpdateTeachingMaterialParams extends AddTeachingMaterialParams {
  /**
   * 教材记录ID
   */
  id: number
}

/**
 * 教材评价请求参数
 */
export interface TextbookEvaluationParams {
  /**
   * 是否教学参考书（0-否，1-是）
   */
  sfjxcks: string
  /**
   * 是否实验指导书（0-否，1-是）
   */
  sfsyzds: string
  /**
   * 教材ID
   */
  id: number
  /**
   * 教材名称
   */
  jcmc?: string
  /**
   * 教材评价意见
   */
  jcpjyj: string
}

/**
 * 更新教材变更申请参数
 */
export interface UpdateChangeTeachingMaterialParams {
  /**
   * 是否首次使用 1:是 0:否
   */
  sfscxy: string
  /**
   * 记录ID
   */
  id: number
  /**
   * 所属教学任务ID
   */
  ssjxrwid: number
  /**
   * 标题
   */
  title: string
  /**
   * 教材名称
   */
  jcmc: string
  /**
   * 原教材选用ID
   */
  yjcxyid: number
  /**
   * 选用类别
   */
  xylb: number | string
  /**
   * 教材信息
   */
  teachingMaterialInfo: {
    /**
     * 教材名称
     */
    name: string
    /**
     * 教材ID
     */
    id: number
  }
  /**
   * 选用理由
   */
  xyly: string
  /**
   * 变更原因说明
   */
  bgyysm: string
  /**
   * 教材ID
   */
  jcid: number
}
