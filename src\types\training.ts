/**
 * 培训会议查询参数
 */
export interface TrainingConferenceQuery {
  /** 当前页码 */
  page: number
  /** 每页条数 */
  pageSize: number
  /** 排序字段 */
  sortBy: string
  /** 排序方式 */
  sortOrder: string
  /** 学期列表 */
  semesters: string[]
  /** 部门名称 */
  deptName: string
  /** 项目名称 */
  projectName: string
  /** 审核数量 */
  auditNumber: string
  /** 项目处理值 */
  projectTreatmentValue: string
  /** 负责教师姓名 */
  responsibleTeacherName: string
  /** 签到状态（中文字符串：已签到、未签到） */
  signInStatus: string
  /** 签到时间 */
  signInTime: string
  /** 签退状态（中文字符串：已签退、未签退） */
  signOutStatus: string
  /** 签退时间 */
  signOutTime: string
  /** 参与状态（中文字符串：有效、无效） */
  participationStatus: string
}

/**
 * 培训会议项目
 */
export interface TrainingConferenceItem {
  /** ID */
  id: number
  /** 活动ID */
  activityId: number
  /** 学生编号 */
  studentCode: string
  /** 添加类型 */
  addType: number
  /** 添加时间 */
  addTime: string
  /** 备注 */
  remark: string
  /** 审核状态 */
  auditStatus: number
  /** 创建时间戳 */
  create_time: number
  /** 更新时间戳 */
  update_time: number
  /** 删除标记 */
  deltag: number
  /** 操作员ID */
  operatorId: string
  /** 签到状态（中文字符串：已签到、未签到） */
  signInStatus: string
  /** 签到时间 */
  signInTime: string
  /** 申请文件 */
  applyFile: string
  /** 申请内容 */
  applyContent: string
  /** 签退状态（中文字符串：已签退、未签退） */
  signOutStatus: string
  /** 签退时间 */
  signOutTime: string
  /** 参与状态（中文字符串：有效、无效） */
  participationStatus: string
  /** 项目处理值 */
  projectTreatmentValue: string
  /** 质量分数 */
  qualityScore: string
  /** 奖励信息 */
  awardInfo: string
  /** 奖励信息备注 */
  awardInfoRemark: string
  /** 是否管理学生 */
  isManageStudent: number
  /** 学习年份 */
  studyYear: string
  /** 学习学期 */
  studyTerm: number
  /** 项目名称 */
  projectName: string
  /** 项目类型 */
  projectType: string
  /** 项目处理类型 */
  projectTreatmentType: number
  /** 部门编码 */
  deptCode: string
  /** 部门名称 */
  deptName: string
  /** 项目描述 */
  projectDescription: string
  /** 开放校区 */
  openCampus: string
  /** 开放校区名称 */
  openCampusName: string
  /** 开放年级 */
  openGrade: string
  /** 开放部门 */
  openDept: string
  /** 开放部门名称 */
  openDeptName: string
  /** 开放专业 */
  openMajor: string
  /** 开放专业名称 */
  openMajorName: string
  /** 开放班级 */
  openClass: string
  /** 开放班级名称 */
  openClassName: string
  /** 总人数 */
  totalNumber: number
  /** 部门审核 */
  deptAudit: number
  /** 主任审核 */
  directorAudit: number
  /** 已有人数 */
  alreadyNumber: number
  /** 审核人数 */
  auditNumber: number
  /** 负责教师 */
  responsibleTeacher: string
  /** 负责教师姓名 */
  responsibleTeacherName: string
  /** 开始时间 */
  startTime: string
  /** 结束时间 */
  endTime: string
  /** 报名开放状态 */
  enrollmentOpenStatus: number
  /** 报名开放开始时间 */
  enrollmentOpenStartTime: string
  /** 报名开放结束时间 */
  enrollmentOpenEndTime: string
  /** 开放签到类型 */
  openSignInType: number
}

/**
 * 培训会议响应数据
 */
export interface TrainingConferenceResponse {
  /** 数据项列表 */
  items: TrainingConferenceItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总条数 */
  total: number
}
