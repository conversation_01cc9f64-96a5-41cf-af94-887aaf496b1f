// 出入校记录查询参数
export interface TransitQuery {
  /** 页码 */
  page: number
  /** 每页条数 */
  pageSize: number
  /** 出入类型: "进"或"出"，不传表示全部 */
  entryExitType?: string
  /** 通行凭证名称 */
  passCredentialName?: string
  /** 通行时间 */
  passTime?: string
  /** 通行门禁名称 */
  passGateName?: string
}

// 出入校记录项
export interface TransitItem {
  /** 记录ID */
  id: number
  /** 人员类型 */
  personnelType: number
  /** 第三方人员类型 */
  thirdPartyPersonnelType: string
  /** 第三方人员类型名称 */
  thirdPartyPersonnelTypeName: string
  /** 部门编码 */
  departmentCode: string
  /** 部门名称 */
  departmentName: string
  /** 学生班级编码 */
  studentClassCode: string
  /** 学生班级名称 */
  studentClassName: string
  /** 人员编码 */
  personnelCode: string
  /** 人员姓名 */
  name: string
  /** 性别: 1-男, 2-女 */
  gender: number
  /** 手机号码 */
  phoneNumber: string
  /** 出入类型: "进"或"出" */
  entryExitType: string
  /** 通行校区编码 */
  passCampusCode: string
  /** 通行校区名称 */
  passCampusName: string
  /** 通行门禁编码 */
  passGateCode: string
  /** 通行门禁名称 */
  passGateName: string
  /** 通行方式 */
  passMethod: string
  /** 通行原因描述 */
  passReasonDescription: string
  /** 通行凭证编码 */
  passCredentialCode: string
  /** 通行凭证名称 */
  passCredentialName: string
  /** 通行日期 */
  passDate: string
  /** 通行时间 */
  passTime: string
  /** 值班人员编码 */
  dutyPersonnelCode: string
  /** 值班人员姓名 */
  dutyPersonnelName: string
  /** 创建时间 */
  createTime: number
  /** 更新时间 */
  updateTime: number
  /** 删除标记 */
  deleteTag: number
  /** 操作人编码 */
  operatorCode: string
}

// 出入校记录响应数据
export interface TransitResponse {
  /** 记录列表 */
  items: TransitItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总记录数 */
  total: number
}
