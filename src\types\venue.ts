/**
 * 授课场地相关类型定义
 */

/**
 * 场地信息查询参数
 */
export interface VenueQuery {
  /** 页码 */
  page: number
  /** 每页大小 */
  pageSize: number
  /** 搜索关键词 */
  searchKeyword?: string
}

/**
 * 场地信息响应结果
 */
export interface VenueResponse {
  /** 列表数据 */
  items: Venue[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总数 */
  total: number
}

/**
 * 场地信息
 */
export interface Venue {
  /** 场地ID */
  id: number
  /** 场地代码 */
  siteCode: string
  /** 场地名称 */
  siteName: string
  /** 场地名称（兼容字段） */
  name?: string
  /** 场地类型 */
  type?: string
  /** 场地类型名称 */
  typeName?: string
  /** 分类名称 */
  categoryName: string
  /** 校区代码 */
  campusCode?: string
  /** 校区名称 */
  campusName: string
  /** 建筑楼代码 */
  buildingCode?: string
  /** 建筑楼名称 */
  buildingName: string
  /** 楼层 */
  floor?: number
  /** 房间号 */
  roomNumber?: string
  /** 容量 */
  capacity?: number
  /** 学生容量 */
  studentCapacity: number
  /** 状态 */
  status?: 'free' | 'occupied' | 'maintenance'
  /** 位置描述 */
  location?: string
  /** 设备信息 */
  equipment?: string
  /** 备注 */
  remark?: string
  /** 创建时间 */
  create_time?: number
  /** 更新时间 */
  update_time?: number
  /** 删除标记 */
  deltag?: number
  /** 操作人员编号 */
  oprybh?: string
}
