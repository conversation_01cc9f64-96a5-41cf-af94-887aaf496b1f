/**
 * 工作流相关类型定义
 */

/**
 * 工作流查询参数
 */
export interface WorkflowQuery {
  /** 页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 排序字段 */
  sortBy: string
  /** 排序方式 */
  sortOrder: 'asc' | 'desc'
  /** 待办类型 */
  todoType: string
  /** 列表类型 */
  listType: string
  /** ID列表 */
  id: number[]
  /** 表单标题 */
  form_title: string[]
  /** 创建人名称 */
  create_user_name: string[]
  /** 申请时间 */
  applyTime: string[]
  /** 当前步骤名称 */
  currentStepName: string[]
  /** 状态 */
  status: number[]
  /** 流程状态 */
  process_status: number[]
  /** 审批意见 */
  approval_opinion: string[]
  /** 审核时间 */
  checkTime: string[]
}

/**
 * 工作流条目类型
 */
export interface WorkflowItem {
  /** 审批意见 */
  approval_opinion: string | null
  /** 审核时间 */
  checkTime: string | null
  /** 状态 */
  status: number
  /** 当前步骤名称 */
  currentStepName: string
  /** 申请时间 */
  applyTime: string
  /** 创建人名称 */
  create_user_name: string
  /** 表单标题 */
  form_title: string
  /** ID */
  id: number
  /** 标题 */
  title: string
  /** 代码 */
  code: string | null
  /** 流程状态 */
  process_status: number
  /** 创建时间 */
  createTime: string
  /** 审批状态颜色 */
  approvalStatusColor: string
  /** 审批状态 */
  approvalStatus: string
  /** 流程状态颜色 */
  processStatusColor: string
  /** 流程状态 */
  processStatus: string
}

/**
 * 工作流响应数据
 */
export interface WorkflowResponse {
  /** 工作流列表 */
  items: WorkflowItem[]
  /** 查询参数 */
  query: Record<string, any>
  /** 总数 */
  total: number
}

/**
 * 工作流表单字段类型
 */
export interface WorkflowField {
  /** 字段名称 */
  name: string
  /** 是否显示 0-不显示 1-显示 */
  show: string
  /** 字段标签 */
  label: string
  /** 字段类型 */
  type: string
  /** 字段属性 */
  itemAttr?: Record<string, any> | any[]
}

/**
 * 工作流节点配置类型
 */
export interface WorkflowNode {
  /** 节点ID */
  id: number
  /** 节点级别 */
  level: number
  /** 节点名称 */
  name: string
  /** 节点类型 */
  type: string
  /** 推送ID */
  push_id: number
  /** 通知ID */
  notify_id: number
  /** 通过ID */
  pass_id: number
  /** 拒绝ID */
  refuse_id: number
  /** 撤销ID */
  revoke_id: number | null
  /** 催促ID */
  urge_id: number | null
  /** 表单ID */
  form_id: number
  /** 记录ID */
  record_id: number
  /** 流程ID */
  process_id: number
  /** 标题 */
  title: string
  /** 用户ID */
  user_id: string
  /** 审批人数量 */
  approver_num: number
  /** 节点配置 */
  node_config: string
  /** 用户名称 */
  user_name: string
  /** 用户标签 */
  user_label: string
  /** 推送类型 */
  push_type: string
  /** 推送状态 */
  push_status: number
  /** 状态 */
  status: number
  /** 催促时间 */
  urge_time: string | null
  /** 是否最后一个节点 */
  is_last: number
  /** 是否自动审批 */
  is_auto_approve: number
  /** 自动审批状态 */
  auto_approve_status: number
  /** 审批意见 */
  approval_opinion: string | null
  /** 创建人 */
  create_by: string
  /** 创建时间 */
  create_time: number
  /** 更新时间 */
  update_time: number
  /** 删除时间 */
  delete_time: number | null
  /** 备注 */
  remark: string
  /** 操作人 */
  operate_by: string
  /** 是否可审批 */
  is_approve?: boolean
}

/**
 * 审批信息类型
 */
export interface ApprovalInfo {
  /** 审批人名称 */
  name: string
  /** 审批意见 */
  value: string
  /** 审批时间 */
  time: string
  /** 审批状态 */
  status: number
  /** 附件文件路径 */
  file?: string
}

/**
 * 工作流过程节点信息类型
 */
export interface WorkflowProcessItem {
  /** 用户名称 */
  user_name: string
  /** 创建用户名称 */
  create_user_name?: string
  /** 创建人 */
  create_by?: string
  /** 节点配置 */
  node_config: string
  /** 节点类型 */
  type: string
  /** 节点级别 */
  level: string | number
  /** 状态 */
  status: string | number
  /** 名称 */
  name: string
  /** 用户ID */
  user_id: string
  /** ID */
  id: number | string
  /** 时间 */
  time: string
  /** 节点信息 */
  info?: ApprovalInfo[]
  // 包含 WorkflowNode 中的其他属性
  push_id?: number
  notify_id?: number
  pass_id?: number
  refuse_id?: number
  revoke_id?: number | null
  urge_id?: number | null
  form_id?: number
  record_id?: number
  process_id?: number
  title?: string
  approver_num?: number
  user_label?: string
  push_type?: string
  push_status?: number
  urge_time?: string | null
  is_last?: number
  is_auto_approve?: number
  auto_approve_status?: number
  approval_opinion?: string | null
  create_time?: number
  update_time?: number | null
  delete_time?: number | null
  remark?: string
  operate_by?: string
}

/**
 * 工作流列表项类型
 */
export interface WorkflowListItem {
  /** 审批人名称 */
  approveName: string
  /** 审批人ID */
  approver: string
  /** 业务ID */
  businessId: number
  /** 类别 */
  category: string | null
  /** 类别名称 */
  categoryName: string | null
  /** 协作者 */
  collaborator: string | null
  /** 协作类型 */
  cooperateType: string | null
  /** 协作类型名称 */
  cooperateTypeName: string | null
  /** 创建人 */
  createBy: string | null
  /** 创建人名称 */
  createByName: string
  /** 创建时间 */
  createTime: string
  /** 定义ID */
  definitionId: number
  /** 删除标志 */
  delFlag: number
  /** 扩展信息 */
  ext: string | null
  /** 流程代码 */
  flowCode: number
  /** 流程名称 */
  flowName: string
  /** 流程状态 */
  flowStatus: string | number
  /** 流程状态名称 */
  flowStatusName: string | null
  /** 流程任务状态 */
  flowTaskStatus: string | null
  /** 表单自定义 */
  formCustom: string
  /** 表单路径 */
  formPath: string
  /** ID */
  id: number
  /** 实例ID */
  instanceId: number
  /** 消息 */
  message: string | null
  /** 节点代码 */
  nodeCode: number
  /** 节点名称 */
  nodeName: string
  /** 节点类型 */
  nodeType: number
  /** 权限列表 */
  permissionList: string | null
  /** 运行时长 */
  runDuration: string | null
  /** 跳过类型 */
  skipType: string | null
  /** 目标节点代码 */
  targetNodeCode: string | null
  /** 目标节点名称 */
  targetNodeName: string | null
  /** 任务ID */
  taskId: string | null
  /** 租户ID */
  tenantId: string
  /** 更新时间 */
  updateTime: string | null
  /** 版本 */
  version: string | null
}

/**
 * 工作流表单信息类型
 */
export interface WorkflowFormInfo {
  /** 状态 */
  status: number
  /** 撤销状态 */
  revokeStatus: boolean
  /** 级别 */
  level: number
  /** 评估状态 */
  evalStatus: boolean
  /** 标题 */
  title: string
  /** ID */
  id: number
  /** 是否申请 */
  isApply: boolean
  /** 评估信息 */
  evaluate: {
    /** 消息 */
    message: string | null
    /** 星级 */
    star: number
  }
}

/**
 * 工作流详情响应类型
 */
export interface WorkflowDetailResponse {
  /** 表单字段 */
  fields: WorkflowField[]
  /** 节点信息 */
  node: WorkflowNode
  /** 流程信息 - 二维数组，每个元素表示一个流程分支 */
  process: WorkflowProcessItem[][]
  /** 表单数据 */
  formData: Record<string, any>
  /** 图片 */
  image: string
  /** 表单信息 */
  form: WorkflowFormInfo
  /** 表格列定义 */
  cols?: Record<
    string,
    Array<{
      title: string
      dataIndex: string
      key: string
      minWidth?: number
      width?: number
    }>
  >
}

/**
 * 审批通过任务请求参数
 */
export interface CompleteTaskRequest {
  /** 任务ID */
  taskId: number
  /** 审批意见 */
  message: string
  /** 附件文件路径 */
  file?: string
  /** 任务变量 */
  taskVariables?: Record<string, any>
  /** 流程变量 */
  variables?: Record<string, any>
  /** 状态：1-通过 */
  status: number
}

/**
 * 审批拒绝任务请求参数
 */
export interface TerminationTaskRequest {
  /** 任务ID */
  taskId: number
  /** 审批意见 */
  message: string
  /** 状态：2-拒绝 */
  status: number
}

/**
 * 审批任务响应
 */
export interface ApprovalTaskResponse {
  /** 状态码 */
  code: number
  /** 消息 */
  msg: string
  /** 时间戳 */
  time: number
  /** 数据 */
  data: boolean
}

/**
 * 获取任务请求参数
 */
export interface GetTaskRequest {
  /**
   * 任务ID
   */
  id: number
  /**
   * 任务代码
   */
  code: string
}

/**
 * 任务详情
 */
export interface TaskDetail {
  /**
   * 任务ID
   */
  id: number
  /**
   * 流程状态 0-处理中
   */
  flowStatus: number
  /**
   * 任务标题
   */
  title: string
  /**
   * 创建时间戳
   */
  create_time: number
  /**
   * 创建时间字符串
   */
  createTime: string
}

/**
 * 工作流时间线请求参数
 */
export interface WorkflowTimelineRequest {
  /** 任务ID */
  id: number
  /** 任务代码 */
  code: string
}

/**
 * 工作流时间线响应数据
 */
export interface WorkflowTimelineResponse {
  /** 节点信息 */
  node: WorkflowNode
  /** 流程信息 - 二维数组，每个元素表示一个流程分支 */
  process: WorkflowProcessItem[][]
  /** 表单数据 */
  formData: Record<string, any>
  /** 表单信息 */
  form: WorkflowFormInfo
}

/**
 * 撤销任务请求参数
 */
export interface RevokeTaskRequest {
  /** 任务ID */
  taskId: number
  /** 撤销原因 */
  message: string
  /** 状态：3-撤销 */
  status: number
  /** 是否撤销 */
  revoke: boolean
}

/**
 * 工作流分类项
 */
export interface WorkflowCategory {
  /**
   * 分类名称
   */
  name: string
  /**
   * 分类编码
   */
  code: string
}

/**
 * 工作流分类列表响应
 */
export type WorkflowCategoryListResponse = WorkflowCategory[]
