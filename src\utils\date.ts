/**
 * 日期格式化
 * @param date 日期对象
 * @param fmt 格式化字符串
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: Date, fmt: string): string {
  const o: Record<string, number> = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    S: date.getMilliseconds(), // 毫秒
  }

  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }

  for (const k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] + '' : ('00' + o[k]).substr(('' + o[k]).length),
      )
    }
  }

  return fmt
}

/**
 * 获取日期是星期几
 * @param date 日期对象
 * @returns 星期几字符串
 */
export function getWeekDay(date: Date): string {
  const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  return weekDays[date.getDay()]
}

/**
 * 获取两个日期之间的天数
 * @param date1 日期1
 * @param date2 日期2
 * @returns 天数
 */
export function getDaysBetween(date1: Date, date2: Date): number {
  const time1 = date1.getTime()
  const time2 = date2.getTime()
  const diff = Math.abs(time1 - time2)
  return Math.floor(diff / (1000 * 60 * 60 * 24))
}

/**
 * 日期加减天数
 * @param date 日期
 * @param days 天数，正数为加，负数为减
 * @returns 新日期
 */
export function addDays(date: Date, days: number): Date {
  const result = new Date(date.getTime())
  result.setDate(result.getDate() + days)
  return result
}

/**
 * 获取日期所在周的开始日期（周一）
 * @param date 日期
 * @returns 周开始日期
 */
export function getWeekStart(date: Date): Date {
  const day = date.getDay() || 7
  return addDays(date, 1 - day)
}

/**
 * 获取日期所在周的结束日期（周日）
 * @param date 日期
 * @returns 周结束日期
 */
export function getWeekEnd(date: Date): Date {
  const day = date.getDay() || 7
  return addDays(date, 7 - day)
}
