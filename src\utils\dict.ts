/**
 * 字典工具函数
 *
 * 本工具提供了统一的字典数据处理方法,包括:
 * 1. 字典数据的加载和缓存
 * 2. 字典标签的获取
 * 3. 字典样式的获取
 * 4. 字典选项的转换
 * 5. 地区数据的获取和缓存
 *
 * 使用示例:
 *
 * 1. 加载字典数据:
 * ```typescript
 * import { loadDictData } from '@/utils/dict'
 *
 * // 加载单个字典
 * const dicts = await loadDictData(['DM_KCXZ'])
 * const courseTypeDict = dicts.DM_KCXZ
 *
 * // 加载多个字典
 * const dicts = await loadDictData(['DM_KCXZ', 'DM_KCLX'])
 * const courseTypeDict = dicts.DM_KCXZ
 * const courseCategoryDict = dicts.DM_KCLX
 * ```
 *
 * 2. 获取字典标签:
 * ```typescript
 * import { getDictLabel } from '@/utils/dict'
 *
 * // 获取字典标签
 * const label = getDictLabel(courseTypeDict, '1')
 * console.log(label) // 输出: 必修课
 * ```
 *
 * 3. 获取字典样式:
 * ```typescript
 * import { getDictClass } from '@/utils/dict'
 *
 * // 获取字典样式类
 * const className = getDictClass(courseTypeDict, '1')
 * console.log(className) // 输出: primary
 * ```
 *
 * 4. 获取字典选项:
 * ```typescript
 * import { getDictOptions } from '@/utils/dict'
 *
 * // 获取字典选项
 * const options = getDictOptions(courseTypeDict)
 * console.log(options) // 输出: [{ label: '必修课', value: '1' }, ...]
 * ```
 *
 * 5. 获取城市字典数据:
 * ```typescript
 * import { getCityDictData } from '@/utils/dict'
 *
 * // 获取城市数据
 * const cityData = await getCityDictData()
 * console.log(cityData) // 输出省市区三级数据
 * ```
 *
 * 注意事项:
 * 1. 字典数据会自动缓存并持久化存储,避免重复请求
 * 2. 如果字典数据加载失败,会返回空数组
 * 3. 如果找不到对应的字典值,会返回空字符串
 * 4. 建议在组件初始化时加载字典数据
 */

import { getDictData } from '@/service/system'
import type { DictData } from '@/types/system'
import { reactive } from 'vue'
import { useDictStore } from '@/store/dict'
import { getCityDict } from '@/service/dict'
import type { ProvinceItem } from '@/types/dict'
import { DictTypeEnum } from '@/types/system'

// 存储正在加载中的字典请求
const loadingDictPromises: Record<string, Promise<DictData[]>> = {}

/**
 * 加载字典数据
 * @param dictTypes 字典类型数组
 * @returns 返回包含所有字典数据的对象
 */
export async function loadDictData(dictTypes: string[]) {
  const dictStore = useDictStore()
  const dictResult = reactive<Record<string, DictData[]>>({})
  const loadingPromises: Promise<void>[] = []

  for (const dictType of dictTypes) {
    // 如果缓存中存在，则直接使用缓存
    if (dictStore.hasDictCache(dictType)) {
      dictResult[dictType] = dictStore.getDictCache(dictType) || []
      continue
    }

    // 如果已经有相同的字典正在请求中，则复用该Promise
    if (loadingDictPromises[dictType]) {
      const loadPromise = loadingDictPromises[dictType]
        .then((data) => {
          dictResult[dictType] = data
        })
        .catch((error) => {
          console.error(`加载字典 ${dictType} 失败:`, error)
          dictResult[dictType] = []
        })

      loadingPromises.push(loadPromise)
      continue
    }

    // 创建新的请求Promise并缓存
    const dictPromise = getDictData(dictType)
      .then((data) => {
        // 缓存结果到 store
        dictStore.setDictCache(dictType, data)
        return data
      })
      .catch((error) => {
        console.error(`加载字典 ${dictType} 失败:`, error)
        /* uni.showToast({
          title: `加载字典数据失败: ${dictType}`,
          icon: 'none',
        }) */
        return [] as DictData[]
      })
      .finally(() => {
        // 请求完成后，删除缓存的Promise
        delete loadingDictPromises[dictType]
      })

    // 缓存正在加载的Promise
    loadingDictPromises[dictType] = dictPromise

    // 添加到等待队列
    const loadPromise = dictPromise.then((data) => {
      dictResult[dictType] = data
    })

    loadingPromises.push(loadPromise)
  }

  // 等待所有请求完成
  await Promise.all(loadingPromises)
  return dictResult
}

/**
 * 获取字典标签
 * @param dictData 字典数据
 * @param value 字典值
 * @returns 字典标签
 */
export function getDictLabel(dictData: DictData[] | undefined, value: string): string {
  if (!dictData || dictData.length === 0 || !value) {
    return ''
  }

  const dict = dictData.find((item) => item.dictValue === value)
  return dict ? dict.dictLabel : ''
}

/**
 * 获取字典样式类
 * @param dictData 字典数据
 * @param value 字典值
 * @returns 样式类
 */
export function getDictClass(dictData: DictData[] | undefined, value: string): string {
  if (!dictData || dictData.length === 0 || !value) {
    return ''
  }

  const dict = dictData.find((item) => item.dictValue === value)
  return dict ? dict.listClass : ''
}

/**
 * 获取字典项
 * @param dictData 字典数据
 * @returns 转换后的下拉选项数组
 */
export function getDictOptions(
  dictData: DictData[] | undefined,
): { label: string; value: string }[] {
  if (!dictData || dictData.length === 0) {
    return []
  }

  return dictData.map((item) => ({
    label: item.dictLabel,
    value: item.dictValue,
  }))
}

/**
 * 直接从store获取字典数据
 * @param dictType 字典类型
 * @returns 字典数据
 */
export function getDictDataFromStore(dictType: string): DictData[] {
  const dictStore = useDictStore()
  return dictStore.getDictCache(dictType) || []
}

/**
 * 清除字典缓存
 * @param dictType 字典类型，不传则清除所有
 */
export function clearDictCache(dictType?: string) {
  const dictStore = useDictStore()
  if (dictType) {
    dictStore.clearDictCache(dictType)
  } else {
    dictStore.clearAllDictCache()
  }
}

/**
 * 获取城市字典数据
 * 会优先从dictStore中获取，如果没有则请求API并缓存结果
 * @returns 省市区三级数据
 */
export async function getCityDictData(): Promise<ProvinceItem[]> {
  const dictStore = useDictStore()

  // 检查缓存中是否已存在城市数据
  if (dictStore.hasDictCache(DictTypeEnum.CITY_DICT)) {
    // 直接从缓存获取
    const cityData = dictStore.getDictCache(DictTypeEnum.CITY_DICT)

    if (cityData && Array.isArray(cityData) && cityData.length > 0) {
      console.log('从缓存中获取城市数据', cityData.length)
      return cityData as unknown as ProvinceItem[]
    }
  }

  // 缓存中不存在，则请求API获取
  try {
    console.log('从API获取城市数据')
    const cityData = await getCityDict()

    // 缓存结果
    dictStore.setDictCache(DictTypeEnum.CITY_DICT, cityData as unknown as DictData[])

    return cityData
  } catch (error) {
    console.error('获取城市数据失败:', error)
    uni.showToast({
      title: '获取城市数据失败',
      icon: 'none',
    })
    return []
  }
}
