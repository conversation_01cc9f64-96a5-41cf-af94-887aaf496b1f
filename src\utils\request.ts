import { CustomRequestOptions } from '@/interceptors/request'
import { IResponse } from '@/types/response'
import { useUserStore } from '@/store/user'
import { useMenuStore } from '@/store/menu'

/**
 * 检查URL中是否包含自动登录参数
 */
const hasAutoLoginParams = (): boolean => {
  // 只在非微信小程序环境下检查URL参数
  if (uni.getSystemInfoSync().platform === 'mp-weixin') {
    return false
  }

  // 获取当前URL
  const currentUrl = window.location.href

  // 检查URL参数中是否包含token和uid
  const queryString = currentUrl.split('?')[1]
  if (!queryString) return false

  const paramPairs = queryString.split('&')
  const params: Record<string, string> = {}
  paramPairs.forEach((pair) => {
    const [key, value] = pair.split('=')
    params[key] = decodeURIComponent(value || '')
  })

  return !!(params.token && params.uid)
}

/**
 * 重定向到自动登录页面
 * @param currentPath 当前路径
 * @param query 查询参数
 */
const redirectToAutoLogin = (currentPath: string, query: Record<string, string> = {}) => {
  // 获取用户store
  const userStore = useUserStore()

  // 保存重定向信息到store（不强制覆盖已有值）
  userStore.setRedirectInfo(
    {
      path: currentPath,
      query,
    },
    false,
  )

  // 跳转到自动登录页面
  uni.reLaunch({ url: '/pages/login/auto-login' })
}

/**
 * 获取当前页面路径和参数
 */
const getCurrentPathAndQuery = (): { path: string; query: Record<string, string> } => {
  // 获取当前页面路径
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const currentPath = currentPage ? '/' + currentPage.route : '/pages/index/index'

  // 获取当前页面参数
  const query: Record<string, string> = {}
  if (currentPage) {
    const options = (currentPage as any).options || {}
    Object.keys(options).forEach((key) => {
      query[key] = options[key]
    })
  }

  return { path: currentPath, query }
}

/**
 * 请求方法: 主要是对 uni.request 的封装，去适配 openapi-ts-request 的 request 方法
 * @param options 请求参数
 * @returns 返回 Promise 对象
 */
const http = <T>(options: CustomRequestOptions) => {
  // 获取用户store
  const userStore = useUserStore()

  // 如果有token，添加到请求头
  if (userStore.tokenInfo.token) {
    options.header = {
      ...options.header,
      'ba-token': userStore.tokenInfo.token,
    }
  }
  options.header = {
    ...options.header,
    server: 1,
  }

  // 1. 返回 Promise 对象
  return new Promise<T>((resolve, reject) => {
    uni.request({
      ...options,
      timeout: 30000,
      dataType: 'json',
      // #ifndef MP-WEIXIN
      responseType: 'json',
      // #endif
      // 响应成功
      success(res) {
        const response = res.data as IResponse<T>
        // 状态码 2xx，参考 axios 的设计
        if (response.code >= 1 && response.code <= 200) {
          // 2.1 提取核心数据 res.data
          let data = response.data
          if (!response.data) {
            data = {
              ...data,
              frontMsg: response.data ? undefined : response.msg,
            }
          }
          resolve(data)
        } else if (response.code === 303 || response.code === 409) {
          // 401错误 -> 清理用户信息
          userStore.logout()
          const menuStore = useMenuStore()
          menuStore.clearMenus() // 清空菜单信息

          // 获取当前页面路径和参数
          const { path, query } = getCurrentPathAndQuery()

          // 如果当前不是登录页，则跳转到自动登录页
          if (!path.startsWith('/pages/login/')) {
            redirectToAutoLogin(path, query)
          } else if (path === '/pages/login/login') {
            uni.showToast({
              title: '登录失败，请重试',
              icon: 'none',
            })
          }

          reject(response)
        } else {
          // 其他错误 -> 根据后端错误信息轻提示

          !options.hideErrorToast &&
            uni.showToast({
              icon: 'none',
              title: response.msg || '请求错误',
            })
          reject(response)
        }
      },
      // 响应失败
      fail(err) {
        uni.showToast({
          icon: 'none',
          title: '网络错误，换个网络试试',
        })
        reject(err)
      },
    })
  })
}

/*
 * openapi-ts-request 工具的 request 跨客户端适配方法
 */
export default function request<T = unknown>(
  url: string,
  options: Omit<CustomRequestOptions, 'url'> & {
    params?: Record<string, unknown>
    headers?: Record<string, unknown>
  },
) {
  const requestOptions = {
    url: '/api' + url,
    ...options,
  }

  if (options.params) {
    requestOptions.query = requestOptions.params
    delete requestOptions.params
  }

  if (options.headers) {
    requestOptions.header = options.headers
    delete requestOptions.headers
  }

  return http<T>(requestOptions)
}
